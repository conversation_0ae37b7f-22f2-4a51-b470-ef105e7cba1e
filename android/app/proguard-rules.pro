# Flutter specific ProGuard rules

# Keep Flutter framework
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }

# Keep Kotlin
-keep class kotlin.** { *; }
-keep class kotlinx.** { *; }

# Keep Firebase and Google services if you use them
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }

# Keep Play Core library classes (required for Flutter deferred components)
-keep class com.google.android.play.core.** { *; }
-keep class com.google.android.play.** { *; }

# Keep R8 safe for Flutter
-dontwarn io.flutter.embedding.engine.deferredcomponents.**
-dontwarn com.google.android.play.**

# If your app uses reflection or has specific serializable classes
# add their specific keep rules here

# The following are example rules you may uncomment if needed for your app
# -keep class com.xoxknit.models.** { *; }  # Keep your model classes
# -keepattributes *Annotation*              # Keep annotations
# -keepattributes SourceFile,LineNumberTable # Keep file names and line numbers for better crash reports 