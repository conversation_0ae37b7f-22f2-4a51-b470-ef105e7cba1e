// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDAV8J8VfGG5f3LsChF1h3dLz_FUoO4lMM',
    appId: '1:50152326994:web:e6d0e0c72fd2e5818bdc8b',
    messagingSenderId: '50152326994',
    projectId: 'xoxknit',
    authDomain: 'xoxknit.firebaseapp.com',
    storageBucket: 'xoxknit.firebasestorage.app',
    measurementId: 'G-MN6X79FB52',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBl7LkLtqt_nJu7j4Lzq4EXwItgaK5gWCc',
    appId: '1:50152326994:android:05d1fcf7dcccc2038bdc8b',
    messagingSenderId: '50152326994',
    projectId: 'xoxknit',
    storageBucket: 'xoxknit.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAK4vP6IeKhY9t5gpQVXgKu0O5LYLboAh4',
    appId: '1:50152326994:ios:1a4439f2a9c9cc218bdc8b',
    messagingSenderId: '50152326994',
    projectId: 'xoxknit',
    storageBucket: 'xoxknit.firebasestorage.app',
    iosBundleId: 'com.xoxknit.xoxknit',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyAK4vP6IeKhY9t5gpQVXgKu0O5LYLboAh4',
    appId: '1:50152326994:ios:1a4439f2a9c9cc218bdc8b',
    messagingSenderId: '50152326994',
    projectId: 'xoxknit',
    storageBucket: 'xoxknit.firebasestorage.app',
    iosBundleId: 'com.xoxknit.xoxknit',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDAV8J8VfGG5f3LsChF1h3dLz_FUoO4lMM',
    appId: '1:50152326994:web:acb57dbe1e61b79f8bdc8b',
    messagingSenderId: '50152326994',
    projectId: 'xoxknit',
    authDomain: 'xoxknit.firebaseapp.com',
    storageBucket: 'xoxknit.firebasestorage.app',
    measurementId: 'G-87RZPPJVW4',
  );
}