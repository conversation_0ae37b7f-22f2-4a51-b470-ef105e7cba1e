import 'package:flutter/material.dart';
import 'package:xoxknit/app/core/theme/app_typography.dart';
import 'app_colors.dart';

class AppTheme {
  static const _defaultBorderRadius = 8.0;
  static const _defaultPadding = 16.0;

  static final _baseTextFieldDecoration = InputDecorationTheme(
    contentPadding: const EdgeInsets.symmetric(
      horizontal: _defaultPadding,
      vertical: _defaultPadding,
    ),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_defaultBorderRadius),
      borderSide: const BorderSide(width: 1),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_defaultBorderRadius),
      borderSide: const BorderSide(width: 1),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_defaultBorderRadius),
      borderSide: const BorderSide(width: 2),
    ),
    errorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_defaultBorderRadius),
      borderSide: const BorderSide(color: Colors.red, width: 1),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_defaultBorderRadius),
      borderSide: const BorderSide(color: Colors.red, width: 2),
    ),
    filled: true,
    isDense: true,
    floatingLabelBehavior: FloatingLabelBehavior.always,
  );

  static final ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    primaryColor: AppColors.magenta,
    scaffoldBackgroundColor: Color(0xFFF5F5F7),
    colorScheme: ColorScheme.light(
      primary: AppColors.magenta,
      onPrimary: AppColors.white,
      secondary: AppColors.purple,
      surface: Colors.white,
      surfaceContainerHighest: Colors.grey.shade50,
      surfaceTint: Colors.white,
      onSurface: Colors.black87,
      shadow: Colors.black,
      error: Colors.red,
    ),
    cardTheme: CardTheme(
      color: Colors.white,
      elevation: 2,
      shadowColor: Colors.black.withOpacity(0.2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    ),
    navigationBarTheme: const NavigationBarThemeData(
      backgroundColor: AppColors.magenta,
      indicatorColor: AppColors.white,
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: AppColors.magenta,
      foregroundColor: AppColors.white,
      elevation: 0,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.magenta,
        foregroundColor: AppColors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    ),
    inputDecorationTheme: _baseTextFieldDecoration.copyWith(
      fillColor: AppColors.fieldFillLight,
      hintStyle: const TextStyle(color: AppColors.fieldHintLight),
      labelStyle: const TextStyle(color: AppColors.dark),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(_defaultBorderRadius),
        borderSide: const BorderSide(
          color: AppColors.fieldBorderLight,
          width: 1,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(_defaultBorderRadius),
        borderSide: const BorderSide(
          color: AppColors.fieldFocusedBorderLight,
          width: 2,
        ),
      ),
    ),
    textTheme: TextTheme(
      displayLarge: AppTypography.displayLarge,
      displayMedium: AppTypography.displayMedium,
      displaySmall: AppTypography.displaySmall,
      headlineLarge: AppTypography.headlineLarge,
      headlineMedium: AppTypography.headlineMedium,
      headlineSmall: AppTypography.headlineSmall,
      titleLarge: AppTypography.titleLarge,
      titleMedium: AppTypography.titleMedium,
      titleSmall: AppTypography.titleSmall,
      labelLarge: AppTypography.labelLarge,
      labelMedium: AppTypography.labelMedium,
      labelSmall: AppTypography.labelSmall,
      bodyLarge: AppTypography.bodyLarge,
      bodyMedium: AppTypography.bodyMedium,
      bodySmall: AppTypography.bodySmall,
    ).apply(
      bodyColor: AppColors.dark,
      displayColor: AppColors.dark,
    ),
  );

  static final ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    primaryColor: AppColors.magenta,
    scaffoldBackgroundColor: AppColors.dark,
    colorScheme: ColorScheme.dark(
      primary: AppColors.magenta,
      onPrimary: AppColors.white,
      secondary: AppColors.purple,
      surface: Color(0xFF2A2A2A),
      surfaceContainerHighest: Color(0xFF353535),
      surfaceTint: Color(0xFF2A2A2A),
      onSurface: Colors.white.withOpacity(0.87),
      shadow: Colors.black,
      error: Colors.red,
    ),
    cardTheme: CardTheme(
      color: Color(0xFF2A2A2A),
      elevation: 2,
      shadowColor: Colors.black.withOpacity(0.5),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    ),
    navigationBarTheme: const NavigationBarThemeData(
      backgroundColor: AppColors.magenta,
      indicatorColor: AppColors.darkGray,
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: AppColors.magenta,
      foregroundColor: AppColors.white,
      elevation: 0,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.magenta,
        foregroundColor: AppColors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    ),
    inputDecorationTheme: _baseTextFieldDecoration.copyWith(
      fillColor: AppColors.fieldFillDark,
      hintStyle: const TextStyle(color: AppColors.fieldHintDark),
      labelStyle: const TextStyle(color: AppColors.white),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(_defaultBorderRadius),
        borderSide: const BorderSide(
          color: AppColors.fieldBorderDark,
          width: 1,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(_defaultBorderRadius),
        borderSide: const BorderSide(
          color: AppColors.fieldFocusedBorderDark,
          width: 2,
        ),
      ),
    ),
    textTheme: TextTheme(
      displayLarge: AppTypography.displayLarge,
      displayMedium: AppTypography.displayMedium,
      displaySmall: AppTypography.displaySmall,
      headlineLarge: AppTypography.headlineLarge,
      headlineMedium: AppTypography.headlineMedium,
      headlineSmall: AppTypography.headlineSmall,
      titleLarge: AppTypography.titleLarge,
      titleMedium: AppTypography.titleMedium,
      titleSmall: AppTypography.titleSmall,
      labelLarge: AppTypography.labelLarge,
      labelMedium: AppTypography.labelMedium,
      labelSmall: AppTypography.labelSmall,
      bodyLarge: AppTypography.bodyLarge,
      bodyMedium: AppTypography.bodyMedium,
      bodySmall: AppTypography.bodySmall,
    ).apply(
      bodyColor: AppColors.white,
      displayColor: AppColors.white,
    ),
  );
}
