import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

class ThemeController extends GetxController {
  final _storage = GetStorage();
  final _key = 'isDarkMode';

  static ThemeController get to => Get.find();

  bool get isDarkMode => _storage.read(_key) ?? false;

  void toggleTheme() async {
    await _storage.write(_key, !isDarkMode);
    Get.changeThemeMode(!isDarkMode ? ThemeMode.light : ThemeMode.dark);
    update(["home"]);
  }

  ThemeMode get theme => isDarkMode ? ThemeMode.dark : ThemeMode.light;

  @override
  void onInit() {
    Get.changeThemeMode(theme);
    super.onInit();
  }
}
