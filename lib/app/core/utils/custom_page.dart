import 'package:flutter/material.dart';

Widget buildCustomPage(Widget body,
    {String title = 'Custom Page',
    bool isHome = false,
    bool isScaffold = true,
    bool isSafeArea = true,
    bool isCentered = false}) {
  return MaterialApp(
    home: isHome
        ? Scaffold(
            appBar: AppBar(
              title: Text(title),
            ),
            body: isSafeArea
                ? SafeArea(
                    child: isCentered
                        ? Center(
                            child: body,
                          )
                        : body,
                  )
                : isCentered
                    ? Center(
                        child: body,
                      )
                    : body,
          )
        : isScaffold
            ? Scaffold(
                appBar: AppBar(
                  title: Text(title),
                ),
                body: isSafeArea
                    ? SafeArea(
                        child: isCentered
                            ? Center(
                                child: body,
                              )
                            : body,
                      )
                    : isCentered
                        ? Center(
                            child: body,
                          )
                        : body,
              )
            : isSafeArea
                ? SafeArea(
                    child: isCentered
                        ? Center(
                            child: body,
                          )
                        : body,
                  )
                : isCentered
                    ? Center(
                        child: body,
                      )
                    : body,
  );
}
