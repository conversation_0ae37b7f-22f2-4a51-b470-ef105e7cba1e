import 'package:cloud_firestore/cloud_firestore.dart';

Future<void> populateMachineCollection() async {
  final List<Map<String, dynamic>> knittingMachines = [
    // Fine Gauge Family (3.5mm pitch)
    {
      'machineClass': 'BrotherKH110',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH110',
      'needlesCount': 258,
      'needlePitch': 3.5,
      'type': 'fine',
      'patternControltype': 'buttons',
      'patternRepeatLength': 8
    },
    {
      'machineClass': 'BrotherKH111',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH111',
      'needlesCount': 258,
      'needlePitch': 3.5,
      'type': 'fine',
      'patternControltype': 'buttons',
      'patternRepeatLength': 8
    },
    {
      'machineClass': 'BrotherKH120',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH120',
      'needlesCount': 258,
      'needlePitch': 3.5,
      'type': 'fine',
      'patternControltype': 'buttons',
      'patternRepeatLength': 8
    },

    // Mid Gauge Family (6.5mm pitch)
    {
      'machineClass': 'BrotherKH160',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH160',
      'needlesCount': 164,
      'needlePitch': 6.5,
      'type': 'mid',
      'patternControltype': null,
      'patternRepeatLength': 0
    },

    // Bulky Gauge Family (9mm pitch)
    {
      'machineClass': 'BrotherKH260',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH260',
      'needlesCount': 114,
      'needlePitch': 9,
      'type': 'bulky',
      'patternControltype': 'card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'BrotherArianna',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'Arianna',
      'needlesCount': 114,
      'needlePitch': 9,
      'type': 'bulky',
      'patternControltype': 'card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'BrotherKH270',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH270',
      'needlesCount': 114,
      'needlePitch': 9,
      'type': 'bulky',
      'patternControltype': 'electronic',
      'patternRepeatLength': 114
    },

    // Standard Gauge Family (4.5mm pitch)
    {
      'machineClass': 'BrotherKH588',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH588',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'buttons',
      'patternRepeatLength': 8
    },
    {
      'machineClass': 'BrotherKH710',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH710',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'buttons',
      'patternRepeatLength': 8
    },
    {
      'machineClass': 'BrotherKH820',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH820',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'BrotherKH830',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH830',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'BrotherKH836',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH836',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'BrotherPrimamore',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'Primamore',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'BrotherKH840',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH840',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'BrotherKH860',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH860',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'BrotherKH868',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH868',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'BrotherKM2000',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KM2000',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'BrotherKH880',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH880',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'BrotherKH891',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH891',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'card',
      'patternRepeatLength': 24
    },
    // Brother Standard Gauge s with Card Pattern Control
    {
      'machineClass': 'BrotherKH892',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH892',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'BrotherKH894',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH894',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'BrotherKM3000',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KM3000',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'BrotherGioia',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'Gioia',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'BrotherElyt',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'Elyt',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'card',
      'patternRepeatLength': 24
    },

    // Brother Electronic and Mylar Pattern Control models
    // Note the increased pattern repeat lengths
    {
      'machineClass': 'BrotherKH900',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH900',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'electronic',
      'patternRepeatLength': 60
    },
    {
      'machineClass': 'BrotherKH910',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH910',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'mylar',
      'patternRepeatLength': 60
    },
    {
      'machineClass': 'BrotherKH920',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH920',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'mylar',
      'patternRepeatLength': 60
    },
    {
      'machineClass': 'BrotherLadyD',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'LadyD',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'mylar',
      'patternRepeatLength': 60
    },

    // Brother Advanced Electronic models
    // These models feature the maximum 200-stitch pattern repeat length
    {
      'machineClass': 'BrotherKH930',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH930',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'electronic',
      'patternRepeatLength': 200
    },
    {
      'machineClass': 'BrotherKH940',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH940',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'electronic',
      'patternRepeatLength': 200
    },
    {
      'machineClass': 'BrotherFirstLady',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'First Lady',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'electronic',
      'patternRepeatLength': 200
    },
    {
      'machineClass': 'BrotherKH950i',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH950i',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'mylar',
      'patternRepeatLength': 60
    },
    {
      'machineClass': 'BrotherKH965',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH965',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'electronic',
      'patternRepeatLength': 200
    },
    {
      'machineClass': 'BrotherKH965i',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH965i',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'electronic',
      'patternRepeatLength': 200
    },
    {
      'machineClass': 'BrotherKH970',
      'mainBrand': 'Brother',
      'altBrands': ['Defendi', 'Vigorelli'],
      'model': 'KH970',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'electronic',
      'patternRepeatLength': 200
    },

    // Creative s
    // Note the unique needle counts and mix of gauges
    {
      'machineClass': 'Creative260XL',
      'mainBrand': 'Creative',
      'altBrands': ['Taitexma', 'xoxknit'],
      'model': '260 XL',
      'needlesCount': 150,
      'needlePitch': 9,
      'type': 'bulky',
      'patternControltype': 'card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'Creative160',
      'mainBrand': 'Creative',
      'altBrands': ['Taitexma', 'xoxknit'],
      'model': '160',
      'needlesCount': 164,
      'needlePitch': 6.5,
      'type': 'mid',
      'patternControltype': null,
      'patternRepeatLength': 0
    },
    {
      'machineClass': 'Creative868',
      'mainBrand': 'Creative',
      'altBrands': ['Taitexma', 'xoxknit'],
      'model': '868',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'Creative860XL',
      'mainBrand': 'Creative',
      'altBrands': ['Taitexma', 'xoxknit'],
      'model': '860 XL',
      'needlesCount': 272,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'card',
      'patternRepeatLength': 24
    },

    // Passap s
    // Note the consistent 180 needle count and 5mm pitch
    {
      'machineClass': 'PassapDuomaticSD',
      'mainBrand': 'Passap',
      'altBrands': ['Pfaff'],
      'model': 'Duomatic SD',
      'needlesCount': 180,
      'needlePitch': 5,
      'type': 'standard',
      'patternControltype': 'card',
      'patternRepeatLength': 40
    },
    {
      'machineClass': 'PassapDuomaticS',
      'mainBrand': 'Passap',
      'altBrands': ['Pfaff'],
      'model': 'Duomatic S',
      'needlesCount': 180,
      'needlePitch': 5,
      'type': 'standard',
      'patternControltype': 'card',
      'patternRepeatLength': 40
    },
    {
      'machineClass': 'PassapDuomatic80',
      'mainBrand': 'Passap',
      'altBrands': ['Pfaff'],
      'model': 'Duomatic 80',
      'needlesCount': 180,
      'needlePitch': 5,
      'type': 'standard',
      'patternControltype': 'card',
      'patternRepeatLength': 40
    },
    {
      'machineClass': 'PassapDuomatic5',
      'mainBrand': 'Passap',
      'altBrands': [],
      'model': 'Duomatic 5 (pinkie)',
      'needlesCount': 180,
      'needlePitch': 5,
      'type': 'standard',
      'patternControltype': null,
      'patternRepeatLength': 0
    },
    {
      'machineClass': 'PassapE6000',
      'mainBrand': 'Passap',
      'altBrands': [],
      'model': 'E6000',
      'needlesCount': 180,
      'needlePitch': 5,
      'type': 'standard',
      'patternControltype': 'electronic',
      'patternRepeatLength': 180
    },
    {
      'machineClass': 'PassapE8000',
      'mainBrand': 'Passap',
      'altBrands': [],
      'model': 'E8000',
      'needlesCount': 289,
      'needlePitch': 3,
      'type': 'fine',
      'patternControltype': 'electronic',
      'patternRepeatLength': 289
    },
    {
      'machineClass': 'PassapE8000 L',
      'mainBrand': 'Passap',
      'altBrands': [],
      'model': 'E8000 L',
      'needlesCount': 383,
      'needlePitch': 3,
      'type': 'fine',
      'patternControltype': 'electronic',
      'patternRepeatLength': 383
    },
    {
      'machineClass': 'SilverReedF270',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'F270',
      'needlesCount': 250,
      'needlePitch': 3.6,
      'type': 'fine',
      'patternControltype': 'card',
      'patternRepeatLength': 30
    },
    {
      'machineClass': 'SilverReedF272',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'F272',
      'needlesCount': 250,
      'needlePitch': 3.6,
      'type': 'fine',
      'patternControltype': 'card',
      'patternRepeatLength': 30
    },
    {
      'machineClass': 'SilverReedF370',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'F370',
      'needlesCount': 250,
      'needlePitch': 3.6,
      'type': 'fine',
      'patternControltype': 'card',
      'patternRepeatLength': 30
    },
    {
      'machineClass': 'SilverReedLK150',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'LK150',
      'needlesCount': 150,
      'needlePitch': 6.5,
      'type': 'mid',
      'patternControltype': null,
      'patternRepeatLength': 0
    },
    {
      'machineClass': 'SilverReedSK160',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'SK160',
      'needlesCount': 150,
      'needlePitch': 6.5,
      'type': 'mid',
      'patternControltype': null,
      'patternRepeatLength': 1
    },
    {
      'machineClass': 'SilverReedMK70',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'MK70',
      'needlesCount': 160,
      'needlePitch': 6,
      'type': 'mid',
      'patternControltype': 'card',
      'patternRepeatLength': 18
    },
    {
      'machineClass': 'SilverReedSK150',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'SK150',
      'needlesCount': 110,
      'needlePitch': 9,
      'type': 'bulky',
      'patternControltype': null,
      'patternRepeatLength': 0
    },
    {
      'machineClass': 'SilverReedSK151',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'SK151',
      'needlesCount': 110,
      'needlePitch': 9,
      'type': 'bulky',
      'patternControltype': null,
      'patternRepeatLength': 0
    },
    {
      'machineClass': 'SilverReedSK155',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'SK155',
      'needlesCount': 110,
      'needlePitch': 9,
      'type': 'fine',
      'patternControltype': 'Card',
      'patternRepeatLength': 12
    },
    {
      'machineClass': 'SilverReedSK570',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'SK570',
      'needlesCount': 250,
      'needlePitch': 3.6,
      'type': 'fine',
      'patternControltype': 'Mylar',
      'patternRepeatLength': 60
    },
    {
      'machineClass': 'SilverReedSK830',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'SK830',
      'needlesCount': 250,
      'needlePitch': 3.6,
      'type': 'fine',
      'patternControltype': 'Electronic',
      'patternRepeatLength': 250
    },
    {
      'machineClass': 'SilverReedSK550',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'SK550',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'Mylar',
      'patternRepeatLength': 60
    },
    {
      'machineClass': 'SilverReedSK560',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'SK560',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'Mylar',
      'patternRepeatLength': 60
    },
    {
      'machineClass': 'SilverReedSK580',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'SK580',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'Mylar',
      'patternRepeatLength': 60
    },
    {
      'machineClass': 'SilverReedSK840',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'SK840',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'Electronic',
      'patternRepeatLength': 200
    },
    {
      'machineClass': 'SilverReedSK860',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'SK860',
      'needlesCount': 150,
      'needlePitch': 6.5,
      'type': 'mid',
      'patternControltype': 'Mylar',
      'patternRepeatLength': 60
    },
    {
      'machineClass': 'SilverReedSK890',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'SK890',
      'needlesCount': 110,
      'needlePitch': 9,
      'type': 'bulky',
      'patternControltype': 'Mylar',
      'patternRepeatLength': 60
    },
    {
      'machineClass': 'SilverReedSK210',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'SK210',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'Card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'SilverReedSK260',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'SK260',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'Card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'SilverReedSK280',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'SK280',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'Card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'SilverReedSK360',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'SK360',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'Card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'SilverReedSK321',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'SK321',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'Card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'SilverReedSK324',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'SK324',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'Card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'SilverReedSK328',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'SK328',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'Card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'SilverReedSK600',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'SK600',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'Card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'SilverReedSK700',
      'mainBrand': 'Silver Reed',
      'altBrands': ['Empisal', 'Necchi', 'Dimac'],
      'model': 'SK700',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'Card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'Singer9000',
      'mainBrand': 'Singer',
      'altBrands': ['Superba'],
      'model': '9000',
      'needlesCount': 180,
      'needlePitch': 5,
      'type': 'standard',
      'patternControltype': 'electronic + card',
      'patternRepeatLength': 60
    },
    {
      'machineClass': 'SingerMagliabellaMemomatic',
      'mainBrand': 'Singer',
      'altBrands': [],
      'model': 'MagliabellaMemomatic',
      'needlesCount': 180,
      'needlePitch': 5,
      'type': 'standard',
      'patternControltype': 'electronic + card',
      'patternRepeatLength': 60
    },
    {
      'machineClass': 'SingerMT624',
      'mainBrand': 'Singer',
      'altBrands': ['Superba'],
      'model': 'MT624',
      'needlesCount': 180,
      'needlePitch': 5,
      'type': 'standard',
      'patternControltype': 'electronic + card',
      'patternRepeatLength': 60
    },
    {
      'machineClass': 'SingerMagliabella',
      'mainBrand': 'Singer',
      'altBrands': [],
      'model': 'Magliabella',
      'needlesCount': 180,
      'needlePitch': 5,
      'type': 'standard',
      'patternControltype': 'No',
      'patternRepeatLength': 0
    },
    {
      'machineClass': 'SingerMemo2',
      'mainBrand': 'Singer',
      'altBrands': [],
      'model': 'Memo2',
      'needlesCount': 180,
      'needlePitch': 5,
      'type': 'standard',
      'patternControltype': 'Memo card',
      'patternRepeatLength': 8
    },
    {
      'machineClass': 'ToyotaKS901',
      'mainBrand': 'Toyota',
      'altBrands': [],
      'model': 'KS901',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'Card',
      'patternRepeatLength': 24
    },
    {
      'machineClass': 'ToyotaKS747',
      'mainBrand': 'Toyota',
      'altBrands': [],
      'model': 'KS747',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'Card',
      'patternRepeatLength': 12
    },
    {
      'machineClass': 'ToyotaKS858',
      'mainBrand': 'Toyota',
      'altBrands': [],
      'model': 'KS858',
      'needlesCount': 200,
      'needlePitch': 4.5,
      'type': 'standard',
      'patternControltype': 'Card',
      'patternRepeatLength': 12
    },
  ];

  final firestore = FirebaseFirestore.instance;

  final CollectionReference collection =
      firestore.collection('knitting_machines');
  for (final machine in knittingMachines) {
    final doc = collection.doc();
    machine['id'] = doc.id;
    await doc.set(machine);
  }
}
