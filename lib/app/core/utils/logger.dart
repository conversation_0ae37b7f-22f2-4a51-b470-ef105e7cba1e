import 'dart:developer';

import 'package:flutter/foundation.dart';

class AppLogger {
  // Singleton instance
  static final AppLogger _instance = AppLogger._internal();
  factory AppLogger() => _instance;
  AppLogger._internal();

  // Log levels
  static const String _info = '💡 INFO';
  static const String _debug = '🐛 DEBUG';
  static const String _warning = '⚠️ WARNING';
  static const String _error = '❌ ERROR';

  void info(String message, [String? tag]) {
    _log(_info, message, tag);
  }

  void debug(String message, [String? tag]) {
    _log(_debug, message, tag);
  }

  void warning(String message, [String? tag]) {
    _log(_warning, message, tag);
  }

  void error(String message,
      [String? tag, dynamic error, StackTrace? stackTrace]) {
    _log(_error, message, tag, error, stackTrace);
  }

  void _log(String level, String message,
      [String? tag, dynamic error, StackTrace? stackTrace]) {
    if (!kDebugMode) return; // Only log in debug mode

    final DateTime now = DateTime.now();
    final String timeString = '${now.hour}:${now.minute}:${now.second}';
    final String tagString = tag != null ? ' [$tag]' : '';

    log('$timeString $level$tagString: $message');

    if (error != null) {
      log('Error: $error');
    }

    if (stackTrace != null) {
      log('StackTrace: $stackTrace');
    }
  }
}
