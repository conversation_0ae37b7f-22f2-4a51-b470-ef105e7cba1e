import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:xoxknit/app/core/exceptions/auth_exception.dart';
import 'package:xoxknit/app/routes/app_pages.dart';
import 'package:xoxknit/app/services/app_settings_service.dart';
import 'package:xoxknit/app/services/auth_service.dart';
import 'package:xoxknit/app/services/knitting/knitting_settings_service.dart';

class LoginController extends GetxController {
  final AppSettingsService _settings = AppSettingsService.to;

  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final formKey = GlobalKey<FormState>();
  final isLoading = false.obs;
  final isPasswordVisible = false.obs;

  static LoginController get to => Get.find();

  void togglePasswordVisibility() {
    isPasswordVisible.value = !isPasswordVisible.value;
  }

  String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'login_validation_emailRequired'.tr;
    }
    if (!GetUtils.isEmail(value)) {
      return 'login_validation_emailInvalid'.tr;
    }
    return null;
  }

  String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'login_validation_passwordRequired'.tr;
    }
    if (value.length < 6) {
      return 'login_validation_passwordLength'.tr;
    }
    return null;
  }

  Future<void> handleLogin() async {
    if (formKey.currentState?.validate() ?? false) {
      try {
        isLoading.value = true;

        await (AuthService.to
            .signInWithEmailAndPassword(
              email: emailController.text,
              password: passwordController.text,
            )
            .timeout(const Duration(seconds: 10)));

        if (AuthService.to.currentUser.value == null) {
          throw AuthException(
            code: 'login-failed',
            message: 'login_loginFailed'.tr,
          );
        }

        _settings.updateSettings(firstLogin: false);

        await KnittingSettingsService.to.initializeService();

        Get.rootDelegate.offNamed(Routes.HOME);
      } catch (e) {
        if (e is AuthException) {
          Get.snackbar(
            'common_error'.tr,
            e.message,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        } else {
          Get.snackbar(
            'common_error'.tr,
            'login_loginFailed'.tr,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        }
      } finally {
        isLoading.value = false;
      }
    }
  }

  @override
  void onClose() {
    emailController.dispose();
    passwordController.dispose();
    super.onClose();
  }
}
