import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/components/app_loader.dart';
import 'package:xoxknit/app/components/language_switcher.dart';
import 'package:xoxknit/app/core/theme/app_typography.dart';
import 'package:xoxknit/app/routes/app_pages.dart';
import '../controllers/login_controller.dart';
import '../../../core/theme/app_colors.dart';

class LoginView extends GetResponsiveView<LoginController> {
  LoginView({super.key}) : super(alwaysUseBuilder: false);

  @override
  Widget? phone() {
    return _buildScaffold(
      padding: const EdgeInsets.all(24.0),
      logoWidth: 150,
    );
  }

  @override
  Widget? tablet() {
    return _buildScaffold(
      padding: const EdgeInsets.symmetric(horizontal: 100.0, vertical: 24.0),
      logoWidth: 200,
    );
  }

  @override
  Widget? desktop() {
    return _buildScaffold(
      padding: const EdgeInsets.symmetric(horizontal: 200.0, vertical: 24.0),
      logoWidth: 250,
      maxWidth: 800,
    );
  }

  Widget _buildScaffold({
    required EdgeInsets padding,
    required double logoWidth,
    double? maxWidth,
  }) {
    Widget content = AutofillGroup(
      child: Form(
        key: controller.formKey,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Align(
              alignment: Alignment.topRight,
              child: LanguageSwitcher(),
            ),
            const SizedBox(height: 40),
            _buildLogo(logoWidth),
            const SizedBox(height: 48),
            _buildEmailField(),
            const SizedBox(height: 16),
            _buildPasswordField(),
            Align(
              alignment: Alignment.centerRight,
              child: TextButton(
                onPressed: () =>
                    Get.rootDelegate.toNamed(Routes.RESET_PASSWORD),
                child: Text(
                  'common_forgotPassword'.tr,
                  style: TextStyle(
                    color: Get.theme.colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 8),
            _buildLoginButton(),
            const SizedBox(height: 16),
            _buildSignUpLink(),
          ],
        ),
      ),
    );

    if (maxWidth != null) {
      content = Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(maxWidth: maxWidth),
          child: content,
        ),
      );
    }

    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: padding,
          child: content,
        ),
      ),
    );
  }

  Widget _buildLogo(double width) {
    return Column(
      children: [
        Image.asset(
          "assets/images/logo_white.png",
          width: width,
        ),
        Text(
          'XOXKnit',
          style: TextStyle(
            fontSize: 32,
            fontFamily: AppTypography.brandFont,
            fontWeight: FontWeight.bold,
            color: Get.isDarkMode ? AppColors.white : AppColors.dark,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'login_welcomeBack'.tr,
          style: TextStyle(
            fontSize: 16,
            color: Get.isDarkMode ? AppColors.white : AppColors.dark,
          ),
        ),
      ],
    );
  }

  Widget _buildEmailField() {
    return TextFormField(
      controller: controller.emailController,
      keyboardType: TextInputType.emailAddress,
      autofillHints: const [AutofillHints.email],
      validator: controller.validateEmail,
      decoration: InputDecoration(
        labelText: 'common_email'.tr,
        hintText: 'login_emailHint'.tr,
      ),
    );
  }

  Widget _buildPasswordField() {
    return Obx(() => TextFormField(
          controller: controller.passwordController,
          validator: controller.validatePassword,
          obscureText: !controller.isPasswordVisible.value,
          autofillHints: const [AutofillHints.password],
          decoration: InputDecoration(
            labelText: 'common_password'.tr,
            hintText: 'login_passwordHint'.tr,
            suffixIcon: IconButton(
              icon: Icon(
                controller.isPasswordVisible.value
                    ? Icons.visibility_off
                    : Icons.visibility,
                color: Get.theme.colorScheme.primary,
              ),
              onPressed: controller.togglePasswordVisibility,
            ),
          ),
        ));
  }

  Widget _buildLoginButton() {
    return Obx(() => ElevatedButton(
          onPressed: controller.isLoading.value ? null : controller.handleLogin,
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
          child: controller.isLoading.value
              ? SizedBox(
                  height: 20,
                  width: 20,
                  child: AppLoader(color: Get.theme.colorScheme.onPrimary),
                )
              : Text(
                  'login_loginButton'.tr,
                  style: TextStyle(fontSize: 16),
                ),
        ));
  }

  Widget _buildSignUpLink() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'login_noAccount'.tr,
          style: TextStyle(
            color: Get.isDarkMode ? AppColors.white : AppColors.dark,
          ),
        ),
        TextButton(
          onPressed: () => Get.rootDelegate.toNamed(Routes.SIGNUP),
          child: Text(
            'login_signUpLink'.tr,
            style: TextStyle(
              color: Get.theme.colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }
}
