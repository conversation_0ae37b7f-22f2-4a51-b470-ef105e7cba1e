import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:intl_phone_field/phone_number.dart';
import 'package:xoxknit/app/core/exceptions/auth_exception.dart';
import 'package:xoxknit/app/routes/app_pages.dart';
import 'package:xoxknit/app/services/auth_service.dart';
import 'package:xoxknit/app/services/knitting/knitting_settings_service.dart';

class SignupController extends GetxController {
  final formKey = GlobalKey<FormState>();
  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final phoneNumberController = TextEditingController();
  final confirmPasswordController = TextEditingController();

  final isLoading = false.obs;
  final isPasswordVisible = false.obs;
  final isConfirmPasswordVisible = false.obs;
  final acceptedTerms = false.obs;

  // Phone number with country code
  final Rxn<PhoneNumber> phoneNumber = Rxn<PhoneNumber>();

  void togglePasswordVisibility() {
    isPasswordVisible.value = !isPasswordVisible.value;
  }

  void toggleConfirmPasswordVisibility() {
    isConfirmPasswordVisible.value = !isConfirmPasswordVisible.value;
  }

  void toggleTermsAcceptance() {
    acceptedTerms.value = !acceptedTerms.value;
  }

  String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'signup_validation_nameRequired'.tr;
    }
    if (value.length < 2) {
      return 'signup_validation_nameLength'.tr;
    }
    return null;
  }

  String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'login_validation_emailRequired'.tr;
    }
    if (!GetUtils.isEmail(value)) {
      return 'login_validation_emailInvalid'.tr;
    }
    return null;
  }

  String? validatePhoneNumber(PhoneNumber? phone) {
    if (phone == null || phone.number.isEmpty) {
      return 'signup_validation_phoneNumberRequired'.tr;
    }
    // No digit length restrictions - accept any number of digits
    // The intl_phone_field package handles basic format validation
    // We only check that a phone number exists
    return null;
  }

  // Method to handle phone number changes from intl_phone_field
  void onPhoneNumberChanged(PhoneNumber phone) {
    phoneNumber.value = phone;
  }

  // Get the complete formatted phone number for API calls
  String get formattedPhoneNumber {
    return phoneNumber.value?.completeNumber ?? '';
  }

  String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'login_validation_passwordRequired'.tr;
    }
    if (value.length < 8) {
      return 'signup_validation_passwordRequirements_length'.tr;
    }
    if (!value.contains(RegExp(r'[A-Z]'))) {
      return 'signup_validation_passwordRequirements_uppercase'.tr;
    }
    if (!value.contains(RegExp(r'[0-9]'))) {
      return 'signup_validation_passwordRequirements_number'.tr;
    }
    return null;
  }

  String? validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'signup_validation_confirmPasswordRequired'.tr;
    }
    if (value != passwordController.text) {
      return 'signup_validation_passwordsMismatch'.tr;
    }
    return null;
  }

  Future<void> handleSignup() async {
    if (!acceptedTerms.value) {
      Get.snackbar(
        'common_error'.tr,
        'signup_acceptTerms'.tr,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    if (formKey.currentState?.validate() ?? false) {
      try {
        isLoading.value = true;
        await AuthService.to.createAccount(
          firstName: nameController.text.trim().split(' ')[0],
          lastName: nameController.text.trim().split(' ')[1],
          email: emailController.text,
          phoneNumber: formattedPhoneNumber,
          password: passwordController.text,
        );

        Fluttertoast.showToast(msg: "signup_success_accountCreated".tr);

        await KnittingSettingsService.to.initializeService();

        Get.rootDelegate
            .offNamed(Routes.HOME); // Navigate to home after successful signup
      } catch (e) {
        if (e is AuthException) {
          Get.snackbar(
            'common_error'.tr,
            e.message,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        } else {
          Get.snackbar(
            'common_error'.tr,
            'signup_signupFailed'.tr,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        }
      } finally {
        isLoading.value = false;
      }
    }
  }

  @override
  void onClose() {
    nameController.dispose();
    emailController.dispose();
    phoneNumberController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    super.onClose();
  }
}
