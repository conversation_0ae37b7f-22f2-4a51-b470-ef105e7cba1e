import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:intl_phone_field/country_picker_dialog.dart';
import 'package:xoxknit/app/components/app_loader.dart';
import 'package:xoxknit/app/components/language_switcher.dart';
import 'package:xoxknit/app/core/theme/app_typography.dart';
import 'package:xoxknit/app/routes/app_pages.dart';
import '../controllers/signup_controller.dart';
import '../../../core/theme/app_colors.dart';

class SignupView extends GetResponsiveView<SignupController> {
  SignupView({super.key}) : super(alwaysUseBuilder: false);

  @override
  Widget? phone() {
    return _buildScaffold(
      padding: const EdgeInsets.all(24.0),
      logoWidth: 150,
    );
  }

  @override
  Widget? tablet() {
    return _buildScaffold(
      padding: const EdgeInsets.symmetric(horizontal: 100.0, vertical: 24.0),
      logoWidth: 200,
    );
  }

  @override
  Widget? desktop() {
    return _buildScaffold(
      padding: const EdgeInsets.symmetric(horizontal: 200.0, vertical: 24.0),
      logoWidth: 250,
      maxWidth: 800,
    );
  }

  Widget _buildScaffold({
    required EdgeInsets padding,
    required double logoWidth,
    double? maxWidth,
  }) {
    Widget content = AutofillGroup(
      child: Form(
        key: controller.formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildWelcomeText(logoWidth),
            const SizedBox(height: 32),
            _buildNameField(),
            const SizedBox(height: 16),
            _buildEmailField(),
            const SizedBox(height: 16),
            _buildPhoneNumberField(),
            const SizedBox(height: 16),
            _buildPasswordField(),
            const SizedBox(height: 16),
            _buildConfirmPasswordField(),
            const SizedBox(height: 24),
            _buildTermsAndConditions(),
            const SizedBox(height: 24),
            _buildSignupButton(),
            const SizedBox(height: 16),
            _buildLoginLink(),
          ],
        ),
      ),
    );

    if (maxWidth != null) {
      content = Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(maxWidth: maxWidth),
          child: content,
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        title: Text('signup_title'.tr),
        actions: [LanguageSwitcher()],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: padding,
          child: content,
        ),
      ),
    );
  }

  Widget _buildWelcomeText(double logoWidth) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Image.asset(
          "assets/images/logo_white.png",
          width: logoWidth,
        ),
        Text(
          'signup_welcomeMessage'.tr,
          style: TextStyle(
            fontSize: 24,
            fontFamily: AppTypography.brandFont,
            fontWeight: FontWeight.bold,
            color: Get.isDarkMode ? AppColors.white : AppColors.dark,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'signup_subtitle'.tr,
          style: TextStyle(
            fontSize: 16,
            color: Get.isDarkMode ? AppColors.white : AppColors.dark,
          ),
        ),
      ],
    );
  }

  Widget _buildNameField() {
    return TextFormField(
      controller: controller.nameController,
      validator: controller.validateName,
      decoration: InputDecoration(
        labelText: 'signup_fullName'.tr,
        hintText: 'signup_fullNameHint'.tr,
      ),
      autofillHints: [AutofillHints.name],
    );
  }

  Widget _buildEmailField() {
    return TextFormField(
      controller: controller.emailController,
      keyboardType: TextInputType.emailAddress,
      validator: controller.validateEmail,
      decoration: InputDecoration(
        labelText: 'common_email'.tr,
        hintText: 'login_emailHint'.tr,
      ),
      autofillHints: [AutofillHints.email],
    );
  }

  Widget _buildPhoneNumberField() {
    return IntlPhoneField(
      decoration: InputDecoration(
        labelText: 'signup_phoneNumber'.tr,
        hintText: 'signup_phoneNumberHint'.tr,
      ),
      initialCountryCode: 'US', // Default to US, but user can change
      onChanged: controller.onPhoneNumberChanged,
      validator: controller.validatePhoneNumber,
      autovalidateMode: AutovalidateMode.disabled,
      showCountryFlag: true,
      showDropdownIcon: true,
      dropdownIconPosition: IconPosition.trailing,
      flagsButtonPadding: const EdgeInsets.symmetric(horizontal: 8),
      dropdownTextStyle: TextStyle(
        color: Get.isDarkMode ? AppColors.white : AppColors.dark,
      ),
      style: TextStyle(
        color: Get.isDarkMode ? AppColors.white : AppColors.dark,
      ),
      invalidNumberMessage: 'signup_validation_phoneNumberInvalid'.tr,
      pickerDialogStyle: PickerDialogStyle(
        searchFieldInputDecoration: InputDecoration(
          hintText: 'signup_countryCode_searchCountries'.tr,
        ),
      ),
      // Remove digit limits - allow unlimited phone number length
      disableLengthCheck: true,
    );
  }

  Widget _buildPasswordField() {
    return Obx(() => TextFormField(
          controller: controller.passwordController,
          validator: controller.validatePassword,
          obscureText: !controller.isPasswordVisible.value,
          decoration: InputDecoration(
            labelText: 'common_password'.tr,
            hintText: 'login_passwordHint'.tr,
            suffixIcon: IconButton(
              icon: Icon(
                controller.isPasswordVisible.value
                    ? Icons.visibility_off
                    : Icons.visibility,
                color: Get.theme.colorScheme.primary,
              ),
              onPressed: controller.togglePasswordVisibility,
            ),
          ),
          autofillHints: [AutofillHints.newPassword],
        ));
  }

  Widget _buildConfirmPasswordField() {
    return Obx(() => TextFormField(
          controller: controller.confirmPasswordController,
          validator: controller.validateConfirmPassword,
          obscureText: !controller.isConfirmPasswordVisible.value,
          decoration: InputDecoration(
            labelText: 'signup_confirmPassword'.tr,
            hintText: 'signup_confirmPasswordHint'.tr,
            suffixIcon: IconButton(
              icon: Icon(
                controller.isConfirmPasswordVisible.value
                    ? Icons.visibility_off
                    : Icons.visibility,
                color: Get.theme.colorScheme.primary,
              ),
              onPressed: controller.toggleConfirmPasswordVisibility,
            ),
          ),
          autofillHints: [AutofillHints.newPassword],
        ));
  }

  Widget _buildTermsAndConditions() {
    return Obx(() => Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Checkbox(
              value: controller.acceptedTerms.value,
              onChanged: (_) => controller.toggleTermsAcceptance(),
              activeColor: Get.theme.colorScheme.primary,
            ),
            Expanded(
              child: GestureDetector(
                onTap: controller.toggleTermsAcceptance,
                child: RichText(
                  text: TextSpan(
                    text: 'signup_termsAndConditions1'.tr,
                    style: TextStyle(
                      color: Get.isDarkMode ? AppColors.white : AppColors.dark,
                    ),
                    children: [
                      TextSpan(
                        text: 'signup_termsAndConditions2'.tr,
                        style: TextStyle(
                          color: Get.theme.colorScheme.primary,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ));
  }

  Widget _buildSignupButton() {
    return Obx(() => ElevatedButton(
          onPressed:
              controller.isLoading.value ? null : controller.handleSignup,
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
          child: controller.isLoading.value
              ? SizedBox(
                  height: 20,
                  width: 20,
                  child: AppLoader(
                    color: Get.theme.colorScheme.onPrimary,
                  ),
                )
              : Text(
                  'signup_createAccountButton'.tr,
                  style: TextStyle(fontSize: 16),
                ),
        ));
  }

  Widget _buildLoginLink() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'signup_haveAccount'.tr,
          style: TextStyle(
            color: Get.isDarkMode ? AppColors.white : AppColors.dark,
          ),
        ),
        TextButton(
          onPressed: () => Get.rootDelegate.toNamed(
            Routes.LOGIN,
          ),
          child: Text(
            'signup_loginLink'.tr,
            style: TextStyle(
              color: Get.theme.colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }
}
