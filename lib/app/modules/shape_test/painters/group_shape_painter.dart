import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../models/group_shape_data.dart';
import '../models/shape_data.dart';
import '../utils/hit_test_utils.dart';
import '../utils/geometry_utils.dart';
import 'shape_painter.dart';

/// A special painter that handles painting grouped shapes
class GroupShapePainter extends CustomPainter implements PathProvider {
  final GroupShapeData groupData;
  final bool curveMode;
  final BoxConstraints constraints;
  final bool selected;

  GroupShapePainter({
    required this.groupData,
    required this.curveMode,
    required this.constraints,
    this.selected = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Safety check for null or zero-sized canvas
    if (size.isEmpty) return;

    // First paint each child shape in the group
    for (final childShape in groupData.childShapes) {
      // Check if the child shape is a group
      if (childShape.type == ShapeType.group && childShape is GroupShapeData) {
        // Create a group painter for nested groups
        final childGroupPainter = GroupShapePainter(
          groupData: childShape,
          curveMode: curveMode,
          constraints: constraints,
          selected: false, // Child shapes don't show selection individually
        );

        // Paint the nested group
        childGroupPainter.paint(canvas, Size.infinite);
      } else {
        // Create a regular painter for non-group shapes
        final childPainter = ShapePainter(
          shapeData: childShape,
          curveMode: curveMode,
          constraints: constraints,
          selected: false, // Child shapes don't show selection individually
        );

        // Paint the child shape
        childPainter.paint(canvas, Size.infinite);
      }
    }

    // Calculate accurate bounding box that includes curves
    // This is essential for drawing the outline correctly
    final boundsData = GeometryUtils.calculateAccurateBoundingRect(groupData);
    final vertices = boundsData is GroupBoundsData
        ? boundsData.vertices
        : groupData.vertices;

    if (vertices.isEmpty) return;

    try {
      // Apply rotation transform to the canvas for the outline
      canvas.save();
      canvas.translate(groupData.center.dx, groupData.center.dy);
      canvas.rotate(groupData.rotation);
      canvas.translate(-groupData.center.dx, -groupData.center.dy);

      // Create an outline path using the accurate vertices
      final groupOutline = Path();
      groupOutline.moveTo(vertices.first.dx, vertices.first.dy);
      for (int i = 1; i < vertices.length; i++) {
        groupOutline.lineTo(vertices[i].dx, vertices[i].dy);
      }
      groupOutline.close();

      // Configure stroke paint based on selection state
      final strokePaint = Paint()
        ..style = PaintingStyle.stroke
        ..strokeWidth = selected ? 2.5 : 1.0;

      if (selected) {
        // Selected groups have a bright blue dashed outline
        strokePaint.color = Colors.blue.shade600;

        // Create a dashed effect for the selected group
        final dashPath = Path();
        const dashWidth = 6.0;
        const dashSpace = 4.0;

        for (int i = 0; i < vertices.length; i++) {
          final start = vertices[i];
          final end = vertices[(i + 1) % vertices.length];
          final dx = end.dx - start.dx;
          final dy = end.dy - start.dy;
          final edgeLength = math.sqrt(dx * dx + dy * dy);

          // Normalize direction vector
          final dirX = dx / edgeLength;
          final dirY = dy / edgeLength;

          // Draw dashed line
          double drawn = 0.0;
          while (drawn < edgeLength) {
            final dashLength = math.min(dashWidth, edgeLength - drawn);
            final dashEndX = start.dx + dirX * (drawn + dashLength);
            final dashEndY = start.dy + dirY * (drawn + dashLength);

            dashPath.moveTo(start.dx + dirX * drawn, start.dy + dirY * drawn);
            dashPath.lineTo(dashEndX, dashEndY);

            drawn += dashLength + dashSpace;
          }
        }

        canvas.drawPath(dashPath, strokePaint);

        // Draw selection handles at the corners
        final handlePaint = Paint()
          ..color = Colors.blue.shade700
          ..style = PaintingStyle.fill;

        for (final vertex in vertices) {
          canvas.drawCircle(vertex, 6.0, handlePaint);
        }
      } else {
        // For non-selected groups, draw a subtle dashed outline
        strokePaint.color = Colors.blue.shade200.withOpacity(0.5);

        // Create a more subtle dashed effect for non-selected groups
        final dashPath = Path();
        const dashWidth = 4.0;
        const dashSpace = 4.0;

        for (int i = 0; i < vertices.length; i++) {
          final start = vertices[i];
          final end = vertices[(i + 1) % vertices.length];
          final dx = end.dx - start.dx;
          final dy = end.dy - start.dy;
          final edgeLength = math.sqrt(dx * dx + dy * dy);

          // Normalize direction vector
          final dirX = dx / edgeLength;
          final dirY = dy / edgeLength;

          // Draw dashed line
          double drawn = 0.0;
          while (drawn < edgeLength) {
            final dashLength = math.min(dashWidth, edgeLength - drawn);
            final dashEndX = start.dx + dirX * (drawn + dashLength);
            final dashEndY = start.dy + dirY * (drawn + dashLength);

            dashPath.moveTo(start.dx + dirX * drawn, start.dy + dirY * drawn);
            dashPath.lineTo(dashEndX, dashEndY);

            drawn += dashLength + dashSpace;
          }
        }

        canvas.drawPath(dashPath, strokePaint);
      }
    } finally {
      canvas.restore();
    }
  }

  // Get the shape path for hit testing - implement PathProvider interface
  @override
  Path getPath() {
    final groupPath = Path();

    // Calculate accurate bounding box for hit testing
    final boundsData = GeometryUtils.calculateAccurateBoundingRect(groupData);
    final vertices = boundsData is GroupBoundsData
        ? boundsData.vertices
        : groupData.vertices;

    // Start with the outline of the group itself
    if (vertices.isNotEmpty) {
      groupPath.moveTo(vertices.first.dx, vertices.first.dy);
      for (int i = 1; i < vertices.length; i++) {
        groupPath.lineTo(vertices[i].dx, vertices[i].dy);
      }
      groupPath.close();
    }

    // Include paths from child groups as well for accurate hit testing
    for (final childShape in groupData.childShapes) {
      if (childShape.type == ShapeType.group && childShape is GroupShapeData) {
        // Create a group painter for the child to get its path
        final childGroupPainter = GroupShapePainter(
          groupData: childShape,
          curveMode: curveMode,
          constraints: constraints,
          selected: false,
        );

        // Add the child group's path to our path
        groupPath.addPath(childGroupPainter.getPath(), Offset.zero);
      }
    }

    // Apply rotation
    final matrix = Matrix4.identity()
      ..translate(groupData.center.dx, groupData.center.dy)
      ..rotateZ(groupData.rotation)
      ..translate(-groupData.center.dx, -groupData.center.dy);

    return groupPath.transform(matrix.storage);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
