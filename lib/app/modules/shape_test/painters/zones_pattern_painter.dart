import 'package:flutter/material.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/knitting_instructions_manager.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/knitting_zone_models.dart';
import 'package:xoxknit/app/modules/shape_test/models/cell_dimensions.dart';

class ZonesPatternPainter extends CustomPainter {
  final List<List<bool>> fullPattern;
  final List<KnittingZone> zones;
  final int selectedZoneIndex;
  final double aspectRatio;

  // Zone colors
  final List<Color> zoneColors = [
    Colors.blue.withValues(alpha: 0.5),
    Colors.green.withValues(alpha: 0.5),
    Colors.orange.withValues(alpha: 0.5),
    Colors.purple.withValues(alpha: 0.5),
    Colors.teal.withValues(alpha: 0.5),
    Colors.red.withValues(alpha: 0.5),
    Colors.amber.withValues(alpha: 0.5),
    Colors.indigo.withValues(alpha: 0.5),
  ];

  ZonesPatternPainter({
    required this.fullPattern,
    required this.zones,
    required this.selectedZoneIndex,
    this.aspectRatio = 0.75,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (fullPattern.isEmpty) return;

    final patternWidth = fullPattern[0].length;
    final patternHeight = fullPattern.length;

    // Calculate cell dimensions
    final double correctedAspectRatio =
        aspectRatio != 0 ? 1.0 / aspectRatio : 1.0;
    final cellDimensions = CellDimensions.calculate(
        size, patternWidth, patternHeight, correctedAspectRatio);

    final cellWidth = cellDimensions.cellWidth;
    final cellHeight = cellDimensions.cellHeight;
    final offsetX = cellDimensions.offsetX;
    final offsetY = cellDimensions.offsetY;

    // Draw grid
    final gridPaint = Paint()
      ..color = Colors.grey.shade300
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // Draw vertical grid lines
    for (int col = 0; col <= patternWidth; col++) {
      final x = offsetX + (col * cellWidth);
      canvas.drawLine(Offset(x, offsetY),
          Offset(x, offsetY + (patternHeight * cellHeight)), gridPaint);
    }

    // Draw horizontal grid lines
    for (int row = 0; row <= patternHeight; row++) {
      final y = offsetY + (row * cellHeight);
      canvas.drawLine(Offset(offsetX, y),
          Offset(offsetX + (patternWidth * cellWidth), y), gridPaint);
    }

    // Set up stitch paint
    final stitchPaint = Paint()
      ..color = Colors.black.withValues(alpha: 0.15)
      ..style = PaintingStyle.fill;

    // Draw background stitches first (the base pattern)
    for (int row = 0; row < patternHeight; row++) {
      for (int col = 0; col < patternWidth; col++) {
        if (fullPattern[row][col]) {
          final rect = Rect.fromLTWH(offsetX + (col * cellWidth),
              offsetY + (row * cellHeight), cellWidth, cellHeight);
          canvas.drawRect(rect, stitchPaint);
        }
      }
    }

    // Now draw each zone with its color
    for (int i = 0; i < zones.length; i++) {
      final zone = zones[i];
      final instructions = zone.instructions;

      // Skip empty zones
      if (instructions.isEmpty) continue;

      // Set zone paint color
      final zonePaint = Paint()
        ..color = zoneColors[i % zoneColors.length]
        ..style = PaintingStyle.fill;

      // If this is the selected zone, use a more opaque color
      if (i == selectedZoneIndex) {
        zonePaint.color =
            zoneColors[i % zoneColors.length].withValues(alpha: 0.8);
      }

      // Draw the zone's stitches
      // IMPORTANT: The zone's instructions are relative to startRow, startNeedle
      // We need to offset both row AND column by startRow, startNeedle
      for (int localRow = 0; localRow < instructions.length; localRow++) {
        final globalRowIndex =
            zone.startRow + localRow; // Calculate global row index
        final currentRowData = instructions[localRow];

        for (int localCol = 0; localCol < currentRowData.length; localCol++) {
          if (currentRowData[localCol]) {
            // Convert local column to global column
            final globalColIndex = zone.startNeedle + localCol;

            final rect = Rect.fromLTWH(offsetX + (globalColIndex * cellWidth),
                offsetY + (globalRowIndex * cellHeight), cellWidth, cellHeight);
            canvas.drawRect(rect, zonePaint);
          }
        }
      }

      // Draw zone outline for selected zone
      if (i == selectedZoneIndex) {
        final outlinePaint = Paint()
          ..color = Colors.white
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.0;

        final rect = Rect.fromLTWH(
            offsetX + (zone.startNeedle * cellWidth),
            offsetY + (zone.startRow * cellHeight),
            (zone.endNeedle - zone.startNeedle + 1) * cellWidth,
            (zone.endRow - zone.startRow + 1) * cellHeight);

        canvas.drawRect(rect, outlinePaint);
      }

      // Draw zone label in the center
      // Calculate the zone's bounding rectangle
      final zoneRect = Rect.fromLTWH(
          offsetX + (zone.startNeedle * cellWidth),
          offsetY + (zone.startRow * cellHeight),
          (zone.endNeedle - zone.startNeedle + 1) * cellWidth,
          (zone.endRow - zone.startRow + 1) * cellHeight);

      // Create a background for better visibility
      final labelBgPaint = Paint()
        ..color = Colors.white.withValues(alpha: 0.3)
        ..style = PaintingStyle.fill;

      // Determine text style based on whether this is the selected zone
      final textStyle = TextStyle(
        color: i == selectedZoneIndex ? Colors.black : Colors.black54,
        fontSize: i == selectedZoneIndex ? 14 : 12,
        fontWeight:
            i == selectedZoneIndex ? FontWeight.bold : FontWeight.normal,
      );

      final textSpan = TextSpan(
        text: zone.name,
        style: textStyle,
      );

      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.center,
      );

      textPainter.layout();

      // Calculate center position of the zone
      final centerX = zoneRect.left + zoneRect.width / 2;
      final centerY = zoneRect.top + zoneRect.height / 2;

      // Draw text background
      final padding = 4.0;
      final bgRect = Rect.fromCenter(
        center: Offset(centerX, centerY),
        width: textPainter.width + padding * 2,
        height: textPainter.height + padding * 2,
      );

      // Draw the background with rounded corners
      canvas.drawRRect(
        RRect.fromRectAndRadius(bgRect, Radius.circular(4.0)),
        labelBgPaint,
      );

      // Draw the text in the center of the zone
      textPainter.paint(
        canvas,
        Offset(
          centerX - textPainter.width / 2,
          centerY - textPainter.height / 2,
        ),
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is ZonesPatternPainter) {
      return oldDelegate.fullPattern != fullPattern ||
          oldDelegate.zones != zones ||
          oldDelegate.selectedZoneIndex != selectedZoneIndex ||
          oldDelegate.aspectRatio != aspectRatio;
    }
    return true;
  }
}
