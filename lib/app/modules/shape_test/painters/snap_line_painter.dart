import 'package:flutter/material.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/shape_editor_controller.dart';
import 'package:xoxknit/app/modules/shape_test/handlers/shape_manipulation_handlers.dart'; // For SnapInfo, SnapType
import 'package:xoxknit/app/modules/shape_test/models/shape_data.dart';
import 'package:xoxknit/app/modules/shape_test/models/group_shape_data.dart';
import 'package:xoxknit/app/modules/shape_test/utils/geometry_utils.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;

class SnapLinePainter extends CustomPainter {
  final ShapeEditorController controller;
  final _linePaint = Paint()
    ..color = Colors.pink.shade400
    ..strokeWidth = 1.8 // Increased from 1.5 for better visibility
    ..style = PaintingStyle.stroke
    ..strokeCap = StrokeCap.round; // Added rounded cap for better appearance

  final _dashPaint = Paint()
    ..color = Colors.pink.shade300
    ..strokeWidth = 1.6 // Increased slightly
    ..style = PaintingStyle.stroke
    ..strokeCap = StrokeCap.round; // Added rounded cap

  // Enhanced properties for snap indicators
  final _indicatorPaint = Paint()
    ..color = Colors.pinkAccent.shade200
    ..style = PaintingStyle.fill;

  // Paint for the snap line glow effect
  final _glowPaint = Paint()
    ..color = Colors.pink.shade200.withOpacity(0.4)
    ..strokeWidth = 6.0
    ..style = PaintingStyle.stroke
    ..strokeCap = StrokeCap.round
    ..maskFilter = MaskFilter.blur(BlurStyle.normal, 3.0); // Add blur for glow

  // Settings for dashed lines
  final double _dashLength = 5.0;
  final double _dashSpace = 5.0;

  final double _indicatorSize = 6.0; // Increased from 5.0
  final double _animationProgress; // Add animation support

  SnapLinePainter(this.controller, {double animationProgress = 1.0})
      : _animationProgress = animationProgress,
        super(repaint: controller); // Repaint when controller notifies changes

  @override
  void paint(Canvas canvas, Size size) {
    final snapInfo = controller.activeSnapInfo.value;
    if (snapInfo == null || controller.selectedIndices.isEmpty) {
      return; // No snap active or no shape selected
    }

    // Assume the last selected shape is the one being dragged
    final draggedShapeIndex = controller.selectedIndices.last;
    if (draggedShapeIndex < 0 ||
        draggedShapeIndex >= controller.shapes.length) {
      return;
    }
    final draggedShapeKey = controller.shapes[draggedShapeIndex].key;
    if (draggedShapeKey == null) return;

    final draggedShapeData =
        controller.shapeManager.shapeStates[draggedShapeKey];

    if (draggedShapeData == null) {
      return; // Need data for the dragged shape
    }

    // Get accurate bounds for the dragged shape
    final draggedBoundsResult =
        GeometryUtils.calculateAccurateBoundingRect(draggedShapeData);
    Rect draggedBounds;
    if (draggedBoundsResult is GroupBoundsData) {
      draggedBounds = draggedBoundsResult.bounds;
    } else {
      draggedBounds = draggedBoundsResult as Rect;
    }

    // --- Get Target Shape Bounds (only if needed) ---
    Rect? targetBounds;
    if (snapInfo.targetShapeKey != null) {
      final targetShapeData =
          controller.shapeManager.shapeStates[snapInfo.targetShapeKey];
      if (targetShapeData != null) {
        final targetBoundsResult =
            GeometryUtils.calculateAccurateBoundingRect(targetShapeData);
        if (targetBoundsResult is GroupBoundsData) {
          targetBounds = targetBoundsResult.bounds;
        } else {
          targetBounds = targetBoundsResult as Rect;
        }
      }
    }

    // --- Calculate Line Coordinates based on SnapType ---
    Offset? startPoint;
    Offset? endPoint;

    // Helper values for vertical/horizontal extent
    // Add extra padding for lines to extend beyond shapes
    final double padding = 20.0;
    double minY = draggedBounds.top - padding;
    double maxY = draggedBounds.bottom + padding;
    double minX = draggedBounds.left - padding;
    double maxX = draggedBounds.right + padding;

    // If target bounds exist, adjust extent to cover both shapes with padding
    if (targetBounds != null) {
      minY = math.min(draggedBounds.top, targetBounds.top) - padding;
      maxY = math.max(draggedBounds.bottom, targetBounds.bottom) + padding;
      minX = math.min(draggedBounds.left, targetBounds.left) - padding;
      maxX = math.max(draggedBounds.right, targetBounds.right) + padding;
    }

    switch (snapInfo.snapType) {
      // --- Shape-to-Shape Horizontal Snaps ---
      case SnapType.leftToLeft:
        if (targetBounds == null) break; // Needs target
        // Line at both shapes' left edges
        startPoint = Offset(draggedBounds.left, minY);
        endPoint = Offset(draggedBounds.left, maxY);
        break;
      case SnapType.rightToRight:
        if (targetBounds == null) break; // Needs target
        // Line at both shapes' right edges
        startPoint = Offset(draggedBounds.right, minY);
        endPoint = Offset(draggedBounds.right, maxY);
        break;
      case SnapType.leftToRight:
        if (targetBounds == null) break; // Needs target
        // Line connecting left edge of dragged to right edge of target
        startPoint = Offset(draggedBounds.left, minY);
        endPoint = Offset(draggedBounds.left, maxY);
        break;
      case SnapType.rightToLeft:
        if (targetBounds == null) break; // Needs target
        // Line connecting right edge of dragged to left edge of target
        startPoint = Offset(draggedBounds.right, minY);
        endPoint = Offset(draggedBounds.right, maxY);
        break;
      case SnapType.horizontalCenter:
        if (targetBounds == null) break; // Needs target
        // Line at both shapes' center X
        final centerX = draggedBounds.center.dx;
        startPoint = Offset(centerX, minY);
        endPoint = Offset(centerX, maxY);
        break;

      // --- Shape-to-Shape Vertical Snaps ---
      case SnapType.topToTop:
        if (targetBounds == null) break; // Needs target
        // Line at both shapes' top edges
        startPoint = Offset(minX, draggedBounds.top);
        endPoint = Offset(maxX, draggedBounds.top);
        break;
      case SnapType.bottomToBottom:
        if (targetBounds == null) break; // Needs target
        // Line at both shapes' bottom edges
        startPoint = Offset(minX, draggedBounds.bottom);
        endPoint = Offset(maxX, draggedBounds.bottom);
        break;
      case SnapType.topToBottom:
        if (targetBounds == null) break; // Needs target
        // Line connecting top edge of dragged to bottom edge of target
        startPoint = Offset(minX, draggedBounds.top);
        endPoint = Offset(maxX, draggedBounds.top);
        break;
      case SnapType.bottomToTop:
        if (targetBounds == null) break; // Needs target
        // Line connecting bottom edge of dragged to top edge of target
        startPoint = Offset(minX, draggedBounds.bottom);
        endPoint = Offset(maxX, draggedBounds.bottom);
        break;
      case SnapType.verticalCenter:
        if (targetBounds == null) break; // Needs target
        // Line at both shapes' center Y
        final centerY = draggedBounds.center.dy;
        startPoint = Offset(minX, centerY);
        endPoint = Offset(maxX, centerY);
        break;

      // --- Canvas Center Snap ---
      case SnapType.canvasCenterHorizontal:
        final canvasCenterX = size.width / 2; // Use canvas size
        // Extend line vertically for full canvas height for better visibility
        startPoint = Offset(canvasCenterX, 0); // Top of canvas
        endPoint = Offset(canvasCenterX, size.height); // Bottom of canvas
        break;
      case SnapType.canvasCenterVertical:
        final canvasCenterY = size.height / 2; // Use canvas size
        // Extend line horizontally for full canvas width
        startPoint = Offset(0, canvasCenterY); // Left of canvas
        endPoint = Offset(size.width, canvasCenterY); // Right of canvas
        break;

      // --- Grid Snaps ---
      case SnapType.gridHorizontal: // Vertical line at snapped X
        final snappedX = draggedBounds.center.dx; // Assume center is snapped
        // Extend line vertically based on dragged shape bounds
        startPoint = Offset(snappedX, draggedBounds.top - padding);
        endPoint = Offset(snappedX, draggedBounds.bottom + padding);
        break;
      case SnapType.gridVertical: // Horizontal line at snapped Y
        final snappedY = draggedBounds.center.dy; // Assume center is snapped
        // Extend line horizontally based on dragged shape bounds
        startPoint = Offset(draggedBounds.left - padding, snappedY);
        endPoint = Offset(draggedBounds.right + padding, snappedY);
        break;
    }

    // Draw the line if points are valid
    if (startPoint != null && endPoint != null) {
      // Apply animation progress if needed
      if (_animationProgress < 1.0) {
        // Animate the line growing from its center
        final centerPoint = Offset(
          (startPoint.dx + endPoint.dx) / 2,
          (startPoint.dy + endPoint.dy) / 2,
        );

        startPoint = Offset.lerp(centerPoint, startPoint, _animationProgress)!;
        endPoint = Offset.lerp(centerPoint, endPoint, _animationProgress)!;
      }

      // For canvas center and grid snaps, use dashed lines with glow
      if (snapInfo.snapType == SnapType.canvasCenterHorizontal ||
          snapInfo.snapType == SnapType.canvasCenterVertical ||
          snapInfo.snapType == SnapType.gridHorizontal ||
          snapInfo.snapType == SnapType.gridVertical) {
        // Draw glow effect first (behind the line)
        canvas.drawLine(startPoint, endPoint, _glowPaint);

        // Then draw the dashed line
        _drawDashedLine(canvas, startPoint, endPoint, _dashPaint);

        // For center lines, add a stronger visual indicator
        if (snapInfo.snapType == SnapType.canvasCenterHorizontal ||
            snapInfo.snapType == SnapType.canvasCenterVertical) {
          // Draw animated markers along the center line
          _drawCenterLineMarkers(
              canvas, startPoint, endPoint, snapInfo.snapType);
        }
      } else {
        // For shape-to-shape snaps, use solid line with glow
        canvas.drawLine(startPoint, endPoint, _glowPaint);
        canvas.drawLine(startPoint, endPoint, _linePaint);

        // Draw additional visual indicators at snap points
        if (snapInfo.targetShapeKey != null &&
            targetBounds != null &&
            draggedBounds != null) {
          _drawSnapIndicators(
              canvas, snapInfo.snapType, draggedBounds, targetBounds);
        }
      }
    }
  }

  // Helper method to draw dashed lines with improved appearance
  void _drawDashedLine(Canvas canvas, Offset start, Offset end, Paint paint) {
    final path = Path();
    final dashPath = Path();

    path.moveTo(start.dx, start.dy);
    path.lineTo(end.dx, end.dy);

    // Convert path to dashes
    final pathMetrics = path.computeMetrics().first;
    final totalLength = pathMetrics.length;

    var distance = 0.0;
    var drawDash = true;

    while (distance < totalLength) {
      final dashLength = drawDash ? _dashLength : _dashSpace;
      final nextDistance = distance + dashLength;

      if (nextDistance <= totalLength) {
        final extractPath = pathMetrics.extractPath(distance, nextDistance);
        if (drawDash) {
          dashPath.addPath(extractPath, Offset.zero);
        }
      } else {
        final extractPath = pathMetrics.extractPath(distance, totalLength);
        if (drawDash) {
          dashPath.addPath(extractPath, Offset.zero);
        }
      }

      distance = nextDistance;
      drawDash = !drawDash;
    }

    canvas.drawPath(dashPath, paint);
  }

  // Helper method to draw markers along center lines
  void _drawCenterLineMarkers(
      Canvas canvas, Offset start, Offset end, SnapType snapType) {
    final isHorizontal = snapType == SnapType.canvasCenterHorizontal;
    final centerLine = isHorizontal ? start.dy : start.dx;
    final lineLength =
        isHorizontal ? (end.dx - start.dx).abs() : (end.dy - start.dy).abs();

    // Draw diamond-shaped markers along the center line
    final markerCount =
        (lineLength / 100).ceil(); // One marker every 100 pixels
    final spacing = lineLength / (markerCount + 1);

    for (int i = 1; i <= markerCount; i++) {
      final position = i * spacing;
      Offset markerCenter;

      if (isHorizontal) {
        markerCenter = Offset(start.dx + position, centerLine);
      } else {
        markerCenter = Offset(centerLine, start.dy + position);
      }

      // Create a diamond path
      final diamondPath = Path();
      final diamondSize = 6.0;

      diamondPath.moveTo(markerCenter.dx, markerCenter.dy - diamondSize);
      diamondPath.lineTo(markerCenter.dx + diamondSize, markerCenter.dy);
      diamondPath.lineTo(markerCenter.dx, markerCenter.dy + diamondSize);
      diamondPath.lineTo(markerCenter.dx - diamondSize, markerCenter.dy);
      diamondPath.close();

      canvas.drawPath(diamondPath, _indicatorPaint);
    }
  }

  // Enhanced helper to draw indicators at snap points
  void _drawSnapIndicators(
      Canvas canvas, SnapType snapType, Rect draggedBounds, Rect targetBounds) {
    final indicatorPaint = Paint()
      ..color = Colors.pinkAccent
      ..style = PaintingStyle.fill;

    // Draw a connecting line "connector" style indicator
    switch (snapType) {
      case SnapType.leftToLeft:
        _drawConnector(
            canvas,
            Offset(draggedBounds.left, draggedBounds.center.dy),
            Offset(targetBounds.left, targetBounds.center.dy),
            indicatorPaint);
        break;
      case SnapType.rightToRight:
        _drawConnector(
            canvas,
            Offset(draggedBounds.right, draggedBounds.center.dy),
            Offset(targetBounds.right, targetBounds.center.dy),
            indicatorPaint);
        break;
      case SnapType.leftToRight:
        _drawConnector(
            canvas,
            Offset(draggedBounds.left, draggedBounds.center.dy),
            Offset(targetBounds.right, targetBounds.center.dy),
            indicatorPaint);
        break;
      case SnapType.rightToLeft:
        _drawConnector(
            canvas,
            Offset(draggedBounds.right, draggedBounds.center.dy),
            Offset(targetBounds.left, targetBounds.center.dy),
            indicatorPaint);
        break;
      case SnapType.horizontalCenter:
        _drawConnector(
            canvas, draggedBounds.center, targetBounds.center, indicatorPaint);
        break;
      case SnapType.topToTop:
        _drawConnector(
            canvas,
            Offset(draggedBounds.center.dx, draggedBounds.top),
            Offset(targetBounds.center.dx, targetBounds.top),
            indicatorPaint);
        break;
      case SnapType.bottomToBottom:
        _drawConnector(
            canvas,
            Offset(draggedBounds.center.dx, draggedBounds.bottom),
            Offset(targetBounds.center.dx, targetBounds.bottom),
            indicatorPaint);
        break;
      case SnapType.topToBottom:
        _drawConnector(
            canvas,
            Offset(draggedBounds.center.dx, draggedBounds.top),
            Offset(targetBounds.center.dx, targetBounds.bottom),
            indicatorPaint);
        break;
      case SnapType.bottomToTop:
        _drawConnector(
            canvas,
            Offset(draggedBounds.center.dx, draggedBounds.bottom),
            Offset(targetBounds.center.dx, targetBounds.top),
            indicatorPaint);
        break;
      case SnapType.verticalCenter:
        _drawConnector(
            canvas, draggedBounds.center, targetBounds.center, indicatorPaint);
        break;
      default:
        break; // Other snap types don't need indicators
    }
  }

  // Draw enhanced connector between two snap points with multiple indicators
  void _drawConnector(
      Canvas canvas, Offset point1, Offset point2, Paint paint) {
    // Draw larger circle indicators at both points
    _drawCircleIndicator(canvas, point1, _indicatorSize + 2.0, paint);
    _drawCircleIndicator(canvas, point2, _indicatorSize + 2.0, paint);

    // Draw a white border around each indicator for contrast
    final borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;

    canvas.drawCircle(point1, _indicatorSize + 2.0, borderPaint);
    canvas.drawCircle(point2, _indicatorSize + 2.0, borderPaint);

    // Draw a smaller white dot in the center for better visibility
    final centerDotPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    canvas.drawCircle(point1, _indicatorSize * 0.4, centerDotPaint);
    canvas.drawCircle(point2, _indicatorSize * 0.4, centerDotPaint);
  }

  // Helper to draw circle indicators
  void _drawCircleIndicator(
      Canvas canvas, Offset position, double size, Paint paint) {
    canvas.drawCircle(position, size, paint);
  }

  @override
  bool shouldRepaint(covariant SnapLinePainter oldDelegate) {
    // Repaint only if the activeSnapInfo value actually changed
    return oldDelegate.controller.activeSnapInfo.value !=
            controller.activeSnapInfo.value ||
        oldDelegate._animationProgress != _animationProgress;
  }
}
