import 'package:display_metrics/display_metrics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:math' as math;

import 'enhanced_grid_painter.dart';
import '../handlers/shape_manipulation_handlers.dart';
import '../models/shape_data.dart';

/// A comprehensive grid system that handles drawing and snapping functionality
/// specifically for needle-based knitting grids
class GridSystem {
  /// Base width of grid cells
  double cellWidth;

  /// The aspect ratio (height/width) of stitches
  final double aspectRatio;

  /// Primary color for important grid elements
  final Color primaryColor;

  /// Color for standard grid lines
  final Color gridColor;

  /// Whether to show center lines
  final bool showCenterLines;

  /// Whether snapping is enabled
  final bool snapToGrid;

  /// Whether snapping to center line is enabled
  bool snapToCenter;

  /// Whether to show needle labels on the grid
  final bool showNeedleLabels;

  /// Snap threshold in pixels (how close to a grid point to trigger snapping)
  final double snapThreshold;

  /// Snap threshold for center line in pixels
  final double centerSnapThreshold;

  /// Opacity for the grid lines (0.0 to 1.0)
  final double opacity;

  /// Current zoom level
  final RxDouble _zoomLevel = 1.0.obs;
  double get zoomLevel => _zoomLevel.value;
  set zoomLevel(double value) => _zoomLevel.value = value;

  /// Current pan offset
  final Rx<Offset> _panOffset = Offset.zero.obs;
  Offset get panOffset => _panOffset.value;
  set panOffset(Offset value) => _panOffset.value = value;

  /// Number of needles in the knitting machine
  final int needleCount;

  /// The machine's needle pitch in millimeters (how far apart the needles are)
  final double machinePitch;

  /// Map from grid coordinates to needle positions
  final Map<int, int> _gridToNeedleMap = {};

  /// Map from needle positions to grid coordinates
  final Map<int, double> _needleToGridMap = {};

  GridSystem({
    this.aspectRatio = 1.5,
    required this.needleCount,
    required this.machinePitch,
    this.cellWidth = 4.0,
    this.primaryColor = Colors.blue,
    this.gridColor = Colors.grey,
    this.showCenterLines = true,
    this.snapToGrid = true,
    this.snapToCenter = true,
    this.showNeedleLabels = true,
    this.snapThreshold = 10.0,
    this.centerSnapThreshold = 35.0,
    this.opacity = 1.0,
    double zoomLevel = 1.0,
    Offset panOffset = Offset.zero,
  }) {
    this.zoomLevel = zoomLevel;
    this.panOffset = panOffset;
  }

  @override
  String toString() {
    return 'GridSystem(cellWidth: $cellWidth, aspectRatio: $aspectRatio, needleCount: $needleCount, machinePitch: $machinePitch, showCenterLines: $showCenterLines, snapToGrid: $snapToGrid, snapToCenter: $snapToCenter, showNeedleLabels: $showNeedleLabels, snapThreshold: $snapThreshold, centerSnapThreshold: $centerSnapThreshold, opacity: $opacity, zoomLevel: $zoomLevel, panOffset: $panOffset)';
  }

  /// Create a custom painter that draws the grid
  CustomPainter createPainter(
      {double? cellWidth,
      SnapInfo? activeSnapInfo,
      Map<Key, ShapeData>? shapeStates}) {
    return EnhancedGridPainter(
      cellWidth: cellWidth ?? this.cellWidth,
      zoomLevel: zoomLevel,
      needleCount: needleCount,
      primaryColor: primaryColor,
      gridColor: gridColor,
      showCenterLines: showCenterLines,
      showNeedleLabels: showNeedleLabels,
      opacity: opacity,
      aspectRatio: aspectRatio,
      activeSnapInfo: activeSnapInfo,
      shapeStates: shapeStates,
      panOffset: panOffset,
    );
  }

  /// Calculate the effective grid size based on current zoom
  double get effectiveGridSize => cellWidth * zoomLevel;

  /// Convert a point in screen space to grid space
  Offset screenToGridPoint(Offset screenPoint, Size canvasSize) {
    // Adjust for pan offset and canvas center
    final centerX = canvasSize.width / 2;
    final centerY = canvasSize.height / 2;

    // Calculate the grid-space point
    final adjustedPoint = Offset(
      (screenPoint.dx - centerX - panOffset.dx) / zoomLevel + centerX,
      (screenPoint.dy - centerY - panOffset.dy) / zoomLevel + centerY,
    );

    return adjustedPoint;
  }

  /// Convert a point in grid space back to screen space
  Offset gridToScreenPoint(Offset gridPoint, Size canvasSize) {
    // Adjust for pan offset and canvas center
    final centerX = canvasSize.width / 2;
    final centerY = canvasSize.height / 2;

    // Calculate the screen-space point
    final adjustedPoint = Offset(
      (gridPoint.dx - centerX) * zoomLevel + centerX + panOffset.dx,
      (gridPoint.dy - centerY) * zoomLevel + centerY + panOffset.dy,
    );

    return adjustedPoint;
  }

  /// Update the zoom level and pan offset
  void updateViewport(double newZoom, Offset newPanOffset,
      {bool updateNeedleMapping = false}) {
    zoomLevel = newZoom;
    panOffset = newPanOffset;

    if (updateNeedleMapping) {
      _updateNeedleGridMapping();
    }
  }

  /// Update the mapping between needles and grid coordinates
  void _updateNeedleGridMapping() {
    // Clear existing mappings
    _gridToNeedleMap.clear();
    _needleToGridMap.clear();

    // Calculate the effective grid size based on current zoom
    final spacing = cellWidth * zoomLevel;

    // For a standard size screen, calculate the center
    final centerX = 500.0; // Approximate center X

    // Calculate total grid width based on needle count
    final totalWidth = needleCount * spacing;

    // Starting position to center the grid
    final leftEdge = centerX - (totalWidth / 2);

    // Create mappings
    for (int i = 0; i <= needleCount; i++) {
      // Calculate grid X position for this needle
      final gridX = leftEdge + (i * spacing);

      // Store mappings in both directions
      _needleToGridMap[i] = gridX;

      // Round to nearest grid cell
      final gridCell = ((gridX - leftEdge) / spacing).round();
      if (gridCell >= 0 && gridCell < needleCount) {
        _gridToNeedleMap[gridCell] = i;
      }
    }
  }
}
