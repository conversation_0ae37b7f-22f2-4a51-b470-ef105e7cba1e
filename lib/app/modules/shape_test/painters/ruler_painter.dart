import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// Required import for ui namespace
import 'dart:ui' as ui;
import '../models/shape_data.dart';
import '../models/group_shape_data.dart';
import '../painters/grid_system.dart';
import '../utils/geometry_utils.dart';

class RulerPainter extends CustomPainter {
  final ShapeData shapeData;
  final double zoomScale;
  final GridSystem gridSystem;
  final Matrix4 canvasTransformMatrix; // Canvas transform (zoom/pan)
  final double rulerOffset = -10.0; // How far left of the box the ruler is
  final double tickLength = 12.0;
  final double labelOffset = 8.0;

  RulerPainter({
    required this.shapeData,
    required this.zoomScale,
    required this.gridSystem,
    required this.canvasTransformMatrix,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // 1. Calculate Accurate Bounding Box & Height in Rows
    final dynamic accurateBoundsResult =
        GeometryUtils.calculateAccurateBoundingRect(shapeData);
    Rect accurateRect;
    if (accurateBoundsResult is GroupBoundsData) {
      accurateRect = accurateBoundsResult.bounds;
    } else if (accurateBoundsResult is Rect) {
      accurateRect = accurateBoundsResult;
    } else {
      return; // Cannot draw ruler without bounds
    }

    final double heightInPixels = accurateRect.height;
    final double rowHeightInPixels =
        gridSystem.cellWidth * gridSystem.aspectRatio;
    if (rowHeightInPixels <= 0) return; // Avoid division by zero

    final double heightInRows = heightInPixels / rowHeightInPixels;
    final int maxRow = heightInRows.round(); // Use rounded value for max label

    // If height is negligible, don't draw
    if (heightInRows < 0.1) return;

    // 2. Determine Ruler Position and Orientation
    final Matrix4 shapeRotationMatrix = Matrix4.identity()
      ..translate(shapeData.center.dx, shapeData.center.dy)
      ..rotateZ(shapeData.rotation)
      ..translate(-shapeData.center.dx, -shapeData.center.dy);

    // Calculate the bottom-left and top-left corners of the bounding box
    final Offset localBottomLeft = accurateRect.bottomLeft;
    final Offset localTopLeft = accurateRect.topLeft;

    // Transform corners by shape's rotation
    final Offset rotatedBottomLeft =
        MatrixUtils.transformPoint(shapeRotationMatrix, localBottomLeft);
    final Offset rotatedTopLeft =
        MatrixUtils.transformPoint(shapeRotationMatrix, localTopLeft);

    // Apply canvas transformation (zoom/pan)
    final Offset screenBottomLeft =
        MatrixUtils.transformPoint(canvasTransformMatrix, rotatedBottomLeft);
    final Offset screenTopLeft =
        MatrixUtils.transformPoint(canvasTransformMatrix, rotatedTopLeft);

    // Calculate the direction vector for the ruler (normalized)
    final Offset rulerVector = screenTopLeft - screenBottomLeft;
    final double rulerLengthOnScreen = rulerVector.distance;
    final Offset rulerDirection =
        rulerVector / rulerLengthOnScreen; // Normalized

    // Calculate the perpendicular vector (pointing left)
    final Offset perpendicularLeft =
        Offset(-rulerDirection.dy, rulerDirection.dx);

    // Calculate ruler start and end points, offset slightly left
    final Offset rulerStart =
        screenBottomLeft + perpendicularLeft * rulerOffset;
    final Offset rulerEnd = screenTopLeft + perpendicularLeft * rulerOffset;

    // 3. Setup Paint and Text Style
    final Paint rulerPaint = Paint()
      ..color = Get.theme.colorScheme.primary
      ..strokeWidth =
          (1.5 / zoomScale).clamp(0.5, 2.0) // Clamp between 0.5 and 2.0
      ..style = PaintingStyle.stroke;

    final ui.ParagraphStyle paragraphStyle = ui.ParagraphStyle(
      textAlign: TextAlign.right,
      fontSize: 10.0 / zoomScale, // Adjust font size based on zoom
      // Consider adding maxLines: 1, ellipsis: '...' if needed
    );
    final ui.TextStyle textStyle = ui.TextStyle(
      color: Colors.blueGrey,
      background: Paint()..color = Colors.white.withOpacity(0.7),
    );

    // 4. Draw Main Ruler Line
    canvas.drawLine(rulerStart, rulerEnd, rulerPaint);

    // 5. Draw Ticks and Labels
    final double rowPixelHeightOnScreen = rowHeightInPixels * zoomScale;
    final double minLabelSpacing = 30.0; // Minimum pixels between labels
    final int step = (minLabelSpacing / rowPixelHeightOnScreen)
        .ceil()
        .clamp(1, maxRow > 0 ? maxRow : 1);

    for (int row = 0; row <= maxRow; row++) {
      // Calculate position along the ruler line for this row
      final double distanceAlongRuler =
          (row / heightInRows) * rulerLengthOnScreen;
      final Offset tickBasePos =
          rulerStart + rulerDirection * distanceAlongRuler;

      // Determine if label should be drawn
      bool drawLabel =
          (row == 0 || row == maxRow || (row % step == 0 && row != maxRow));

      if (drawLabel) {
        // Draw tick mark
        final Offset tickEndPos =
            tickBasePos - perpendicularLeft * tickLength / zoomScale;
        canvas.drawLine(tickBasePos, tickEndPos, rulerPaint);

        final String label = row.toString();
        final ui.ParagraphBuilder paragraphBuilder =
            ui.ParagraphBuilder(paragraphStyle)
              ..pushStyle(textStyle)
              ..addText(label);
        final ui.Paragraph paragraph = paragraphBuilder.build()
          ..layout(
              const ui.ParagraphConstraints(width: 50.0)); // Constrain width

        // Calculate label position (adjust based on text size)
        final Offset labelPos = tickEndPos -
            perpendicularLeft * labelOffset / zoomScale -
            Offset(paragraph.width, paragraph.height / 2);

        canvas.drawParagraph(paragraph, labelPos);
      }
    }
  }

  @override
  bool shouldRepaint(covariant RulerPainter oldDelegate) {
    return oldDelegate.shapeData != shapeData ||
        oldDelegate.zoomScale != zoomScale ||
        oldDelegate.gridSystem != gridSystem || // Might need deeper comparison
        oldDelegate.canvasTransformMatrix != canvasTransformMatrix;
  }
}

// Required import for ui namespace - REMOVE THIS LINE
// import 'dart:ui' as ui; 