import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../handlers/shape_manipulation_handlers.dart'; // For SnapInfo, SnapType
import '../models/shape_data.dart';
import '../utils/geometry_utils.dart';

/// Enhanced grid painter focused solely on needle-based knitting grids
class EnhancedGridPainter extends CustomPainter {
  /// Base width of grid cells at 1.0 zoom level
  final double cellWidth;

  /// The aspect ratio (height/width) of stitches
  final double aspectRatio;

  /// Current zoom level (1.0 is default)
  final double zoomLevel;

  /// Number of needles in the knitting machine
  final int needleCount;

  /// Theme color for center lines and special highlights
  final Color primaryColor;

  /// Color for grid lines
  final Color gridColor;

  /// Whether to show center lines
  final bool showCenterLines;

  /// Grid opacity (overall adjustment)
  final double opacity;

  /// Major grid line interval (e.g., every 10th line)
  final int majorGridInterval;

  /// Secondary grid line interval (e.g., every 5th line)
  final int secondaryGridInterval;

  /// Whether to show needle labels
  final bool showNeedleLabels;

  /// Snap information for displaying snap lines (null if no active snap)
  final SnapInfo? activeSnapInfo;

  /// Map of shape states for snap line visualization
  final Map<Key, ShapeData>? shapeStates;

  /// Current pan offset for proper positioning
  final Offset panOffset;

  /// Creates an EnhancedGridPainter for needle-based knitting
  const EnhancedGridPainter({
    this.aspectRatio = 1.5,
    this.cellWidth = 1.0,
    this.zoomLevel = 1.0,
    required this.needleCount,
    this.primaryColor = Colors.blue,
    this.gridColor = Colors.grey,
    this.showCenterLines = true,
    this.opacity = 1.0,
    this.majorGridInterval = 10,
    this.secondaryGridInterval = 5,
    this.showNeedleLabels = true,
    this.activeSnapInfo,
    this.shapeStates,
    this.panOffset = Offset.zero,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (size.isEmpty) {
      return; // Don't attempt to draw on empty canvas
    }

    // Calculate the center of the canvas
    final centerX = size.width / 2;
    final centerY = size.height / 2;

    final extendedHeight = size.height;
    final double validAspectRatio =
        (aspectRatio <= 0 || aspectRatio.isNaN || aspectRatio.isInfinite)
            ? 1.5
            : aspectRatio;
    final rowSpacingPaint = cellWidth * validAspectRatio;
    final topEdge = centerY % rowSpacingPaint;

    // Calculate the grid boundaries
    final totalNeedles = needleCount;
    final totalWidth = totalNeedles * cellWidth;
    final leftEdge = centerX - (totalWidth / 2);
    final rightEdge = leftEdge + totalWidth;

    // Define base stroke widths
    const double baseCenterStrokeWidth = 1.5;
    // Scale stroke width based on zoom, ensuring a minimum thickness
    final double scaledCenterStrokeWidth =
        math.max(0.5, baseCenterStrokeWidth / zoomLevel);

    // Draw center lines first (if enabled)
    if (showCenterLines) {
      final centerLinePaint = Paint()
        ..color = primaryColor.withOpacity(0.5 * opacity)
        ..strokeWidth = scaledCenterStrokeWidth; // Use scaled width

      // Draw horizontal center line - only within grid bounds
      canvas.drawLine(
        Offset(leftEdge, centerY),
        Offset(rightEdge, centerY),
        centerLinePaint,
      );

      // Draw vertical center line - only within grid bounds
      canvas.drawLine(
        Offset(centerX, topEdge),
        Offset(centerX, extendedHeight),
        centerLinePaint,
      );
    }

    // Determine which grid levels to show based on zoom
    final visibleLevels = _determineVisibleGridLevels(zoomLevel);

    // Draw the grid with borders
    _drawGridBorders(canvas, size, centerX, centerY, cellWidth);

    // Draw the needle-based grid
    _drawNeedleBasedGrid(
        canvas, size, centerX, centerY, cellWidth, visibleLevels);

    // Draw needle labels if enabled
    if (showNeedleLabels) {
      _drawNeedleLabels(
          canvas, size, centerX, centerY, cellWidth, visibleLevels);
      // Also draw row labels if enabled
      _drawRowLabels(canvas, size, centerX, centerY, cellWidth, visibleLevels);
    }

    // Draw snap lines if active
    if (activeSnapInfo != null) {
      _drawSnapLines(
          canvas, size, centerX, centerY, leftEdge, rightEdge, topEdge);
    }
  }

  /// Draw snap lines based on active snap info
  void _drawSnapLines(Canvas canvas, Size size, double centerX, double centerY,
      double leftEdge, double rightEdge, double topEdge) {
    if (activeSnapInfo == null) return;

    // Prepare snap line paints
    final linePaint = Paint()
      ..color = Colors.pink.shade400
      ..strokeWidth = 1.8 / zoomLevel // Scale with zoom
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final glowPaint = Paint()
      ..color = Colors.pink.shade200.withOpacity(0.4)
      ..strokeWidth = 6.0 / zoomLevel // Scale with zoom
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, 3.0);

    final dashPaint = Paint()
      ..color = Colors.pink.shade300
      ..strokeWidth = 1.6 / zoomLevel // Scale with zoom
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // Find snapped shape data (if available)
    ShapeData? draggedShapeData;
    Rect? draggedBounds;
    Rect? targetBounds;

    if (shapeStates != null && !shapeStates!.isEmpty) {
      // Attempt to find the key of the dragged shape
      // (This is simplified - in a real implementation we would need the exact key)
      Key? draggedKey;
      for (final entry in shapeStates!.entries) {
        // For demonstration, we'll just take any key
        // In real implementation, we need the actual dragged shape key
        draggedKey = entry.key;
        break;
      }

      if (draggedKey != null) {
        draggedShapeData = shapeStates![draggedKey];
        if (draggedShapeData != null) {
          final bounds =
              GeometryUtils.calculateAccurateBoundingRect(draggedShapeData);
          if (bounds is Rect) {
            draggedBounds = bounds;
          } else if (bounds is GroupBoundsData) {
            draggedBounds = bounds.bounds;
          }
        }

        // If there's a target shape, get its bounds too
        if (activeSnapInfo!.targetShapeKey != null) {
          final targetShapeData = shapeStates![activeSnapInfo!.targetShapeKey];
          if (targetShapeData != null) {
            final bounds =
                GeometryUtils.calculateAccurateBoundingRect(targetShapeData);
            if (bounds is Rect) {
              targetBounds = bounds;
            } else if (bounds is GroupBoundsData) {
              targetBounds = bounds.bounds;
            }
          }
        }
      }
    }

    // Calculate start and end points for the snap line
    Offset? startPoint;
    Offset? endPoint;

    // Helper values for extending lines
    final double padding = 20.0;

    // Handle each snap type
    switch (activeSnapInfo!.snapType) {
      // *** CENTER LINE SNAPS - the key focus for this fix ***
      case SnapType.canvasCenterHorizontal:
        // Use the actual grid center X, not the canvas center
        // This ensures correct alignment when zoomed/panned
        startPoint = Offset(centerX, topEdge);
        endPoint = Offset(centerX, size.height);
        break;

      case SnapType.canvasCenterVertical:
        // Use the actual grid center Y, not the canvas center
        startPoint = Offset(leftEdge, centerY);
        endPoint = Offset(rightEdge, centerY);
        break;

      // Shape-to-shape snaps (simplified implementation)
      default:
        if (draggedBounds != null) {
          // Handle other snap types if needed
          // This is a simplified placeholder
          if (activeSnapInfo!.isHorizontal) {
            // Handle horizontal snaps
            startPoint =
                Offset(draggedBounds.left - padding, draggedBounds.center.dy);
            endPoint =
                Offset(draggedBounds.right + padding, draggedBounds.center.dy);
          } else {
            // Handle vertical snaps
            startPoint =
                Offset(draggedBounds.center.dx, draggedBounds.top - padding);
            endPoint =
                Offset(draggedBounds.center.dx, draggedBounds.bottom + padding);
          }
        }
        break;
    }

    // Draw the snap line if points are valid
    if (startPoint != null && endPoint != null) {
      // For center line snaps, draw dashed lines with glow
      if (activeSnapInfo!.snapType == SnapType.canvasCenterHorizontal ||
          activeSnapInfo!.snapType == SnapType.canvasCenterVertical) {
        // Draw glow effect first
        canvas.drawLine(startPoint, endPoint, glowPaint);

        // Then draw dashed line for center snaps
        _drawDashedLine(canvas, startPoint, endPoint, dashPaint);

        // Add visual indicators for center lines
        _drawCenterLineMarkers(
            canvas, startPoint, endPoint, activeSnapInfo!.snapType);
      } else {
        // For other snaps, draw solid line with glow
        canvas.drawLine(startPoint, endPoint, glowPaint);
        canvas.drawLine(startPoint, endPoint, linePaint);
      }
    }
  }

  /// Helper method to draw dashed lines
  void _drawDashedLine(Canvas canvas, Offset start, Offset end, Paint paint) {
    final path = Path();
    final dashPath = Path();

    path.moveTo(start.dx, start.dy);
    path.lineTo(end.dx, end.dy);

    // Define dash properties
    final double dashLength = 5.0;
    final double dashSpace = 5.0;

    // Convert path to dashes
    final pathMetrics = path.computeMetrics().first;
    final totalLength = pathMetrics.length;

    var distance = 0.0;
    var drawDash = true;

    while (distance < totalLength) {
      final dash = drawDash ? dashLength : dashSpace;
      final nextDistance = distance + dash;

      if (nextDistance <= totalLength) {
        final extractPath = pathMetrics.extractPath(distance, nextDistance);
        if (drawDash) {
          dashPath.addPath(extractPath, Offset.zero);
        }
      } else {
        final extractPath = pathMetrics.extractPath(distance, totalLength);
        if (drawDash) {
          dashPath.addPath(extractPath, Offset.zero);
        }
      }

      distance = nextDistance;
      drawDash = !drawDash;
    }

    canvas.drawPath(dashPath, paint);
  }

  /// Helper method to draw markers along center lines
  void _drawCenterLineMarkers(
      Canvas canvas, Offset start, Offset end, SnapType snapType) {
    final isHorizontal = snapType == SnapType.canvasCenterHorizontal;
    final centerLine = isHorizontal ? start.dy : start.dx;
    final lineLength =
        isHorizontal ? (end.dx - start.dx).abs() : (end.dy - start.dy).abs();

    // Draw diamond-shaped markers
    final markerCount =
        (lineLength / 100).ceil(); // One marker every 100 pixels
    final spacing = lineLength / (markerCount + 1);

    final markerPaint = Paint()
      ..color = Colors.pinkAccent.shade200
      ..style = PaintingStyle.fill;

    for (int i = 1; i <= markerCount; i++) {
      final position = i * spacing;
      Offset markerCenter;

      if (isHorizontal) {
        markerCenter = Offset(start.dx + position, centerLine);
      } else {
        markerCenter = Offset(centerLine, start.dy + position);
      }

      // Create diamond path
      final diamondPath = Path();
      final diamondSize = 6.0 / math.sqrt(zoomLevel); // Scale with zoom

      diamondPath.moveTo(markerCenter.dx, markerCenter.dy - diamondSize);
      diamondPath.lineTo(markerCenter.dx + diamondSize, markerCenter.dy);
      diamondPath.lineTo(markerCenter.dx, markerCenter.dy + diamondSize);
      diamondPath.lineTo(markerCenter.dx - diamondSize, markerCenter.dy);
      diamondPath.close();

      canvas.drawPath(diamondPath, markerPaint);
    }
  }

  // /// Calculate effective grid size based on zoom level
  // double _calculateEffectiveGridSize(double baseSize, double zoom) {
  //   // At zoom = 1.0, we want the standard grid size
  //   // At zoom = 0.5, we want larger cells (to show fewer)
  //   // At zoom = 2.0, we want smaller cells (to show more)

  //   // For a smoother transition, we might use a non-linear scaling
  //   if (zoom < 0.1) zoom = 0.1; // Prevent division by near-zero

  //   return baseSize;
  // }

  /// Determine which grid levels should be visible based on zoom
  Map<int, double> _determineVisibleGridLevels(double zoom) {
    // Define grid levels with their corresponding opacity
    // Level 1 = Major grid lines (every majorGridInterval)
    // Level 2 = Secondary grid lines (every secondaryGridInterval)
    // Level 3 = Minor grid lines (normal lines)
    // Level 4 = Micro grid lines (only visible at high zoom)

    final Map<int, double> levels = {};

    // Major grid lines are always visible, but opacity varies with zoom
    levels[majorGridInterval] = math.min(0.7, 0.4 + (zoom * 0.15)) * opacity;

    // Secondary grid lines appear at moderate zoom
    if (zoom >= 0.75) {
      levels[secondaryGridInterval] =
          math.min(0.5, 0.2 + (zoom * 0.1)) * opacity;
    }

    // Minor grid lines appear at normal zoom
    if (zoom >= 1.0) {
      levels[1] = math.min(0.3, 0.1 + (zoom * 0.05)) * opacity;
    }

    // Micro grid (subdivisions) appear at high zoom
    if (zoom >= 2.0) {
      levels[0] = 0.15 * opacity;
    }

    return levels;
  }

  /// Draw the grid borders (top, left, and right only)
  void _drawGridBorders(
    Canvas canvas,
    Size size,
    double centerX,
    double centerY,
    double effectiveGridSize,
  ) {
    final totalWidth = needleCount * effectiveGridSize;
    final leftEdge = centerX - (totalWidth / 2);
    final rightEdge = leftEdge + totalWidth;
    final double validAspectRatio =
        (aspectRatio <= 0 || aspectRatio.isNaN || aspectRatio.isInfinite)
            ? 1.5
            : aspectRatio;
    final rowSpacingBorder = effectiveGridSize * validAspectRatio;
    final topEdge = centerY % rowSpacingBorder;

    final borderPaint = Paint()
      ..color = gridColor.withOpacity(opacity)
      ..strokeWidth = 1;

    // Draw top border
    canvas.drawLine(
      Offset(leftEdge, topEdge),
      Offset(rightEdge, topEdge),
      borderPaint,
    );

    // Draw left border
    canvas.drawLine(
      Offset(leftEdge, topEdge),
      Offset(leftEdge, size.height),
      borderPaint,
    );

    // Draw right border
    canvas.drawLine(
      Offset(rightEdge, topEdge),
      Offset(rightEdge, size.height),
      borderPaint,
    );
  }

  /// Draws a grid based on the knitting machine's needle count
  void _drawNeedleBasedGrid(
    Canvas canvas,
    Size size,
    double centerX,
    double centerY,
    double effectiveGridSize,
    Map<int, double> visibleLevels,
  ) {
    final totalNeedles = needleCount;

    // Calculate total grid width based on needle count
    final totalWidth = totalNeedles * effectiveGridSize;

    // Starting position to center the grid
    final leftEdge = centerX - (totalWidth / 2);
    final rightEdge = leftEdge + totalWidth;

    // Calculate grid boundaries for horizontal lines - rows are closer together
    final double validAspectRatio =
        (aspectRatio <= 0 || aspectRatio.isNaN || aspectRatio.isInfinite)
            ? 1.5
            : aspectRatio;
    final rowSpacing = effectiveGridSize * validAspectRatio;
    final topEdgeOffset = centerY % rowSpacing;

    // Calculate the top edge of the grid (align with the first visible row)
    final gridTop = topEdgeOffset;

    // Define a bottom boundary for the grid (no longer extending indefinitely)
    // Use the actual canvas height as the bottom boundary
    final gridBottom = size.height;

    // Define base stroke widths for grid lines
    const double baseMajorStrokeWidth = 1.0;
    const double baseMinorStrokeWidth = 0.8;
    // Scale stroke width based on zoom, ensuring a minimum thickness
    final double scaledMajorStrokeWidth =
        math.max(0.3, baseMajorStrokeWidth / zoomLevel);
    final double scaledMinorStrokeWidth =
        math.max(0.2, baseMinorStrokeWidth / zoomLevel);

    // Draw vertical grid lines for needles (excluding border lines)
    for (int i = 1; i < totalNeedles; i++) {
      final x = leftEdge + (i * effectiveGridSize);

      // Skip if outside grid boundaries
      if (x <= leftEdge || x >= rightEdge) continue;

      // Determine line importance and opacity
      double lineOpacity = 0;
      double strokeWidth = scaledMinorStrokeWidth; // Default to minor width

      if (i % majorGridInterval == 0 &&
          visibleLevels.containsKey(majorGridInterval)) {
        lineOpacity = visibleLevels[majorGridInterval]!;
        strokeWidth = scaledMajorStrokeWidth; // Use major width
      } else if (i % secondaryGridInterval == 0 &&
          visibleLevels.containsKey(secondaryGridInterval)) {
        lineOpacity = visibleLevels[secondaryGridInterval]!;
        // Use slightly thicker than minor for secondary
        strokeWidth = math.max(0.25,
            (baseMinorStrokeWidth + baseMajorStrokeWidth) / 2 / zoomLevel);
      } else if (visibleLevels.containsKey(1)) {
        lineOpacity = visibleLevels[1]!;
      }

      // Only draw if the line should be visible
      if (lineOpacity > 0) {
        final gridPaint = Paint()
          ..color = gridColor.withOpacity(lineOpacity)
          ..strokeWidth = strokeWidth;

        // Draw vertical line from grid top to grid bottom (not extending beyond)
        canvas.drawLine(
          Offset(x, gridTop),
          Offset(x, gridBottom),
          gridPaint,
        );
      }
    }

    // Draw horizontal grid lines (excluding top border)
    for (double y = gridTop + rowSpacing; y <= gridBottom; y += rowSpacing) {
      // Skip if outside grid boundaries
      if (y < gridTop || y > gridBottom) continue;

      // Calculate row number from top edge instead of relative to center
      final rowNumber = ((y - gridTop) / rowSpacing).round();
      if (rowNumber <= 0) continue; // Ensure positive row numbers

      // Determine line importance and opacity
      double lineOpacity = 0;
      double strokeWidth = scaledMinorStrokeWidth; // Default to minor width

      if (rowNumber % majorGridInterval == 0 &&
          visibleLevels.containsKey(majorGridInterval)) {
        lineOpacity = visibleLevels[majorGridInterval]!;
        strokeWidth = scaledMajorStrokeWidth; // Use major width
      } else if (rowNumber % secondaryGridInterval == 0 &&
          visibleLevels.containsKey(secondaryGridInterval)) {
        lineOpacity = visibleLevels[secondaryGridInterval]!;
        // Use slightly thicker than minor for secondary
        strokeWidth = math.max(0.25,
            (baseMinorStrokeWidth + baseMajorStrokeWidth) / 2 / zoomLevel);
      } else if (visibleLevels.containsKey(1)) {
        lineOpacity = visibleLevels[1]!;
      }

      // Only draw if the line should be visible
      if (lineOpacity > 0) {
        final gridPaint = Paint()
          ..color = gridColor.withOpacity(lineOpacity)
          ..strokeWidth = strokeWidth;

        // Draw horizontal line exactly within grid boundaries
        canvas.drawLine(
          Offset(leftEdge, y),
          Offset(rightEdge, y),
          gridPaint,
        );
      }
    }
  }

  /// Draws needle labels based on the zoom level
  void _drawNeedleLabels(
    Canvas canvas,
    Size size,
    double centerX,
    double centerY,
    double effectiveGridSize,
    Map<int, double> visibleLevels,
  ) {
    final totalNeedles = needleCount;
    final halfNeedles = totalNeedles ~/ 2;

    // Calculate total grid width based on needle count
    final totalWidth = totalNeedles * effectiveGridSize;

    // Starting position to center the grid
    final leftEdge = centerX - (totalWidth / 2);
    final double validAspectRatio =
        (aspectRatio <= 0 || aspectRatio.isNaN || aspectRatio.isInfinite)
            ? 1.5
            : aspectRatio;
    final rowSpacingLabel = effectiveGridSize * validAspectRatio;
    final topEdge = centerY % rowSpacingLabel;

    // Determine label interval based on zoom level - show fewer labels at lower zoom
    int labelInterval = _determineLabelInterval(zoomLevel);

    // Define text style for labels
    final fontSize = math.max(6.0, math.min(14.0, 9.0 / math.sqrt(zoomLevel)));
    const fontFamily = 'Roboto';
    final textColor = primaryColor
        .withOpacity(math.min(0.9, 0.6 + (zoomLevel * 0.15)) * opacity);

    // Function to draw a needle label
    void drawNeedleLabel(int needleNumber, String prefix, double x) {
      final label = '$prefix$needleNumber';
      final textSpan = TextSpan(
        text: label,
        style: TextStyle(
          color: textColor,
          fontSize: fontSize,
          fontFamily: fontFamily,
        ),
      );

      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.center,
      );

      textPainter.layout();

      // Calculate position to place label ABOVE the top grid border
      final double labelPadding =
          4.0; // Space between grid line and bottom of text
      final labelY = topEdge -
          labelPadding -
          textPainter.height; // Y-coord for top of text

      textPainter.paint(
        canvas,
        Offset((x - textPainter.width / 2),
            labelY), // Position text above the top edge, removed -1 horizontal offset
      );

      // Draw the tick mark pointing UPWARDS from the top grid line
      final tickStartY = topEdge + 2; // Start tick exactly at the top grid line
      final tickEndY = topEdge - 5.0; // Tick length of 5 pixels upwards
      final tickPaint = Paint()
        ..color = textColor // Use the same color as the label
        ..strokeWidth = 0.5; // Thin line

      canvas.drawLine(Offset(x, tickStartY), Offset(x, tickEndY), tickPaint);
    }

    // Draw left side (L) needle labels from center outward
    // Corrected: L1 should be closest to center, increasing outwards.
    for (int i = 1; i <= halfNeedles; i++) {
      // Position at this offset from center
      // L1 is positioned half a needle width to the left of center
      // Corrected: Align with the grid line i steps to the left of center
      final needleX = centerX - (i * effectiveGridSize);

      // Skip if outside the grid
      if (needleX < leftEdge) continue;
      // Corrected label numbering for left side
      final int needleNumber = i;
      // Determine label visibility based *only* on the interval
      final bool showThisLabel = (needleNumber % labelInterval == 0);
      if (showThisLabel) {
        // Check minimum screen distance to previous label if needed (advanced)
        // For now, rely solely on the interval.
        drawNeedleLabel(needleNumber, 'L', needleX);
      }
    }

    // Draw right side (R) needle labels from center outward
    // R1, R2, ... increasing outwards.
    for (int i = 1; i <= halfNeedles; i++) {
      // Position at this offset from center
      // R1 is positioned half a needle width to the right of center
      // Corrected: Align with the grid line i steps to the right of center
      final needleX = centerX + (i * effectiveGridSize);
      // Skip if outside the grid
      if (needleX > leftEdge + totalWidth) continue;
      // Numbering is already correct for right side
      final int needleNumber = i;
      // Determine label visibility based *only* on the interval
      final bool showThisLabel = (needleNumber % labelInterval == 0);
      if (showThisLabel) {
        // Check minimum screen distance to previous label if needed (advanced)
        // For now, rely solely on the interval.
        drawNeedleLabel(needleNumber, 'R', needleX);
      }
    }
  }

  /// Draws row labels based on the zoom level
  void _drawRowLabels(
    Canvas canvas,
    Size size,
    double centerX,
    double centerY,
    double effectiveGridSize,
    Map<int, double> visibleLevels,
  ) {
    final totalNeedles = needleCount;
    final totalWidth = totalNeedles * effectiveGridSize;
    final leftEdge = centerX - (totalWidth / 2);

    // Row calculations using aspect ratio
    final double validAspectRatio =
        (aspectRatio <= 0 || aspectRatio.isNaN || aspectRatio.isInfinite)
            ? 1.5
            : aspectRatio;
    final rowSpacing = effectiveGridSize * validAspectRatio;
    final topEdgeOffset = centerY % rowSpacing;
    final gridTop = topEdgeOffset;
    final gridBottom = size.height;

    // Determine label interval based on zoom level
    int labelInterval =
        _determineLabelInterval(zoomLevel, true); // Reuse needle interval logic

    // Define text style for labels (similar to needle labels)
    final fontSize = math.max(6.0, math.min(14.0, 9.0 / math.sqrt(zoomLevel)));
    const fontFamily = 'Roboto';
    final textColor = primaryColor
        .withOpacity(math.min(0.9, 0.6 + (zoomLevel * 0.15)) * opacity);

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign:
          TextAlign.right, // Align text to the right for left-side labels
    );

    // Loop through horizontal grid lines to draw labels
    for (double y = gridTop + rowSpacing; y <= gridBottom; y += rowSpacing) {
      // Skip if outside grid boundaries (redundant check, but safe)
      if (y < gridTop || y > gridBottom) continue;

      // Calculate simple row number (1 for the first line below top border)
      final int rowNumber = ((y - gridTop) / rowSpacing).round();
      if (rowNumber <= 0) continue; // Ensure positive row numbers

      // Determine label visibility based on the interval
      final bool showThisLabel = (rowNumber % labelInterval == 0);

      if (showThisLabel) {
        // Prepare text span
        textPainter.text = TextSpan(
          text: rowNumber.toString(),
          style: TextStyle(
            color: textColor,
            fontSize: fontSize,
            fontFamily: fontFamily,
          ),
        );
        textPainter.layout();

        // Calculate position for the label text (left of the grid)
        final double labelPadding = 5.0; // Space between grid edge and text
        final labelX = leftEdge - labelPadding - textPainter.width;
        final labelY =
            y - (textPainter.height / 2); // Center text vertically on the line

        textPainter.paint(
            canvas, Offset(labelX, labelY)); // Removed -1 vertical offset

        // Draw the horizontal tick mark
        final tickStartX = leftEdge + 2;
        final tickEndX = leftEdge - 5.0; // Tick length of 5 pixels to the left
        final tickPaint = Paint()
          ..color = textColor // Use the same color as the label
          ..strokeWidth = 0.5; // Thin line

        canvas.drawLine(Offset(tickStartX, y), Offset(tickEndX, y),
            tickPaint); // Removed -1 vertical offset from y
      }
    }
  }

  /// Determines the interval for showing needle labels based on zoom level
  int _determineLabelInterval(double zoom, [bool isRowLabel = false]) {
    // Adjust intervals for less clutter at low zoom and reasonable density at high zoom
    if (zoom < 0.4) {
      return 50; // Very sparse at lowest zoom
    } else if (zoom < 0.7) {
      return 40; // Sparse at low zoom
    } else if (zoom < 1.5) {
      return 20; // Medium density around default zoom
    } else if (zoom < 3.0) {
      return 20; // Higher density at moderate zoom
    } else {
      return isRowLabel
          ? 10
          : 15; // Highest density (every other) at max zoom (avoid 1 for clutter)
    }
  }

  @override
  bool shouldRepaint(EnhancedGridPainter oldDelegate) =>
      oldDelegate.cellWidth != cellWidth ||
      oldDelegate.aspectRatio != aspectRatio ||
      oldDelegate.zoomLevel != zoomLevel ||
      oldDelegate.needleCount != needleCount ||
      oldDelegate.primaryColor != primaryColor ||
      oldDelegate.gridColor != gridColor ||
      oldDelegate.showCenterLines != showCenterLines ||
      oldDelegate.opacity != opacity ||
      oldDelegate.showNeedleLabels != showNeedleLabels ||
      oldDelegate.majorGridInterval != majorGridInterval ||
      oldDelegate.secondaryGridInterval != secondaryGridInterval ||
      oldDelegate.activeSnapInfo != activeSnapInfo ||
      oldDelegate.panOffset != panOffset;
}
