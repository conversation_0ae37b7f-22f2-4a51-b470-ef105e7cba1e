import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Painter that draws a needle-based grid for knitting patterns
/// with dynamic resolution based on zoom level
class GridPainter extends CustomPainter {
  final double gridSize;
  final double zoomLevel;
  final int needleCount;

  /// Creates a GridPainter for needle-based knitting grids
  /// - [gridSize]: Base size of grid cells (based on needle pitch)
  /// - [zoomLevel]: Current zoom level (1.0 is default)
  /// - [needleCount]: Number of needles in the knitting machine
  const GridPainter({
    required this.gridSize,
    required this.zoomLevel,
    required this.needleCount,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (size.isEmpty) {
      return; // Don't attempt to draw on empty canvas
    }

    // Calculate the center of the canvas
    final centerX = size.width / 2;
    final centerY = size.height / 2;

    // Calculate the effective grid size based on zoom
    final effectiveGridSize = gridSize * zoomLevel;

    // Draw center lines first - these are always visible
    final centerLinePaint = Paint()
      ..color = Get.theme.colorScheme.primary.withOpacity(0.5)
      ..strokeWidth = 2.0;

    canvas.drawLine(
      Offset(0, centerY),
      Offset(size.width, centerY),
      centerLinePaint,
    );

    canvas.drawLine(
      Offset(centerX, 0),
      Offset(centerX, size.height),
      centerLinePaint,
    );

    // Calculate total grid width based on needle count
    final totalWidth = needleCount * effectiveGridSize;

    // Starting position to center the grid
    final leftEdge = centerX - (totalWidth / 2);

    // Draw vertical grid lines for needles
    for (int i = 0; i <= needleCount; i++) {
      final x = leftEdge + (i * effectiveGridSize);

      // Skip if outside canvas
      if (x < 0 || x > size.width) continue;

      // Determine line importance and opacity
      double opacity = 0;
      double strokeWidth = 1.0;

      if (i % 10 == 0) {
        // Major lines - every 10th needle
        opacity = 0.5;
        strokeWidth = 1.5;
      } else if (i % 5 == 0) {
        // Secondary lines - every 5th needle
        opacity = 0.25;
      } else {
        // Normal grid lines
        opacity = 0.1;
      }

      final gridPaint = Paint()
        ..color = Colors.grey.withOpacity(opacity)
        ..strokeWidth = strokeWidth;

      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        gridPaint,
      );
    }

    // Draw horizontal grid lines
    final topEdgeOffset = centerY % effectiveGridSize;
    final startY = topEdgeOffset - effectiveGridSize;

    // Draw horizontal grid lines
    for (double y = startY;
        y <= size.height + effectiveGridSize;
        y += effectiveGridSize) {
      // Skip if outside canvas
      if (y < 0 || y > size.height) continue;

      // Calculate grid line position relative to center
      final pos = ((y - centerY) / effectiveGridSize).round();
      final absPos = pos.abs();

      // Determine line importance and opacity
      double opacity = 0;
      double strokeWidth = 1.0;

      if (absPos % 10 == 0) {
        // Major lines - every 10th row
        opacity = 0.5;
        strokeWidth = 1.5;
      } else if (absPos % 5 == 0) {
        // Secondary lines - every 5th row
        opacity = 0.25;
      } else {
        // Normal grid lines
        opacity = 0.1;
      }

      final gridPaint = Paint()
        ..color = Colors.grey.withOpacity(opacity)
        ..strokeWidth = strokeWidth;

      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        gridPaint,
      );
    }
  }

  @override
  bool shouldRepaint(GridPainter oldDelegate) =>
      oldDelegate.gridSize != gridSize ||
      oldDelegate.zoomLevel != zoomLevel ||
      oldDelegate.needleCount != needleCount;
}
