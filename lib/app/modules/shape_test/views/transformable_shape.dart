import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // Add import for HapticFeedback
// Import the math library for min/max functions
import 'dart:math' as math;
import 'package:get/get.dart';
import '../models/shape_data.dart';
import '../models/group_shape_data.dart';
import '../painters/shape_painter.dart';
import '../painters/group_shape_painter.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/shape_editor_controller.dart';
import '../utils/geometry_utils.dart';
import '../utils/hit_test_utils.dart';
import '../handlers/shape_manipulation_handlers.dart';
import '../widgets/shape_handles_widget.dart';
import '../constants/grid_constants.dart';
// *** ADD IMPORT FOR GRID SYSTEM ***
import '../painters/grid_system.dart';

/// TransformableShape is the main interactive widget that renders a shape
/// and handles user interaction with it (dragging, resizing, rotating)
class TransformableShape extends StatefulWidget {
  final BoxConstraints constraints;
  final ShapeType initialShapeType;
  final Rect? initialRect;
  final bool selected;
  final ShapeData? initialShapeData;
  final bool? initialCurveMode;

  // Registry to keep track of shape states by key
  // This allows finding a shape's state from its key
  static final Map<Key, _TransformableShapeState> _stateRegistry = {};

  const TransformableShape({
    super.key,
    required this.constraints,
    required this.initialShapeType,
    this.initialRect,
    this.selected = false,
    this.initialShapeData,
    this.initialCurveMode,
  });

  @override
  State<TransformableShape> createState() => _TransformableShapeState();

  // Utility method to get shape data from its context
  // Useful for finding a shape's data from outside
  ShapeData? getShapeData(BuildContext context) {
    return initialShapeData;
  }

  Path getShapePath(BuildContext context) {
    final state = context.findAncestorStateOfType<_TransformableShapeState>();
    return state?.getShapePath() ?? Path();
  }

  // Utility method to get the painter from the state
  // This is useful for accessing the rendering details
  CustomPainter? getPainter(BuildContext context) {
    final state = context.findAncestorStateOfType<_TransformableShapeState>();
    return state?._painter;
  }

  BuildContext? getContext(BuildContext context) {
    final state = context.findAncestorStateOfType<_TransformableShapeState>();
    return state?._painterKey.currentContext;
  }

  /// Toggle curve mode for this shape
  /// Switches between normal and bezier curve editing
  void toggleCurveMode() {
    if (key != null && _stateRegistry.containsKey(key)) {
      final state = _stateRegistry[key];
      if (state != null) {
        state._toggleCurveMode();
      }
    }
  }
}

class _TransformableShapeState extends State<TransformableShape> {
  // Curve mode allows editing the bezier curves between vertices
  bool curveMode = false;

  // Core shape data that defines this shape's appearance and behavior
  late ShapeData shapeData;

  // Key to access this shape's render object
  final GlobalKey _painterKey = GlobalKey();

  // Reference to the controller managing all shapes
  late final ShapeEditorController _controller;

  // Painter that actually draws the shape
  late CustomPainter _painter;

  // Used for rotation calculations
  Offset _lastRotationPosition = Offset.zero;

  bool _isDragThrottleActive = false;
  Offset _accumulatedDelta = Offset.zero;

  // Track active edge dragging state
  bool _isEdgeDragging = false;
  int? _activeDragEdgeIndex;
  Offset? _currentControlPoint;

  // Flag to indicate an active drag operation (vertex, edge, shape, rotation)
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    _controller = Get.find<ShapeEditorController>();
    final rect = widget.initialRect ?? Rect.fromLTWH(100, 100, 200, 150);
    shapeData = widget.initialShapeData ?? _createInitialShape(rect);

    // Initialize curve mode from widget parameter if provided
    if (widget.initialCurveMode != null) {
      curveMode = widget.initialCurveMode!;
    }

    // Ensure we're using the full vertical height of the grid
    // by checking if our constraints are properly extended
    if (widget.constraints.maxHeight <
        GridConstants.getExtendedHeight(Get.height / 2)) {
      debugPrint(
          'Warning: TransformableShape constraints not properly extended');
    }

    // Set up the painter that will render this shape
    _updatePainter();

    // Register this state so it can be found from the key
    if (widget.key != null) {
      TransformableShape._stateRegistry[widget.key!] = this;
    }

    // Defer saving the state until after the build completes
    // This prevents triggering reactive UI updates during the build phase
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Make sure the controller knows about the initial state
      if (mounted) {
        _saveState();
      }
    });
  }

  @override
  void didUpdateWidget(TransformableShape oldWidget) {
    super.didUpdateWidget(oldWidget);

    // --- Check if an update is needed based on initialShapeData ---
    bool shapeDataChanged = widget.initialShapeData != null &&
        widget.initialShapeData != oldWidget.initialShapeData;

    // --- Check if an update is needed based on initialCurveMode ---
    bool curveModeChanged =
        widget.initialCurveMode != null && widget.initialCurveMode != curveMode;

    // --- Prevent state overwrite during active drag operations ---
    if (_isDragging) {
      // If actively dragging, only update curveMode if it changed externally.
      // Do NOT overwrite shapeData being manipulated by the drag handler.
      if (curveModeChanged) {
        curveMode = widget.initialCurveMode!;
        _updatePainter(); // Update painter if curve mode changes
      }
      // Skip shapeData update during drag
      return;
    }
    // --- End Drag Prevention ---

    // --- Apply updates if not dragging ---
    if (shapeDataChanged) {
      // Get the key to check if this is a mirrored shape
      final Key? shapeKey = widget.key;
      bool isMirroredShape = false;

      if (shapeKey != null) {
        // Check if this is a mirrored shape by checking if it appears in the mirroredPairs values
        isMirroredShape = _controller.isShapeMirrored(shapeKey);
      }

      // Always update the local shape data if not dragging
      shapeData = widget.initialShapeData!;

      // Special handling for groups - ensure bounding box integrity
      if (shapeData is GroupShapeData && shapeData.type == ShapeType.group) {
        _recalculateGroupBoundingBox();
      }

      // Only save state back to controller if this is NOT a mirrored shape update
      // This breaks the circular dependency for mirrored shapes
      if (!isMirroredShape) {
        // Defer saving state until after the current build completes
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            _saveState();
          }
        });
      }
    }

    // Update curve mode if it changed and we're not dragging
    if (curveModeChanged) {
      curveMode = widget.initialCurveMode!;
    }

    // Update painter if selection state, shape data (and not dragging), or curve mode changes
    if (oldWidget.selected != widget.selected ||
        shapeDataChanged ||
        curveModeChanged) {
      _updatePainter();
    }
  }

  @override
  void dispose() {
    // Remove this state from registry
    if (widget.key != null) {
      TransformableShape._stateRegistry.remove(widget.key);
    }
    super.dispose();
  }

  // Update the painter based on current shape state
  void _updatePainter() {
    if (shapeData.vertices.isEmpty) {
      // Ensure we have valid vertices before creating a painter
      // This prevents rendering issues with Impeller
      _painter = ShapePainter(
        shapeData: ShapeData.rectangle(
            Rect.fromLTWH(0, 0, 1, 1)), // Minimal valid shape
        curveMode: false,
        constraints: widget.constraints,
        selected: false,
      );
      return;
    }

    // Use appropriate painter based on shape type
    if (shapeData.type == ShapeType.group && shapeData is GroupShapeData) {
      _painter = GroupShapePainter(
        groupData: shapeData as GroupShapeData,
        curveMode: curveMode,
        constraints: widget.constraints,
        selected: widget.selected,
      );
    } else {
      _painter = ShapePainter(
        shapeData: shapeData,
        curveMode: curveMode,
        constraints: widget.constraints,
        selected: widget.selected,
        controller: _controller,
        shapeKey: widget.key,
      );
    }
  }

  // Create initial shape data based on the requested shape type
  ShapeData _createInitialShape(Rect rect) {
    switch (widget.initialShapeType) {
      case ShapeType.rectangle:
        return ShapeData.rectangle(rect);
      case ShapeType.triangle:
        return ShapeData.triangle(rect);
      case ShapeType.rightTriangle:
        return ShapeData.rightTriangle(rect);
      case ShapeType.trapezoid:
        return ShapeData.trapezoid(rect);
      case ShapeType.group:
        // This shouldn't happen normally as groups are created from existing shapes
        // but a simple default group shape is created just in case
        return ShapeData.rectangle(rect);
      case ShapeType.custom:
        // For custom shapes, initialize with a rectangular boundary
        // (actual vertices will be set during creation)
        return ShapeData(
          type: ShapeType.custom,
          vertices: [
            rect.topLeft,
            rect.topRight,
            rect.bottomRight,
            rect.bottomLeft,
          ],
          boundingRect: rect,
          center: rect.center,
          rotation: 0.0,
          visualRotation: 0.0,
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    _updatePainter();

    // *** Get the current zoom scale from the controller ***
    final double currentZoomScale = _controller.zoomScale.value;

    // Create a transform matrix for rotation
    final transformMatrix = GeometryUtils.createRotationMatrix(
        shapeData.center, shapeData.rotation);

    // Note: The constraints may be extended vertically (up to 10x screen height)
    // This allows the shape to be manipulated throughout the entire grid area
    return RepaintBoundary(
      child: SizedBox(
        // Use the full available width/height to ensure interaction works
        // throughout the entire grid area
        width: widget.constraints.maxWidth,
        height: widget.constraints.maxHeight,
        child: Stack(
          children: [
            // Main shape
            ShapeHitDetector(
              painter: _painter,
              onPanStart: _onShapeDragStart,
              onPanUpdate: _handleShapeDrag,
              onPanEnd: _onDragEnd,
              child: CustomPaint(
                key: _painterKey,
                painter: _painter,
                // Set size to match the full grid extent
                size: Size(
                  widget.constraints.maxWidth,
                  widget.constraints.maxHeight,
                ),
              ),
            ),

            // Curves mode indicator
            if (curveMode &&
                widget.selected &&
                !(shapeData.type == ShapeType.group ||
                    shapeData is GroupShapeData))
              Positioned(
                right: 10,
                top: 10,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.purple.withOpacity(0.8),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'Curve Mode',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

            // Handles for selected shape (except rotation which is positioned separately)
            if (widget.selected)
              ShapeHandlesWidget(
                shapeData: shapeData,
                curveMode: curveMode,
                transformMatrix: transformMatrix,
                onVertexDrag: _handleVertexDrag,
                onEdgeDrag: _handleEdgeDrag,
                onDragEnd: _handleDragEnd,
                zoomScale: currentZoomScale, // *** Pass zoom scale here ***
                // *** PASS GRID SYSTEM ***
                gridSystem: _controller.gridSystem,
                shapeKey: widget.key, // *** Pass the shape's actual key ***
              ),

            // Control point visualization during edge dragging
            if (_isEdgeDragging && _currentControlPoint != null)
              Positioned(
                left: _currentControlPoint!.dx -
                    6, // Center the indicator on the control point
                top: _currentControlPoint!.dy -
                    6, // Center the indicator on the control point
                child: Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: Colors.purple.withOpacity(0.7),
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 2),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                ),
              ),

            // Property badges for selected shape - only show if badge mode or both modes are enabled
            // if (widget.selected &&
            //     !_controller.isHandleInteraction &&
            //     (_controller.propertyDisplayMode.value ==
            //             PropertyDisplayMode.badges ||
            //         _controller.propertyDisplayMode.value ==
            //             PropertyDisplayMode.both))
            //   PropertyBadges(
            //     shapeData: shapeData,
            //     controller: _controller,
            //     transformMatrix: transformMatrix,
            //   ),
          ],
        ),
      ),
    );
  }

  // --- Drag Start Handlers ---

  void _onShapeDragStart(DragStartDetails details) {
    _isDragging = true; // Set drag flag
    // Existing onPanStart logic from ShapeHitDetector
    _controller.isHandleInteraction = false;
    if (!widget.selected) {
      final shapeKey = widget.key;
      if (shapeKey != null) {
        _controller.handleShapeTap(shapeKey);
      }
    }
    _accumulatedDelta = Offset.zero;
    _isDragThrottleActive = false;
    _controller.startHistoryTracking("Move Shape");
  }

  // --- Drag End Handlers ---

  void _onDragEnd(DragEndDetails details) {
    _isDragging = false; // Clear drag flag
    // Existing logic...
    if (_accumulatedDelta.distance > 0) {
      _handleShapeDrag(DragUpdateDetails(
        globalPosition: details.primaryVelocity != null
            ? Offset(details.primaryVelocity!, 0)
            : Offset.zero,
        delta: _accumulatedDelta,
      ));
    }
    setState(() {});
    _saveState();
    _accumulatedDelta = Offset.zero;

    // Defer controller operations until after the current frame completes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _controller.finishHistoryTracking();
      }
    });

    _controller.setActiveSnapInfo(null); // Clear snap info
  }

  void _handleDragEnd(DragEndDetails details) {
    _isDragging = false; // Clear drag flag
    // Reset edge dragging state
    _isEdgeDragging = false;
    _activeDragEdgeIndex = null;
    _currentControlPoint = null;

    // Defer state updates until after the current frame completes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _updateShapeData();
        _controller.finishHistoryTracking();
      }
    });

    _controller.setActiveSnapInfo(null); // Clear snap info

    // Set isHandleInteraction directly
    if (mounted && _controller.activePointerCount == 0) {
      _controller.isHandleInteraction = false;
    }
    // Add setState to ensure the final state is rendered
    // setState(() {});
  }

  // --- Drag Update Handlers (Ensure _isDragging is set on first update) ---

  void _handleVertexDrag(int index, Offset globalPos) {
    if (!_isDragging) _isDragging = true; // Set flag on first update if missed
    _controller.isHandleInteraction = true;

    // --- Start of Restored Logic ---
    final renderBox =
        _painterKey.currentContext?.findRenderObject() as RenderBox;
    final localPos = renderBox.globalToLocal(globalPos);

    // Apply grid snapping if enabled
    Offset snappedLocalPos = localPos;
    if (_controller.gridSystem.snapToGrid) {
      final size = MediaQuery.of(Get.context!).size;
      final snappedGlobalPos =
          _controller.snappingManager.snapPoint(globalPos, size);
      snappedLocalPos = renderBox.globalToLocal(snappedGlobalPos);
      if ((snappedGlobalPos - globalPos).distance > 0.5) {
        _controller.addSnapIndicator(snappedGlobalPos);
      }
    }

    final bool preserveAspectRatio = _controller.isShiftKeyDown.value;

    final originalShapeData = shapeData.deepCopy();
    BoxConstraints modifiedConstraints = BoxConstraints(
      minWidth: 0,
      maxWidth: widget.constraints.maxWidth,
      minHeight: 0,
      maxHeight: GridConstants.getExtendedHeight(
          widget.constraints.maxHeight / GridConstants.verticalExtendFactor),
    );

    shapeData = ShapeManipulationHandlers.handleVertexDrag(
      shapeData: shapeData,
      vertexIndex: index,
      newPosition: snappedLocalPos,
      constraints: modifiedConstraints,
      curveMode: false, // Vertex handles don't control curves
      preserveAspectRatio: preserveAspectRatio,
    );

    // Handle Group Scaling (Simplified version from previous state)
    if (shapeData is GroupShapeData && originalShapeData is GroupShapeData) {
      final newGroupData = shapeData as GroupShapeData;
      final oldGroupData = originalShapeData;
      final oldWidth = oldGroupData.boundingRect.width;
      final oldHeight = oldGroupData.boundingRect.height;
      final newWidth = newGroupData.boundingRect.width;
      final newHeight = newGroupData.boundingRect.height;
      final scaleX = (oldWidth != 0) ? newWidth / oldWidth : 1.0;
      final scaleY = (oldHeight != 0) ? newHeight / oldHeight : 1.0;

      Offset scaleOrigin = oldGroupData.center;
      if (oldGroupData.vertices.length == 4) {
        final oppositeVertexIndex = (index + 2) % 4;
        // Ensure index is valid before accessing vertices
        if (oppositeVertexIndex >= 0 &&
            oppositeVertexIndex < oldGroupData.vertices.length) {
          scaleOrigin = oldGroupData.vertices[oppositeVertexIndex];
        } else {
          print(
              "Warning: Invalid opposite vertex index calculated during group scaling.");
        }
      }

      if ((scaleX - 1.0).abs() > 1e-6 || (scaleY - 1.0).abs() > 1e-6) {
        final transformedChildren = oldGroupData.transformChildShapes(
          scaleX: scaleX,
          scaleY: scaleY,
          scaleOrigin: scaleOrigin,
        );
        shapeData = newGroupData.copyWithChildren(
          childShapes: transformedChildren,
        );
      }
    }

    // Apply mirror mode constraints
    if (_controller.isMirrorModeActive.value && widget.key != null) {
      final constrainedData =
          _controller.constrainShapePosition(widget.key!, shapeData);
      if (constrainedData != null) {
        shapeData = constrainedData;
      }
    }

    _saveStateWithoutRebuild(); // Save state without full rebuild

    // Update the painter with new shape data
    _updatePainter();

    // Schedule a UI update after the current frame is complete
    // This avoids the "widget tree locked" error while allowing smooth updates
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          // The state was already updated above, this just triggers a rebuild
        });
      }
    });

    // Also mark the current render object for repaint for immediate feedback
    _painterKey.currentContext?.findRenderObject()?.markNeedsPaint();
    // --- End of Restored Logic ---
  }

  void _handleEdgeDrag(int edgeIndex, Offset globalPos) {
    if (!_isDragging) _isDragging = true; // Set flag on first update if missed
    _controller.isHandleInteraction = true;
    _isEdgeDragging = true;
    _activeDragEdgeIndex = edgeIndex;

    // --- CRITICAL FIX: Use proper coordinate transformation for edge dragging ---
    final renderBox =
        _painterKey.currentContext?.findRenderObject() as RenderBox;
    final localPos = renderBox.globalToLocal(globalPos);

    // CRITICAL FIX: Force grid system synchronization before coordinate conversion
    _controller.ensureGridSystemSync();

    // CRITICAL FIX: Use the exact same transformation matrix as InteractiveViewer
    // This ensures curve control points work correctly at all zoom levels
    final size = MediaQuery.of(Get.context!).size;
    final gridPos = _controller.screenToGridPoint(localPos, size);

    Offset snappedGridPos = gridPos;
    if (_controller.gridSystem.snapToGrid) {
      final snappedGlobalPos =
          _controller.snappingManager.snapPoint(globalPos, size);
      final snappedLocalPos = renderBox.globalToLocal(snappedGlobalPos);
      snappedGridPos = _controller.screenToGridPoint(snappedLocalPos, size);
      if ((snappedGlobalPos - globalPos).distance > 0.5) {
        _controller.addSnapIndicator(snappedGlobalPos);
      }
    }

    setState(() {
      BoxConstraints modifiedConstraints = BoxConstraints(
        minWidth: 0,
        maxWidth: widget.constraints.maxWidth,
        minHeight: 0,
        maxHeight: GridConstants.getExtendedHeight(
            widget.constraints.maxHeight / GridConstants.verticalExtendFactor),
      );

      // CRITICAL FIX: Use the properly transformed grid coordinates for edge dragging
      shapeData = ShapeManipulationHandlers.handleEdgeDrag(
        shapeData: shapeData,
        edgeIndex: edgeIndex,
        dragPosition: snappedGridPos,
        constraints: modifiedConstraints,
        curveMode: true, // Edge handles always control curves now
      );

      _updateCurrentControlPoint(edgeIndex, snappedGridPos);

      // Apply mirror mode constraints
      if (_controller.isMirrorModeActive.value && widget.key != null) {
        final constrainedData =
            _controller.constrainShapePosition(widget.key!, shapeData);
        if (constrainedData != null) {
          shapeData = constrainedData;
        }
      }

      _saveState();
    });
    // --- End of Restored Logic ---
  }

  // Called when the entire shape is dragged
  void _handleShapeDrag(DragUpdateDetails details) {
    // Only proceed if the shape is selected
    if (!widget.selected) return;

    // Don't use setState for every drag update - directly modify properties
    // We'll only rebuild at the end of the drag operation

    // Calculate accumulated delta to reduce multiple small updates
    _accumulatedDelta += details.delta;

    // Only process if delta is significant or if throttle timer is inactive
    if (_accumulatedDelta.distance >= 1.0 || !_isDragThrottleActive) {
      try {
        // Create modified constraints to allow shapes to be dragged far down
        // but maintain top boundary constraint
        BoxConstraints modifiedConstraints = BoxConstraints(
          minWidth: 0,
          maxWidth: widget.constraints.maxWidth,
          minHeight: 0,
          // Set maxHeight using the grid constants
          maxHeight: GridConstants.getExtendedHeight(
              widget.constraints.maxHeight /
                  GridConstants.verticalExtendFactor),
        );

        // --- MIRROR MODE ENHANCEMENT ---
        // In mirror mode, completely block horizontal movement
        if (_controller.isMirrorModeActive.value && widget.key != null) {
          // Zero out the horizontal component of the delta
          // This ensures shapes remain fixed to the center line
          _accumulatedDelta = Offset(0, _accumulatedDelta.dy);
        }
        // --- END MIRROR MODE ENHANCEMENT ---

        // Get updated shape data from the handler
        final handlerResult = ShapeManipulationHandlers.handleShapeDrag(
          shapeData: shapeData, // Pass the current state
          delta: _accumulatedDelta,
          constraints: modifiedConstraints,
          allShapeStates: _controller.shapeStates,
          currentShapeKey: widget.key!,
          // Add missing parameters for snapping functionality
          globalPointerPosition: details.globalPosition,
          getSnapInfo: (
              {required ShapeData originalShapeData,
              required ShapeData potentialShapeData,
              required Offset globalPointerPosition,
              required Key shapeKey}) {
            return _controller.snappingManager.calculateDragSnap(
              originalShapeData: originalShapeData,
              potentialShapeData: potentialShapeData,
              globalPointerPosition: globalPointerPosition,
              shapeKey: shapeKey,
              allShapeStates: _controller.shapeStates,
              constraints: modifiedConstraints,
              isCenterSnapEnabled: _controller.snapToCenter.value,
              // Professional snap parameters for smooth, predictable behavior
              shapeSnapThreshold: 12.0, // Reduced for less aggressive snapping
              centerSnapThreshold:
                  16.0, // Slightly higher for center line priority
              gridSnapThreshold: 8.0, // Reduced for lighter grid snapping
              isShapeSnapEnabled: true, // Enable shape snapping
            );
          },
        );

        final handlerResultData = handlerResult.shapeData;
        final snapInfo = handlerResult.snapInfo;

        // Update the controller's active snap info for visualizing snap lines
        if (snapInfo != null) {
          _controller.setActiveSnapInfo(snapInfo);

          // Add haptic feedback for tactile feel on snaps
          HapticFeedback.mediumImpact();
        }

        // --- Visibility Check ---
        final screenSize = Size(Get.width, Get.height);
        bool isWithinScreen = false;
        final margin = -screenSize.width * 0.5;
        for (final vertex in handlerResultData.vertices) {
          // Check using handler result vertices
          if (vertex.dx > margin &&
              vertex.dx < screenSize.width - margin &&
              vertex.dy > margin &&
              vertex.dy < screenSize.height - margin) {
            isWithinScreen = true;
            break;
          }
        }
        if (!isWithinScreen) {
          _accumulatedDelta = Offset.zero;
          return; // Don't update if shape moved too far off-screen
        }
        // --- End Visibility Check ---

        // Reset accumulated delta only after using it
        _accumulatedDelta = Offset.zero;

        // Start with the result from the handler
        ShapeData finalShapeData = handlerResultData;

        // Apply group transformation if necessary
        if (finalShapeData is GroupShapeData && shapeData is GroupShapeData) {
          // Note: using the current state's shapeData for oldGroupData
          final groupData = shapeData as GroupShapeData;
          final translation = finalShapeData.center - groupData.center;
          if (translation != Offset.zero) {
            // Use efficient translation, preserving bounds from handlerResultData
            finalShapeData = _efficientGroupTranslation(
                finalShapeData, groupData, translation);
          }
        }

        // Apply mirror mode constraints if needed
        if (_controller.isMirrorModeActive.value && widget.key != null) {
          final constrainedData =
              _controller.constrainShapePosition(widget.key!, finalShapeData);
          if (constrainedData != null) {
            // Use the constrained data
            finalShapeData = constrainedData;
          }
        }

        // *** Assign the final calculated data to the state variable ***
        // This happens right before saving and setting state.
        shapeData = finalShapeData;

        // Explicitly update the painter instance *before* setState
        _updatePainter();

        // Save state but don't trigger unnecessary widget rebuilds
        _saveStateWithoutRebuild();

        // Trigger the rebuild using the updated shapeData
        setState(() {});

        // Set throttle timer to limit update frequency
        _setDragThrottle();
      } catch (e, stackTrace) {
        // Handle any errors during shape manipulation without crashing the app
        debugPrint('Error during shape drag: $e');
        debugPrint('Stack trace: $stackTrace');

        // Try to recover from error by resetting accumulated delta
        _accumulatedDelta = Offset.zero;

        // If we have a reference to the controller, get the last valid state
        if (widget.key != null) {
          final savedState = _controller.getShapeState(widget.key);
          if (savedState != null) {
            // Restore from saved state
            shapeData = savedState;
            _painterKey.currentContext?.findRenderObject()?.markNeedsPaint();
          }
        }
      }
    }
  }

// Optimized version that avoids expensive operations during drag
  ShapeData _efficientGroupTranslation(GroupShapeData updatedShapeData,
      GroupShapeData oldGroupData, Offset translation) {
    // Quick path: if we're just translating, we can directly offset all vertices
    // without doing expensive matrix calculations
    final List<ShapeData> updatedChildren = [];

    for (final childShape in oldGroupData.childShapes) {
      // For simple translation, just offset each vertex and the center
      final newVertices =
          childShape.vertices.map((v) => v + translation).toList();

      final newCenter = childShape.center + translation;

      // Create bounds from vertices to avoid expensive calculations
      final newChildShape = childShape.copyWith(
        vertices: newVertices,
        center: newCenter,
        boundingRect: Rect.fromLTRB(
          childShape.boundingRect.left + translation.dx,
          childShape.boundingRect.top + translation.dy,
          childShape.boundingRect.right + translation.dx,
          childShape.boundingRect.bottom + translation.dy,
        ),
      );

      updatedChildren.add(newChildShape);
    }

    return updatedShapeData.copyWithChildren(
      childShapes: updatedChildren,
    );
  }

  void _setDragThrottle() {
    if (!_isDragThrottleActive) {
      _isDragThrottleActive = true;

      // Limit updates to 60fps (≈16ms) for smooth experience
      Future.delayed(const Duration(milliseconds: 16), () {
        _isDragThrottleActive = false;
      });
    }
  }

// Save state without triggering unnecessary rebuilds
  void _saveStateWithoutRebuild() {
    if (widget.key != null) {
      // Access controller's state map directly without calling update
      _controller.shapeStates[widget.key!] = shapeData;

      // When in mirror mode, ensure the mirrored shape is updated in real-time
      if (_controller.isMirrorModeActive.value) {
        // Check if this is an original shape (not a mirrored one)
        if (!_controller.isShapeMirrored(widget.key!)) {
          // Update the mirrored shape immediately
          _controller.handleShapeTransformation(widget.key!);
        }
      }
    }
  }

  // Save state safely from gesture handlers to avoid widget tree lock issues
  void _saveStateDeferred() {
    if (widget.key != null) {
      try {
        // Make sure we save a deep copy to ensure complete state isolation
        final stateCopy = shapeData.deepCopy();

        // Get the controller instance
        final controller = Get.find<ShapeEditorController>();

        // Defer reactive state updates to avoid widget tree lock issues
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            // Let the controller handle converting to grid coordinates if needed
            controller.saveShapeState(widget.key!, stateCopy);
          }
        });
      } catch (e, stackTrace) {
        debugPrint('Error saving shape state: $e');
        debugPrint('Stack trace: $stackTrace');
      }
    }
  }

  // Save the current state to the controller
  // This keeps the controller in sync with local changes
  void _saveState() {
    if (widget.key != null) {
      try {
        // Make sure we save a deep copy to ensure complete state isolation
        final stateCopy = shapeData.deepCopy();

        // Get the controller instance
        final controller = Get.find<ShapeEditorController>();

        // Let the controller handle converting to grid coordinates if needed
        controller.saveShapeState(widget.key!, stateCopy);
      } catch (e, stackTrace) {
        debugPrint('Error saving shape state: $e');
        debugPrint('Stack trace: $stackTrace');
      }
    }
  }

  // Toggle between normal mode and curve editing mode - now deprecated as edge handles always curve
  void _toggleCurveMode() {
    // This method is kept for backward compatibility but no longer changes behavior
    // Edge handles now always control curves, and vertex handles always control resizing
    return;
  }

  /// Recalculate the bounding box for a group shape to ensure integrity
  void _recalculateGroupBoundingBox() {
    if (shapeData is! GroupShapeData) return;

    final groupData = shapeData as GroupShapeData;

    // Use the GroupBoundsData from accurate calculation
    final boundsData = GeometryUtils.calculateAccurateBoundingRect(groupData);

    if (boundsData is GroupBoundsData) {
      // Create new GroupShapeData with accurate bounds and vertices
      shapeData = groupData.copyWithChildren(
        vertices: boundsData.vertices,
        boundingRect: boundsData.bounds,
        center: boundsData.center,
      );
    } else {
      // Fallback to original calculation if accurate method returns just a Rect
      final childShapes = groupData.childShapes;
      if (childShapes.isEmpty) return;

      // Calculate the bounding rectangle that encompasses all shapes
      double minX = double.infinity;
      double minY = double.infinity;
      double maxX = -double.infinity;
      double maxY = -double.infinity;

      // For each shape, calculate its bounding box accounting for rotation
      for (final shape in childShapes) {
        // Apply the shape's rotation to its vertices
        final matrix = Matrix4.identity()
          ..translate(shape.center.dx, shape.center.dy)
          ..rotateZ(shape.rotation)
          ..translate(-shape.center.dx, -shape.center.dy);

        for (final vertex in shape.vertices) {
          final transformed = MatrixUtils.transformPoint(matrix, vertex);
          minX = math.min(minX, transformed.dx);
          minY = math.min(minY, transformed.dy);
          maxX = math.max(maxX, transformed.dx);
          maxY = math.max(maxY, transformed.dy);
        }
      }

      // Create a new rectangle from the extreme points
      final boundingRect = Rect.fromLTRB(minX, minY, maxX, maxY);
      final center = Offset(
        boundingRect.left + boundingRect.width / 2,
        boundingRect.top + boundingRect.height / 2,
      );

      // Create new vertices for the bounding box
      final vertices = [
        Offset(minX, minY), // Top-left
        Offset(maxX, minY), // Top-right
        Offset(maxX, maxY), // Bottom-right
        Offset(minX, maxY), // Bottom-left
      ];

      // Update the shape data with corrected bounding box
      shapeData = groupData.copyWithChildren(
        vertices: vertices,
        boundingRect: boundingRect,
        center: center,
        childShapes: childShapes, // Keep same child shapes
      );
    }
  }

  Path getShapePath() {
    if (shapeData.type == ShapeType.group) {
      return (_painter as GroupShapePainter).getPath();
    }
    return (_painter as ShapePainter).getPath();
  }

  // Update the ShapeData with new vertices, center, and bounding box.
  // This function is called after various transformations (drag, scale, rotate, curve adjust).
  void _updateShapeData({
    List<Offset>? newVertices,
    Offset? newCenter,
    double? newRotation,
    Map<int, Offset>? newCurveControls,
    bool recalculateBounds = true, // Flag to control bound recalculation
  }) {
    final Map<int, Offset> updatedCurveControls =
        newCurveControls ?? shapeData.curveControls;
    final List<Offset> updatedVertices = newVertices ?? shapeData.vertices;

    if (updatedVertices.isEmpty) return; // Don't update if vertices are empty

    final Offset calculatedCenter;
    // If vertices changed, calculate the center from the new vertices' bounds
    if (newVertices != null) {
      final tempShapeData = ShapeData(
        type: shapeData.type,
        vertices: updatedVertices,
        center: shapeData.center,
        rotation: shapeData.rotation,
        curveControls: updatedCurveControls,
        boundingRect: shapeData.boundingRect,
      );
      final tempBounds =
          GeometryUtils.calculateAccurateBoundingRect(tempShapeData);
      if (tempBounds is GroupBoundsData) {
        calculatedCenter = tempBounds.center;
      } else {
        calculatedCenter = tempBounds;
      }
    } else {
      // Otherwise, use the provided new center or the existing one
      calculatedCenter = newCenter ?? shapeData.center;
    }

    final double calculatedRotation = newRotation ?? shapeData.rotation;

    Rect calculatedBounds = shapeData.boundingRect; // Keep old bounds initially

    // --- Create a temporary ShapeData with the updated values for accurate bounds calculation ---
    final intermediateShapeData = shapeData.copyWith(
      vertices: updatedVertices,
      center: calculatedCenter,
      rotation: calculatedRotation,
      curveControls: updatedCurveControls,
      // IMPORTANT: Don't pass the old boundingRect here, let the utility calculate it
    );
    // ---------------------------------------------------------------------------------------

    bool needsRecalculation = recalculateBounds ||
        (newVertices != null || newCenter != null || newRotation != null);

    if (needsRecalculation) {
      // --- Use the centralized accurate bounding box calculation ---
      final boundsResult =
          GeometryUtils.calculateAccurateBoundingRect(intermediateShapeData);
      if (boundsResult is GroupBoundsData) {
        calculatedBounds = boundsResult.bounds;
      } else {
        calculatedBounds = boundsResult;
      }
      // --- End accurate calculation ---
    }

    // --- Handle Group Shape Updates ---
    // If the parent shape is a group, we need to update its structure
    // based on the changes applied (e.g., scaling, rotation).
    if (shapeData is GroupShapeData) {
      final oldGroupData = shapeData as GroupShapeData;

      // Determine the type of transformation applied
      Offset translationDelta = Offset.zero;
      double scaleX = 1.0, scaleY = 1.0;
      double rotationDelta = 0.0;
      Offset scaleOrigin = oldGroupData.center; // Default scale origin

      if (newCenter != null && newCenter != oldGroupData.center) {
        translationDelta = newCenter - oldGroupData.center;
      }
      if (newRotation != null && newRotation != oldGroupData.rotation) {
        rotationDelta = newRotation - oldGroupData.rotation;
      }

      // Infer scaling if vertices changed significantly without just rotation/translation
      if (newVertices != null &&
          newVertices != oldGroupData.vertices &&
          rotationDelta == 0.0 &&
          translationDelta == Offset.zero) {
        // More complex scaling logic might be needed here if non-uniform scaling happened
        // For simplicity, assume uniform scaling if bounding box size changed
        final oldWidth = oldGroupData.boundingRect.width;
        final oldHeight = oldGroupData.boundingRect.height;
        final newWidth = calculatedBounds.width;
        final newHeight = calculatedBounds.height;

        if (oldWidth > 1e-6) scaleX = newWidth / oldWidth;
        if (oldHeight > 1e-6) scaleY = newHeight / oldHeight;

        // Note: Determining the correct scaleOrigin after arbitrary vertex edits
        // can be complex. Using the center is a common approximation.
        scaleOrigin = calculatedCenter;
      }

      // Apply transformations to child shapes if necessary
      List<ShapeData> updatedChildren = oldGroupData.childShapes;
      if (translationDelta != Offset.zero ||
          rotationDelta != 0.0 ||
          scaleX != 1.0 ||
          scaleY != 1.0) {
        updatedChildren = oldGroupData.transformChildShapes(
          translation: translationDelta,
          rotationDelta: rotationDelta,
          scaleX: scaleX,
          scaleY: scaleY,
          scaleOrigin: scaleOrigin,
        );
      }

      // Update the GroupShapeData
      shapeData = (shapeData as GroupShapeData).copyWith(
        vertices: updatedVertices,
        center: calculatedCenter,
        rotation: calculatedRotation,
        curveControls: updatedCurveControls,
        boundingRect: calculatedBounds,
        // childShapes are updated separately below if needed
      );
      // Explicitly update children using copyWithChildren if they were transformed
      if (updatedChildren != oldGroupData.childShapes) {
        shapeData = (shapeData as GroupShapeData)
            .copyWithChildren(childShapes: updatedChildren);
      }
    } else {
      // For regular shapes, just update the data
      shapeData = shapeData.copyWith(
        vertices: updatedVertices,
        center: calculatedCenter,
        rotation: calculatedRotation,
        curveControls: updatedCurveControls,
        boundingRect: calculatedBounds,
      );
    }

    // --- End Group Shape Updates ---

    _saveStateDeferred(); // Save the updated state to the controller safely
    _updatePainter(); // Update the painter to reflect changes visually
    if (mounted) {
      // setState(() {}); // Trigger rebuild if mounted
    }
  }

  // Update the current control point for visualization
  void _updateCurrentControlPoint(int edgeIndex, Offset dragPosition) {
    if (_activeDragEdgeIndex != edgeIndex) return;

    final points = shapeData.vertices;
    if (edgeIndex < 0 || edgeIndex >= points.length) return; // Bounds check

    final startPoint = points[edgeIndex];
    final endPoint = points[(edgeIndex + 1) % points.length];
    final edgeCenter = (startPoint + endPoint) / 2;

    // Use the same calculations as in ShapeManipulationHandlers._handleCurveEdgeDrag
    // to determine the control point position
    final matrix = GeometryUtils.createRotationMatrix(
        shapeData.center, -shapeData.rotation);

    // Use the *dragPosition* (snappedLocalPos) passed to _handleEdgeDrag
    final transformedDragPos = MatrixUtils.transformPoint(matrix, dragPosition);
    final transformedEdgeCenter =
        MatrixUtils.transformPoint(matrix, edgeCenter);

    // Calculate the control offset in the un-rotated space
    final desiredOffset = transformedDragPos - transformedEdgeCenter;

    // Store the actual control point location (relative to unrotated edge center)
    // We need to re-apply rotation to get the screen position
    final calculatedControlPoint = edgeCenter + desiredOffset;

    if (shapeData.rotation != 0) {
      final rotationMatrix = Matrix4.identity()
        ..translate(shapeData.center.dx, shapeData.center.dy)
        ..rotateZ(shapeData.rotation)
        ..translate(-shapeData.center.dx, -shapeData.center.dy);
      _currentControlPoint =
          MatrixUtils.transformPoint(rotationMatrix, calculatedControlPoint);
    } else {
      _currentControlPoint = calculatedControlPoint;
    }
  }
}
