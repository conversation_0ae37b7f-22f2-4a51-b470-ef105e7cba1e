import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/zones_editor_controller.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/zone_creation_controller.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/shape_editor_controller.dart';
import 'package:xoxknit/app/modules/new_item/controllers/new_item_wizard_controller.dart';

// Reuse existing widgets from shape editor for consistent UI
import '../widgets/zoom_indicator.dart';
import '../widgets/zoomable_board.dart';
import '../widgets/professional_grid.dart';
import '../widgets/zones_expandable_toolbar.dart';
import '../widgets/help_button.dart';
import '../widgets/zones_visualization_widget.dart';
import '../widgets/zone_creation_widget.dart';
import '../widgets/zone_context_menu.dart';

class UnifiedZonesEditorView extends StatefulWidget {
  final bool hideAppBar;

  const UnifiedZonesEditorView({
    super.key,
    this.hideAppBar = false,
  });

  @override
  State<UnifiedZonesEditorView> createState() => _UnifiedZonesEditorViewState();
}

class _UnifiedZonesEditorViewState extends State<UnifiedZonesEditorView> {
  late final ZonesEditorController _zonesController;
  late final ShapeEditorController _shapeController;

  // Define a unique tag for this instance's controllers
  final String _controllerTag =
      'zones_editor_${DateTime.now().millisecondsSinceEpoch}';

  // Reference to the toolbar for collapsing rows when clicking elsewhere
  final GlobalKey<ZonesExpandableToolbarState> toolbarKey =
      GlobalKey<ZonesExpandableToolbarState>();

  @override
  void initState() {
    super.initState();

    // Get the existing shape controller
    _shapeController = Get.find<ShapeEditorController>();

    // Register the zones editor controller
    _zonesController = Get.put(ZonesEditorController(), tag: _controllerTag);

    // Register the zone creation controller with a reference to the editor controller
    final zoneCreationController = Get.put(
        ZoneCreationController(editorController: _zonesController),
        tag: _controllerTag);

    // Set the zone creation controller reference in the zones editor controller
    _zonesController.setZoneCreationController(zoneCreationController);

    debugPrint('Zones controller initialized: ${_zonesController.hashCode}');
  }

  @override
  void dispose() {
    // Clean up the controllers in reverse order of dependency
    Get.delete<ZoneCreationController>(tag: _controllerTag);
    Get.delete<ZonesEditorController>(tag: _controllerTag);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final wizardController = Get.find<NewItemWizardController>();

    return Scaffold(
      appBar: widget.hideAppBar
          ? null
          : AppBar(title: Text('interactiveKnitting_zoneEditor'.tr)),
      body: Focus(
        autofocus: true,
        onKeyEvent: (node, event) {
          // Handle keyboard shortcuts for undo/redo
          if (event is KeyDownEvent) {
            if (event.logicalKey == LogicalKeyboardKey.keyZ &&
                HardwareKeyboard.instance.isControlPressed) {
              if (HardwareKeyboard.instance.isShiftPressed) {
                // Ctrl+Shift+Z = Redo
                if (_zonesController.canRedoZones.value) {
                  _zonesController.redoZoneOperation();
                  return KeyEventResult.handled;
                }
              } else {
                // Ctrl+Z = Undo
                if (_zonesController.canUndoZones.value) {
                  _zonesController.undoZoneOperation();
                  return KeyEventResult.handled;
                }
              }
            } else if (event.logicalKey == LogicalKeyboardKey.keyY &&
                HardwareKeyboard.instance.isControlPressed) {
              // Ctrl+Y = Redo (alternative)
              if (_zonesController.canRedoZones.value) {
                _zonesController.redoZoneOperation();
                return KeyEventResult.handled;
              }
            }
          }
          return KeyEventResult.ignored;
        },
        child: LayoutBuilder(
          builder: (context, constraints) {
            // Handle screen size changes when the view builds/rebuilds
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (_shapeController.gridCoordinateConverter.screenSize !=
                  Size(Get.width, Get.height)) {
                _shapeController.handleScreenSizeChange();
              }
            });

            return Stack(
              children: [
                // Zoomable drawing board with zones visualization
                ZoomableBoard(
                  controller:
                      _shapeController, // Reuse shape controller for zoom/pan
                  child: Stack(
                    children: [
                      // Professional grid with axis labels
                      ProfessionalGridWidget(
                        gridSystem: _shapeController.gridSystem,
                        activeSnapInfo: _shapeController.activeSnapInfo.value,
                        shapeStates: const {}, // No shapes to show in zone editor
                      ),

                      // Zones visualization or creation
                      _buildZonesCanvas(),
                    ],
                  ),
                ),

                // Position ZonesExpandableToolbar on the left side (same position as shape editor)
                Positioned(
                  left: 16,
                  bottom: 80, // Same margin as shape editor
                  child: ZonesExpandableToolbar(
                    key: toolbarKey,
                    controller: _zonesController,
                  ),
                ),

                // Zoom indicator (reuse from shape editor)
                ZoomIndicator(controller: _shapeController),

                // Help button at top right corner (same as shape editor)
                Positioned(
                  right: 16,
                  top: 0,
                  child: const HelpButton(isAppBar: false),
                ),

                // Navigation buttons for wizard flow
                Obx(() {
                  final bool isInWizardFlow =
                      Get.isRegistered<NewItemWizardController>() &&
                          wizardController.currentStep.value == 3;

                  if (!isInWizardFlow) {
                    return Positioned(
                      right: 16,
                      bottom: 16,
                      child: FloatingActionButton(
                        heroTag: 'back_to_shapes_standalone',
                        onPressed: () => Get.rootDelegate.popRoute(),
                        backgroundColor: Colors.grey,
                        tooltip: 'interactiveKnitting_backToShapeEditor'.tr,
                        child: const Icon(Icons.arrow_back),
                      ),
                    );
                  }
                  // Show wizard navigation if in wizard flow
                  return const SizedBox.shrink();
                }),

                // Zone context menu
                Obx(() => _zonesController.isZoneContextMenuVisible.value
                    ? ZoneContextMenu(
                        controller: _zonesController,
                        onDismiss: () => _zonesController.hideZoneContextMenu(),
                      )
                    : const SizedBox.shrink()),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildZonesCanvas() {
    // Check if ShapeEditorController is available and has instructions
    if (!Get.isRegistered<ShapeEditorController>()) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    // Check if instructions are available
    if (_shapeController
        .knittingInstructionsManager.currentInstructions.value.isEmpty) {
      // Generate instructions if not available
      _shapeController.knittingInstructionsManager.generateInstructions();
    }

    final instructions =
        _shapeController.knittingInstructionsManager.currentInstructions.value;
    if (instructions.isEmpty) {
      return Center(child: Text('interactiveKnitting_noPatternAvailable'.tr));
    }

    // Calculate aspect ratio based on the knitting gauge
    double aspectRatio = 0.75; // Default fallback
    final wizardController = Get.find<NewItemWizardController>();
    final stitchesPerCm = wizardController.newItem.value.stitchesPerCm;
    final rowsPerCm = wizardController.newItem.value.rowsPerCm;

    if (rowsPerCm != null &&
        rowsPerCm > 0 &&
        stitchesPerCm != null &&
        stitchesPerCm > 0) {
      aspectRatio = stitchesPerCm / rowsPerCm;
    }

    // Calculate the pattern's display ratio
    final patternWidth = instructions.isNotEmpty ? instructions[0].length : 0;
    final patternHeight = instructions.length;
    final double patternDisplayRatio = patternWidth > 0 && patternHeight > 0
        ? (patternWidth / patternHeight) * aspectRatio
        : 1.0;

    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate optimal dimensions to fill available space
        // The constraints here are from the ZoomableBoard's child Stack
        final availableWidth = constraints.maxWidth;
        final availableHeight = constraints.maxHeight;

        final widthLimited =
            availableWidth / patternDisplayRatio < availableHeight;

        double containerWidth;
        double containerHeight;

        if (widthLimited) {
          containerWidth = availableWidth;
          containerHeight = availableWidth / patternDisplayRatio;
        } else {
          containerHeight = availableHeight;
          containerWidth = availableHeight * patternDisplayRatio;
        }

        // Ensure the container doesn't exceed the available space,
        // as ZoomableBoard handles the overall sizing and scrolling.
        containerWidth =
            containerWidth.isFinite ? containerWidth : availableWidth;
        containerHeight =
            containerHeight.isFinite ? containerHeight : availableHeight;

        return Padding(
          padding: const EdgeInsets.all(8.0),
          child: Container(
            width: containerWidth,
            height: containerHeight,
            child: Obx(() {
              final isCreatingNewZone =
                  _zonesController.isCreatingNewZone.value;
              final isEditingMode = _zonesController.isEditingMode.value;
              final selectedZoneIndex =
                  _zonesController.selectedZoneIndex.value;

              debugPrint(
                  'UnifiedZonesEditor: Building with isCreatingNewZone=$isCreatingNewZone, isEditingMode=$isEditingMode, selectedZoneIndex=$selectedZoneIndex');

              return isCreatingNewZone
                  ? ZoneCreationWidget(
                      key: ValueKey(
                          'zone_creation_${DateTime.now().millisecondsSinceEpoch}'),
                      fullPattern: instructions,
                      selectionGrid: _zonesController.newZoneSelection,
                      aspectRatio: aspectRatio,
                      onSelectionChanged: _zonesController.updateSelectionCell,
                      controller:
                          Get.find<ZoneCreationController>(tag: _controllerTag),
                      existingZones: _shapeController
                          .knittingInstructionsManager.knittingZones.value,
                      allowZoomGestures: true,
                    )
                  : ZonesVisualizationWidget(
                      fullPattern: instructions,
                      zones: _shapeController
                          .knittingInstructionsManager.knittingZones.value,
                      selectedZoneIndex: selectedZoneIndex,
                      aspectRatio: aspectRatio,
                      isEditingMode: isEditingMode,
                      onZoneTap: (index) {
                        debugPrint(
                            'UnifiedZonesEditor: Zone tap callback called with index $index, isEditingMode=$isEditingMode');
                        if (isEditingMode) {
                          debugPrint(
                              'UnifiedZonesEditor: Calling selectZone($index)');
                          _zonesController.selectZone(index);
                        } else {
                          debugPrint(
                              'UnifiedZonesEditor: Zone tap ignored - not in editing mode');
                        }
                      },
                      onZoneTapWithPosition: isEditingMode
                          ? (tapPosition, canvasSize) {
                              debugPrint(
                                  'UnifiedZonesEditor: Zone tap with position callback called: $tapPosition, canvasSize: $canvasSize');
                              _zonesController.handleZoneTap(
                                  tapPosition, canvasSize);
                            }
                          : null,
                      onZoneLongPress: (tapPosition, canvasSize) {
                        debugPrint(
                            'UnifiedZonesEditor: Zone long press callback called: $tapPosition');

                        // Convert local position to global position for context menu
                        final RenderBox? renderBox =
                            context.findRenderObject() as RenderBox?;
                        if (renderBox != null) {
                          final globalPosition =
                              renderBox.localToGlobal(tapPosition);
                          _zonesController.showZoneContextMenu(globalPosition);
                        }
                      },
                      onZoneBoundaryEdit: isEditingMode
                          ? (zoneIndex, edge, position) {
                              debugPrint(
                                  'UnifiedZonesEditor: Zone boundary edit called: zone=$zoneIndex, edge=$edge, position=$position');
                              return _zonesController.updateZoneBoundary(
                                  zoneIndex,
                                  edge,
                                  position -
                                      (edge == 'left' || edge == 'right'
                                          ? _zonesController
                                              .shapeController
                                              .knittingInstructionsManager
                                              .knittingZones
                                              .value[zoneIndex]
                                              .startNeedle
                                          : _zonesController
                                              .shapeController
                                              .knittingInstructionsManager
                                              .knittingZones
                                              .value[zoneIndex]
                                              .startRow),
                                  edge == 'left' || edge == 'right');
                            }
                          : null,
                      controller: _zonesController,
                    );
            }),
          ),
        );
      },
    );
  }
}
