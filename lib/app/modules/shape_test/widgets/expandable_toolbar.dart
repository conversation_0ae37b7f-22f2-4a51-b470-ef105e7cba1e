import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/shape_editor_controller.dart';
import '../models/shape_data.dart';
import 'shape_icons.dart';

// Enum for toolbar row types
enum ToolbarRowType { shapes, transforms, grouping, utilities }

class ExpandableToolbar extends StatefulWidget {
  final ShapeEditorController controller;

  const ExpandableToolbar({super.key, required this.controller});

  @override
  State<ExpandableToolbar> createState() => ExpandableToolbarState();
}

class ExpandableToolbarState extends State<ExpandableToolbar>
    with TickerProviderStateMixin {
  late AnimationController _mainAnimationController;
  late Animation<double> _expandAnimation;

  // Tool row expansion states and animations
  final Map<ToolbarRowType, bool> _rowExpandedStates = {};
  final Map<ToolbarRowType, AnimationController> _rowAnimControllers = {};
  final Map<ToolbarRowType, Animation<double>> _rowAnimations = {};

  // Track last used tool in each category
  final Map<ToolbarRowType, dynamic> _lastUsedTool = {};

  @override
  void initState() {
    super.initState();
    // Main toolbar expansion animation
    _mainAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _expandAnimation = CurvedAnimation(
      parent: _mainAnimationController,
      curve: Curves.easeInOut,
    );

    // Initialize from controller's state
    if (widget.controller.isToolbarExpanded.value) {
      _mainAnimationController.value = 1.0;
    } else {
      _mainAnimationController.value = 0.0;
    }

    // Initialize row expansion states and animations
    for (var rowType in ToolbarRowType.values) {
      _rowExpandedStates[rowType] = false;
      _rowAnimControllers[rowType] = AnimationController(
        vsync: this,
        duration: const Duration(milliseconds: 250),
      );
      _rowAnimations[rowType] = CurvedAnimation(
        parent: _rowAnimControllers[rowType]!,
        curve: Curves.easeInOut,
      );
    }

    // Set initial last used tool for shapes category
    if (widget.controller.lastAddedShapeType != null) {
      _lastUsedTool[ToolbarRowType.shapes] =
          widget.controller.lastAddedShapeType;
    }
  }

  @override
  void dispose() {
    _mainAnimationController.dispose();
    for (var controller in _rowAnimControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  void _toggleRowExpanded(ToolbarRowType rowType) {
    // Check if the row is enabled before allowing expansion
    if (!_isRowEnabled(rowType)) {
      return; // Don't allow expanding disabled rows
    }

    setState(() {
      // Close all other rows first
      for (var type in ToolbarRowType.values) {
        if (type != rowType) {
          _rowExpandedStates[type] = false;
          _rowAnimControllers[type]?.reverse();
        }
      }

      // Toggle the selected row
      _rowExpandedStates[rowType] = !(_rowExpandedStates[rowType] ?? false);
      if (_rowExpandedStates[rowType]!) {
        _rowAnimControllers[rowType]?.forward();
      } else {
        _rowAnimControllers[rowType]?.reverse();
      }
    });
  }

  // Check if a row is enabled based on its type
  bool _isRowEnabled(ToolbarRowType rowType) {
    switch (rowType) {
      case ToolbarRowType.shapes:
        return true; // Always enabled
      case ToolbarRowType.transforms:
        return widget.controller.selectedIndices.isNotEmpty;
      case ToolbarRowType.grouping:
        return widget.controller.canGroupSelectedShapes() ||
            widget.controller.canUngroupSelectedShape();
      case ToolbarRowType.utilities:
        // At least one utility feature is available

        widget.controller.panOffset.value != Offset.zero;

        // Always enable utilities since grid snapping is always available
        return true;
      default:
        return true;
    }
  }

  void _setLastUsedTool(ToolbarRowType rowType, dynamic toolIdentifier) {
    setState(() {
      _lastUsedTool[rowType] = toolIdentifier;

      // Automatically collapse the row after setting the tool
      _rowExpandedStates[rowType] = false;
      _rowAnimControllers[rowType]?.reverse();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // The toggle button is always visible
        // FloatingActionButton(
        //   heroTag: 'toggle_toolbar',
        //   mini: true,
        //   onPressed: _toggleMainExpanded,
        //   backgroundColor: Colors.grey[800],
        //   child: Obx(() => Icon(
        //         widget.controller.isToolbarExpanded.value
        //             ? Icons.keyboard_arrow_left
        //             : Icons.keyboard_arrow_right,
        //         color: Colors.white,
        //       )),
        // ),
        // const SizedBox(height: 6),

        // The expandable content
        SizeTransition(
          sizeFactor: _expandAnimation,
          axis: Axis.vertical,
          child: FadeTransition(
            opacity: _expandAnimation,
            child: Container(
              constraints:
                  BoxConstraints(maxWidth: MediaQuery.of(context).size.width),
              child: GestureDetector(
                onTap: collapseAllRows,
                behavior: HitTestBehavior.translucent,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 1. SHAPE ADD TOOLS ROW
                    _buildShapeAddRow(),

                    const SizedBox(height: 4),

                    // 2. UNDO/REDO ROW (always visible)
                    _buildUndoRedoRow(),

                    const SizedBox(height: 4),

                    // 3. DELETE ROW (always visible)
                    _buildDeleteRow(),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // 1. SHAPE ADD TOOLS ROW
  Widget _buildShapeAddRow() {
    final currentShape =
        _lastUsedTool[ToolbarRowType.shapes] ?? ShapeType.rectangle;

    return _buildToolbarRow(
      rowType: ToolbarRowType.shapes,
      collapsedIcon: _getShapeIcon(currentShape),
      collapsedColor: _getShapeColor(currentShape),
      collapsedTooltip: 'shapeEditor_toolbar_addShapes'.tr,
      expandedContent: Row(
        mainAxisSize: MainAxisSize.min,
        children: _buildOrderedShapeTools(currentShape),
      ),
    );
  }

  // Build ordered shape tools with last used first
  List<Widget> _buildOrderedShapeTools(ShapeType currentShape) {
    List<Widget> tools = [];
    List<Widget> otherTools = [];

    // Rectangle tool
    final rectangleTool = _buildShapeButton(
      'shapeEditor_shapes_rectangle'.tr,
      Colors.blue.shade300,
      () {
        widget.controller.addShape(ShapeType.rectangle);
        _setLastUsedTool(ToolbarRowType.shapes, ShapeType.rectangle);
      },
      ShapeTestIcons.rectangle(size: 24),
    );

    // Triangle tool
    final triangleTool = _buildShapeButton(
      'shapeEditor_shapes_triangle'.tr,
      Colors.green.shade300,
      () {
        widget.controller.addShape(ShapeType.triangle);
        _setLastUsedTool(ToolbarRowType.shapes, ShapeType.triangle);
      },
      ShapeTestIcons.triangle(size: 24),
    );

    // Trapezoid tool
    final trapezoidTool = _buildShapeButton(
      'shapeEditor_shapes_trapezoid'.tr,
      Colors.orange.shade300,
      () {
        widget.controller.addShape(ShapeType.trapezoid);
        _setLastUsedTool(ToolbarRowType.shapes, ShapeType.trapezoid);
      },
      ShapeTestIcons.trapezoid(size: 24),
    );

    // Right triangle tool
    final rightTriangleTool = _buildShapeButton(
      'shapeEditor_shapes_rightTriangle'.tr,
      Colors.purple.shade300,
      () {
        widget.controller.addShape(ShapeType.rightTriangle);
        _setLastUsedTool(ToolbarRowType.shapes, ShapeType.rightTriangle);
      },
      ShapeTestIcons.rightTriangle(size: 24),
    );

    // Custom shape tool
    final customShapeTool = _buildShapeButton(
      'shapeEditor_toolbar_customShape'.tr,
      Colors.pink.shade300,
      () {
        widget.controller.startCustomShapeCreation();
        _setLastUsedTool(ToolbarRowType.shapes, ShapeType.custom);
      },
      ShapeTestIcons.customShape(size: 24),
    );

    // Order tools based on last selected
    if (currentShape == ShapeType.rectangle) {
      tools.add(rectangleTool);
      otherTools.addAll(
          [triangleTool, trapezoidTool, rightTriangleTool, customShapeTool]);
    } else if (currentShape == ShapeType.triangle) {
      tools.add(triangleTool);
      otherTools.addAll(
          [rectangleTool, trapezoidTool, rightTriangleTool, customShapeTool]);
    } else if (currentShape == ShapeType.trapezoid) {
      tools.add(trapezoidTool);
      otherTools.addAll(
          [rectangleTool, triangleTool, rightTriangleTool, customShapeTool]);
    } else if (currentShape == ShapeType.rightTriangle) {
      tools.add(rightTriangleTool);
      otherTools.addAll(
          [rectangleTool, triangleTool, trapezoidTool, customShapeTool]);
    } else if (currentShape == ShapeType.custom) {
      tools.add(customShapeTool);
      otherTools.addAll(
          [rectangleTool, triangleTool, trapezoidTool, rightTriangleTool]);
    } else {
      // Default ordering
      tools.addAll([
        rectangleTool,
        triangleTool,
        trapezoidTool,
        rightTriangleTool,
        customShapeTool
      ]);
    }

    // Combine tools with most recently used first
    final allTools = [...tools, ...otherTools];

    // Always add padding between buttons
    final spacedTools = <Widget>[];
    for (int i = 0; i < allTools.length; i++) {
      spacedTools.add(allTools[i]);
      if (i < allTools.length - 1) {
        spacedTools.add(const SizedBox(width: 8));
      }
    }

    return spacedTools;
  }

  // 2. UNDO/REDO ROW (always visible, no expansion)
  Widget _buildUndoRedoRow() {
    return Obx(() {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Undo button
          _buildUndoButton(),
          const SizedBox(width: 8),
          // Redo button
          _buildRedoButton(),
        ],
      );
    });
  }

  // 3. DELETE ROW (always visible, no expansion)
  Widget _buildDeleteRow() {
    return Obx(() {
      final hasSelection = widget.controller.selectedIndices.isNotEmpty;
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Delete/Trash button
          FloatingActionButton.small(
            heroTag: 'delete_shapes',
            onPressed: hasSelection
                ? () {
                    widget.controller.deleteSelectedShapes();
                  }
                : null,
            backgroundColor: hasSelection ? Colors.red : Colors.grey,
            tooltip: hasSelection
                ? 'shapeEditor_toolbar_actions_delete'.tr
                : 'shapeEditor_toolbar_actions_delete_disabled'.tr,
            child: const Icon(Icons.delete, size: 20),
          ),
        ],
      );
    });
  }

  // Generic row builder with collapsible content
  Widget _buildToolbarRow({
    required ToolbarRowType rowType,
    required Widget collapsedIcon,
    required Color collapsedColor,
    required String collapsedTooltip,
    required Widget expandedContent,
  }) {
    // Check if any row is expanded to determine opacity for inactive buttons
    bool anyRowExpanded = _rowExpandedStates.values.any((expanded) => expanded);
    double inactiveOpacity = anyRowExpanded ? 0.5 : 1.0;

    // Check if this row is enabled
    bool isEnabled = _isRowEnabled(rowType);

    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Collapsed row button - only visible when row is not expanded
        if (!(_rowExpandedStates[rowType] ?? false))
          Opacity(
            opacity: (anyRowExpanded && !(_rowExpandedStates[rowType] ?? false))
                ? inactiveOpacity
                : isEnabled
                    ? 1.0
                    : 0.5,
            child: rowType == ToolbarRowType.grouping
                ? GestureDetector(
                    onLongPress:
                        isEnabled ? () => _toggleRowExpanded(rowType) : null,
                    child: FloatingActionButton.small(
                      heroTag: 'toolbar_row_${rowType.toString()}',
                      onPressed: isEnabled
                          ? () {
                              final bool canUngroup =
                                  widget.controller.canUngroupSelectedShape();
                              final bool canGroup =
                                  widget.controller.canGroupSelectedShapes();

                              if (canUngroup) {
                                widget.controller.ungroupSelectedShape();
                                _setLastUsedTool(
                                    ToolbarRowType.grouping, 'ungroup');
                              } else if (canGroup) {
                                widget.controller.groupSelectedShapes();
                                _setLastUsedTool(
                                    ToolbarRowType.grouping, 'group');
                              }
                            }
                          : null,
                      backgroundColor: collapsedColor,
                      // tooltip: isEnabled
                      //     ? '$collapsedTooltip (Long press to see all options)'
                      //     : '$collapsedTooltip (Disabled)',
                      child: collapsedIcon,
                    ),
                  )
                : FloatingActionButton.small(
                    heroTag: 'toolbar_row_${rowType.toString()}',
                    onPressed:
                        isEnabled ? () => _toggleRowExpanded(rowType) : null,
                    backgroundColor: collapsedColor,
                    tooltip: isEnabled ? collapsedTooltip : collapsedTooltip,
                    child: collapsedIcon,
                  ),
          ),

        // Expanded content with a tap detector to handle outside clicks
        SizeTransition(
          sizeFactor: _rowAnimations[rowType]!,
          axis: Axis.horizontal,
          axisAlignment: -1, // Start from the left side (where the button is)
          child: ClipRect(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width -
                    50, // Leave small margin
              ),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: expandedContent,
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Shape button builder
  Widget _buildShapeButton(
      String tooltip, Color color, VoidCallback onPressed, Widget icon) {
    return FloatingActionButton.small(
      heroTag: 'add_${tooltip.toLowerCase()}',
      onPressed: onPressed,
      backgroundColor: color,
      tooltip: tooltip,
      child: icon,
    );
  }

  // Get appropriate icon for the shape type
  Widget _getShapeIcon(ShapeType type) {
    switch (type) {
      case ShapeType.rectangle:
        return ShapeTestIcons.rectangle(size: 24);
      case ShapeType.triangle:
        return ShapeTestIcons.triangle(size: 24);
      case ShapeType.trapezoid:
        return ShapeTestIcons.trapezoid(size: 24);
      case ShapeType.rightTriangle:
        return ShapeTestIcons.rightTriangle(size: 24);
      case ShapeType.custom:
        return ShapeTestIcons.customShape(size: 24);
      default:
        return const Icon(Icons.add_box, size: 20);
    }
  }

  // Get appropriate color for the shape type
  Color _getShapeColor(ShapeType type) {
    switch (type) {
      case ShapeType.rectangle:
        return Colors.blue.shade300;
      case ShapeType.triangle:
        return Colors.green.shade300;
      case ShapeType.trapezoid:
        return Colors.orange.shade300;
      case ShapeType.rightTriangle:
        return Colors.purple.shade300;
      case ShapeType.custom:
        return Colors.pink.shade300;
      default:
        return Colors.amber;
    }
  }

  // Public method to collapse all expanded rows
  void collapseAllRows() {
    if (_rowExpandedStates.values.any((expanded) => expanded)) {
      setState(() {
        for (var type in ToolbarRowType.values) {
          _rowExpandedStates[type] = false;
          _rowAnimControllers[type]?.reverse();
        }
      });
    }
  }

  // Update undo button tooltip
  Widget _buildUndoButton() {
    return FloatingActionButton.small(
      heroTag: 'undo_action',
      onPressed: widget.controller.hasUndo
          ? () {
              widget.controller.undo();
              _setLastUsedTool(ToolbarRowType.utilities, 'undo');
            }
          : null,
      backgroundColor: widget.controller.hasUndo ? Colors.indigo : Colors.grey,
      tooltip: widget.controller.hasUndo
          ? 'shapeEditor_toolbar_actions_undo'.tr
          : 'shapeEditor_toolbar_actions_undo'.tr,
      child: const Icon(Icons.undo, size: 20),
    );
  }

  // Update redo button tooltip
  Widget _buildRedoButton() {
    return FloatingActionButton.small(
      heroTag: 'redo_action',
      onPressed: widget.controller.hasRedo
          ? () {
              widget.controller.redo();
              _setLastUsedTool(ToolbarRowType.utilities, 'redo');
            }
          : null,
      backgroundColor: widget.controller.hasRedo ? Colors.indigo : Colors.grey,
      tooltip: widget.controller.hasRedo
          ? 'shapeEditor_toolbar_actions_redo'.tr
          : 'shapeEditor_toolbar_actions_redo'.tr,
      child: const Icon(Icons.redo, size: 20),
    );
  }
}
