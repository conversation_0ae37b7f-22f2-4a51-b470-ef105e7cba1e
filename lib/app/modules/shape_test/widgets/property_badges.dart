import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../models/shape_data.dart';
import '../controllers/shape_editor_controller.dart';

/// A set of badges that display shape properties near the shape handles
class PropertyBadges extends StatelessWidget {
  final ShapeData shapeData;
  final ShapeEditorController controller;
  final Matrix4 transformMatrix;

  const PropertyBadges({
    super.key,
    required this.shapeData,
    required this.controller,
    required this.transformMatrix,
  });

  @override
  Widget build(BuildContext context) {
    // Get grid size for measurements
    final cellWidth = controller.gridSystem.cellWidth;
    final gridGaugeCm = controller.gridSystem.machinePitch! / 10.0;

    // Calculate dimensions in cm
    final rect = shapeData.boundingRect;
    final widthInCm = (rect.width / cellWidth) * gridGaugeCm;
    final heightInCm = (rect.height / cellWidth) * gridGaugeCm;

    // Get position in grid units
    final center = shapeData.center;
    final xPos = center.dx / cellWidth;
    final yPos = center.dy / cellWidth;

    // Calculate rotation in degrees
    final rotationDegrees = (shapeData.rotation * 180 / math.pi) % 360;

    // Determine if we should show metric values or grid units
    final showMetric = controller.showMetricValues.value;

    // Format values based on units preference
    final widthLabel = showMetric
        ? '${widthInCm.toStringAsFixed(1)} cm'
        : '${(rect.width / cellWidth).toStringAsFixed(1)} u';
    final heightLabel = showMetric
        ? '${heightInCm.toStringAsFixed(1)} cm'
        : '${(rect.height / cellWidth).toStringAsFixed(1)} u';

    // Transform the shape corners to get proper badge positions
    // Apply the transformation matrix to get screen coordinates
    final transformedCenter =
        MatrixUtils.transformPoint(transformMatrix, center);
    final transformedTopCenter = MatrixUtils.transformPoint(
        transformMatrix, Offset(center.dx, rect.top - 10));
    final transformedRightCenter = MatrixUtils.transformPoint(
        transformMatrix, Offset(rect.right + 10, center.dy));
    final transformedBottomCenter = MatrixUtils.transformPoint(
        transformMatrix, Offset(center.dx, rect.bottom + 10));
    final transformedLeftCenter = MatrixUtils.transformPoint(
        transformMatrix, Offset(rect.left - 10, center.dy));

    // Build badges at transformed positions
    return Stack(
      children: [
        // Position badge at top
        Positioned(
          left: transformedCenter.dx - 45,
          top: transformedTopCenter.dy - 30,
          child: _buildBadge(
            'Pos',
            '${xPos.toStringAsFixed(1)}, ${yPos.toStringAsFixed(1)}',
            Colors.blue.shade800,
          ),
        ),

        // Width badge on right edge
        Positioned(
          left: transformedRightCenter.dx + 5,
          top: transformedCenter.dy - 15,
          child: _buildBadge(
            'W',
            widthLabel,
            Colors.green.shade800,
          ),
        ),

        // Height badge on bottom edge
        Positioned(
          left: transformedCenter.dx - 40,
          top: transformedBottomCenter.dy + 5,
          child: _buildBadge(
            'H',
            heightLabel,
            Colors.green.shade800,
          ),
        ),

        // Rotation badge on left edge
        Positioned(
          left: transformedLeftCenter.dx - 85,
          top: transformedCenter.dy - 15,
          child: _buildBadge(
            'Rot',
            '${rotationDegrees.toStringAsFixed(0)}°',
            Colors.purple.shade800,
          ),
        ),
      ],
    );
  }

  Widget _buildBadge(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.8),
        borderRadius: BorderRadius.circular(4),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 10,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }
}
