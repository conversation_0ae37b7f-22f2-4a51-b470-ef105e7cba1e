import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/zones_editor_controller.dart';

class InstructionPanelWidget extends StatelessWidget {
  final ZonesEditorController controller;

  const InstructionPanelWidget({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() => Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue.shade200),
          ),
          child: Text(
            _getInstructionText(controller),
            textAlign: TextAlign.center,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.blue.shade900,
            ),
          ),
        ));
  }

  String _getInstructionText(ZonesEditorController controller) {
    if (controller.isCreatingNewZone.value) {
      return 'Create a polygon zone: CLICK to add corner points. To finish, click near the first point (red dot). Use pinch gesture with two fingers to zoom in/out.';
    } else if (controller.isEditingMode.value &&
        controller.selectedZoneIndex.value >= 0) {
      return 'DRAG the blue handles to resize this zone. Changes are applied immediately.';
    } else if (controller.isEditingMode.value) {
      return 'TAP on a zone to select it for editing, or create a new zone using the + button.';
    } else {
      return 'Enable EDIT mode to modify zones or create new ones.';
    }
  }
}
