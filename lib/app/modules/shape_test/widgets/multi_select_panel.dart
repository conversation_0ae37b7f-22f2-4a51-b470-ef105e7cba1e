import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/shape_editor_controller.dart';

class MultiSelectPanel extends StatelessWidget {
  final ShapeEditorController controller;
  final GlobalKey panelKey;

  const MultiSelectPanel({
    super.key,
    required this.controller,
    required this.panelKey,
  });

  @override
  Widget build(BuildContext context) {
    // Get the actual screen width
    final screenWidth = MediaQuery.of(context).size.width;
    final useVerticalLayout = screenWidth < 450;

    return Container(
      key: panelKey,
      margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).padding.bottom + 16.0,
          left: 16.0,
          right: 16.0),
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: Get.isDarkMode ? Colors.grey[800] : Colors.white,
        borderRadius: BorderRadius.circular(16.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8.0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: useVerticalLayout
          ? Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Selection controls stacked vertically
                Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSelectAllButton(),
                    _buildDeselectAllButton(),
                  ],
                ),

                // Action buttons on the right
                Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    _buildGroupButton(),
                    ElevatedButton(
                      onPressed: _exitMultiSelectMode,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16.0, vertical: 8.0),
                        backgroundColor: Get.theme.colorScheme.primary,
                        foregroundColor: Colors.white,
                      ),
                      child: Text('shapeEditor_multiSelect_done'.tr),
                    ),
                  ],
                ),
              ],
            )
          : Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildSelectAllButton(),

                // Small space if both buttons might be visible
                Obx(() => controller.selectedIndices.length <
                            controller.shapes.length &&
                        controller.selectedIndices.isNotEmpty
                    ? const SizedBox(width: 8)
                    : const SizedBox.shrink()),

                _buildDeselectAllButton(),

                // Group button with spacing
                Obx(() => controller.canGroupSelectedShapes()
                    ? Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const SizedBox(width: 8),
                          _buildGroupButton(),
                        ],
                      )
                    : const SizedBox.shrink()),

                // Only show divider if at least one button is visible
                Obx(() => controller.selectedIndices.length <
                            controller.shapes.length ||
                        controller.selectedIndices.isNotEmpty ||
                        controller.canGroupSelectedShapes()
                    ? Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const SizedBox(width: 8),
                          VerticalDivider(
                            color: Colors.grey[300],
                            thickness: 1,
                            width: 16,
                            indent: 8,
                            endIndent: 8,
                          ),
                          const SizedBox(width: 8),
                        ],
                      )
                    : const SizedBox.shrink()),

                ElevatedButton(
                  onPressed: _exitMultiSelectMode,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16.0, vertical: 8.0),
                    backgroundColor: Get.theme.colorScheme.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: Text('shapeEditor_multiSelect_done'.tr),
                ),
              ],
            ),
    );
  }

  Widget _buildSelectAllButton() {
    return Obx(
        () => controller.selectedIndices.length < controller.shapes.length
            ? TextButton.icon(
                icon: const Icon(Icons.select_all),
                label: Text('shapeEditor_multiSelect_selectAll'.tr),
                onPressed: _selectAll,
              )
            : const SizedBox.shrink());
  }

  Widget _buildDeselectAllButton() {
    return Obx(() => controller.selectedIndices.isNotEmpty
        ? TextButton.icon(
            icon: const Icon(Icons.deselect),
            label: Text('shapeEditor_multiSelect_deselectAll'.tr),
            onPressed: _deselectAll,
          )
        : const SizedBox.shrink());
  }

  Widget _buildGroupButton() {
    return Obx(() => controller.canGroupSelectedShapes()
        ? TextButton.icon(
            icon: const Icon(Icons.group_work),
            label: Text('shapeEditor_multiSelect_group'.tr),
            onPressed: _groupSelectedShapes,
            style: TextButton.styleFrom(
              foregroundColor: Get.theme.colorScheme.primary,
            ),
          )
        : const SizedBox.shrink());
  }

  void _selectAll() {
    final allIndices = List<int>.generate(controller.shapes.length, (i) => i);
    controller.selectedIndices.clear();
    controller.selectedIndices.addAll(allIndices);

    // Update the visual selection state of all shapes
    controller.selectionManager
        .updateShapeSelectionState(curveModeStates: controller.curveModeStates);

    controller.update();
  }

  void _exitMultiSelectMode() {
    controller.toggleMultiSelectionMode();
  }

  void _deselectAll() {
    controller.selectedIndices.clear();

    // Update the visual selection state of all shapes
    controller.selectionManager
        .updateShapeSelectionState(curveModeStates: controller.curveModeStates);

    controller.update();
  }

  void _groupSelectedShapes() {
    controller.groupSelectedShapes();
    // After grouping, exit multi-select mode as the shapes are now grouped
    _exitMultiSelectMode();
  }
}
