import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../widgets/shape_handles.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/shape_editor_controller.dart';

/// Widget that renders a rotation handle and handles the rotation gestures
class RotationHandleWidget extends StatelessWidget {
  final Offset position;
  final GestureDragStartCallback onRotationStart;
  final GestureDragUpdateCallback onRotationUpdate;

  const RotationHandleWidget({
    super.key,
    required this.position,
    required this.onRotationStart,
    required this.onRotationUpdate,
  });

  @override
  Widget build(BuildContext context) {
    // Find the controller
    final controller = Get.find<ShapeEditorController>();

    // Increased touch area while maintaining visual center
    return Positioned(
      left: position.dx - 36, // Increased from 24 to 36
      top: position.dy - 36, // Increased from 24 to 36
      // Larger container for hit detection
      child: Container(
        width: 72, // 72x72 hit area (50% larger than original)
        height: 72,
        color: Colors.transparent, // Invisible container for hit detection
        // Add a parent GestureDetector to catch any taps that might bubble up
        child: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            // Prevent shape deselection
            controller.isHandleInteraction = true;
            Future.delayed(const Duration(milliseconds: 100), () {
              controller.isHandleInteraction = false;
            });
          },
          onTapDown: (_) {
            // Set flag to prevent shape deselection
            controller.isHandleInteraction = true;
          },
          onTapUp: (_) {
            // Reset flag after a short delay
            Future.delayed(const Duration(milliseconds: 100), () {
              controller.isHandleInteraction = false;
            });
          },
          onTapCancel: () {
            // Reset flag
            controller.isHandleInteraction = false;
          },
          onPanStart: (details) {
            // Set flag and call the original callback
            controller.isHandleInteraction = true;
            onRotationStart(details);
          },
          onPanUpdate: onRotationUpdate,
          onPanEnd: (details) {
            // Reset flag after a short delay
            Future.delayed(const Duration(milliseconds: 100), () {
              controller.isHandleInteraction = false;
            });
          },
          onPanCancel: () {
            // Reset flag
            controller.isHandleInteraction = false;
          },
          child: Center(
            child: AbsorbPointer(
              absorbing: false,
              child: Material(
                elevation: 6,
                color: Colors.transparent,
                shadowColor: Colors.black.withOpacity(0.5),
                shape: const CircleBorder(),
                child: InkWell(
                  customBorder: const CircleBorder(),
                  onTap: () {
                    // Prevent shape deselection
                    controller.isHandleInteraction = true;
                    Future.delayed(const Duration(milliseconds: 100), () {
                      controller.isHandleInteraction = false;
                    });
                  },
                  child: Obx(() {
                    return RotationHandle(
                        zoomScale: controller.zoomScale.value);
                  }),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
