import 'package:flutter/material.dart';
import '../controllers/shape_editor_controller.dart';

/// Widget that shows a tinted overlay on the mirrored side of the screen
class MirrorModeOverlay extends StatelessWidget {
  final ShapeEditorController controller;

  const MirrorModeOverlay({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    if (!controller.isMirrorModeActive.value) return const SizedBox.shrink();

    return Positioned.fill(
      child: IgnorePointer(
        child: Row(
          children: [
            // Left side with tinted overlay (mirrored side)
            Expanded(
              child: Container(
                color: Colors.blue.withOpacity(0.1),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // Calculate how many texts we can fit with appropriate spacing
                    const textHeight = 48.0; // Approximate height of each text
                    const spacingFactor =
                        10.0; // Space between texts (in multiples of text height)
                    final totalSpacing = textHeight * spacingFactor;
                    final itemCount =
                        (constraints.maxHeight / totalSpacing).floor();

                    return Column(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: List.generate(
                        itemCount,
                        (index) => RotatedBox(
                          quarterTurns: 1,
                          child: Text(
                            'MIRRORED',
                            style: TextStyle(
                              color: Colors.blue.withOpacity(0.3),
                              fontSize: 48,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
            // Right side (active editing side)
            const Expanded(child: SizedBox()),
          ],
        ),
      ),
    );
  }
}
