import 'package:flutter/material.dart';
import 'package:get/get.dart'; // Add import for GetX
import 'package:xoxknit/app/modules/shape_test/controllers/shape_editor_controller.dart';

import '../painters/grid_system.dart';
import '../constants/grid_constants.dart';
import '../handlers/shape_manipulation_handlers.dart'; // For SnapInfo
import '../models/shape_data.dart'; // For ShapeData

/// A widget that displays a professional drawing grid
class ProfessionalGridWidget extends StatelessWidget {
  /// The grid system that manages all grid functionality
  final GridSystem gridSystem;

  /// Callback when a point is snapped to the grid
  final Function(Offset)? onSnapPoint;

  /// Child widgets to display on top of the grid
  final Widget? child;

  /// Minimum size for clipping the grid (performance optimization)
  final Size? clipSize;

  /// Active snap info for drawing snap lines directly in the grid
  final SnapInfo? activeSnapInfo;

  /// Map of shape states for drawing snap lines
  final Map<Key, ShapeData>? shapeStates;

  const ProfessionalGridWidget({
    super.key,
    required this.gridSystem,
    this.onSnapPoint,
    this.child,
    this.clipSize,
    this.activeSnapInfo,
    this.shapeStates,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        gridSystem.cellWidth = constraints.maxWidth / gridSystem.needleCount;
        // Use Obx to make the grid reactive to zoom and pan changes
        return Obx(() {
          // Force the widget to rebuild when zoom or pan changes
          final _ = gridSystem.zoomLevel;
          final __ = gridSystem.panOffset;
          // Define grid size to fit width and extend height using the constant
          final fullGridSize = Size(
            constraints.maxWidth,
            GridConstants.getExtendedHeight(constraints.maxHeight),
          );

          return Stack(
            clipBehavior: Clip.none, // Prevent clipping at the stack level
            children: [
              // The grid - fit width perfectly, extend height
              SizedBox(
                width: fullGridSize.width,
                height: fullGridSize.height,

                // Remove ClipRect and use direct CustomPaint
                child: Padding(
                  padding: const EdgeInsets.only(top: 0.0),
                  child: CustomPaint(
                    painter: gridSystem.createPainter(
                        cellWidth: fullGridSize.width / gridSystem.needleCount,
                        activeSnapInfo: activeSnapInfo,
                        shapeStates: shapeStates),
                    size: Size(
                        fullGridSize.width,
                        fullGridSize
                            .height), // Adjust size to account for padding
                    isComplex: true, // Optimize for complex painting
                  ),
                ),
              ),

              // The child content
              if (child != null)
                Positioned.fill(
                  child: child!,
                ),
            ],
          );
        });
      },
    );
  }

  /// Helper method to snap a point to the grid
  Offset snapToGrid(Offset point, Size size) {
    final snappedPoint =
        ShapeEditorController.to.snappingManager.snapPoint(point, size);

    if (onSnapPoint != null) {
      onSnapPoint!(snappedPoint);
    }

    return snappedPoint;
  }
}

/// Widget that shows a brief visual feedback when a point is snapped to grid
class SnapIndicator extends StatefulWidget {
  final Offset position;
  final bool isCenterSnap;

  const SnapIndicator({
    super.key,
    required this.position,
    this.isCenterSnap = false,
  });

  @override
  State<SnapIndicator> createState() => _SnapIndicatorState();
}

class _SnapIndicatorState extends State<SnapIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _opacityAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(milliseconds: widget.isCenterSnap ? 800 : 500),
      vsync: this,
    );

    // Create an animation sequence for more dynamic feedback
    _opacityAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.0, end: 1.0)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 20,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.0),
        weight: 60,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 0.0)
            .chain(CurveTween(curve: Curves.easeOut)),
        weight: 20,
      ),
    ]).animate(_controller);

    // Scale animation for pop effect
    _scaleAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.3, end: 1.2)
            .chain(CurveTween(curve: Curves.elasticOut)),
        weight: 30,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.2, end: 1.0)
            .chain(CurveTween(curve: Curves.easeOutQuad)),
        weight: 20,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.0),
        weight: 30,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 0.0)
            .chain(CurveTween(curve: Curves.easeInQuad)),
        weight: 20,
      ),
    ]).animate(_controller);

    // Add a pulse animation for center snaps
    _pulseAnimation = widget.isCenterSnap
        ? TweenSequence<double>([
            TweenSequenceItem(
              tween: Tween<double>(begin: 1.0, end: 1.25)
                  .chain(CurveTween(curve: Curves.easeInOut)),
              weight: 50,
            ),
            TweenSequenceItem(
              tween: Tween<double>(begin: 1.25, end: 1.0)
                  .chain(CurveTween(curve: Curves.easeInOut)),
              weight: 50,
            ),
          ]).animate(CurvedAnimation(
            parent: _controller,
            curve: Interval(0.0, 0.75, curve: Curves.easeInOut),
          ))
        : AlwaysStoppedAnimation(1.0);

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Enhanced appearance for better visibility
    final double baseSize =
        widget.isCenterSnap ? 18.0 : 14.0; // Increased from 14.0/10.0

    // More vibrant colors for better visibility
    final Color primaryColor =
        widget.isCenterSnap ? Colors.red.shade500 : Colors.blue.shade600;

    final Color glowColor =
        widget.isCenterSnap ? Colors.red.shade300 : Colors.blue.shade400;

    return Positioned(
      left: widget.position.dx - (baseSize / 2),
      top: widget.position.dy - (baseSize / 2),
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          // Apply all animations
          final scale = _scaleAnimation.value * _pulseAnimation.value;

          return Opacity(
            opacity: _opacityAnimation.value,
            child: Transform.scale(
              scale: scale,
              child: Container(
                width: baseSize,
                height: baseSize,
                decoration: BoxDecoration(
                  // Use a white ring with colored fill for better contrast
                  color: primaryColor.withOpacity(0.85),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Colors.white,
                    width: 2.0,
                  ),
                  // Enhanced glow effect for all snap indicators
                  boxShadow: [
                    BoxShadow(
                      color: glowColor.withOpacity(0.6),
                      blurRadius: widget.isCenterSnap ? 12.0 : 8.0,
                      spreadRadius: widget.isCenterSnap ? 3.0 : 2.0,
                    ),
                  ],
                ),
                child: widget.isCenterSnap
                    ? Center(
                        child: Icon(
                          Icons.add,
                          size: baseSize * 0.6,
                          color: Colors.white,
                        ),
                      )
                    : Center(
                        child: Container(
                          width: baseSize * 0.3,
                          height: baseSize * 0.3,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Extension to add more functionality to the grid widget
extension ProfessionalGridExtensions on ProfessionalGridWidget {
  /// Create a widget that displays coordinates when items are dragged
  Widget withCoordinateDisplay({
    required BuildContext context,
    required Offset position,
    bool showSnapped = true,
  }) {
    final size = MediaQuery.of(context).size;
    final snappedPosition = showSnapped ? snapToGrid(position, size) : position;

    return Stack(
      children: [
        this,
        Positioned(
          left: snappedPosition.dx + 10,
          top: snappedPosition.dy - 30,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.7),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              '(${snappedPosition.dx.toStringAsFixed(0)}, ${snappedPosition.dy.toStringAsFixed(0)})',
              style: const TextStyle(color: Colors.white, fontSize: 12),
            ),
          ),
        ),
      ],
    );
  }
}
