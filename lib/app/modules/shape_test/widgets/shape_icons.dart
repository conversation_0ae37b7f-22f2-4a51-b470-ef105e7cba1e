import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class ShapeTestIcons {
  static Widget rectangle({double? size, Color? color}) {
    return SvgPicture.string(
      '''
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="2" y="4" width="20" height="16" stroke="currentColor" stroke-width="2" fill="none"/>
      </svg>
      ''',
      width: size ?? 24,
      height: size ?? 24,
      colorFilter: ColorFilter.mode(color ?? Colors.white, BlendMode.srcIn),
    );
  }

  static Widget triangle({double? size, Color? color}) {
    return SvgPicture.string(
      '''
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 4L22 20H2L12 4Z" stroke="currentColor" stroke-width="2" fill="none"/>
      </svg>
      ''',
      width: size ?? 24,
      height: size ?? 24,
      colorFilter: ColorFilter.mode(color ?? Colors.white, BlendMode.srcIn),
    );
  }

  static Widget rightTriangle({double? size, Color? color}) {
    return SvgPicture.string(
      '''
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M4 4L20 20H4L4 4Z" stroke="currentColor" stroke-width="2" fill="none"/>
      </svg>
      ''',
      width: size ?? 24,
      height: size ?? 24,
      colorFilter: ColorFilter.mode(color ?? Colors.white, BlendMode.srcIn),
    );
  }

  static Widget trapezoid({double? size, Color? color}) {
    return SvgPicture.string(
      '''
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8 4H16L20 20H4L8 4Z" stroke="currentColor" stroke-width="2" fill="none"/>
      </svg>
      ''',
      width: size ?? 24,
      height: size ?? 24,
      colorFilter: ColorFilter.mode(color ?? Colors.white, BlendMode.srcIn),
    );
  }

  // Added additional custom icons specific to the drawing board

  static Widget pentagon({double? size, Color? color}) {
    return SvgPicture.string(
      '''
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 2L22 9.5L18 20H6L2 9.5L12 2Z" stroke="currentColor" stroke-width="2" fill="none"/>
      </svg>
      ''',
      width: size ?? 24,
      height: size ?? 24,
      colorFilter: ColorFilter.mode(color ?? Colors.white, BlendMode.srcIn),
    );
  }

  static Widget hexagon({double? size, Color? color}) {
    return SvgPicture.string(
      '''
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 2L21 7V17L12 22L3 17V7L12 2Z" stroke="currentColor" stroke-width="2" fill="none"/>
      </svg>
      ''',
      width: size ?? 24,
      height: size ?? 24,
      colorFilter: ColorFilter.mode(color ?? Colors.white, BlendMode.srcIn),
    );
  }

  static Widget curvedLine({double? size, Color? color, bool active = false}) {
    // Different SVG based on whether curve mode is active or not
    final svgContent = active
        ? '''
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M4 18C4 18 8 6 12 12C16 18 20 6 20 6" stroke="currentColor" stroke-width="2.5" stroke-linecap="round"/>
        <circle cx="12" cy="12" r="2" fill="currentColor"/>
      </svg>
      '''
        : '''
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M4 18L20 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-dasharray="1 3"/>
        <path d="M4 18C4 18 8 6 12 12C16 18 20 6 20 6" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
      </svg>
      ''';

    return SvgPicture.string(
      svgContent,
      width: size ?? 24,
      height: size ?? 24,
      colorFilter: ColorFilter.mode(color ?? Colors.white, BlendMode.srcIn),
    );
  }

  static Widget duplicateIcon({double? size, Color? color}) {
    return SvgPicture.string(
      '''
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="4" y="8" width="12" height="12" stroke="currentColor" stroke-width="2" fill="none"/>
        <rect x="8" y="4" width="12" height="12" stroke="currentColor" stroke-width="2" fill="none"/>
      </svg>
      ''',
      width: size ?? 24,
      height: size ?? 24,
      colorFilter: ColorFilter.mode(color ?? Colors.white, BlendMode.srcIn),
    );
  }

  static Widget mirrorIcon({double? size, Color? color, bool active = false}) {
    return SvgPicture.string(
      '''
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <!-- Center line -->
        <path d="M12 2V22" stroke="currentColor" stroke-width="${active ? "2" : "1.5"}" stroke-linecap="round" stroke-dasharray="${active ? "" : "2 2"}"/>
        
        <!-- Left shape (trapezoid) -->
        <path d="M3 8H9L7 16H5L3 8Z" stroke="currentColor" stroke-width="1.5" fill="${active ? 'currentColor' : 'none'}" fill-opacity="${active ? '0.2' : '0'}"/>
        
        <!-- Right shape (mirrored trapezoid) -->
        <path d="M21 8H15L17 16H19L21 8Z" stroke="currentColor" stroke-width="1.5" fill="${active ? 'currentColor' : 'none'}" fill-opacity="${active ? '0.2' : '0'}"/>
        
        <!-- Connection indicators/arrows suggesting the mirroring relationship -->
        <path d="M10 7L8 7" stroke="currentColor" stroke-width="1.3" stroke-linecap="round" />
        <path d="M14 7L16 7" stroke="currentColor" stroke-width="1.3" stroke-linecap="round" />
        
        <!-- Small directional indicators -->
        <path d="M10 13L9 12L10 11" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" />
        <path d="M14 13L15 12L14 11" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" />
      </svg>
      ''',
      width: size ?? 24,
      height: size ?? 24,
      colorFilter: ColorFilter.mode(color ?? Colors.white, BlendMode.srcIn),
    );
  }

  static Widget snapToCenter(
      {double? size, Color? color, bool active = false}) {
    return SvgPicture.string(
      '''
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <!-- Center point/target -->
        <circle cx="12" cy="12" r="2" fill="currentColor" stroke="currentColor" stroke-width="1" />
        
        <!-- Horizontal alignment line -->
        <path d="M2 12H8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
        <path d="M16 12H22" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
        
        <!-- Vertical alignment line -->
        <path d="M12 2V8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
        <path d="M12 16V22" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
        
        <!-- Corner arrows pointing to center -->
        <path d="M5 5L8 8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
        <path d="M19 5L16 8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
        <path d="M5 19L8 16" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
        <path d="M19 19L16 16" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
      </svg>
      ''',
      width: size ?? 24,
      height: size ?? 24,
      colorFilter: ColorFilter.mode(color ?? Colors.white, BlendMode.srcIn),
    );
  }

  static Widget customShape({double? size, Color? color}) {
    return SvgPicture.string(
      '''
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <!-- Custom polygon shape with points -->
        <path d="M5 8L9 4L15 5L20 9L18 15L12 20L4 17L5 8Z" stroke="currentColor" stroke-width="2" fill="none"/>
        <!-- Small markers at vertices to suggest points -->
        <circle cx="5" cy="8" r="1" fill="currentColor"/>
        <circle cx="9" cy="4" r="1" fill="currentColor"/>
        <circle cx="15" cy="5" r="1" fill="currentColor"/>
        <circle cx="20" cy="9" r="1" fill="currentColor"/>
        <circle cx="18" cy="15" r="1" fill="currentColor"/>
        <circle cx="12" cy="20" r="1" fill="currentColor"/>
        <circle cx="4" cy="17" r="1" fill="currentColor"/>
      </svg>
      ''',
      width: size ?? 24,
      height: size ?? 24,
      colorFilter: ColorFilter.mode(color ?? Colors.white, BlendMode.srcIn),
    );
  }
}
