import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/zones_editor_controller.dart';

// Enum for zone toolbar row types
enum ZoneToolbarRowType { zoneActions, utilities }

class ZonesExpandableToolbar extends StatefulWidget {
  final ZonesEditorController controller;

  const ZonesExpandableToolbar({super.key, required this.controller});

  @override
  State<ZonesExpandableToolbar> createState() => ZonesExpandableToolbarState();
}

class ZonesExpandableToolbarState extends State<ZonesExpandableToolbar>
    with TickerProviderStateMixin {
  late AnimationController _mainAnimationController;
  late Animation<double> _expandAnimation;

  // Tool row expansion states and animations
  final Map<ZoneToolbarRowType, bool> _rowExpandedStates = {};
  final Map<ZoneToolbarRowType, AnimationController> _rowAnimControllers = {};
  final Map<ZoneToolbarRowType, Animation<double>> _rowAnimations = {};

  // Track last used tool in each category
  final Map<ZoneToolbarRowType, dynamic> _lastUsedTool = {};

  @override
  void initState() {
    super.initState();
    // Main toolbar expansion animation
    _mainAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _expandAnimation = CurvedAnimation(
      parent: _mainAnimationController,
      curve: Curves.easeInOut,
    );

    // Always start expanded for zone editor
    _mainAnimationController.value = 1.0;

    // Initialize row expansion states and animations
    for (var rowType in ZoneToolbarRowType.values) {
      _rowExpandedStates[rowType] = false;
      _rowAnimControllers[rowType] = AnimationController(
        vsync: this,
        duration: const Duration(milliseconds: 250),
      );
      _rowAnimations[rowType] = CurvedAnimation(
        parent: _rowAnimControllers[rowType]!,
        curve: Curves.easeInOut,
      );
    }

    // Set initial last used tools
    _lastUsedTool[ZoneToolbarRowType.zoneActions] = 'edit_mode';
    _lastUsedTool[ZoneToolbarRowType.utilities] = 'undo';
  }

  @override
  void dispose() {
    _mainAnimationController.dispose();
    for (var controller in _rowAnimControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  void _toggleRowExpanded(ZoneToolbarRowType rowType) {
    // Check if the row is enabled before allowing expansion
    if (!_isRowEnabled(rowType)) {
      return; // Don't allow expanding disabled rows
    }

    setState(() {
      // Close all other rows first
      for (var type in ZoneToolbarRowType.values) {
        if (type != rowType) {
          _rowExpandedStates[type] = false;
          _rowAnimControllers[type]?.reverse();
        }
      }

      // Toggle the selected row
      _rowExpandedStates[rowType] = !(_rowExpandedStates[rowType] ?? false);
      if (_rowExpandedStates[rowType]!) {
        _rowAnimControllers[rowType]?.forward();
      } else {
        _rowAnimControllers[rowType]?.reverse();
      }
    });
  }

  // Check if a row is enabled based on its type
  bool _isRowEnabled(ZoneToolbarRowType rowType) {
    switch (rowType) {
      case ZoneToolbarRowType.zoneActions:
        return true; // Zone actions are always available
      case ZoneToolbarRowType.utilities:
        return true; // Utilities are always available
      default:
        return true;
    }
  }

  void _setLastUsedTool(ZoneToolbarRowType rowType, dynamic toolIdentifier) {
    setState(() {
      _lastUsedTool[rowType] = toolIdentifier;

      // Automatically collapse the row after setting the tool
      _rowExpandedStates[rowType] = false;
      _rowAnimControllers[rowType]?.reverse();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // The expandable content
        SizeTransition(
          sizeFactor: _expandAnimation,
          axis: Axis.vertical,
          child: FadeTransition(
            opacity: _expandAnimation,
            child: Container(
              constraints:
                  BoxConstraints(maxWidth: MediaQuery.of(context).size.width),
              child: GestureDetector(
                onTap: collapseAllRows,
                behavior: HitTestBehavior.translucent,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 1. ZONE ACTIONS ROW
                    _buildZoneActionsRow(),

                    const SizedBox(height: 4),

                    // 2. UTILITY TOOLS ROW
                    _buildUtilityToolsRow(),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // 1. ZONE ACTIONS ROW
  Widget _buildZoneActionsRow() {
    final currentTool =
        _lastUsedTool[ZoneToolbarRowType.zoneActions] ?? 'edit_mode';

    return Obx(() => _buildToolbarRow(
          rowType: ZoneToolbarRowType.zoneActions,
          collapsedIcon: _getZoneActionIcon(currentTool),
          collapsedColor: _getZoneActionColor(currentTool),
          collapsedTooltip: 'Zone Actions',
          expandedContent: Row(
            mainAxisSize: MainAxisSize.min,
            children: _buildOrderedZoneActionTools(currentTool),
          ),
        ));
  }

  List<Widget> _buildOrderedZoneActionTools(String currentTool) {
    // Edit mode toggle - wrap in Obx for reactivity
    final editModeToggle = Obx(() => FloatingActionButton.small(
          heroTag: 'zone_edit_mode',
          onPressed: () {
            widget.controller.toggleEditMode();
            _setLastUsedTool(ZoneToolbarRowType.zoneActions, 'edit_mode');
          },
          backgroundColor: widget.controller.isEditingMode.value
              ? Colors.green.shade700
              : Colors.green,
          tooltip: widget.controller.isEditingMode.value
              ? 'zonesEditor_toolbar_editMode_exit'.tr
              : 'zonesEditor_toolbar_editMode_enter'.tr,
          child: Icon(
            widget.controller.isEditingMode.value ? Icons.edit_off : Icons.edit,
            size: 20,
          ),
        ));

    // Save new zone - wrap in Obx for reactivity
    final saveZone = Obx(() => FloatingActionButton.small(
          heroTag: 'zone_save',
          onPressed: widget.controller.isCreatingNewZone.value
              ? () {
                  widget.controller.saveNewZone();
                  _setLastUsedTool(ZoneToolbarRowType.zoneActions, 'save_zone');
                }
              : null,
          backgroundColor: widget.controller.isCreatingNewZone.value
              ? Colors.orange
              : Colors.grey,
          tooltip: 'zonesEditor_toolbar_saveNewZone'.tr,
          child: const Icon(Icons.save, size: 20),
        ));

    // Cancel zone creation - wrap in Obx for reactivity
    final cancelZone = Obx(() => FloatingActionButton.small(
          heroTag: 'zone_cancel',
          onPressed: widget.controller.isCreatingNewZone.value
              ? () {
                  widget.controller.cancelZoneCreation();
                  _setLastUsedTool(
                      ZoneToolbarRowType.zoneActions, 'cancel_zone');
                }
              : null,
          backgroundColor: widget.controller.isCreatingNewZone.value
              ? Colors.grey.shade700
              : Colors.grey,
          tooltip: 'zonesEditor_toolbar_cancelZoneCreation'.tr,
          child: const Icon(Icons.cancel, size: 20),
        ));

    // Undo last point during zone creation - wrap in Obx for reactivity
    final undoLastPoint = Obx(() => FloatingActionButton.small(
          heroTag: 'zone_undo_point',
          onPressed: widget.controller.isCreatingNewZone.value
              ? () {
                  // Use the zone creation controller reference from the zones editor controller
                  final creationController =
                      widget.controller.zoneCreationController;
                  if (creationController != null) {
                    creationController.undoLastPoint();
                  } else {
                    debugPrint(
                        'ZoneCreationController reference not found for undo last point');
                  }
                  _setLastUsedTool(
                      ZoneToolbarRowType.zoneActions, 'undo_point');
                }
              : null,
          backgroundColor: widget.controller.isCreatingNewZone.value
              ? Colors.purple
              : Colors.grey,
          tooltip: 'zonesEditor_toolbar_undoLastPoint'.tr,
          child: const Icon(Icons.undo, size: 20),
        ));

    // Create a map of tools (kept edit mode, removed other context menu items)
    Map<String, Widget> tools = {
      'edit_mode': editModeToggle,
      'save_zone': saveZone,
      'undo_point': undoLastPoint,
      'cancel_zone': cancelZone,
    };

    // Ordered list of tools
    List<Widget> orderedTools = [];

    // Add current tool first if it exists
    if (tools.containsKey(currentTool)) {
      orderedTools.add(tools[currentTool]!);
      tools.remove(currentTool);
    }

    // Add remaining tools
    for (var entry in tools.entries) {
      if (orderedTools.isNotEmpty) {
        orderedTools.add(const SizedBox(width: 8));
      }
      orderedTools.add(entry.value);
    }

    return orderedTools;
  }

  // 2. UTILITY TOOLS ROW
  Widget _buildUtilityToolsRow() {
    final currentTool = _lastUsedTool[ZoneToolbarRowType.utilities] ?? 'undo';

    return _buildToolbarRow(
      rowType: ZoneToolbarRowType.utilities,
      collapsedIcon: _getUtilityIcon(currentTool),
      collapsedColor: Colors.blueGrey,
      collapsedTooltip: 'zonesEditor_toolbar_utilityTools'.tr,
      expandedContent: Row(
        mainAxisSize: MainAxisSize.min,
        children: _buildOrderedUtilityTools(currentTool),
      ),
    );
  }

  List<Widget> _buildOrderedUtilityTools(String currentTool) {
    // Undo tool
    final undoTool = _buildUndoButton();

    // Redo tool
    final redoTool = _buildRedoButton();

    // Reset zoom tool
    final resetZoomTool = FloatingActionButton.small(
      heroTag: 'zones_reset_zoom',
      onPressed: () {
        // Access the shape controller through the zones controller for zoom functionality
        if (Get.isRegistered()) {
          final shapeController = widget.controller.shapeController;
          shapeController.resetZoomAndCenterContent();
        }
        _setLastUsedTool(ZoneToolbarRowType.utilities, 'reset_zoom');
      },
      backgroundColor: Colors.teal,
      tooltip: 'zonesEditor_toolbar_resetZoom'.tr,
      child: const Icon(Icons.zoom_out_map, size: 20),
    );

    // Create a map of tools
    Map<String, Widget> tools = {
      'undo': undoTool,
      'redo': redoTool,
      'reset_zoom': resetZoomTool,
    };

    // Ordered list of tools
    List<Widget> orderedTools = [];

    // Add current tool first
    if (tools.containsKey(currentTool)) {
      orderedTools.add(tools[currentTool]!);
      tools.remove(currentTool);
    }

    // Add remaining tools
    for (var entry in tools.entries) {
      if (orderedTools.isNotEmpty) {
        orderedTools.add(const SizedBox(width: 8));
      }
      orderedTools.add(entry.value);
    }

    return orderedTools;
  }

  // Generic row builder with collapsible content
  Widget _buildToolbarRow({
    required ZoneToolbarRowType rowType,
    required Widget collapsedIcon,
    required Color collapsedColor,
    required String collapsedTooltip,
    required Widget expandedContent,
  }) {
    // Check if any row is expanded to determine opacity for inactive buttons
    bool anyRowExpanded = _rowExpandedStates.values.any((expanded) => expanded);
    double inactiveOpacity = anyRowExpanded ? 0.5 : 1.0;

    // Check if this row is enabled
    bool isEnabled = _isRowEnabled(rowType);

    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Collapsed row button - only visible when row is not expanded
        if (!(_rowExpandedStates[rowType] ?? false))
          Opacity(
            opacity: (anyRowExpanded && !(_rowExpandedStates[rowType] ?? false))
                ? inactiveOpacity
                : isEnabled
                    ? 1.0
                    : 0.5,
            child: FloatingActionButton.small(
              heroTag: 'zones_toolbar_row_${rowType.toString()}',
              onPressed: isEnabled ? () => _toggleRowExpanded(rowType) : null,
              backgroundColor: collapsedColor,
              tooltip: isEnabled ? collapsedTooltip : collapsedTooltip,
              child: collapsedIcon,
            ),
          ),

        // Expanded content
        SizeTransition(
          sizeFactor: _rowAnimations[rowType]!,
          axis: Axis.horizontal,
          axisAlignment: -1, // Start from the left side (where the button is)
          child: ClipRect(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width - 50,
              ),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: expandedContent,
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Get appropriate icon for zone actions
  Widget _getZoneActionIcon(String toolIdentifier) {
    switch (toolIdentifier) {
      case 'edit_mode':
        return Icon(
          widget.controller.isEditingMode.value ? Icons.edit_off : Icons.edit,
          size: 20,
        );
      case 'save_zone':
        return const Icon(Icons.save, size: 20);
      case 'cancel_zone':
        return const Icon(Icons.cancel, size: 20);
      case 'undo_point':
        return const Icon(Icons.undo, size: 20);
      default:
        return Icon(
          widget.controller.isEditingMode.value ? Icons.edit_off : Icons.edit,
          size: 20,
        );
    }
  }

  // Get appropriate color for zone actions
  Color _getZoneActionColor(String toolIdentifier) {
    switch (toolIdentifier) {
      case 'edit_mode':
        return widget.controller.isEditingMode.value
            ? Colors.green.shade700
            : Colors.green;
      case 'save_zone':
        return Colors.orange;
      case 'cancel_zone':
        return Colors.grey.shade700;
      case 'undo_point':
        return Colors.purple;
      default:
        return widget.controller.isEditingMode.value
            ? Colors.green.shade700
            : Colors.green;
    }
  }

  // Get appropriate icon for utility tools
  Widget _getUtilityIcon(String toolIdentifier) {
    switch (toolIdentifier) {
      case 'undo':
        return const Icon(Icons.undo, size: 20);
      case 'redo':
        return const Icon(Icons.redo, size: 20);
      case 'reset_zoom':
        return const Icon(Icons.zoom_out_map, size: 20);
      default:
        return const Icon(Icons.settings, size: 20);
    }
  }

  // Build undo button for zone operations
  Widget _buildUndoButton() {
    return Obx(() {
      final canUndo = widget.controller.canUndoZones.value;
      final lastOperation = widget.controller.lastZoneOperation.value;

      return FloatingActionButton.small(
        heroTag: 'zones_undo_action',
        onPressed: canUndo
            ? () {
                widget.controller.undoZoneOperation();
                _setLastUsedTool(ZoneToolbarRowType.utilities, 'undo');
              }
            : null,
        backgroundColor: canUndo ? Colors.indigo : Colors.grey,
        tooltip: canUndo
            ? (lastOperation != null
                ? 'zonesEditor_toolbar_undo_withOperation'
                    .trParams({'operation': lastOperation})
                : 'zonesEditor_toolbar_undo_available'.tr)
            : 'zonesEditor_toolbar_undo_unavailable'.tr,
        child: const Icon(Icons.undo, size: 20),
      );
    });
  }

  // Build redo button for zone operations
  Widget _buildRedoButton() {
    return Obx(() {
      final canRedo = widget.controller.canRedoZones.value;
      final historyManager = widget.controller.shapeController
          .knittingInstructionsManager.zoneHistoryManager;
      final nextOperation = historyManager.getNextRedoOperationName();

      return FloatingActionButton.small(
        heroTag: 'zones_redo_action',
        onPressed: canRedo
            ? () {
                widget.controller.redoZoneOperation();
                _setLastUsedTool(ZoneToolbarRowType.utilities, 'redo');
              }
            : null,
        backgroundColor: canRedo ? Colors.indigo : Colors.grey,
        tooltip: canRedo
            ? (nextOperation != null
                ? 'zonesEditor_toolbar_redo_withOperation'
                    .trParams({'operation': nextOperation})
                : 'zonesEditor_toolbar_redo_available'.tr)
            : 'zonesEditor_toolbar_redo_unavailable'.tr,
        child: const Icon(Icons.redo, size: 20),
      );
    });
  }

  // Public method to collapse all expanded rows
  void collapseAllRows() {
    if (_rowExpandedStates.values.any((expanded) => expanded)) {
      setState(() {
        for (var type in ZoneToolbarRowType.values) {
          _rowExpandedStates[type] = false;
          _rowAnimControllers[type]?.reverse();
        }
      });
    }
  }
}
