import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/shape_editor_controller.dart';

class PointEditingModal extends StatelessWidget {
  final VoidCallback? onCancel;
  final VoidCallback? onDone;

  const PointEditingModal({
    super.key,
    this.onCancel,
    this.onDone,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(16.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.green.shade300,
        borderRadius: BorderRadius.circular(12.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8.0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Instruction text
          const Text(
            'Select any point by touching it. Drag it in any direction. Adjust values in the box.',
            style: TextStyle(
              color: Colors.black87,
              fontSize: 16.0,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16.0),

          // Divider line
          Container(
            height: 2.0,
            width: double.infinity,
            color: Colors.black54,
          ),

          const SizedBox(height: 16.0),

          // Cancel and Done buttons
          Row(
            children: [
              // Cancel button
              Expanded(
                child: GestureDetector(
                  onTap: onCancel ??
                      () {
                        final controller = Get.find<ShapeEditorController>();
                        controller.exitCurrentHandleMode();
                      },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12.0),
                    alignment: Alignment.center,
                    child: const Text(
                      'Cancel',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18.0,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),

              // Divider between buttons
              Container(
                width: 2.0,
                height: 40.0,
                color: Colors.black54,
              ),

              // Done button
              Expanded(
                child: GestureDetector(
                  onTap: onDone ??
                      () {
                        final controller = Get.find<ShapeEditorController>();
                        controller.exitCurrentHandleMode();
                      },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12.0),
                    alignment: Alignment.center,
                    child: const Text(
                      'Done',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18.0,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
