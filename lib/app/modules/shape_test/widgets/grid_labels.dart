import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:math' as math;

import 'package:xoxknit/app/modules/shape_test/painters/grid_system.dart';

/// An enhanced grid label widget that displays needle positions and row numbers
class GridLabelsWidget extends StatelessWidget {
  final GridSystem gridSystem;
  final double zoomLevel;
  final Offset panOffset;
  final bool showLabels;
  final RxBool isInteracting;

  const GridLabelsWidget({
    super.key,
    required this.gridSystem,
    required this.zoomLevel,
    required this.panOffset,
    required this.showLabels,
    required this.isInteracting,
  });

  @override
  Widget build(BuildContext context) {
    if (!showLabels) return const SizedBox.shrink();

    final size = MediaQuery.of(context).size;

    return IgnorePointer(
      child: Stack(
        children: [
          // Needle labels (top)
          Positioned(
            left: 0,
            right: 0,
            top: 0,
            height: 30,
            child: CustomPaint(
              painter: <PERSON><PERSON><PERSON>abe<PERSON><PERSON>ainter(
                gridSystem: gridSystem,
                zoomLevel: zoomLevel,
                panOffset: panOffset,
                isInteracting: isInteracting,
                size: size,
              ),
            ),
          ),

          // Row labels (left)
          Positioned(
            left: 0,
            top: 30,
            bottom: 0,
            width: 40,
            child: CustomPaint(
              painter: RowLabelPainter(
                gridSystem: gridSystem,
                zoomLevel: zoomLevel,
                panOffset: panOffset,
                isInteracting: isInteracting,
                size: size,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Painter for needle labels at the top of the grid
class NeedleLabelPainter extends CustomPainter {
  final GridSystem gridSystem;
  final double zoomLevel;
  final Offset panOffset;
  final RxBool isInteracting;
  final Size size;

  NeedleLabelPainter({
    required this.gridSystem,
    required this.zoomLevel,
    required this.panOffset,
    required this.isInteracting,
    required this.size,
  });

  @override
  void paint(Canvas canvas, Size canvasSize) {
    // Only proceed if we have valid needle count
    final needleCount = gridSystem.needleCount;
    if (needleCount == null || needleCount <= 0) return;

    // Calculate center position
    final centerX = size.width / 2 + panOffset.dx;

    // Get grid sizing
    final effectiveGridSize = gridSystem.effectiveGridSize;

    // Calculate left edge of the needle grid
    final totalWidth = needleCount * effectiveGridSize;
    final leftEdge = centerX - (totalWidth / 2);

    // Prepare text styles
    final labelStyle = TextStyle(
      color: Colors.black87.withOpacity(isInteracting.value ? 0.3 : 0.6),
      fontSize: math.max(10.0, 10.0 * zoomLevel),
      fontWeight: FontWeight.w600,
    );

    final TextStyle centerLabelStyle = TextStyle(
      color: Colors.blue.shade700.withOpacity(isInteracting.value ? 0.5 : 0.8),
      fontSize: math.max(11.0, 11.0 * zoomLevel),
      fontWeight: FontWeight.bold,
    );

    // Determine label interval based on zoom level
    int interval = _calculateLabelInterval(zoomLevel);

    // Draw needle labels
    for (int needle = 0; needle < needleCount; needle++) {
      // Skip labels based on interval for readability
      if (needle % interval != 0) continue;

      // Calculate x position
      final needleX = leftEdge + (needle * effectiveGridSize);

      // Skip if outside visible range
      if (needleX < 0 || needleX > size.width) continue;

      // Format label text - convert 0-based index to L/R notation
      String label;
      TextStyle style;

      if (needle == needleCount ~/ 2) {
        // Center needle (special case)
        label = "C";
        style = centerLabelStyle;
      } else if (needle < needleCount ~/ 2) {
        // Left side needles
        int leftNeedle = (needleCount ~/ 2) - needle;
        label = "L$leftNeedle";
        style = labelStyle;
      } else {
        // Right side needles
        int rightNeedle = needle - (needleCount ~/ 2);
        label = "R$rightNeedle";
        style = labelStyle;
      }

      // Create and paint text
      final textSpan = TextSpan(
        text: label,
        style: style,
      );

      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();

      // Center the text on the needle position
      textPainter.paint(
        canvas,
        Offset(needleX - (textPainter.width / 2), 5),
      );
    }
  }

  /// Calculate an appropriate label interval based on zoom level
  int _calculateLabelInterval(double zoom) {
    if (zoom >= 1.5) return 1; // Show every needle
    if (zoom >= 1.0) return 2; // Show every 2nd needle
    if (zoom >= 0.7) return 5; // Show every 5th needle
    if (zoom >= 0.5) return 10; // Show every 10th needle
    return 20; // Show every 20th needle
  }

  @override
  bool shouldRepaint(covariant NeedleLabelPainter oldDelegate) {
    return oldDelegate.zoomLevel != zoomLevel ||
        oldDelegate.panOffset != panOffset ||
        oldDelegate.isInteracting.value != isInteracting.value;
  }
}

/// Painter for row labels on the left side of the grid
class RowLabelPainter extends CustomPainter {
  final GridSystem gridSystem;
  final double zoomLevel;
  final Offset panOffset;
  final RxBool isInteracting;
  final Size size;

  RowLabelPainter({
    required this.gridSystem,
    required this.zoomLevel,
    required this.panOffset,
    required this.isInteracting,
    required this.size,
  });

  @override
  void paint(Canvas canvas, Size canvasSize) {
    // Get grid sizing
    final effectiveGridSize = gridSystem.effectiveGridSize;

    // Calculate center position
    final centerY = size.height / 2 + panOffset.dy;

    // Calculate grid offset from center
    final gridOffsetY = centerY % effectiveGridSize;

    // Prepare text style
    final labelStyle = TextStyle(
      color: Colors.black87.withOpacity(isInteracting.value ? 0.3 : 0.6),
      fontSize: math.max(10.0, 10.0 * zoomLevel),
      fontWeight: FontWeight.w600,
    );

    // Determine label interval based on zoom level
    int interval = _calculateLabelInterval(zoomLevel);

    // Calculate visible rows
    final visibleRowsAbove = (centerY / effectiveGridSize).ceil();
    final visibleRowsBelow =
        ((size.height - centerY) / effectiveGridSize).ceil();
    final totalVisibleRows = visibleRowsAbove + visibleRowsBelow;

    // Draw row labels
    for (int i = -visibleRowsAbove; i <= visibleRowsBelow; i++) {
      // Skip labels based on interval for readability
      if (i % interval != 0) continue;

      // Calculate y position
      final rowY = gridOffsetY + (i * effectiveGridSize);

      // Skip if outside visible range
      if (rowY < 0 || rowY > size.height) continue;

      // Row numbers start from 1 at the top
      // We reverse the direction because in knitting, row 1
      // is typically at the bottom and increases upward
      final rowNumber = totalVisibleRows - i;

      // Create and paint text
      final textSpan = TextSpan(
        text: rowNumber.toString(),
        style: labelStyle,
      );

      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();

      // Right-align the row number
      textPainter.paint(
        canvas,
        Offset(35 - textPainter.width, rowY - (textPainter.height / 2)),
      );
    }
  }

  /// Calculate an appropriate label interval based on zoom level
  int _calculateLabelInterval(double zoom) {
    if (zoom >= 1.2) return 1; // Show every row
    if (zoom >= 0.8) return 2; // Show every 2nd row
    if (zoom >= 0.5) return 5; // Show every 5th row
    if (zoom >= 0.3) return 10; // Show every 10th row
    return 20; // Show every 20th row
  }

  @override
  bool shouldRepaint(covariant RowLabelPainter oldDelegate) {
    return oldDelegate.zoomLevel != zoomLevel ||
        oldDelegate.panOffset != panOffset ||
        oldDelegate.isInteracting.value != isInteracting.value;
  }
}
