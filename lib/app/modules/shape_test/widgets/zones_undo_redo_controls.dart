import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/zones_editor_controller.dart';

class ZonesUndoRedoControls extends StatelessWidget {
  final ZonesEditorController controller;
  final bool isHorizontal;

  const ZonesUndoRedoControls({
    super.key,
    required this.controller,
    this.isHorizontal = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isHorizontal) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildUndoButton(context),
          const SizedBox(width: 8),
          _buildRedoButton(context),
        ],
      );
    } else {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildUndoButton(context),
          const SizedBox(height: 8),
          _buildRedoButton(context),
        ],
      );
    }
  }

  Widget _buildUndoButton(BuildContext context) {
    return Obx(() {
      final canUndo = controller.canUndoZones.value;
      final lastOperation = controller.lastZoneOperation.value;

      return Tooltip(
        message: canUndo
            ? 'Undo: ${lastOperation ?? 'Last Operation'}'
            : 'Nothing to undo',
        child: IconButton(
          onPressed: canUndo ? controller.undoZoneOperation : null,
          icon: Icon(
            Icons.undo,
            color: canUndo
                ? Theme.of(context).primaryColor
                : Theme.of(context).disabledColor,
          ),
          splashRadius: 20,
          constraints: const BoxConstraints(
            minWidth: 40,
            minHeight: 40,
          ),
        ),
      );
    });
  }

  Widget _buildRedoButton(BuildContext context) {
    return Obx(() {
      final canRedo = controller.canRedoZones.value;
      final historyManager = controller
          .shapeController.knittingInstructionsManager.zoneHistoryManager;
      final redoOperation = historyManager.getNextRedoOperationName();

      return Tooltip(
        message: canRedo
            ? 'Redo: ${redoOperation ?? 'Next Operation'}'
            : 'Nothing to redo',
        child: IconButton(
          onPressed: canRedo ? controller.redoZoneOperation : null,
          icon: Icon(
            Icons.redo,
            color: canRedo
                ? Theme.of(context).primaryColor
                : Theme.of(context).disabledColor,
          ),
          splashRadius: 20,
          constraints: const BoxConstraints(
            minWidth: 40,
            minHeight: 40,
          ),
        ),
      );
    });
  }
}

/// Compact version for toolbar integration
class CompactZonesUndoRedoControls extends StatelessWidget {
  final ZonesEditorController controller;

  const CompactZonesUndoRedoControls({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildCompactButton(
          context: context,
          icon: Icons.undo,
          onPressed: () => controller.undoZoneOperation(),
          enabled: controller.canUndoZones,
          tooltip: 'Undo Zone Operation',
        ),
        const SizedBox(width: 4),
        _buildCompactButton(
          context: context,
          icon: Icons.redo,
          onPressed: () => controller.redoZoneOperation(),
          enabled: controller.canRedoZones,
          tooltip: 'Redo Zone Operation',
        ),
      ],
    );
  }

  Widget _buildCompactButton({
    required BuildContext context,
    required IconData icon,
    required VoidCallback onPressed,
    required RxBool enabled,
    required String tooltip,
  }) {
    return Obx(() {
      final isEnabled = enabled.value;

      return Tooltip(
        message: tooltip,
        child: InkWell(
          onTap: isEnabled ? onPressed : null,
          borderRadius: BorderRadius.circular(4),
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: isEnabled
                  ? Theme.of(context).primaryColor.withOpacity(0.1)
                  : Colors.transparent,
              border: Border.all(
                color: isEnabled
                    ? Theme.of(context).primaryColor.withOpacity(0.3)
                    : Theme.of(context).disabledColor.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Icon(
              icon,
              size: 18,
              color: isEnabled
                  ? Theme.of(context).primaryColor
                  : Theme.of(context).disabledColor,
            ),
          ),
        ),
      );
    });
  }
}
