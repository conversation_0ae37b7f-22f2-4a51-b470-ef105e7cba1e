import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'help_widget.dart';

/// A persistent help button that opens the help widget
class HelpButton extends StatelessWidget {
  // Control where the button appears
  final bool isAppBar;

  const HelpButton({
    super.key,
    this.isAppBar = true,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: 'help_title'.tr,
      child: isAppBar
          ? IconButton(
              icon: const Icon(Icons.help_outline),
              onPressed: () => _showHelp(context),
            )
          : FloatingActionButton(
              mini: true,
              heroTag: 'help_fab',
              backgroundColor: Theme.of(context).colorScheme.primaryContainer,
              foregroundColor: Theme.of(context).colorScheme.onPrimaryContainer,
              elevation: 4,
              child: const Icon(Icons.help_outline),
              onPressed: () => _showHelp(context),
            ),
    );
  }

  void _showHelp(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    if (isSmallScreen) {
      // For small screens, show as a full-screen page
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const HelpWidget(isFullScreen: true),
        ),
      );
    } else {
      // For larger screens, show as an overlay panel
      showDialog(
        context: context,
        barrierColor: Colors.transparent,
        builder: (context) => const Stack(
          children: [
            // Transparent overlay to capture taps outside the help panel
            Positioned.fill(
              child: ModalBarrier(
                dismissible: true,
                color: Colors.black12,
              ),
            ),
            // Help panel with default positioning
            HelpWidget(isFullScreen: false),
          ],
        ),
      );
    }
  }
}
