import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/shape_editor_controller.dart';

/// Widget that handles the creation of custom polygon shapes
/// Displays an overlay with buttons to cancel or finish the shape
class CustomShapeCreator extends StatelessWidget {
  final ShapeEditorController controller;
  final Function onCancel;
  final Function onFinish;

  const CustomShapeCreator({
    Key? key,
    required this.controller,
    required this.onCancel,
    required this.onFinish,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 16,
      left: 0,
      right: 0,
      child: SafeArea(
        child: Center(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Instructions
                Obx(() {
                  String instruction;
                  if (controller.selectedCustomShapeVertexIndex.value >= 0) {
                    instruction = 'customShapeCreator_selectedVertex'.tr;
                  } else if (controller.customShapeVertices.length < 3) {
                    instruction = 'customShapeCreator_addMorePoints'.tr;
                  } else {
                    instruction = 'customShapeCreator_instructions'.tr;
                  }
                  return Text(
                    instruction,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                    textAlign: TextAlign.center,
                  );
                }),
                const SizedBox(height: 8),
                // Main row with point count and controls
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Undo button
                    Obx(() => IconButton(
                          onPressed: controller.canUndoCustomShapeVertex()
                              ? () => controller.undoCustomShapeVertex()
                              : null,
                          icon: const Icon(Icons.undo, size: 20),
                          tooltip: 'customShapeCreator_undo'.tr,
                          constraints: const BoxConstraints(
                            minWidth: 32,
                            minHeight: 32,
                          ),
                        )),
                    // Redo button
                    Obx(() => IconButton(
                          onPressed: controller.canRedoCustomShapeVertex()
                              ? () => controller.redoCustomShapeVertex()
                              : null,
                          icon: const Icon(Icons.redo, size: 20),
                          tooltip: 'customShapeCreator_redo'.tr,
                          constraints: const BoxConstraints(
                            minWidth: 32,
                            minHeight: 32,
                          ),
                        )),
                    const SizedBox(width: 8),
                    // Display point count
                    Obx(() => Text(
                          'customShapeCreator_pointsAdded'.trParams({
                            'count':
                                controller.customShapeVertices.length.toString()
                          }),
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        )),
                    const SizedBox(width: 8),
                    // Delete selected vertex button
                    Obx(() => controller.selectedCustomShapeVertexIndex.value >=
                            0
                        ? IconButton(
                            onPressed: () =>
                                controller.deleteSelectedCustomShapeVertex(),
                            icon: const Icon(Icons.delete,
                                size: 20, color: Colors.red),
                            tooltip: 'customShapeCreator_deleteVertex'.tr,
                            constraints: const BoxConstraints(
                              minWidth: 32,
                              minHeight: 32,
                            ),
                          )
                        : const SizedBox(width: 32)),
                  ],
                ),
                const SizedBox(height: 8),
                // Action buttons row
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Cancel button
                    ElevatedButton.icon(
                      onPressed: () => onCancel(),
                      icon: const Icon(Icons.cancel, color: Colors.red),
                      label: Text('customShapeCreator_cancel'.tr),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: Colors.red,
                        elevation: 0,
                        side: const BorderSide(color: Colors.red),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Finish button
                    Obx(() => ElevatedButton.icon(
                          onPressed: controller.customShapeVertices.length >= 3
                              ? () => onFinish()
                              : null,
                          icon: const Icon(Icons.check, color: Colors.white),
                          label: Text('customShapeCreator_finished'.tr),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                            disabledBackgroundColor: Colors.grey,
                            elevation: 0,
                          ),
                        )),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Widget to render the vertices and lines of the shape being created
class CustomShapePreview extends StatelessWidget {
  final List<Offset> vertices;
  final bool connectToFirst;
  final int selectedVertexIndex;
  final Function(int)? onVertexTap;
  final Function(int)? onVertexDragStart;
  final Function(int, Offset)? onVertexDragUpdate;
  final Function()? onVertexDragEnd;

  const CustomShapePreview({
    Key? key,
    required this.vertices,
    this.connectToFirst = false,
    this.selectedVertexIndex = -1,
    this.onVertexTap,
    this.onVertexDragStart,
    this.onVertexDragUpdate,
    this.onVertexDragEnd,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Draw the lines and static vertices
        CustomPaint(
          painter: CustomShapePreviewPainter(
            vertices: vertices,
            connectToFirst: connectToFirst,
            selectedVertexIndex: selectedVertexIndex,
          ),
          child: Container(),
        ),
        // Add interactive vertex handles
        ...vertices.asMap().entries.map((entry) {
          final index = entry.key;
          final vertex = entry.value;
          return Positioned(
            left: vertex.dx - 15, // Half of the hit area
            top: vertex.dy - 15,
            child: GestureDetector(
              onTap: () => onVertexTap?.call(index),
              onPanStart: (_) => onVertexDragStart?.call(index),
              onPanUpdate: (details) {
                // Use absolute position from global coordinates
                // Convert the global position to local coordinates relative to the canvas
                final RenderBox? renderBox =
                    context.findRenderObject() as RenderBox?;
                if (renderBox != null) {
                  final localPosition =
                      renderBox.globalToLocal(details.globalPosition);
                  onVertexDragUpdate?.call(index, localPosition);
                } else {
                  // Fallback: use original vertex position + accumulated delta
                  // This is less smooth but works if renderBox is not available
                  onVertexDragUpdate?.call(
                    index,
                    Offset(vertex.dx + details.delta.dx,
                        vertex.dy + details.delta.dy),
                  );
                }
              },
              onPanEnd: (_) => onVertexDragEnd?.call(),
              child: Container(
                width: 30,
                height: 30,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.transparent, // Invisible hit area
                ),
              ),
            ),
          );
        }).toList(),
      ],
    );
  }
}

/// Painter for the custom shape preview
class CustomShapePreviewPainter extends CustomPainter {
  final List<Offset> vertices;
  final bool connectToFirst;
  final int selectedVertexIndex;

  CustomShapePreviewPainter({
    required this.vertices,
    this.connectToFirst = false,
    this.selectedVertexIndex = -1,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (vertices.isEmpty) return;

    // Paint for the lines
    final linePaint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    // Paint for the points
    final pointPaint = Paint()
      ..color = Colors.red
      ..strokeWidth = 2.0
      ..style = PaintingStyle.fill;

    // Paint for the first point (different color)
    final firstPointPaint = Paint()
      ..color = Colors.green
      ..strokeWidth = 2.0
      ..style = PaintingStyle.fill;

    // Paint for the selected point
    final selectedPointPaint = Paint()
      ..color = Colors.orange
      ..strokeWidth = 3.0
      ..style = PaintingStyle.fill;

    // Paint for the selected point outline
    final selectedPointOutlinePaint = Paint()
      ..color = Colors.white
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    // Draw the lines between points
    final path = Path();
    path.moveTo(vertices[0].dx, vertices[0].dy);

    for (int i = 1; i < vertices.length; i++) {
      path.lineTo(vertices[i].dx, vertices[i].dy);
    }

    // If needed, connect back to the first point
    if (connectToFirst && vertices.length > 2) {
      path.close();
    }

    canvas.drawPath(path, linePaint);

    // Draw points on top of lines
    for (int i = 0; i < vertices.length; i++) {
      final point = vertices[i];

      if (i == selectedVertexIndex) {
        // Draw selected vertex with special styling
        canvas.drawCircle(point, 8.0, selectedPointPaint);
        canvas.drawCircle(point, 8.0, selectedPointOutlinePaint);
      } else if (i == 0) {
        // Draw first vertex (green)
        canvas.drawCircle(point, 6.0, firstPointPaint);
      } else {
        // Draw regular vertex (red)
        canvas.drawCircle(point, 6.0, pointPaint);
      }
    }
  }

  @override
  bool shouldRepaint(CustomShapePreviewPainter oldDelegate) {
    return vertices != oldDelegate.vertices ||
        connectToFirst != oldDelegate.connectToFirst ||
        selectedVertexIndex != oldDelegate.selectedVertexIndex;
  }
}

/// Widget to render vertex edit handles for existing shapes
class VertexEditHandles extends StatelessWidget {
  final List<Offset> vertices;
  final int selectedVertexIndex;
  final Function(int)? onVertexTap;
  final Function(int, Offset)? onVertexDragStart;
  final Function(int, Offset)? onVertexDragUpdate;
  final Function()? onVertexDragEnd;

  const VertexEditHandles({
    Key? key,
    required this.vertices,
    this.selectedVertexIndex = -1,
    this.onVertexTap,
    this.onVertexDragStart,
    this.onVertexDragUpdate,
    this.onVertexDragEnd,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Draw the vertex handles
        CustomPaint(
          painter: VertexEditHandlesPainter(
            vertices: vertices,
            selectedVertexIndex: selectedVertexIndex,
          ),
          child: Container(),
        ),
        // Add interactive vertex handles
        ...vertices.asMap().entries.map((entry) {
          final index = entry.key;
          final vertex = entry.value;
          return Positioned(
            left: vertex.dx - 15, // Half of the hit area
            top: vertex.dy - 15,
            child: GestureDetector(
              onTap: () => onVertexTap?.call(index),
              onPanStart: (details) {
                // Convert the global position to local coordinates
                final RenderBox? renderBox =
                    context.findRenderObject() as RenderBox?;
                if (renderBox != null) {
                  final localPosition =
                      renderBox.globalToLocal(details.globalPosition);
                  onVertexDragStart?.call(index, localPosition);
                } else {
                  onVertexDragStart?.call(index, vertex);
                }
              },
              onPanUpdate: (details) {
                // Convert the global position to local coordinates
                final RenderBox? renderBox =
                    context.findRenderObject() as RenderBox?;
                if (renderBox != null) {
                  final localPosition =
                      renderBox.globalToLocal(details.globalPosition);
                  onVertexDragUpdate?.call(index, localPosition);
                } else {
                  // Fallback: use original vertex position + accumulated delta
                  onVertexDragUpdate?.call(
                    index,
                    Offset(vertex.dx + details.delta.dx,
                        vertex.dy + details.delta.dy),
                  );
                }
              },
              onPanEnd: (_) => onVertexDragEnd?.call(),
              child: Container(
                width: 30,
                height: 30,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.transparent, // Invisible hit area
                ),
              ),
            ),
          );
        }).toList(),
      ],
    );
  }
}

/// Painter for vertex edit handles
class VertexEditHandlesPainter extends CustomPainter {
  final List<Offset> vertices;
  final int selectedVertexIndex;

  VertexEditHandlesPainter({
    required this.vertices,
    this.selectedVertexIndex = -1,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (vertices.isEmpty) return;

    // Paint for regular vertex handles
    final handlePaint = Paint()
      ..color = Colors.blue.shade400
      ..strokeWidth = 2.0
      ..style = PaintingStyle.fill;

    // Paint for handle outlines
    final outlinePaint = Paint()
      ..color = Colors.white
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    // Paint for the selected vertex handle
    final selectedHandlePaint = Paint()
      ..color = Colors.orange.shade400
      ..strokeWidth = 3.0
      ..style = PaintingStyle.fill;

    // Paint for the selected handle outline
    final selectedOutlinePaint = Paint()
      ..color = Colors.white
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    // Draw vertex handles
    for (int i = 0; i < vertices.length; i++) {
      final vertex = vertices[i];

      if (i == selectedVertexIndex) {
        // Draw selected vertex with special styling
        canvas.drawCircle(vertex, 8.0, selectedHandlePaint);
        canvas.drawCircle(vertex, 8.0, selectedOutlinePaint);
      } else {
        // Draw regular vertex handle
        canvas.drawCircle(vertex, 6.0, handlePaint);
        canvas.drawCircle(vertex, 6.0, outlinePaint);
      }
    }
  }

  @override
  bool shouldRepaint(VertexEditHandlesPainter oldDelegate) {
    return vertices != oldDelegate.vertices ||
        selectedVertexIndex != oldDelegate.selectedVertexIndex;
  }
}
