import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/zones_editor_controller.dart';

class ZoneContextMenu extends StatefulWidget {
  final ZonesEditorController controller;
  final VoidCallback onDismiss;

  // Define a breakpoint for switching layouts
  static const double horizontalLayoutBreakpoint = 600.0;

  const ZoneContextMenu({
    super.key,
    required this.controller,
    required this.onDismiss,
  });

  @override
  State<ZoneContextMenu> createState() => _ZoneContextMenuState();
}

class _ZoneContextMenuState extends State<ZoneContextMenu>
    with SingleTickerProviderStateMixin {
  // Add state variables
  int _currentPageIndex = 0; // 0 for primary, 1 for secondary
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _togglePage(int newPageIndex) {
    if (_currentPageIndex != newPageIndex) {
      setState(() {
        _currentPageIndex = newPageIndex;
      });
      if (newPageIndex == 1) {
        _slideAnimation = Tween<Offset>(
          begin: const Offset(1.0, 0.0),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _animationController,
          curve: Curves.easeInOut,
        ));
        _animationController.forward(from: 0.0);
      } else {
        _slideAnimation = Tween<Offset>(
          begin: const Offset(-1.0, 0.0),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _animationController,
          curve: Curves.easeInOut,
        ));
        _animationController.forward(from: 0.0);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.controller.isZoneContextMenuVisible.value &&
          widget.controller.zoneContextMenuKey.currentContext != null &&
          context.mounted) {
        widget.controller.calculateAndSetZoneMenuPosition(context);
      }
    });

    final screenWidth = MediaQuery.of(context).size.width;
    final bool useHorizontalLayout =
        screenWidth < ZoneContextMenu.horizontalLayoutBreakpoint;

    return Obx(() {
      final Offset currentPosition =
          widget.controller.calculatedZoneContextMenuPosition.value ??
              Offset.zero;

      if (!widget.controller.isZoneContextMenuVisible.value ||
          widget.controller.calculatedZoneContextMenuPosition.value == null) {
        return const SizedBox.shrink();
      }

      return Stack(
        children: [
          // Overlay for capturing taps outside the menu
          Positioned.fill(
            child: GestureDetector(
              onTap: widget.onDismiss,
              behavior: HitTestBehavior.translucent,
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),

          // The actual menu
          Positioned(
            left: currentPosition.dx,
            top: currentPosition.dy,
            child: GestureDetector(
              onTap: () {}, // Prevent taps on menu background closing it
              behavior: HitTestBehavior.opaque,
              child: AnimatedSize(
                key: widget.controller.zoneContextMenuKey,
                duration: const Duration(milliseconds: 150),
                curve: Curves.easeInOut,
                child: Material(
                  elevation: 5,
                  borderRadius:
                      BorderRadius.circular(useHorizontalLayout ? 10 : 8),
                  clipBehavior: Clip.antiAlias,
                  color: Get.isDarkMode ? Colors.grey[850] : Colors.white,
                  child: Obx(() {
                    final hasSelectedZone =
                        widget.controller.selectedZoneIndex.value >= 0;
                    final isEditingMode = widget.controller.isEditingMode.value;

                    return useHorizontalLayout
                        ? _buildHorizontalMenu(
                            context, hasSelectedZone, isEditingMode)
                        : _buildVerticalMenu(
                            context, hasSelectedZone, isEditingMode);
                  }),
                ),
              ),
            ),
          ),
        ],
      );
    });
  }

  Widget _buildVerticalMenu(
      BuildContext context, bool hasSelectedZone, bool isEditingMode) {
    final page0Items =
        _buildMenuPage(0, context, hasSelectedZone, isEditingMode, false);
    final page1Items =
        _buildMenuPage(1, context, hasSelectedZone, isEditingMode, false);

    final page0Column = Column(
      key: const ValueKey(0),
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: page0Items,
    );
    final page1Column = Column(
      key: const ValueKey(1),
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: page1Items,
    );

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 200),
        transitionBuilder: (Widget child, Animation<double> animation) {
          final pageKey = child.key as ValueKey<int>;
          final bool isEnteringPage1 = pageKey.value == 1;

          final slideAnimation = Tween<Offset>(
            begin: isEnteringPage1
                ? const Offset(1.0, 0.0)
                : const Offset(-1.0, 0.0),
            end: Offset.zero,
          ).animate(
              CurvedAnimation(parent: animation, curve: Curves.easeInOut));

          return SlideTransition(
            position: slideAnimation,
            child: child,
          );
        },
        child: _currentPageIndex == 0 ? page0Column : page1Column,
      ),
    );
  }

  Widget _buildHorizontalMenu(
      BuildContext context, bool hasSelectedZone, bool isEditingMode) {
    final page0Items =
        _buildMenuPage(0, context, hasSelectedZone, isEditingMode, true);
    final page1Items =
        _buildMenuPage(1, context, hasSelectedZone, isEditingMode, true);

    final page0Row = Row(
      key: const ValueKey(0),
      mainAxisSize: MainAxisSize.min,
      children: page0Items,
    );
    final page1Row = Row(
      key: const ValueKey(1),
      mainAxisSize: MainAxisSize.min,
      children: page1Items,
    );

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 0.0),
        child: AnimatedSwitcher(
          duration: const Duration(milliseconds: 200),
          transitionBuilder: (Widget child, Animation<double> animation) {
            final pageKey = child.key as ValueKey<int>;
            final bool isEnteringPage1 = pageKey.value == 1;

            final slideAnimation = Tween<Offset>(
              begin: isEnteringPage1
                  ? const Offset(1.0, 0.0)
                  : const Offset(-1.0, 0.0),
              end: Offset.zero,
            ).animate(
                CurvedAnimation(parent: animation, curve: Curves.easeInOut));

            return SlideTransition(
              position: slideAnimation,
              child: child,
            );
          },
          child: _currentPageIndex == 0 ? page0Row : page1Row,
        ),
      ),
    );
  }

  List<Widget> _buildMenuPage(int pageIndex, BuildContext context,
      bool hasSelectedZone, bool isEditingMode, bool isHorizontal) {
    final textColor = Get.isDarkMode ? Colors.white : Colors.black87;
    final dividerColor = Get.isDarkMode ? Colors.grey[700] : Colors.grey[300];

    final actions = <String, Widget Function()>{
      'rename': () => _buildMenuItem(
            label: 'Rename Zone',
            icon: Icons.edit,
            onTap: () {
              widget.controller.showZoneRenameDialog();
              widget.onDismiss();
            },
            textColor: textColor,
            isHorizontal: isHorizontal,
          ),
      'delete': () => _buildMenuItem(
            label: 'Delete Zone',
            icon: Icons.delete,
            onTap: () {
              widget.controller.deleteSelectedZone();
              widget.onDismiss();
            },
            textColor: isHorizontal ? Colors.red : textColor,
            isHorizontal: isHorizontal,
          ),
      'save': () => _buildMenuItem(
            label: 'Save Zones',
            icon: Icons.save,
            onTap: () {
              widget.controller.saveZones("Manual save from context menu");
              widget.controller.showFeedback('Zones Saved');
              widget.onDismiss();
            },
            textColor: textColor,
            isHorizontal: isHorizontal,
          ),
      'create_new': () => _buildMenuItem(
            label: 'Create New Zone',
            icon: Icons.add,
            onTap: () {
              widget.controller.startNewZoneCreation();
              widget.onDismiss();
            },
            textColor: textColor,
            isHorizontal: isHorizontal,
          ),
      'toggle_edit': () => _buildMenuItem(
            label: isEditingMode ? 'Exit Edit Mode' : 'Enter Edit Mode',
            icon: isEditingMode ? Icons.check : Icons.edit_note,
            onTap: () {
              widget.controller.toggleEditMode();
              widget.onDismiss();
            },
            textColor: textColor,
            isHorizontal: isHorizontal,
          ),
      'more': () => _buildMenuItem(
            label: 'More',
            icon: Icons.arrow_forward_ios,
            onTap: () => _togglePage(1),
            textColor: textColor,
            isHorizontal: isHorizontal,
            isNavigation: true,
          ),
      'back': () => _buildMenuItem(
            label: 'Back',
            icon: Icons.arrow_back_ios,
            onTap: () => _togglePage(0),
            textColor: textColor,
            isHorizontal: isHorizontal,
            isNavigation: true,
          ),
    };

    List<Widget Function()> pageActions = [];

    if (pageIndex == 0) {
      // Primary page
      if (hasSelectedZone && isEditingMode) {
        // Zone selected in edit mode
        pageActions.add(actions['rename']!);
        pageActions.add(actions['delete']!);
        pageActions.add(actions['save']!);
      } else if (isEditingMode) {
        // Edit mode but no zone selected
        pageActions.add(actions['create_new']!);
        pageActions.add(actions['save']!);
      } else {
        // Not in edit mode
        pageActions.add(actions['toggle_edit']!);
      }

      // Show more button if there are secondary actions
      if (pageActions.length > 2) {
        pageActions.add(actions['more']!);
      }
    } else {
      // Secondary page
      pageActions.add(actions['back']!);

      if (isEditingMode) {
        pageActions.add(actions['toggle_edit']!);
        if (!hasSelectedZone) {
          pageActions.add(actions['create_new']!);
        }
      } else {
        pageActions.add(actions['toggle_edit']!);
      }
    }

    // Build the final list of widgets with dividers
    List<Widget> widgets = [];
    for (int i = 0; i < pageActions.length; i++) {
      widgets.add(pageActions[i]());

      if (i < pageActions.length - 1) {
        if (isHorizontal) {
          bool isCurrentNav = pageActions[i] == actions['more'] ||
              pageActions[i] == actions['back'];
          bool isNextNav = (i + 1 < pageActions.length) &&
              (pageActions[i + 1] == actions['more'] ||
                  pageActions[i + 1] == actions['back']);
          if (!isCurrentNav && !isNextNav) {
            widgets.add(_buildVerticalDivider(dividerColor ?? Colors.grey));
          }
        } else {
          bool isNavigationButton = pageActions[i] == actions['more'] ||
              pageActions[i] == actions['back'];

          if (pageIndex == 1 && ((i == 0 && isNavigationButton) || i > 0)) {
            widgets.add(_buildHorizontalDivider(dividerColor ?? Colors.grey));
          } else if (pageIndex == 0 && i < pageActions.length - 1) {
            widgets.add(_buildHorizontalDivider(dividerColor ?? Colors.grey));
          }
        }
      }
    }
    return widgets;
  }

  Widget _buildMenuItem({
    required String label,
    IconData? icon,
    required VoidCallback onTap,
    required Color textColor,
    bool enabled = true,
    required bool isHorizontal,
    bool isNavigation = false,
    double? iconRotation,
  }) {
    final Color currentTextColor =
        enabled ? textColor : textColor.withOpacity(0.4);
    final Color? specificColor =
        (label.contains('Delete') && isHorizontal) ? Colors.red : null;
    final Color effectiveTextColor = specificColor ?? currentTextColor;

    final double verticalPadding = isHorizontal ? 8.0 : 10.0;
    final double horizontalPadding = isNavigation && isHorizontal ? 8.0 : 14.0;

    Widget child;
    if (isHorizontal) {
      child = isNavigation
          ? icon != null
              ? iconRotation != null
                  ? Transform.rotate(
                      angle: iconRotation,
                      child: Icon(icon, size: 20, color: effectiveTextColor))
                  : Icon(icon, size: 20, color: effectiveTextColor)
              : Text(
                  label,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: effectiveTextColor,
                  ),
                )
          : Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: effectiveTextColor,
              ),
              overflow: TextOverflow.ellipsis,
            );
    } else {
      child = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            iconRotation != null
                ? Transform.rotate(
                    angle: iconRotation,
                    child: Icon(icon, size: 20, color: effectiveTextColor),
                  )
                : Icon(icon, size: 20, color: effectiveTextColor),
            const SizedBox(width: 12),
          ],
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: effectiveTextColor,
            ),
          ),
        ],
      );
    }

    return TextButton(
      onPressed: enabled ? onTap : null,
      style: TextButton.styleFrom(
        padding: EdgeInsets.symmetric(
            horizontal: horizontalPadding, vertical: verticalPadding),
        minimumSize: const Size(0, 36),
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.zero),
        foregroundColor: effectiveTextColor,
        backgroundColor: Colors.transparent,
        disabledForegroundColor: effectiveTextColor.withOpacity(0.5),
        splashFactory:
            isHorizontal ? NoSplash.splashFactory : InkSplash.splashFactory,
      ),
      child: child,
    );
  }

  Widget _buildHorizontalDivider(Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: Container(
        height: 1,
        color: color,
      ),
    );
  }

  Widget _buildVerticalDivider(Color color) {
    return Container(
      height: 20,
      width: 1,
      color: color,
      margin: const EdgeInsets.symmetric(vertical: 8.0),
    );
  }
}
