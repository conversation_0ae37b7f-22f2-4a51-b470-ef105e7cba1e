import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/shape_editor_controller.dart';

class EditSidesModal extends StatelessWidget {
  final VoidCallback? onDismiss;

  const EditSidesModal({
    super.key,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 16,
      right: 16,
      bottom: 16,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: Colors.orange.shade300,
          borderRadius: BorderRadius.circular(12.0),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8.0,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Instruction text
            const Text(
              'Select any edge control point by touching it. Drag in any direction to adjust the curve.',
              style: TextStyle(
                color: Colors.black87,
                fontSize: 16.0,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16.0),

            // Divider line
            Container(
              height: 2.0,
              width: double.infinity,
              color: Colors.black54,
            ),

            const SizedBox(height: 16.0),

            // Done button (full width)
            GestureDetector(
              onTap: onDismiss ??
                  () {
                    final controller = Get.find<ShapeEditorController>();
                    controller.exitCurrentHandleMode();
                  },
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 12.0),
                alignment: Alignment.center,
                child: const Text(
                  'Done',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18.0,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
