import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/zones_editor_controller.dart';

class FeedbackOverlayWidget extends StatelessWidget {
  final ZonesEditorController controller;

  const FeedbackOverlayWidget({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() => controller.showSuccessOverlay.value
        ? Positioned.fill(
            child: Material(
              color: Colors.transparent,
              child: Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 24.0, vertical: 16.0),
                  decoration: BoxDecoration(
                    color: Colors.green.shade700,
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.check_circle,
                          color: Colors.white, size: 48.0),
                      const SizedBox(height: 16.0),
                      Text(
                        controller.feedbackMessage.value,
                        style: const TextStyle(
                            color: Colors.white, fontSize: 16.0),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          )
        : const SizedBox.shrink());
  }
}
