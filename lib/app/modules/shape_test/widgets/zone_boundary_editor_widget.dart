import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/knitting_instructions_manager.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/knitting_zone_models.dart';
import 'package:xoxknit/app/modules/shape_test/models/cell_dimensions.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/zones_editor_controller.dart';

class ZoneBoundaryEditorWidget extends StatefulWidget {
  final List<List<bool>> fullPattern;
  final List<KnittingZone> zones;
  final int selectedZoneIndex;
  final double aspectRatio;
  final Function(int)? onZoneTap;
  final Function(Offset tapPosition, Size canvasSize)? onZoneTapWithPosition;
  final Function(Offset tapPosition, Size canvasSize)? onZoneLongPress;
  final Function(int, String, int)? onZoneBoundaryEdit;
  final ZonesEditorController? controller;

  const ZoneBoundaryEditorWidget({
    super.key,
    required this.fullPattern,
    required this.zones,
    required this.selectedZoneIndex,
    this.aspectRatio = 0.75,
    this.onZoneTap,
    this.onZoneTapWithPosition,
    this.onZoneLongPress,
    required this.onZoneBoundaryEdit,
    this.controller,
  });

  @override
  State<ZoneBoundaryEditorWidget> createState() =>
      _ZoneBoundaryEditorWidgetState();
}

class _ZoneBoundaryEditorWidgetState extends State<ZoneBoundaryEditorWidget> {
  String? _draggingEdge;
  final GlobalKey _drawingAreaKey = GlobalKey();

  // For hover feedback
  final RxString _hoveredEdge = "none".obs;

  @override
  Widget build(BuildContext context) {
    if (widget.fullPattern.isEmpty) {
      return Container(color: Colors.transparent);
    }

    // If a controller is provided, wrap with GetBuilder to listen for updates
    if (widget.controller != null) {
      return GetBuilder<ZonesEditorController>(
        init: widget.controller,
        builder: (_) => _buildEditor(context),
      );
    }

    return _buildEditor(context);
  }

  Widget _buildEditor(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final size = Size(constraints.maxWidth, constraints.maxHeight);

        // Calculate cell dimensions (always needed for zone tap detection)
        final cellDimensions = CellDimensions.calculate(
            size,
            widget.fullPattern[0].length,
            widget.fullPattern.length,
            widget.aspectRatio);

        // Check if we have a valid selected zone for boundary editing
        final hasValidSelection = widget.selectedZoneIndex >= 0 &&
            widget.selectedZoneIndex < widget.zones.length;

        // Get the selected zone if valid
        final zone =
            hasValidSelection ? widget.zones[widget.selectedZoneIndex] : null;

        // Calculate zone boundaries in pixels (only if we have a selected zone)
        double? left, top, right, bottom;
        if (zone != null) {
          left = zone.startNeedle * cellDimensions.cellWidth +
              cellDimensions.offsetX;
          top = zone.startRow * cellDimensions.cellHeight +
              cellDimensions.offsetY;
          right = (zone.endNeedle + 1) * cellDimensions.cellWidth +
              cellDimensions.offsetX;
          bottom = (zone.endRow + 1) * cellDimensions.cellHeight +
              cellDimensions.offsetY;
        }

        // Build the stack with zone tap detection always available
        List<Widget> stackChildren = [
          // Entire area for detecting taps on zones (ALWAYS AVAILABLE)
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTapDown: (details) {
              debugPrint(
                  'ZoneBoundaryEditor: onTapDown detected at ${details.globalPosition}');

              // Check if tap is inside the context menu first
              if (widget.controller
                      ?.isTapInsideZoneContextMenu(details.globalPosition) ==
                  true) {
                return; // Let the menu handle it
              }

              // Hide context menu if visible and tap is outside
              if (widget.controller?.isZoneContextMenuVisible.value == true) {
                widget.controller?.hideZoneContextMenu();
              }
            },
            onTapUp: (details) {
              debugPrint(
                  'ZoneBoundaryEditor: onTapUp detected at ${details.globalPosition}');

              final RenderBox box = context.findRenderObject() as RenderBox;
              final localPos = box.globalToLocal(details.globalPosition);
              final canvasSize = box.size;

              // Use the advanced overlap detection if available
              if (widget.onZoneTapWithPosition != null) {
                debugPrint(
                    'ZoneBoundaryEditor: Using advanced overlap detection');
                widget.onZoneTapWithPosition!(localPos, canvasSize);
                return;
              }

              // Fallback to simple detection
              if (widget.onZoneTap == null) {
                debugPrint(
                    'ZoneBoundaryEditor: No tap callbacks available, returning');
                return;
              }

              debugPrint('ZoneBoundaryEditor: Using simple zone detection');
              debugPrint(
                  'ZoneBoundaryEditor: converted to local position: $localPos');
              debugPrint(
                  'ZoneBoundaryEditor: cellDimensions - width: ${cellDimensions.cellWidth}, height: ${cellDimensions.cellHeight}, offsetX: ${cellDimensions.offsetX}, offsetY: ${cellDimensions.offsetY}');

              bool foundZone = false;
              // Find which zone was tapped
              for (int i = 0; i < widget.zones.length; i++) {
                final z = widget.zones[i];

                // Calculate zone boundaries
                final zLeft = z.startNeedle * cellDimensions.cellWidth +
                    cellDimensions.offsetX;
                final zTop = z.startRow * cellDimensions.cellHeight +
                    cellDimensions.offsetY;
                final zRight = (z.endNeedle + 1) * cellDimensions.cellWidth +
                    cellDimensions.offsetX;
                final zBottom = (z.endRow + 1) * cellDimensions.cellHeight +
                    cellDimensions.offsetY;

                debugPrint(
                    'ZoneBoundaryEditor: Zone $i (${z.name}) boundaries: left=$zLeft, top=$zTop, right=$zRight, bottom=$zBottom');

                // Check if tap is within zone boundaries
                if (localPos.dx >= zLeft &&
                    localPos.dx <= zRight &&
                    localPos.dy >= zTop &&
                    localPos.dy <= zBottom) {
                  debugPrint(
                      'ZoneBoundaryEditor: ✅ TAP DETECTED on zone $i (${z.name})');
                  foundZone = true;
                  widget.onZoneTap!(i);
                  break;
                } else {
                  debugPrint(
                      'ZoneBoundaryEditor: ❌ Tap outside zone $i bounds');
                }
              }

              if (!foundZone) {
                debugPrint(
                    'ZoneBoundaryEditor: ⚠️ No zone found at tap position $localPos');
              }
            },
            onLongPressStart: widget.onZoneLongPress != null
                ? (details) {
                    final RenderBox box =
                        context.findRenderObject() as RenderBox;
                    final localPos = box.globalToLocal(details.globalPosition);
                    final Size size = box.size;

                    widget.onZoneLongPress!(localPos, size);
                  }
                : null,
            child: Container(
              color: Colors.transparent,
              width: double.infinity,
              height: double.infinity,
            ),
          ),
        ];

        // Add boundary editing handles only if we have a valid selection
        if (hasValidSelection &&
            zone != null &&
            left != null &&
            top != null &&
            right != null &&
            bottom != null) {
          stackChildren.addAll([
            // Left resize handle with improved visual feedback
            Positioned(
              left: left - 10, // Make handle larger for easier targeting
              top: top,
              width: 20,
              height: bottom - top,
              child: Obx(() => MouseRegion(
                    cursor: SystemMouseCursors.resizeLeftRight,
                    onEnter: (_) => _hoveredEdge.value = 'left',
                    onExit: (_) => _hoveredEdge.value = _draggingEdge ?? 'none',
                    child: GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onPanStart: (_) {
                        _draggingEdge = 'left';
                        _hoveredEdge.value = 'left';
                      },
                      onPanUpdate: (details) {
                        if (_draggingEdge != 'left') return;
                        final RenderBox? drawingAreaRenderBox =
                            _drawingAreaKey.currentContext?.findRenderObject()
                                as RenderBox?;
                        if (drawingAreaRenderBox == null) return;

                        final localDragPosition = drawingAreaRenderBox
                            .globalToLocal(details.globalPosition);
                        final newGridPosition =
                            ((localDragPosition.dx - cellDimensions.offsetX) /
                                    cellDimensions.cellWidth)
                                .floor();

                        widget.onZoneBoundaryEdit!(
                            widget.selectedZoneIndex, 'left', newGridPosition);
                      },
                      onPanEnd: (_) {
                        _draggingEdge = null;
                        _hoveredEdge.value = 'none';
                      },
                      child: Container(
                        color: Colors.transparent,
                        child: Center(
                          child: Container(
                            width: 4,
                            height: double.infinity,
                            color: _hoveredEdge.value == 'left' ||
                                    _draggingEdge == 'left'
                                ? Colors.blue
                                : Colors.transparent,
                          ),
                        ),
                      ),
                    ),
                  )),
            ),

            // Right resize handle
            Positioned(
              left: right - 10,
              top: top,
              width: 20,
              height: bottom - top,
              child: Obx(() => MouseRegion(
                    cursor: SystemMouseCursors.resizeLeftRight,
                    onEnter: (_) => _hoveredEdge.value = 'right',
                    onExit: (_) => _hoveredEdge.value = _draggingEdge ?? 'none',
                    child: GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onPanStart: (_) {
                        _draggingEdge = 'right';
                        _hoveredEdge.value = 'right';
                      },
                      onPanUpdate: (details) {
                        if (_draggingEdge != 'right') return;
                        final RenderBox? drawingAreaRenderBox =
                            _drawingAreaKey.currentContext?.findRenderObject()
                                as RenderBox?;
                        if (drawingAreaRenderBox == null) return;

                        final localDragPosition = drawingAreaRenderBox
                            .globalToLocal(details.globalPosition);
                        final newGridPosition =
                            ((localDragPosition.dx - cellDimensions.offsetX) /
                                    cellDimensions.cellWidth)
                                .floor();

                        widget.onZoneBoundaryEdit!(
                            widget.selectedZoneIndex, 'right', newGridPosition);
                      },
                      onPanEnd: (_) {
                        _draggingEdge = null;
                        _hoveredEdge.value = 'none';
                      },
                      child: Container(
                        color: Colors.transparent,
                        child: Center(
                          child: Container(
                            width: 4,
                            height: double.infinity,
                            color: _hoveredEdge.value == 'right' ||
                                    _draggingEdge == 'right'
                                ? Colors.blue
                                : Colors.transparent,
                          ),
                        ),
                      ),
                    ),
                  )),
            ),

            // Top resize handle
            Positioned(
              left: left,
              top: top - 10,
              width: right - left,
              height: 20,
              child: Obx(() => MouseRegion(
                    cursor: SystemMouseCursors.resizeUpDown,
                    onEnter: (_) => _hoveredEdge.value = 'top',
                    onExit: (_) => _hoveredEdge.value = _draggingEdge ?? 'none',
                    child: GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onPanStart: (_) {
                        _draggingEdge = 'top';
                        _hoveredEdge.value = 'top';
                      },
                      onPanUpdate: (details) {
                        if (_draggingEdge != 'top') return;
                        final RenderBox? drawingAreaRenderBox =
                            _drawingAreaKey.currentContext?.findRenderObject()
                                as RenderBox?;
                        if (drawingAreaRenderBox == null) return;

                        final localDragPosition = drawingAreaRenderBox
                            .globalToLocal(details.globalPosition);
                        final newGridPosition =
                            ((localDragPosition.dy - cellDimensions.offsetY) /
                                    cellDimensions.cellHeight)
                                .floor();

                        widget.onZoneBoundaryEdit!(
                            widget.selectedZoneIndex, 'top', newGridPosition);
                      },
                      onPanEnd: (_) {
                        _draggingEdge = null;
                        _hoveredEdge.value = 'none';
                      },
                      child: Container(
                        color: Colors.transparent,
                        child: Center(
                          child: Container(
                            width: double.infinity,
                            height: 4,
                            color: _hoveredEdge.value == 'top' ||
                                    _draggingEdge == 'top'
                                ? Colors.blue
                                : Colors.transparent,
                          ),
                        ),
                      ),
                    ),
                  )),
            ),

            // Bottom resize handle
            Positioned(
              left: left,
              top: bottom - 10,
              width: right - left,
              height: 20,
              child: Obx(() => MouseRegion(
                    cursor: SystemMouseCursors.resizeUpDown,
                    onEnter: (_) => _hoveredEdge.value = 'bottom',
                    onExit: (_) => _hoveredEdge.value = _draggingEdge ?? 'none',
                    child: GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onPanStart: (_) {
                        _draggingEdge = 'bottom';
                        _hoveredEdge.value = 'bottom';
                      },
                      onPanUpdate: (details) {
                        if (_draggingEdge != 'bottom') return;
                        final RenderBox? drawingAreaRenderBox =
                            _drawingAreaKey.currentContext?.findRenderObject()
                                as RenderBox?;
                        if (drawingAreaRenderBox == null) return;

                        final localDragPosition = drawingAreaRenderBox
                            .globalToLocal(details.globalPosition);
                        final newGridPosition =
                            ((localDragPosition.dy - cellDimensions.offsetY) /
                                    cellDimensions.cellHeight)
                                .floor();

                        widget.onZoneBoundaryEdit!(widget.selectedZoneIndex,
                            'bottom', newGridPosition);
                      },
                      onPanEnd: (_) {
                        _draggingEdge = null;
                        _hoveredEdge.value = 'none';
                      },
                      child: Container(
                        color: Colors.transparent,
                        child: Center(
                          child: Container(
                            width: double.infinity,
                            height: 4,
                            color: _hoveredEdge.value == 'bottom' ||
                                    _draggingEdge == 'bottom'
                                ? Colors.blue
                                : Colors.transparent,
                          ),
                        ),
                      ),
                    ),
                  )),
            ),
          ]);
        }

        // Return the Stack with all children
        return Stack(
          key: _drawingAreaKey,
          children: stackChildren,
        );
      },
    );
  }
}
