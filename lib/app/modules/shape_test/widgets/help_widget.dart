import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

/// A comprehensive help widget with search functionality and categorized topics
class HelpWidget extends StatefulWidget {
  final bool isFullScreen;

  const HelpWidget({
    super.key,
    this.isFullScreen = false,
  });

  @override
  State<HelpWidget> createState() => _HelpWidgetState();
}

class _HelpWidgetState extends State<HelpWidget> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _isDragging = false;
  Offset _offset = const Offset(0, 0);

  // Keep track of which categories are expanded
  final Map<String, bool> _expandedCategories = {
    'help_categories_gettingStarted': true,
    'help_categories_gridFeatures': false,
    'help_categories_shapeTools': false,
    'help_categories_advancedTechniques': false,
    'help_categories_keyboardShortcuts': false,
  };

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.toLowerCase();

      // If search is active, expand all categories that have matching topics
      if (_searchQuery.isNotEmpty) {
        for (var categoryKey in _helpContent.keys) {
          final categoryTopics = _helpContent[categoryKey]!;
          final hasMatch = categoryTopics.any((topic) =>
              topic.title.toLowerCase().contains(_searchQuery) ||
              topic.description.toLowerCase().contains(_searchQuery));

          if (hasMatch) {
            _expandedCategories[categoryKey] = true;
          }
        }
      }
    });
  }

  bool _topicMatchesSearch(HelpTopic topic) {
    if (_searchQuery.isEmpty) return true;

    return topic.title.toLowerCase().contains(_searchQuery) ||
        topic.description.toLowerCase().contains(_searchQuery);
  }

  @override
  Widget build(BuildContext context) {
    final isDesktop = MediaQuery.of(context).size.width > 600;

    // Determine width based on screen size and mode
    final width = widget.isFullScreen
        ? double.infinity
        : isDesktop
            ? 380.0
            : MediaQuery.of(context).size.width * 0.9;

    // Determine height based on screen size and mode
    final height = widget.isFullScreen
        ? double.infinity
        : isDesktop
            ? MediaQuery.of(context).size.height * 0.8
            : MediaQuery.of(context).size.height * 0.9;

    final helpContent = widget.isFullScreen
        ? _buildFullScreenContent(context)
        : _buildFloatingContent(context, width, height);

    // Wrap with FocusScope and keyboard listener for improved keyboard navigation
    return FocusScope(
      autofocus: true,
      child: KeyboardListener(
        focusNode: FocusNode(),
        onKeyEvent: (event) {
          if (event is KeyDownEvent) {
            if (event.logicalKey == LogicalKeyboardKey.escape) {
              Navigator.of(context).pop();
            }
          }
        },
        child: helpContent,
      ),
    );
  }

  Widget _buildFullScreenContent(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('help_title'.tr),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          Expanded(
            child: _buildHelpContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingContent(
      BuildContext context, double width, double height) {
    return Positioned(
      right: 16 + _offset.dx,
      top: 70 + _offset.dy,
      child: GestureDetector(
        onPanStart: (details) {
          setState(() => _isDragging = true);
        },
        onPanUpdate: (details) {
          setState(() => _offset += details.delta);
        },
        onPanEnd: (details) {
          setState(() => _isDragging = false);
        },
        child: Material(
          elevation: 8,
          borderRadius: BorderRadius.circular(12),
          color: Theme.of(context).scaffoldBackgroundColor,
          child: Container(
            width: width,
            height: height,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildHeader(context),
                _buildSearchBar(),
                Expanded(
                  child: _buildHelpContent(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Get.theme.colorScheme.primaryContainer,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          const Icon(Icons.help_outline, size: 22),
          const SizedBox(width: 8),
          Text(
            'help_title'.tr,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Get.theme.colorScheme.onPrimaryContainer,
            ),
          ),
          const Spacer(),
          // Add a "move handle" indicator to show the panel is draggable
          Icon(
            Icons.drag_indicator,
            color: Get.theme.colorScheme.onPrimaryContainer.withOpacity(0.7),
            size: 20,
          ),
          const SizedBox(width: 8),
          IconButton(
            icon: const Icon(Icons.close, size: 20),
            onPressed: () => Navigator.of(context).pop(),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            color: Get.theme.colorScheme.onPrimaryContainer,
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'help_search_hint'.tr,
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                  },
                  tooltip: 'help_search_clear'.tr,
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          contentPadding:
              const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        ),
        autofocus: widget.isFullScreen,
        keyboardType: TextInputType.text,
        textInputAction: TextInputAction.search,
        style: const TextStyle(fontSize: 16),
      ),
    );
  }

  Widget _buildHelpContent() {
    return ListView(
      padding: const EdgeInsets.only(bottom: 24),
      children: _helpContent.entries.map((entry) {
        final categoryKey = entry.key;
        final topics = entry.value;

        // Filter topics based on search
        final filteredTopics = _searchQuery.isEmpty
            ? topics
            : topics.where((topic) => _topicMatchesSearch(topic)).toList();

        // Skip this category if no topics match the search
        if (filteredTopics.isEmpty) return const SizedBox.shrink();

        return _buildCategorySection(categoryKey, filteredTopics);
      }).toList(),
    );
  }

  Widget _buildCategorySection(String categoryKey, List<HelpTopic> topics) {
    final isExpanded = _expandedCategories[categoryKey] ?? false;
    final categoryIcon = _categoryIcons[categoryKey] ?? Icons.help_outline;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Category header
          InkWell(
            onTap: () {
              setState(() {
                _expandedCategories[categoryKey] = !isExpanded;
              });
            },
            borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
            focusColor: Get.theme.colorScheme.primaryContainer.withOpacity(0.2),
            hoverColor: Get.theme.colorScheme.primaryContainer.withOpacity(0.1),
            child: Semantics(
              button: true,
              label: isExpanded
                  ? '${'help_collapse'.tr} ${categoryKey.tr}'
                  : '${'help_expand'.tr} ${categoryKey.tr}',
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Row(
                  children: [
                    Icon(categoryIcon,
                        size: 20, color: Get.theme.colorScheme.primary),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        categoryKey.tr,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Icon(
                      isExpanded
                          ? Icons.keyboard_arrow_up
                          : Icons.keyboard_arrow_down,
                      color: Colors.grey,
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Category content - only show if expanded
          if (isExpanded)
            ...topics.map((topic) => _buildHelpTopic(topic)).toList(),
        ],
      ),
    );
  }

  Widget _buildHelpTopic(HelpTopic topic) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Small bullet point
              const Padding(
                padding: EdgeInsets.only(top: 5, right: 8),
                child: Icon(Icons.circle, size: 8, color: Colors.grey),
              ),

              // Topic content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      topic.title.tr,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      topic.description.tr,
                      style: const TextStyle(fontSize: 13),
                    ),

                    // Show image if provided
                    if (topic.imagePath != null) ...[
                      const SizedBox(height: 8),
                      ClipRRect(
                        borderRadius: BorderRadius.circular(6),
                        child: Image.asset(
                          topic.imagePath!,
                          height: 120,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
          // Add a divider between topics
          const Divider(height: 16),
        ],
      ),
    );
  }

  // Map of category names to their icons
  final Map<String, IconData> _categoryIcons = {
    'help_categories_gettingStarted': Icons.play_circle_outline,
    'help_categories_gridFeatures': Icons.grid_on,
    'help_categories_shapeTools': Icons.category,
    'help_categories_advancedTechniques': Icons.build,
    'help_categories_keyboardShortcuts': Icons.keyboard,
  };

  // Updated help content organized by categories
  final Map<String, List<HelpTopic>> _helpContent = {
    'help_categories_gettingStarted': [
      HelpTopic(
        title: 'help_topics_gettingStarted_addingShapes_title',
        description: 'help_topics_gettingStarted_addingShapes_description',
        imagePath: null,
      ),
      HelpTopic(
        title: 'help_topics_gettingStarted_selectingShapes_title',
        description: 'help_topics_gettingStarted_selectingShapes_description',
        imagePath: null,
      ),
      HelpTopic(
        title: 'help_topics_gettingStarted_usingToolbar_title',
        description: 'help_topics_gettingStarted_usingToolbar_description',
        imagePath: null,
      ),
      HelpTopic(
        title: 'help_topics_gettingStarted_multiSelection_title',
        description: 'help_topics_gettingStarted_multiSelection_description',
        imagePath: null,
      ),
    ],
    'help_categories_gridFeatures': [
      HelpTopic(
        title: 'help_topics_gridFeatures_gridSnapping_title',
        description: 'help_topics_gridFeatures_gridSnapping_description',
        imagePath: null,
      ),
      HelpTopic(
        title: 'help_topics_gridFeatures_centerSnapping_title',
        description: 'help_topics_gridFeatures_centerSnapping_description',
        imagePath: null,
      ),
      HelpTopic(
        title: 'help_topics_gridFeatures_zoomAndPan_title',
        description: 'help_topics_gridFeatures_zoomAndPan_description',
        imagePath: null,
      ),
      HelpTopic(
        title: 'help_topics_gridFeatures_needlePositioning_title',
        description: 'help_topics_gridFeatures_needlePositioning_description',
        imagePath: null,
      ),
      HelpTopic(
        title: 'help_topics_gridFeatures_unitToggle_title',
        description: 'help_topics_gridFeatures_unitToggle_description',
        imagePath: null,
      ),
    ],
    'help_categories_shapeTools': [
      HelpTopic(
        title: 'help_topics_shapeTools_availableShapes_title',
        description: 'help_topics_shapeTools_availableShapes_description',
        imagePath: null,
      ),
      HelpTopic(
        title: 'help_topics_shapeTools_transformations_title',
        description: 'help_topics_shapeTools_transformations_description',
        imagePath: null,
      ),
      HelpTopic(
        title: 'help_topics_shapeTools_propertyHud_title',
        description: 'help_topics_shapeTools_propertyHud_description',
        imagePath: null,
      ),
      HelpTopic(
        title: 'help_topics_shapeTools_rotation_title',
        description: 'help_topics_shapeTools_rotation_description',
        imagePath: null,
      ),
      HelpTopic(
        title: 'help_topics_shapeTools_resizing_title',
        description: 'help_topics_shapeTools_resizing_description',
        imagePath: null,
      ),
    ],
    'help_categories_advancedTechniques': [
      HelpTopic(
        title: 'help_topics_advancedTechniques_curveMode_title',
        description: 'help_topics_advancedTechniques_curveMode_description',
        imagePath: null,
      ),
      HelpTopic(
        title: 'help_topics_advancedTechniques_mirrorMode_title',
        description: 'help_topics_advancedTechniques_mirrorMode_description',
        imagePath: null,
      ),
      HelpTopic(
        title: 'help_topics_advancedTechniques_groupingShapes_title',
        description:
            'help_topics_advancedTechniques_groupingShapes_description',
        imagePath: null,
      ),
      HelpTopic(
        title: 'help_topics_advancedTechniques_snapToCenter_title',
        description: 'help_topics_advancedTechniques_snapToCenter_description',
        imagePath: null,
      ),
      HelpTopic(
        title: 'help_topics_advancedTechniques_undo_redo_title',
        description: 'help_topics_advancedTechniques_undo_redo_description',
        imagePath: null,
      ),
    ],
    'help_categories_keyboardShortcuts': [
      HelpTopic(
        title: 'help_topics_keyboardShortcuts_common_title',
        description: 'help_topics_keyboardShortcuts_common_description',
        imagePath: null,
      ),
      HelpTopic(
        title: 'help_topics_keyboardShortcuts_selection_title',
        description: 'help_topics_keyboardShortcuts_selection_description',
        imagePath: null,
      ),
      HelpTopic(
        title: 'help_topics_keyboardShortcuts_manipulation_title',
        description: 'help_topics_keyboardShortcuts_manipulation_description',
        imagePath: null,
      ),
    ],
  };
}

/// Model class for help topics
class HelpTopic {
  final String title;
  final String description;
  final String? imagePath;

  HelpTopic({
    required this.title,
    required this.description,
    this.imagePath,
  });
}
