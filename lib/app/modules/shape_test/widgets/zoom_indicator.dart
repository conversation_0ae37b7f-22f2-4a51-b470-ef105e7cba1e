import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/shape_editor_controller.dart';

/// Widget that displays the current zoom level temporarily
class ZoomIndicator extends StatefulWidget {
  final ShapeEditorController controller;

  const ZoomIndicator({
    super.key,
    required this.controller,
  });

  @override
  State<ZoomIndicator> createState() => _ZoomIndicatorState();
}

class _ZoomIndicatorState extends State<ZoomIndicator> {
  Timer? _hideTimer;
  RxBool _isVisible = false.obs;

  @override
  void initState() {
    super.initState();
    // Listen for zoom changes to show the indicator
    ever(widget.controller.zoomScale, _onZoomChanged);
    // Also listen for pan changes
    ever(widget.controller.panOffset, _onPanChanged);
  }

  void _onZoomChanged(double value) {
    _showIndicator();
  }

  void _onPanChanged(Offset value) {
    // Only show indicator when we're zoomed in
    if (widget.controller.zoomScale.value != 1.0) {
      _showIndicator();
    }
  }

  void _showIndicator() {
    // Make indicator visible
    _isVisible.value = true;

    // Reset any existing timer
    _hideTimer?.cancel();

    // Set timer to hide the indicator after 1.5 seconds (faster feedback)
    _hideTimer = Timer(const Duration(milliseconds: 1500), () {
      _isVisible.value = false;
    });
  }

  @override
  void dispose() {
    _hideTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (!_isVisible.value) {
        return const SizedBox.shrink();
      }

      return Positioned(
        top: 20,
        left: 20,
        child: AnimatedOpacity(
          opacity: _isVisible.value ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 200),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              '${(widget.controller.zoomScale.value * 100).toInt()}%',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      );
    });
  }
}
