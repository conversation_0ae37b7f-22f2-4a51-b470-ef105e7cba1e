import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'shape_data.dart';
import '../utils/geometry_utils.dart';

/// A special type of ShapeData that represents a group of shapes
class GroupShapeData extends ShapeData {
  /// The original shapes that make up this group
  final List<ShapeData> childShapes;

  /// Original keys of the shapes that are part of this group
  final List<Key?> originalKeys;

  GroupShapeData({
    required this.childShapes,
    required this.originalKeys,
    required super.vertices,
    required super.boundingRect,
    required super.center,
    Map<int, Offset>? curveControls,
    Map<int, List<Offset>>? cubicCurveControls,
    super.rotation,
    super.gridVertices,
    super.gridCenter,
    super.gridBoundingRect,
    super.gridCurveControls,
    super.gridCubicCurveControls,
    required super.visualRotation,
  }) : super(
          type: ShapeType.group,
          curveControls: curveControls ?? {},
          cubicCurveControls: cubicCurveControls ?? {},
        );

  /// Create a group shape from a list of shapes
  factory GroupShapeData.fromShapes(List<ShapeData> shapes, List<Key?> keys) {
    if (shapes.isEmpty) {
      throw ArgumentError('Cannot create a group from an empty list of shapes');
    }

    double minX = double.infinity;
    double minY = double.infinity;
    double maxX = -double.infinity;
    double maxY = -double.infinity;

    // For each shape, calculate its *accurate* bounding box considering curves and rotation
    for (final shape in shapes) {
      // Use the new accurate method
      final accurateBounds = GeometryUtils.calculateAccurateBoundingRect(shape);

      // Update the overall min/max based on the accurate bounds
      minX = math.min(minX, accurateBounds.left);
      minY = math.min(minY, accurateBounds.top);
      maxX = math.max(maxX, accurateBounds.right);
      maxY = math.max(maxY, accurateBounds.bottom);
    }

    // If min/max values are still infinite (e.g., if all shapes were empty),
    // create a default small rectangle at the origin.
    if (minX == double.infinity ||
        minY == double.infinity ||
        maxX == -double.infinity ||
        maxY == -double.infinity) {
      minX = 0;
      minY = 0;
      maxX = 10;
      maxY = 10;
    }

    // Create the final bounding rectangle from the overall extreme points
    final boundingRect = Rect.fromLTRB(minX, minY, maxX, maxY);
    final center = Offset(
      boundingRect.left + boundingRect.width / 2,
      boundingRect.top + boundingRect.height / 2,
    );

    // Create a simple rectangular outline for the group
    final vertices = [
      Offset(minX, minY), // Top-left
      Offset(maxX, minY), // Top-right
      Offset(maxX, maxY), // Bottom-right
      Offset(minX, maxY), // Bottom-left
    ];

    return GroupShapeData(
      childShapes: List.from(shapes), // Make a copy of the shapes
      originalKeys: List.from(keys),
      vertices: vertices,
      boundingRect: boundingRect,
      center: center,
      visualRotation: 0.0,
    );
  }

  @override
  ShapeData copyWith({
    ShapeType? type,
    List<Offset>? vertices,
    Map<int, Offset>? curveControls,
    Rect? boundingRect,
    Offset? center,
    double? rotation,
    List<Offset>? gridVertices,
    Offset? gridCenter,
    Rect? gridBoundingRect,
    Map<int, Offset>? gridCurveControls,
    Map<int, List<Offset>>? cubicCurveControls,
    Map<int, List<Offset>>? gridCubicCurveControls,
    double? visualRotation,
  }) {
    final baseData = super.copyWith(
      type: type,
      vertices: vertices,
      curveControls: curveControls,
      boundingRect: boundingRect,
      center: center,
      rotation: rotation,
      gridVertices: gridVertices,
      gridCenter: gridCenter,
      gridBoundingRect: gridBoundingRect,
      gridCurveControls: gridCurveControls,
      cubicCurveControls: cubicCurveControls,
      gridCubicCurveControls: gridCubicCurveControls,
      visualRotation: visualRotation,
    );

    return GroupShapeData(
      childShapes: childShapes,
      originalKeys: originalKeys,
      vertices: baseData.vertices,
      curveControls: baseData.curveControls,
      boundingRect: baseData.boundingRect,
      center: baseData.center,
      rotation: baseData.rotation,
      visualRotation: baseData.visualRotation,
      gridVertices: baseData.gridVertices,
      gridCenter: baseData.gridCenter,
      gridBoundingRect: baseData.gridBoundingRect,
      gridCurveControls: baseData.gridCurveControls,
    );
  }

  /// Special version of copyWith that can update the child shapes
  GroupShapeData copyWithChildren({
    List<ShapeData>? childShapes,
    List<Key?>? originalKeys,
    ShapeType? type,
    List<Offset>? vertices,
    Map<int, Offset>? curveControls,
    Rect? boundingRect,
    Offset? center,
    double? rotation,
    List<Offset>? gridVertices,
    Offset? gridCenter,
    Rect? gridBoundingRect,
    Map<int, Offset>? gridCurveControls,
    Map<int, List<Offset>>? cubicCurveControls,
    Map<int, List<Offset>>? gridCubicCurveControls,
    double? visualRotation,
  }) {
    final baseData = super.copyWith(
      type: type,
      vertices: vertices,
      curveControls: curveControls,
      boundingRect: boundingRect,
      center: center,
      rotation: rotation,
      gridVertices: gridVertices,
      gridCenter: gridCenter,
      gridBoundingRect: gridBoundingRect,
      gridCurveControls: gridCurveControls,
      cubicCurveControls: cubicCurveControls,
      gridCubicCurveControls: gridCubicCurveControls,
      visualRotation: visualRotation,
    );

    return GroupShapeData(
      childShapes: childShapes ?? this.childShapes,
      originalKeys: originalKeys ?? this.originalKeys,
      vertices: baseData.vertices,
      curveControls: baseData.curveControls,
      boundingRect: baseData.boundingRect,
      center: baseData.center,
      rotation: baseData.rotation,
      visualRotation: baseData.visualRotation,
      gridVertices: baseData.gridVertices,
      gridCenter: baseData.gridCenter,
      gridBoundingRect: baseData.gridBoundingRect,
      gridCurveControls: baseData.gridCurveControls,
    );
  }

  /// Convert this group and all child shapes to grid coordinates
  @override
  GroupShapeData toGridCoordinates(GridCoordinateConverter converter) {
    // First convert the group itself using the parent method
    final groupWithGrid = super.toGridCoordinates(converter) as ShapeData;

    // Then convert all child shapes to grid coordinates
    final childShapesWithGrid =
        childShapes.map((shape) => shape.toGridCoordinates(converter)).toList();

    // Return a new GroupShapeData with all grid coordinates set
    return GroupShapeData(
      childShapes: childShapesWithGrid,
      originalKeys: originalKeys,
      vertices: vertices,
      boundingRect: boundingRect,
      center: center,
      rotation: rotation,
      curveControls: curveControls,
      cubicCurveControls: cubicCurveControls,
      gridVertices: groupWithGrid.gridVertices,
      gridCenter: groupWithGrid.gridCenter,
      gridBoundingRect: groupWithGrid.gridBoundingRect,
      gridCurveControls: groupWithGrid.gridCurveControls,
      gridCubicCurveControls: groupWithGrid.gridCubicCurveControls,
      visualRotation: groupWithGrid.visualRotation,
    );
  }

  /// Convert this group and all child shapes from grid coordinates to pixel coordinates
  @override
  GroupShapeData fromGridCoordinates(GridCoordinateConverter converter) {
    // First convert the group itself using the parent method
    final groupWithPixels = super.fromGridCoordinates(converter);

    // Then convert all child shapes from grid coordinates
    final childShapesWithPixels = childShapes
        .map((shape) => shape.fromGridCoordinates(converter))
        .toList();

    // Return a new GroupShapeData with pixel coordinates
    return GroupShapeData(
      childShapes: childShapesWithPixels,
      originalKeys: originalKeys,
      vertices: groupWithPixels.vertices,
      boundingRect: groupWithPixels.boundingRect,
      center: groupWithPixels.center,
      rotation: rotation,
      curveControls: groupWithPixels.curveControls,
      cubicCurveControls: groupWithPixels.cubicCurveControls,
      gridVertices: gridVertices,
      gridCenter: gridCenter,
      gridBoundingRect: gridBoundingRect,
      gridCurveControls: gridCurveControls,
      gridCubicCurveControls: gridCubicCurveControls,
      visualRotation: groupWithPixels.visualRotation,
    );
  }

  /// Transform all child shapes according to the changes in the group
  List<ShapeData> transformChildShapes({
    Offset? translation,
    double? rotationDelta,
    double? scale,
    double? scaleX,
    double? scaleY,
    Offset? scaleOrigin,
  }) {
    final List<ShapeData> transformed = [];

    // Create transformation matrix for the group change
    final groupMatrix = Matrix4.identity();

    if (translation != null) {
      groupMatrix.translate(translation.dx, translation.dy);
    }

    if (rotationDelta != null) {
      groupMatrix
        ..translate(center.dx, center.dy)
        ..rotateZ(rotationDelta)
        ..translate(-center.dx, -center.dy);
    }

    // Uniform scaling
    if (scale != null) {
      final origin = scaleOrigin ?? center;
      groupMatrix
        ..translate(origin.dx, origin.dy)
        ..scale(scale)
        ..translate(-origin.dx, -origin.dy);
    }

    // Non-uniform scaling (separate X and Y)
    if (scaleX != null || scaleY != null) {
      final origin = scaleOrigin ?? center;
      final sx = scaleX ?? 1.0;
      final sy = scaleY ?? 1.0;

      groupMatrix
        ..translate(origin.dx, origin.dy)
        ..scale(sx, sy, 1.0)
        ..translate(-origin.dx, -origin.dy);
    }

    // Apply the transformation to each child shape
    for (final shape in childShapes) {
      // Transform vertices
      final newVertices = shape.vertices.map((vertex) {
        return MatrixUtils.transformPoint(groupMatrix, vertex);
      }).toList();

      // Transform center
      final newCenter = MatrixUtils.transformPoint(groupMatrix, shape.center);

      // Calculate new rotation
      final newRotation = shape.rotation + (rotationDelta ?? 0.0);

      // Transform curve controls if they exist
      final newCurveControls = <int, Offset>{};
      if (shape.curveControls.isNotEmpty) {
        for (final entry in shape.curveControls.entries) {
          if (entry.value != Offset.zero) {
            // Apply same transformation to curve controls
            newCurveControls[entry.key] = entry.value;
            if (scaleX != null || scaleY != null) {
              // Scale the control points
              final sx = scaleX ?? 1.0;
              final sy = scaleY ?? 1.0;
              newCurveControls[entry.key] =
                  Offset(entry.value.dx * sx, entry.value.dy * sy);
            }
          }
        }
      }

      // Special handling for nested groups
      if (shape.type == ShapeType.group && shape is GroupShapeData) {
        // Recursively transform children of the nested group
        final transformedNestedChildren = GroupShapeData(
          childShapes: shape.childShapes,
          originalKeys: shape.originalKeys,
          vertices: shape.vertices,
          boundingRect: shape.boundingRect,
          center: shape.center,
          rotation: shape.rotation,
          curveControls: shape.curveControls,
          cubicCurveControls: shape.cubicCurveControls,
          gridVertices: shape.gridVertices,
          gridCenter: shape.gridCenter,
          gridBoundingRect: shape.gridBoundingRect,
          gridCurveControls: shape.gridCurveControls,
          gridCubicCurveControls: shape.gridCubicCurveControls,
          visualRotation: shape.visualRotation,
        ).transformChildShapes(
          translation: translation,
          rotationDelta: rotationDelta,
          scale: scale,
          scaleX: scaleX,
          scaleY: scaleY,
          scaleOrigin: scaleOrigin,
        );

        // Create a new nested group with transformed children and properties
        final transformedNestedGroup = GroupShapeData(
          childShapes: transformedNestedChildren,
          originalKeys: shape.originalKeys,
          vertices: newVertices,
          boundingRect: Rect.fromPoints(
            newVertices[0],
            newVertices[2],
          ),
          center: newCenter,
          rotation: newRotation,
          curveControls:
              newCurveControls.isEmpty ? shape.curveControls : newCurveControls,
          cubicCurveControls: shape.cubicCurveControls,
          gridVertices: shape.gridVertices,
          gridCenter: shape.gridCenter,
          gridBoundingRect: shape.gridBoundingRect,
          gridCurveControls: shape.gridCurveControls,
          gridCubicCurveControls: shape.gridCubicCurveControls,
          visualRotation: shape.visualRotation,
        );

        transformed.add(transformedNestedGroup);
      } else {
        // Handle non-group shapes as before
        final transformedShape = shape.copyWith(
          vertices: newVertices,
          center: newCenter,
          rotation: newRotation,
          curveControls:
              newCurveControls.isEmpty ? shape.curveControls : newCurveControls,
          gridVertices: shape.gridVertices,
          gridCenter: shape.gridCenter,
          gridBoundingRect: shape.gridBoundingRect,
          gridCurveControls: shape.gridCurveControls,
          visualRotation: shape.visualRotation,
        );

        transformed.add(transformedShape);
      }
    }

    return transformed;
  }

  /// Ungroup this shape into its constituent shapes
  List<ShapeData> ungroup() {
    return List.from(childShapes);
  }

  @override
  ShapeData deepCopy() {
    // Deep copy all child shapes
    final copiedChildren = <ShapeData>[];
    for (final child in childShapes) {
      copiedChildren.add(child.deepCopy());
    }

    // Create a new GroupShapeData with deep copies of all properties
    return GroupShapeData(
      childShapes: copiedChildren,
      originalKeys: List<Key?>.from(originalKeys),
      vertices: List<Offset>.from(vertices),
      curveControls: Map<int, Offset>.from(curveControls),
      cubicCurveControls: Map<int, List<Offset>>.from(cubicCurveControls),
      boundingRect: boundingRect,
      center: center,
      rotation: rotation,
      gridVertices:
          gridVertices != null ? List<Offset>.from(gridVertices!) : null,
      gridCenter: gridCenter,
      gridBoundingRect: gridBoundingRect,
      gridCurveControls: gridCurveControls != null
          ? Map<int, Offset>.from(gridCurveControls!)
          : null,
      gridCubicCurveControls: gridCubicCurveControls != null
          ? Map<int, List<Offset>>.from(gridCubicCurveControls!)
          : null,
      visualRotation: visualRotation,
    );
  }
}
