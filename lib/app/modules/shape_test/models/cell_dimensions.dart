import 'package:flutter/material.dart';

class CellDimensions {
  final double cellWidth;
  final double cellHeight;
  final double offsetX;
  final double offsetY;

  CellDimensions({
    required this.cellWidth,
    required this.cellHeight,
    required this.offsetX,
    required this.offsetY,
  });

  // Helper function to calculate cell dimensions
  static CellDimensions calculate(Size canvasSize, int patternWidth,
      int patternHeight, double aspectRatio) {
    // Calculate grid dimensions based on content and aspect ratio
    double cellWidth;
    double cellHeight;

    // Determine if width or height is the limiting factor
    final widthLimited = canvasSize.width / patternWidth <
        (canvasSize.height / patternHeight) * aspectRatio;

    if (widthLimited) {
      // Width is the limiting factor
      cellWidth = canvasSize.width / patternWidth;
      cellHeight = cellWidth / aspectRatio;
    } else {
      // Height is the limiting factor
      cellHeight = canvasSize.height / patternHeight;
      cellWidth = cellHeight * aspectRatio;
    }

    // Calculate the drawing offset to center the content
    final offsetX = (canvasSize.width - (patternWidth * cellWidth)) / 2;
    final offsetY = (canvasSize.height - (patternHeight * cellHeight)) / 2;

    return CellDimensions(
      cellWidth: cellWidth,
      cellHeight: cellHeight,
      offsetX: offsetX,
      offsetY: offsetY,
    );
  }

  // Convert Y coordinate to row index
  int getRowFromY(double y) {
    if (cellHeight <= 0) return 0;
    int row = ((y - offsetY) / cellHeight).floor();
    return row < 0 ? 0 : row;
  }

  // Convert X coordinate to column index
  int getColFromX(double x) {
    if (cellWidth <= 0) return 0;
    int col = ((x - offsetX) / cellWidth).floor();
    return col < 0 ? 0 : col;
  }

  // Get X coordinate from column index (center of cell)
  double getXFromCol(int col) {
    return offsetX + (col * cellWidth) + (cellWidth / 2);
  }

  // Get Y coordinate from row index (center of cell)
  double getYFromRow(int row) {
    return offsetY + (row * cellHeight) + (cellHeight / 2);
  }
}
