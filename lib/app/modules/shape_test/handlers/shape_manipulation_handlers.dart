import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../models/shape_data.dart';
import '../models/group_shape_data.dart';
import '../utils/geometry_utils.dart';
import '../constants/grid_constants.dart';

/// Enum defining the different types of snaps that can occur.
enum SnapType {
  // Shape-to-shape edge snaps
  leftToRight,
  rightToLeft,
  leftToLeft,
  rightToRight,
  topToBottom,
  bottomToTop,
  topToTop,
  bottomToBottom,
  // Shape-to-shape center snaps
  horizontalCenter,
  verticalCenter,
  // Grid snaps
  gridHorizontal, // Snap to a vertical grid line
  gridVertical, // Snap to a horizontal grid line
  // Center line snaps (distinct from shape-to-shape center)
  canvasCenterHorizontal,
  canvasCenterVertical,
}

/// Holds information about a specific snap event.
class SnapInfo {
  final Key? targetShapeKey; // Make nullable
  final SnapType snapType;
  final double snapOffset; // The magnitude of the snap adjustment
  final bool isHorizontal; // True for horizontal, false for vertical

  SnapInfo({
    required this.targetShapeKey,
    required this.snapType,
    required this.snapOffset,
    required this.isHorizontal,
  });
}

/// Record to return both the updated shape data and potential snap info.
typedef ShapeDragResult = ({
  ShapeData shapeData,
  SnapInfo? snapInfo,
  bool didCenterSnap
});

/// Handlers for shape manipulation operations (dragging vertices, edges, rotation, etc.)
/// Central logic class for all shape transformations
class ShapeManipulationHandlers {
  /// Define the threshold for snapping to other shapes
  static const double defaultSnapToShapesThreshold = 10.0;

  /// Handle the dragging of a vertex
  /// This function calculates new shape data when the user drags a corner point of the bounding box
  static ShapeData handleVertexDrag({
    required ShapeData shapeData,
    required int vertexIndex,
    required Offset newPosition,
    required BoxConstraints constraints,
    required bool curveMode,
    bool preserveAspectRatio = false,
  }) {
    // --- Store original state in case we need to revert ---
    final originalShapeData = shapeData.deepCopy();

    // Make sure we stay within the screen bounds
    final constrainedPos =
        GeometryUtils.constrainOffset(newPosition, constraints);

    // Create copies of vertices to modify (immutable pattern)
    final vertices = List<Offset>.from(shapeData.vertices);
    final originalVertices = List<Offset>.from(shapeData.vertices);
    // Copy curve controls as they might be needed for bounds calculation
    final curveControls = Map<int, Offset>.from(shapeData.curveControls);

    // For preserving aspect ratio, calculate the original aspect ratio
    double? originalAspectRatio;
    if (preserveAspectRatio) {
      final bounds = GeometryUtils.calculateBoundingRect(originalVertices);
      if (bounds.height != 0) {
        // Avoid division by zero
        originalAspectRatio = bounds.width / bounds.height;
      }
    }

    // Store original center for group transformations
    final originalCenter = shapeData.center;

    // --- Calculate the accurate bounding box of the shape with curves ---
    final accurateBounds =
        GeometryUtils.calculateAccurateBoundingRect(shapeData);
    Rect originalAccurateRect;
    if (accurateBounds is GroupBoundsData) {
      originalAccurateRect = accurateBounds.bounds;
    } else {
      originalAccurateRect = accurateBounds;
    }

    // Define bounding box corners in the correct order matching the vertex indices
    final boundingBoxCorners = [
      Offset(
          originalAccurateRect.left, originalAccurateRect.top), // Top-left (0)
      Offset(originalAccurateRect.right,
          originalAccurateRect.top), // Top-right (1)
      Offset(originalAccurateRect.right,
          originalAccurateRect.bottom), // Bottom-right (2)
      Offset(originalAccurateRect.left,
          originalAccurateRect.bottom) // Bottom-left (3)
    ];

    // HANDLE ROTATION: For rotated shapes, we need to transform the drag position
    // into the shape's local coordinate system before applying vertex movement
    if (shapeData.rotation != 0) {
      // Create inverse rotation matrix to convert global coordinates to local shape coordinates
      final inverseRotationMatrix = Matrix4.identity()
        ..translate(shapeData.center.dx, shapeData.center.dy)
        ..rotateZ(-shapeData.rotation) // Use negative rotation to invert
        ..translate(-shapeData.center.dx, -shapeData.center.dy);

      // Transform the new position to shape's local coordinate system
      final localNewPosition =
          MatrixUtils.transformPoint(inverseRotationMatrix, constrainedPos);

      // Transform all vertices and bounding box corners to local coordinate system
      final localVertices = vertices.map((vertex) {
        return MatrixUtils.transformPoint(inverseRotationMatrix, vertex);
      }).toList();

      final localBoundingBoxCorners = boundingBoxCorners.map((corner) {
        return MatrixUtils.transformPoint(inverseRotationMatrix, corner);
      }).toList();

      // Get the local bounding box from the transformed corners
      final localAccurateRect = Rect.fromLTRB(
          localBoundingBoxCorners[0].dx, // left
          localBoundingBoxCorners[0].dy, // top
          localBoundingBoxCorners[2].dx, // right
          localBoundingBoxCorners[2].dy // bottom
          );

      // Apply the appropriate vertex drag handler in local coordinates
      // Use bounding box corners as reference for dragging operation
      switch (shapeData.type) {
        case ShapeType.rectangle:
          _handleRectangleVertexDragWithBounds(
              localVertices, vertexIndex, localNewPosition, localAccurateRect);
          break;
        case ShapeType.triangle:
          _handleTriangleVertexDragWithBounds(
              localVertices, vertexIndex, localNewPosition, localAccurateRect);
          break;
        case ShapeType.rightTriangle:
          _handleRightTriangleVertexDragWithBounds(
              localVertices, vertexIndex, localNewPosition, localAccurateRect);
          break;
        case ShapeType.trapezoid:
          _handleTrapezoidVertexDragWithBounds(
              localVertices, vertexIndex, localNewPosition, localAccurateRect);
          break;
        case ShapeType.group:
          _handleGroupVertexDragWithBounds(
              localVertices, vertexIndex, localNewPosition, localAccurateRect);
          break;
        case ShapeType.custom:
          // Handle custom shapes similarly to rectangles
          _handleRectangleVertexDragWithBounds(
              localVertices, vertexIndex, localNewPosition, localAccurateRect);
          break;
      }

      // If preserving aspect ratio, adjust the vertices
      if (preserveAspectRatio && originalAspectRatio != null) {
        _adjustVerticesToPreserveAspectRatio(
            localVertices, vertexIndex, originalAspectRatio, shapeData.type);
      }

      // Create rotation matrix to transform back to global coordinates
      final rotationMatrix = Matrix4.identity()
        ..translate(shapeData.center.dx, shapeData.center.dy)
        ..rotateZ(shapeData.rotation)
        ..translate(-shapeData.center.dx, -shapeData.center.dy);

      // Transform the modified vertices back to global coordinate system
      for (int i = 0; i < vertices.length; i++) {
        vertices[i] =
            MatrixUtils.transformPoint(rotationMatrix, localVertices[i]);
      }
    } else {
      // No rotation, use the bounds-aware handlers directly
      switch (shapeData.type) {
        case ShapeType.rectangle:
          _handleRectangleVertexDragWithBounds(
              vertices, vertexIndex, constrainedPos, originalAccurateRect);
          break;
        case ShapeType.triangle:
          _handleTriangleVertexDragWithBounds(
              vertices, vertexIndex, constrainedPos, originalAccurateRect);
          break;
        case ShapeType.rightTriangle:
          _handleRightTriangleVertexDragWithBounds(
              vertices, vertexIndex, constrainedPos, originalAccurateRect);
          break;
        case ShapeType.trapezoid:
          _handleTrapezoidVertexDragWithBounds(
              vertices, vertexIndex, constrainedPos, originalAccurateRect);
          break;
        case ShapeType.group:
          _handleGroupVertexDragWithBounds(
              vertices, vertexIndex, constrainedPos, originalAccurateRect);
          break;
        case ShapeType.custom:
          // Handle custom shapes similarly to rectangles
          _handleRectangleVertexDragWithBounds(
              vertices, vertexIndex, constrainedPos, originalAccurateRect);
          break;
      }

      // If preserving aspect ratio, adjust the vertices
      if (preserveAspectRatio && originalAspectRatio != null) {
        _adjustVerticesToPreserveAspectRatio(
            vertices, vertexIndex, originalAspectRatio, shapeData.type);
      }
    }

    // --- Recalculate Bounding Box and Center ACCURATELY ---
    // For non-group shapes, we recalculate bounds based on updated vertices
    // For group shapes, we calculate the target bounds directly and use that.
    Rect finalBoundingRect;
    Offset finalCenter;
    List<Offset> finalVertices =
        List.from(vertices); // Start with potentially updated vertices

    if (shapeData is GroupShapeData) {
      // --- Direct Calculation for Group Shape ---
      Rect localOriginalRect;
      Offset localNewPosition;

      // Determine local coordinates if rotated
      if (shapeData.rotation != 0) {
        final inverseRotationMatrix = Matrix4.identity()
          ..translate(shapeData.center.dx, shapeData.center.dy)
          ..rotateZ(-shapeData.rotation)
          ..translate(-shapeData.center.dx, -shapeData.center.dy);

        localNewPosition =
            MatrixUtils.transformPoint(inverseRotationMatrix, constrainedPos);
        final localBoundingBoxCorners = boundingBoxCorners.map((corner) {
          return MatrixUtils.transformPoint(inverseRotationMatrix, corner);
        }).toList();
        localOriginalRect = Rect.fromLTRB(
            localBoundingBoxCorners[0].dx,
            localBoundingBoxCorners[0].dy,
            localBoundingBoxCorners[2].dx,
            localBoundingBoxCorners[2].dy);
      } else {
        localNewPosition = constrainedPos;
        localOriginalRect = originalAccurateRect;
      }

      // Calculate the target local bounding box based on drag
      Rect localNewTargetRect;
      switch (vertexIndex) {
        case 0: // Top-left
          localNewTargetRect = Rect.fromLTRB(
              localNewPosition.dx,
              localNewPosition.dy,
              localOriginalRect.right,
              localOriginalRect.bottom);
          break;
        case 1: // Top-right
          localNewTargetRect = Rect.fromLTRB(
              localOriginalRect.left,
              localNewPosition.dy,
              localNewPosition.dx,
              localOriginalRect.bottom);
          break;
        case 2: // Bottom-right
          localNewTargetRect = Rect.fromLTRB(localOriginalRect.left,
              localOriginalRect.top, localNewPosition.dx, localNewPosition.dy);
          break;
        case 3: // Bottom-left
          localNewTargetRect = Rect.fromLTRB(
              localNewPosition.dx,
              localOriginalRect.top,
              localOriginalRect.right,
              localNewPosition.dy);
          break;
        default:
          return originalShapeData; // Invalid index
      }

      // Ensure minimum size
      if (localNewTargetRect.width < GridConstants.minShapeSize ||
          localNewTargetRect.height < GridConstants.minShapeSize) {
        return originalShapeData;
      }

      // Transform target rect back to global if needed
      if (shapeData.rotation != 0) {
        final rotationMatrix = Matrix4.identity()
          ..translate(shapeData.center.dx, shapeData.center.dy)
          ..rotateZ(shapeData.rotation)
          ..translate(-shapeData.center.dx, -shapeData.center.dy);

        final globalCorners = [
          Offset(localNewTargetRect.left, localNewTargetRect.top),
          Offset(localNewTargetRect.right, localNewTargetRect.top),
          Offset(localNewTargetRect.right, localNewTargetRect.bottom),
          Offset(localNewTargetRect.left, localNewTargetRect.bottom),
        ].map((c) => MatrixUtils.transformPoint(rotationMatrix, c)).toList();

        // Recalculate bounds from transformed corners
        double minX = globalCorners.map((c) => c.dx).reduce(math.min);
        double maxX = globalCorners.map((c) => c.dx).reduce(math.max);
        double minY = globalCorners.map((c) => c.dy).reduce(math.min);
        double maxY = globalCorners.map((c) => c.dy).reduce(math.max);
        finalBoundingRect = Rect.fromLTRB(minX, minY, maxX, maxY);
      } else {
        finalBoundingRect = localNewTargetRect;
      }

      finalCenter = finalBoundingRect.center;

      // --- Constraint Check ---
      if (finalBoundingRect.left < constraints.minWidth ||
          finalBoundingRect.top < constraints.minHeight ||
          finalBoundingRect.right > constraints.maxWidth ||
          finalBoundingRect.bottom > constraints.maxHeight) {
        return originalShapeData;
      }
      // --- End Constraint Check ---

      // Calculate scaling factors based on the intended size change
      double scaleX = (originalAccurateRect.width != 0)
          ? finalBoundingRect.width / originalAccurateRect.width
          : 1.0;
      double scaleY = (originalAccurateRect.height != 0)
          ? finalBoundingRect.height / originalAccurateRect.height
          : 1.0;

      // Calculate the translation based on the center shift
      final translation = finalCenter - originalCenter;

      // Determine the scale origin (opposite corner of original bounds)
      final oppositeVertexIndex = (vertexIndex + 2) % 4;
      final scaleOrigin = boundingBoxCorners[oppositeVertexIndex];

      // Transform all the child shapes
      final transformedChildren = shapeData.transformChildShapes(
        translation: translation,
        scaleX: scaleX,
        scaleY: scaleY,
        scaleOrigin: scaleOrigin,
      );

      // Update the group's vertices to match the final bounding box corners
      finalVertices = [
        finalBoundingRect.topLeft,
        finalBoundingRect.topRight,
        finalBoundingRect.bottomRight,
        finalBoundingRect.bottomLeft,
      ];

      // Create and return the updated group
      return (shapeData).copyWithChildren(
        childShapes: transformedChildren,
        vertices: finalVertices,
        boundingRect: finalBoundingRect,
        center: finalCenter,
        visualRotation: shapeData.visualRotation, // Preserve visual rotation
      );
    } else {
      // --- Original Logic for Non-Group Shapes ---
      // Build the path using the *final* vertices and existing curve controls
      final path = _buildPathWithCurves(finalVertices, shapeData.curveControls);

      Path pathToMeasure = path;
      // Apply rotation if necessary before measuring bounds
      if (shapeData.rotation != 0) {
        // Note: Center for rotation should ideally be the new potential center
        final tempCenter =
            GeometryUtils.calculateBoundingRect(finalVertices).center;
        final rotationMatrix = Matrix4.identity()
          ..translate(tempCenter.dx, tempCenter.dy)
          ..rotateZ(shapeData.rotation)
          ..translate(-tempCenter.dx, -tempCenter.dy);
        pathToMeasure = path.transform(rotationMatrix.storage);
      }

      // Calculate potential bounds using the accurate method on the final geometry
      final tempShapeData = shapeData.copyWith(
          vertices: finalVertices,
          center: shapeData.center, // Keep original center for now
          curveControls: shapeData.curveControls,
          rotation: shapeData.rotation);

      final accurateBoundsResult =
          GeometryUtils.calculateAccurateBoundingRect(tempShapeData);
      // We know it's not a GroupBoundsData here
      finalBoundingRect = accurateBoundsResult as Rect;
      finalCenter = finalBoundingRect.center;
      // --- End Accurate Bounding Box Calculation for Non-Groups ---

      // --- Constraint Check ---
      if (finalBoundingRect.left < constraints.minWidth ||
          finalBoundingRect.top < constraints.minHeight ||
          finalBoundingRect.right > constraints.maxWidth ||
          finalBoundingRect.bottom > constraints.maxHeight) {
        return originalShapeData;
      }
      // --- End Constraint Check ---

      // For regular shapes, return updated data with accurate bounds/center
      return shapeData.copyWith(
        vertices: finalVertices,
        boundingRect: finalBoundingRect,
        center: finalCenter,
        visualRotation: shapeData.visualRotation, // Preserve visual rotation
      );
    }
  }

  /// Add new helper methods that use bounds instead of original vertex positions

  /// Handle vertex dragging for a rectangle using accurate bounding box
  static void _handleRectangleVertexDragWithBounds(List<Offset> vertices,
      int index, Offset newPosition, Rect accurateBounds) {
    // Calculate the new bounding box based on which corner was dragged
    Rect newBoundingRect;

    switch (index) {
      case 0: // Top-left corner of bounding box
        newBoundingRect = Rect.fromLTRB(newPosition.dx, newPosition.dy,
            accurateBounds.right, accurateBounds.bottom);
        break;
      case 1: // Top-right corner of bounding box
        newBoundingRect = Rect.fromLTRB(accurateBounds.left, newPosition.dy,
            newPosition.dx, accurateBounds.bottom);
        break;
      case 2: // Bottom-right corner of bounding box
        newBoundingRect = Rect.fromLTRB(accurateBounds.left, accurateBounds.top,
            newPosition.dx, newPosition.dy);
        break;
      case 3: // Bottom-left corner of bounding box
        newBoundingRect = Rect.fromLTRB(newPosition.dx, accurateBounds.top,
            accurateBounds.right, newPosition.dy);
        break;
      default:
        return; // Invalid index
    }

    // Ensure minimum size
    if (newBoundingRect.width < GridConstants.minShapeSize ||
        newBoundingRect.height < GridConstants.minShapeSize) {
      return;
    }

    // Update each vertex maintaining its relative position within the bounding box
    for (int i = 0; i < vertices.length; i++) {
      // Calculate relative position within original bounding box
      final relativeX =
          (vertices[i].dx - accurateBounds.left) / accurateBounds.width;
      final relativeY =
          (vertices[i].dy - accurateBounds.top) / accurateBounds.height;

      // Apply to new bounding box
      vertices[i] = Offset(
          newBoundingRect.left + relativeX * newBoundingRect.width,
          newBoundingRect.top + relativeY * newBoundingRect.height);
    }
  }

  /// Handle vertex dragging for a triangle using accurate bounding box
  static void _handleTriangleVertexDragWithBounds(List<Offset> vertices,
      int index, Offset newPosition, Rect accurateBounds) {
    // Calculate the new bounding box based on which corner was dragged
    Rect newBoundingRect;

    switch (index) {
      case 0: // Top-left corner of bounding box
        newBoundingRect = Rect.fromLTRB(newPosition.dx, newPosition.dy,
            accurateBounds.right, accurateBounds.bottom);
        break;
      case 1: // Top-right corner of bounding box
        newBoundingRect = Rect.fromLTRB(accurateBounds.left, newPosition.dy,
            newPosition.dx, accurateBounds.bottom);
        break;
      case 2: // Bottom-right corner of bounding box
        newBoundingRect = Rect.fromLTRB(accurateBounds.left, accurateBounds.top,
            newPosition.dx, newPosition.dy);
        break;
      case 3: // Bottom-left corner of bounding box
        newBoundingRect = Rect.fromLTRB(newPosition.dx, accurateBounds.top,
            accurateBounds.right, newPosition.dy);
        break;
      default:
        return; // Invalid index
    }

    // Ensure minimum size
    if (newBoundingRect.width < GridConstants.minShapeSize ||
        newBoundingRect.height < GridConstants.minShapeSize) {
      return;
    }

    // Update each vertex maintaining its relative position within the bounding box
    for (int i = 0; i < vertices.length; i++) {
      // Calculate relative position within original bounding box
      final relativeX =
          (vertices[i].dx - accurateBounds.left) / accurateBounds.width;
      final relativeY =
          (vertices[i].dy - accurateBounds.top) / accurateBounds.height;

      // Apply to new bounding box
      vertices[i] = Offset(
          newBoundingRect.left + relativeX * newBoundingRect.width,
          newBoundingRect.top + relativeY * newBoundingRect.height);
    }
  }

  /// Handle vertex dragging for a right triangle using accurate bounding box
  static void _handleRightTriangleVertexDragWithBounds(List<Offset> vertices,
      int index, Offset newPosition, Rect accurateBounds) {
    // Use the same approach as regular triangles but with accurate bounds
    _handleTriangleVertexDragWithBounds(
        vertices, index, newPosition, accurateBounds);
  }

  /// Handle vertex dragging for a trapezoid using accurate bounding box
  static void _handleTrapezoidVertexDragWithBounds(List<Offset> vertices,
      int index, Offset newPosition, Rect accurateBounds) {
    // Calculate the new bounding box based on which corner was dragged
    Rect newBoundingRect;

    switch (index) {
      case 0: // Top-left corner of bounding box
        newBoundingRect = Rect.fromLTRB(newPosition.dx, newPosition.dy,
            accurateBounds.right, accurateBounds.bottom);
        break;
      case 1: // Top-right corner of bounding box
        newBoundingRect = Rect.fromLTRB(accurateBounds.left, newPosition.dy,
            newPosition.dx, accurateBounds.bottom);
        break;
      case 2: // Bottom-right corner of bounding box
        newBoundingRect = Rect.fromLTRB(accurateBounds.left, accurateBounds.top,
            newPosition.dx, newPosition.dy);
        break;
      case 3: // Bottom-left corner of bounding box
        newBoundingRect = Rect.fromLTRB(newPosition.dx, accurateBounds.top,
            accurateBounds.right, newPosition.dy);
        break;
      default:
        return; // Invalid index
    }

    // Ensure minimum size
    if (newBoundingRect.width < GridConstants.minShapeSize ||
        newBoundingRect.height < GridConstants.minShapeSize) {
      return;
    }

    // Update each vertex maintaining its relative position within the bounding box
    for (int i = 0; i < vertices.length; i++) {
      // Calculate relative position within original bounding box
      final relativeX =
          (vertices[i].dx - accurateBounds.left) / accurateBounds.width;
      final relativeY =
          (vertices[i].dy - accurateBounds.top) / accurateBounds.height;

      // Apply to new bounding box
      vertices[i] = Offset(
          newBoundingRect.left + relativeX * newBoundingRect.width,
          newBoundingRect.top + relativeY * newBoundingRect.height);
    }
  }

  /// Handle vertex dragging for a group shape using accurate bounding box
  static void _handleGroupVertexDragWithBounds(List<Offset> vertices, int index,
      Offset newPosition, Rect accurateBounds) {
    // Calculate the new bounding box based on which corner was dragged
    Rect newBoundingRect;

    switch (index) {
      case 0: // Top-left corner of bounding box
        newBoundingRect = Rect.fromLTRB(newPosition.dx, newPosition.dy,
            accurateBounds.right, accurateBounds.bottom);
        break;
      case 1: // Top-right corner of bounding box
        newBoundingRect = Rect.fromLTRB(accurateBounds.left, newPosition.dy,
            newPosition.dx, accurateBounds.bottom);
        break;
      case 2: // Bottom-right corner of bounding box
        newBoundingRect = Rect.fromLTRB(accurateBounds.left, accurateBounds.top,
            newPosition.dx, newPosition.dy);
        break;
      case 3: // Bottom-left corner of bounding box
        newBoundingRect = Rect.fromLTRB(newPosition.dx, accurateBounds.top,
            accurateBounds.right, newPosition.dy);
        break;
      default:
        return; // Invalid index
    }

    // Ensure minimum size
    if (newBoundingRect.width < GridConstants.minShapeSize ||
        newBoundingRect.height < GridConstants.minShapeSize) {
      return;
    }

    // Update each vertex maintaining its relative position within the bounding box
    for (int i = 0; i < vertices.length; i++) {
      // Calculate relative position within original bounding box
      final relativeX =
          (vertices[i].dx - accurateBounds.left) / accurateBounds.width;
      final relativeY =
          (vertices[i].dy - accurateBounds.top) / accurateBounds.height;

      // Apply to new bounding box
      vertices[i] = Offset(
          newBoundingRect.left + relativeX * newBoundingRect.width,
          newBoundingRect.top + relativeY * newBoundingRect.height);
    }
  }

  /// Handle the dragging of an edge
  /// This allows the user to curve the edges of the shape
  static ShapeData handleEdgeDrag({
    required ShapeData shapeData,
    required int edgeIndex,
    required Offset dragPosition,
    required BoxConstraints constraints,
    required bool curveMode,
  }) {
    // Get the vertices and edge points
    final vertices = List<Offset>.from(shapeData.vertices);
    final startPoint = vertices[edgeIndex];
    final endPoint = vertices[(edgeIndex + 1) % vertices.length];
    final edgeCenter = (startPoint + endPoint) / 2;

    // Make sure we stay within the screen bounds
    final constrainedPos =
        GeometryUtils.constrainOffset(dragPosition, constraints);

    // Always use curve mode for edge handles
    return _handleCurveEdgeDrag(
      shapeData: shapeData,
      edgeIndex: edgeIndex,
      edgeCenter: edgeCenter,
      dragPosition: constrainedPos,
      constraints: constraints,
    );
  }

  /// Handle rotation of the shape
  /// This is triggered when the user drags the rotation handle
  static ShapeData handleRotation({
    required ShapeData shapeData,
    required Offset lastPosition,
    required Offset newPosition,
    required BoxConstraints constraints,
  }) {
    // --- Calculate Delta Angle ---
    final lastAngle = math.atan2(
      lastPosition.dy - shapeData.center.dy,
      lastPosition.dx - shapeData.center.dx,
    );
    final newAngle = math.atan2(
      newPosition.dy - shapeData.center.dy,
      newPosition.dx - shapeData.center.dx,
    );
    final deltaAngle = newAngle - lastAngle;

    // Convert deltaAngle to degrees
    final deltaAngleDegrees = deltaAngle * (180 / math.pi);

    // Calculate the new cumulative visual rotation in degrees, wrap around 360
    final newVisualRotation =
        (shapeData.visualRotation + deltaAngleDegrees + 360) % 360;

    // If deltaAngle is negligible, return original data
    if (deltaAngle.abs() < 1e-6) {
      return shapeData;
    }

    // --- Create Rotation Matrix ---
    final transformMatrix = Matrix4.identity()
      ..translate(shapeData.center.dx, shapeData.center.dy)
      ..rotateZ(deltaAngle)
      ..translate(-shapeData.center.dx, -shapeData.center.dy);

    // --- Transform Vertices ---
    final rotatedVertices = shapeData.vertices.map((vertex) {
      return MatrixUtils.transformPoint(transformMatrix, vertex);
    }).toList();

    // --- Transform Curve Controls ---
    final rotatedCurveControls = <int, Offset>{};
    for (final entry in shapeData.curveControls.entries) {
      final index = entry.key;
      final originalOffset = entry.value;

      if (originalOffset == Offset.zero) {
        // Skip zero offsets
        rotatedCurveControls[index] = Offset.zero;
        continue;
      }

      // Calculate the original edge midpoint
      final startPoint = shapeData.vertices[index];
      final endPoint =
          shapeData.vertices[(index + 1) % shapeData.vertices.length];
      final originalMidpoint = Offset(
        (startPoint.dx + endPoint.dx) / 2,
        (startPoint.dy + endPoint.dy) / 2,
      );

      // Calculate the original control point position
      final originalControlPoint = originalMidpoint + originalOffset;

      // Rotate the control point
      final rotatedControlPoint =
          MatrixUtils.transformPoint(transformMatrix, originalControlPoint);

      // Calculate the new midpoint using the *rotated* vertices
      final rotatedStartPoint = rotatedVertices[index];
      final rotatedEndPoint =
          rotatedVertices[(index + 1) % rotatedVertices.length];
      final rotatedMidpoint = Offset(
        (rotatedStartPoint.dx + rotatedEndPoint.dx) / 2,
        (rotatedStartPoint.dy + rotatedEndPoint.dy) / 2,
      );

      // Calculate the new offset relative to the rotated midpoint
      final newOffset = rotatedControlPoint - rotatedMidpoint;
      rotatedCurveControls[index] = newOffset;
    }

    // --- Recalculate Bounding Box and Center ---

    // Calculate center based *only* on rotated vertices first
    final tempBoundsFromVertices =
        GeometryUtils.calculateBoundingRect(rotatedVertices);
    final newCenter = tempBoundsFromVertices.center;

    // Create a temporary ShapeData for accurate bounds calculation
    final tempShapeData = shapeData.copyWith(
      vertices: rotatedVertices,
      center: newCenter,
      curveControls: rotatedCurveControls,
      rotation: 0.0, // Rotation is baked into vertices already
    );

    // Use accurate bounds calculation
    final accurateBoundsResult =
        GeometryUtils.calculateAccurateBoundingRect(tempShapeData);

    // Handle case where calculateAccurateBoundingRect returns GroupBoundsData
    Rect newBounds;
    if (accurateBoundsResult is GroupBoundsData) {
      newBounds = accurateBoundsResult.bounds;
    } else {
      newBounds = accurateBoundsResult;
    }

    // Recalculate center based on the final bounds
    final finalCenter = newBounds.center;

    // --- ADD CONSTRAINT CHECK ---
    if (newBounds.left < constraints.minWidth ||
        newBounds.top < constraints.minHeight ||
        newBounds.right > constraints.maxWidth ||
        newBounds.bottom > constraints.maxHeight) {
      // If the rotation goes out of bounds, return the original shape data
      return shapeData;
    }
    // --- END CONSTRAINT CHECK ---

    // --- Special handling for Group Shapes ---
    if (shapeData is GroupShapeData) {
      final groupData = shapeData;

      // Transform each child shape using the same matrix relative to the group center
      final transformedChildren = <ShapeData>[];
      for (final childShape in groupData.childShapes) {
        // Apply the same rotation logic recursively to the child
        // Note: This assumes child rotation is relative to its own center,
        // but the group rotation applies relative to the group's center.
        // We need to rotate the child's vertices and center around the *group's* center.

        // Rotate child vertices around group center
        final childRotatedVertices = childShape.vertices.map((vertex) {
          return MatrixUtils.transformPoint(transformMatrix, vertex);
        }).toList();

        // Rotate child center around group center
        final childRotatedCenter =
            MatrixUtils.transformPoint(transformMatrix, childShape.center);

        // Rotate child curve controls (similar logic as above)
        final childRotatedCurveControls = <int, Offset>{};
        for (final entry in childShape.curveControls.entries) {
          final index = entry.key;
          final originalOffset = entry.value;
          if (originalOffset == Offset.zero) {
            childRotatedCurveControls[index] = Offset.zero;
            continue;
          }
          final startPoint = childShape.vertices[index];
          final endPoint =
              childShape.vertices[(index + 1) % childShape.vertices.length];
          final originalMidpoint = Offset((startPoint.dx + endPoint.dx) / 2,
              (startPoint.dy + endPoint.dy) / 2);
          final originalControlPoint = originalMidpoint + originalOffset;
          final rotatedControlPoint =
              MatrixUtils.transformPoint(transformMatrix, originalControlPoint);

          final rotatedStartPoint = childRotatedVertices[index];
          final rotatedEndPoint =
              childRotatedVertices[(index + 1) % childRotatedVertices.length];
          final rotatedMidpoint = Offset(
              (rotatedStartPoint.dx + rotatedEndPoint.dx) / 2,
              (rotatedStartPoint.dy + rotatedEndPoint.dy) / 2);

          final newOffset = rotatedControlPoint - rotatedMidpoint;
          childRotatedCurveControls[index] = newOffset;
        }

        // Create temporary ShapeData for accurate bounds calculation
        final tempChildShapeData = childShape.copyWith(
          vertices: childRotatedVertices,
          center: childRotatedCenter,
          curveControls: childRotatedCurveControls,
          rotation: 0.0, // Rotation is baked into vertices
        );

        // Get accurate bounds, handling GroupBoundsData if needed
        final childBoundsResult =
            GeometryUtils.calculateAccurateBoundingRect(tempChildShapeData);
        Rect childNewBounds;
        if (childBoundsResult is GroupBoundsData) {
          childNewBounds = childBoundsResult.bounds;
        } else {
          childNewBounds = childBoundsResult;
        }

        // Add the transformed child, keeping its internal rotation property unchanged
        // (as the vertex rotation handles the visual change)
        transformedChildren.add(childShape.copyWith(
          vertices: childRotatedVertices,
          center: childRotatedCenter, // Use rotated center
          curveControls: childRotatedCurveControls,
          boundingRect: childNewBounds, // Use recalculated bounds
          rotation: childShape.rotation, // Keep original relative rotation
          visualRotation:
              childShape.visualRotation, // Keep original visual rotation
        ));
      }

      // Create and return the updated group
      // Rotation property is set to 0 as rotation is baked into vertices
      return groupData.copyWithChildren(
        childShapes: transformedChildren,
        vertices: rotatedVertices, // Use the group's rotated vertices
        boundingRect: newBounds, // Use the group's recalculated bounds
        center: finalCenter, // Use the group's recalculated center
        curveControls: rotatedCurveControls, // Use rotated curve controls
        rotation: 0.0, // Reset rotation
        visualRotation: newVisualRotation, // Set new visual rotation
      );
    }

    // --- Return updated ShapeData for regular shapes ---
    return shapeData.copyWith(
      vertices: rotatedVertices,
      center: finalCenter, // Use recalculated center
      boundingRect: newBounds, // Use recalculated bounds
      curveControls: rotatedCurveControls,
      rotation: 0.0, // Reset rotation property
      visualRotation: newVisualRotation, // Set new visual rotation
    );
  }

  /// Handle dragging the entire shape
  /// This keeps the shape within the screen bounds and snaps to other shapes
  /// Returns a record containing the updated shape data and optional snap info.
  static ShapeDragResult handleShapeDrag({
    required ShapeData shapeData,
    required Offset delta,
    required BoxConstraints constraints,
    required Map<Key, ShapeData> allShapeStates,
    required Key currentShapeKey,
    // Add snap manager parameters
    Function? getSnapInfo,
    Offset? globalPointerPosition,
  }) {
    // Calculate the potential new vertex positions based on the initial delta
    final initialNewVertices = shapeData.vertices
        .map((vertex) => vertex.translate(delta.dx, delta.dy))
        .toList();

    // --- Initial Boundary Check (Vertices Only) ---
    bool wouldBeInBoundsBasedOnVertices =
        GeometryUtils.areVerticesInBounds(initialNewVertices, constraints);

    double allowedDx = delta.dx;
    double allowedDy = delta.dy;

    if (!wouldBeInBoundsBasedOnVertices) {
      // Calculate how far we can safely move based on vertices only
      if (delta.dx != 0) {
        double minAllowedDx = -double.infinity;
        double maxAllowedDx = double.infinity;
        for (final vertex in shapeData.vertices) {
          if (delta.dx > 0) {
            // Moving right
            maxAllowedDx =
                math.min(maxAllowedDx, constraints.maxWidth - vertex.dx);
          } else {
            // Moving left
            minAllowedDx =
                math.max(minAllowedDx, constraints.minWidth - vertex.dx);
          }
        }
        allowedDx = delta.dx.clamp(minAllowedDx, maxAllowedDx);
      }
      if (delta.dy != 0) {
        double minAllowedDy = -double.infinity;
        double maxAllowedDy = double.infinity;
        for (final vertex in shapeData.vertices) {
          if (delta.dy > 0) {
            // Moving down
            maxAllowedDy =
                math.min(maxAllowedDy, constraints.maxHeight - vertex.dy);
          } else {
            // Moving up
            minAllowedDy =
                math.max(minAllowedDy, constraints.minHeight - vertex.dy);
          }
        }
        allowedDy = delta.dy.clamp(minAllowedDy, maxAllowedDy);
      }
    }
    // --- End Initial Boundary Check ---

    // If no movement is allowed based on vertex check, return original shape
    if (allowedDx == 0 && allowedDy == 0 && !wouldBeInBoundsBasedOnVertices) {
      return (
        shapeData: shapeData,
        snapInfo: null,
        didCenterSnap: false
      ); // Return original data
    }

    // Apply the allowed movement based on vertex check
    final constrainedVertices = shapeData.vertices
        .map((vertex) => vertex.translate(allowedDx, allowedDy))
        .toList();
    final potentialCenter = shapeData.center.translate(allowedDx, allowedDy);

    // --- Accurate Bounding Box Calculation (Post-Initial-Move) ---
    final tempShapeData = shapeData.copyWith(
      vertices: constrainedVertices,
      center: potentialCenter,
      curveControls: shapeData.curveControls,
      rotation: shapeData.rotation,
    );
    final accurateBoundsResult =
        GeometryUtils.calculateAccurateBoundingRect(tempShapeData);
    Rect potentialBoundingRect;
    if (accurateBoundsResult is GroupBoundsData) {
      potentialBoundingRect = accurateBoundsResult.bounds;
    } else {
      potentialBoundingRect = accurateBoundsResult;
    }
    // --- End Accurate Bounding Box Calculation ---

    // --- Final Constraint Check & Correction (Based on Accurate Bounds) ---
    double dxCorrection = 0.0;
    double dyCorrection = 0.0;

    if (potentialBoundingRect.left < constraints.minWidth) {
      dxCorrection = constraints.minWidth - potentialBoundingRect.left;
    } else if (potentialBoundingRect.right > constraints.maxWidth) {
      dxCorrection = constraints.maxWidth - potentialBoundingRect.right;
    }

    if (potentialBoundingRect.top < constraints.minHeight) {
      dyCorrection = constraints.minHeight - potentialBoundingRect.top;
    } else if (potentialBoundingRect.bottom > constraints.maxHeight) {
      dyCorrection = constraints.maxHeight - potentialBoundingRect.bottom;
    }

    Offset finalCenter = potentialCenter;
    List<Offset> finalVertices = constrainedVertices;
    Rect finalBoundingRect = potentialBoundingRect;

    if (dxCorrection != 0 || dyCorrection != 0) {
      finalCenter = potentialCenter.translate(dxCorrection, dyCorrection);
      finalVertices = constrainedVertices
          .map((vertex) => vertex.translate(dxCorrection, dyCorrection))
          .toList();

      // Recalculate bounds after correction
      final correctedTempShapeData = shapeData.copyWith(
        vertices: finalVertices,
        center: finalCenter,
        curveControls: shapeData.curveControls,
        rotation: shapeData.rotation,
      );
      final correctedBoundsResult =
          GeometryUtils.calculateAccurateBoundingRect(correctedTempShapeData);
      if (correctedBoundsResult is GroupBoundsData) {
        finalBoundingRect = correctedBoundsResult.bounds;
      } else {
        finalBoundingRect = correctedBoundsResult;
      }
    }
    // --- End Final Constraint Check & Correction ---

    // At this point we have a valid potential new shape position
    // but we haven't applied any snap adjustments yet

    // Get snap information (if available) and apply snap offset
    SnapInfo? snapInfo;
    bool didCenterSnap = false;

    if (getSnapInfo != null && globalPointerPosition != null) {
      final snapResult = getSnapInfo(
          originalShapeData: shapeData,
          potentialShapeData: tempShapeData,
          globalPointerPosition: globalPointerPosition,
          shapeKey: currentShapeKey);

      if (snapResult.snapOffset != Offset.zero) {
        // Apply the snap offset
        finalCenter = finalCenter.translate(
            snapResult.snapOffset.dx, snapResult.snapOffset.dy);
        finalVertices = finalVertices
            .map((vertex) => vertex.translate(
                snapResult.snapOffset.dx, snapResult.snapOffset.dy))
            .toList();

        // Also update bounding rect when snapped
        finalBoundingRect = Rect.fromLTWH(
          finalBoundingRect.left + snapResult.snapOffset.dx,
          finalBoundingRect.top + snapResult.snapOffset.dy,
          finalBoundingRect.width,
          finalBoundingRect.height,
        );

        snapInfo = snapResult.snapInfo;
        didCenterSnap = snapResult.didCenterSnap;
      }
    }

    ShapeData finalShapeData;

    // --- Special Handling for Group Shapes ---
    if (shapeData is GroupShapeData) {
      final groupData = shapeData;
      final finalTranslation =
          finalCenter - shapeData.center; // Use the potentially snapped center

      final transformedChildren = groupData.childShapes.map((childShape) {
        final newChildVertices = childShape.vertices
            .map((v) => v.translate(finalTranslation.dx, finalTranslation.dy))
            .toList();
        final newChildCenter = childShape.center
            .translate(finalTranslation.dx, finalTranslation.dy);
        // Note: Bounds for children are not recalculated here, maybe they should be?
        return childShape.copyWith(
          vertices: newChildVertices,
          center: newChildCenter,
        );
      }).toList();

      finalShapeData = groupData.copyWithChildren(
        childShapes: transformedChildren,
        vertices: finalVertices,
        center: finalCenter,
        boundingRect: finalBoundingRect,
        visualRotation: shapeData.visualRotation,
      );
    } else {
      // --- Regular Shape Update ---
      finalShapeData = shapeData.copyWith(
        vertices: finalVertices,
        center: finalCenter,
        boundingRect: finalBoundingRect,
        visualRotation: shapeData.visualRotation,
      );
    }
    // --- End Special Handling ---

    // Return the final shape data and any snap info
    return (
      shapeData: finalShapeData,
      snapInfo: snapInfo,
      didCenterSnap: didCenterSnap
    );
  }

  // --- Helper function to build path (extracted for reuse) ---
  static Path _buildPathWithCurves(
      List<Offset> vertices, Map<int, Offset> curveControls) {
    // Create a temporary ShapeData for consistent path building
    final tempShapeData = ShapeData(
      type: ShapeType.rectangle, // Type doesn't matter for path building
      vertices: vertices,
      center: Offset(0, 0), // Center doesn't affect path building
      boundingRect: Rect.zero, // Not used for path building
      curveControls: curveControls,
      rotation: 0.0, // Not applying rotation here
    );

    // Use the centralized path building function
    return GeometryUtils.buildShapePath(tempShapeData);
  }

  /// Handle edge dragging in curve mode
  /// This manipulates the bezier curve control points
  static ShapeData _handleCurveEdgeDrag({
    required ShapeData shapeData,
    required int edgeIndex,
    required Offset edgeCenter,
    required Offset dragPosition,
    required BoxConstraints constraints,
  }) {
    // --- Store original state in case we need to revert ---
    final originalShapeData = shapeData.deepCopy();

    // Transform the positions to account for rotation
    final matrix = GeometryUtils.createRotationMatrix(
        shapeData.center, -shapeData.rotation);

    final points = shapeData.vertices;
    final startPoint = points[edgeIndex];
    final endPoint = points[(edgeIndex + 1) % points.length];

    final transformedDragPos = MatrixUtils.transformPoint(matrix, dragPosition);
    final transformedEdgeCenter =
        MatrixUtils.transformPoint(matrix, edgeCenter);

    // Calculate the desired offset in the un-rotated space
    final desiredOffset = transformedDragPos - transformedEdgeCenter;

    // Calculate the edge length to determine snap threshold
    final edgeLength = (endPoint - startPoint).distance;

    // Determine snap threshold based on edge length (smaller edges = smaller threshold)
    // Using 5% of edge length as threshold with minimum of 5.0 and maximum of 15.0
    final snapThreshold = (edgeLength * 0.05).clamp(5.0, 15.0);

    Offset finalOffset = desiredOffset;
    Map<int, Offset> curveControls =
        Map<int, Offset>.from(shapeData.curveControls);

    // Check if the offset is small enough to snap to a straight line
    if (desiredOffset.distance < snapThreshold) {
      // Snap to straight line by removing the curve control for this edge
      curveControls.remove(edgeIndex);
      finalOffset = Offset.zero;
    } else {
      // Calculate how much we need to scale back the offset
      final edgeVector = endPoint - startPoint;
      final desiredControl1 = startPoint + edgeVector * (1 / 3) + desiredOffset;
      final desiredControl2 = startPoint + edgeVector * (2 / 3) + desiredOffset;

      // Calculate bounds scaling to keep control points within screen
      double scale = _calculateBoundsScaling(
        startPoint: startPoint,
        edgeVector: edgeVector,
        desiredControl1: desiredControl1,
        desiredControl2: desiredControl2,
        constraints: constraints,
      );

      // Apply the scaled offset
      finalOffset = desiredOffset * scale;
      // Update the curve control map
      curveControls[edgeIndex] = finalOffset;
    }

    // --- Bounding Box Calculation with Path Flattening ---
    // Create a temporary ShapeData with the updated curve controls for accurate bounds calculation
    final tempShapeData = shapeData.copyWith(
      curveControls: curveControls,
      // Keep existing values for other properties
    );

    // Use the centralized accurate bounds calculation
    final accurateBoundsResult =
        GeometryUtils.calculateAccurateBoundingRect(tempShapeData);

    // Handle case where calculateAccurateBoundingRect returns GroupBoundsData
    Rect potentialBounds;
    if (accurateBoundsResult is GroupBoundsData) {
      potentialBounds = accurateBoundsResult.bounds;
    } else {
      potentialBounds = accurateBoundsResult;
    }
    // --- End Bounding Box Calculation ---

    // --- Constraint Check ---
    // Check if the *entire shape* (including curves) fits within the constraints.
    if (potentialBounds.left < constraints.minWidth ||
        potentialBounds.top < constraints.minHeight ||
        potentialBounds.right > constraints.maxWidth ||
        potentialBounds.bottom > constraints.maxHeight) {
      // If the new shape goes out of bounds, revert to the original state.
      return originalShapeData;
    }
    // --- End Constraint Check ---

    // If constraints are met, proceed with the update.
    return shapeData.copyWith(
      curveControls: curveControls, // Use the final curve controls map
      boundingRect:
          potentialBounds, // Use the calculated (potentially flattened) bounds
      visualRotation: shapeData.visualRotation, // Preserve visual rotation
      // Center and vertices remain the same when only curving edges
    );
  }

  /// Calculate scaling factor to keep bezier control points within bounds
  static double _calculateBoundsScaling({
    required Offset startPoint,
    required Offset edgeVector,
    required Offset desiredControl1,
    required Offset desiredControl2,
    required BoxConstraints constraints,
  }) {
    double scale = 1.0;

    // Check X bounds for both control points
    if (desiredControl1.dx < 0) {
      scale = math.min(
          scale,
          (startPoint.dx + edgeVector.dx / 3) /
              (startPoint.dx + edgeVector.dx / 3 - desiredControl1.dx));
    }
    if (desiredControl1.dx > constraints.maxWidth) {
      scale = math.min(
          scale,
          (constraints.maxWidth - (startPoint.dx + edgeVector.dx / 3)) /
              (desiredControl1.dx - (startPoint.dx + edgeVector.dx / 3)));
    }
    if (desiredControl2.dx < 0) {
      scale = math.min(
          scale,
          (startPoint.dx + 2 * edgeVector.dx / 3) /
              (startPoint.dx + 2 * edgeVector.dx / 3 - desiredControl2.dx));
    }
    if (desiredControl2.dx > constraints.maxWidth) {
      scale = math.min(
          scale,
          (constraints.maxWidth - (startPoint.dx + 2 * edgeVector.dx / 3)) /
              (desiredControl2.dx - (startPoint.dx + 2 * edgeVector.dx / 3)));
    }

    // Check Y bounds for both control points
    if (desiredControl1.dy < 0) {
      scale = math.min(
          scale,
          (startPoint.dy + edgeVector.dy / 3) /
              (startPoint.dy + edgeVector.dy / 3 - desiredControl1.dy));
    }
    if (desiredControl1.dy > constraints.maxHeight) {
      scale = math.min(
          scale,
          (constraints.maxHeight - (startPoint.dy + edgeVector.dy / 3)) /
              (desiredControl1.dy - (startPoint.dy + edgeVector.dy / 3)));
    }
    if (desiredControl2.dy < 0) {
      scale = math.min(
          scale,
          (startPoint.dy + 2 * edgeVector.dy / 3) /
              (startPoint.dy + 2 * edgeVector.dy / 3 - desiredControl2.dy));
    }
    if (desiredControl2.dy > constraints.maxHeight) {
      scale = math.min(
          scale,
          (constraints.maxHeight - (startPoint.dy + 2 * edgeVector.dy / 3)) /
              (desiredControl2.dy - (startPoint.dy + 2 * edgeVector.dy / 3)));
    }

    return scale;
  }

  /// Helper method to adjust vertices to preserve aspect ratio
  static void _adjustVerticesToPreserveAspectRatio(List<Offset> vertices,
      int draggedVertexIndex, double targetAspectRatio, ShapeType shapeType) {
    // This implementation focuses on rectangles which are the most common case
    if (shapeType == ShapeType.rectangle || shapeType == ShapeType.group) {
      // For rectangles, the opposite corner is our anchor point
      final oppositeIndex = (draggedVertexIndex + 2) % 4;
      final anchorPoint = vertices[oppositeIndex];

      // Create a temporary ShapeData for accurate bounds calculation
      final tempShapeData = ShapeData(
        type: shapeType,
        vertices: vertices,
        center: Offset(0, 0), // Center doesn't matter for this calculation
        boundingRect: Rect.zero, // This will be calculated
        curveControls: {}, // No curves in this calculation
      );

      // Calculate accurate bounds
      final boundsResult =
          GeometryUtils.calculateAccurateBoundingRect(tempShapeData);

      // Handle case where calculateAccurateBoundingRect returns GroupBoundsData
      Rect rect;
      if (boundsResult is GroupBoundsData) {
        rect = boundsResult.bounds;
      } else {
        rect = boundsResult;
      }

      double width = rect.width;
      double height = rect.height;

      // Calculate the current aspect ratio
      final currentAspectRatio = width / height;

      if (currentAspectRatio != targetAspectRatio) {
        // Determine whether to adjust width or height
        if (currentAspectRatio > targetAspectRatio) {
          // Too wide, need to adjust width
          width = height * targetAspectRatio;
        } else {
          // Too tall, need to adjust height
          height = width / targetAspectRatio;
        }

        // Create a new rect with the proper aspect ratio
        final newRect = Rect.fromCenter(
          center: rect.center,
          width: width,
          height: height,
        );

        // Recalculate the vertices based on the new rect
        final newTopLeft = newRect.topLeft;
        final newTopRight = newRect.topRight;
        final newBottomRight = newRect.bottomRight;
        final newBottomLeft = newRect.bottomLeft;

        // Update vertices while keeping the opposite corner fixed
        switch (draggedVertexIndex) {
          case 0: // Top-left
            vertices[0] = newTopLeft;
            vertices[1] = newTopRight;
            vertices[2] = anchorPoint; // Bottom-right (fixed)
            vertices[3] = newBottomLeft;
            break;
          case 1: // Top-right
            vertices[0] = newTopLeft;
            vertices[1] = newTopRight;
            vertices[2] = newBottomRight;
            vertices[3] = anchorPoint; // Bottom-left (fixed)
            break;
          case 2: // Bottom-right
            vertices[0] = anchorPoint; // Top-left (fixed)
            vertices[1] = newTopRight;
            vertices[2] = newBottomRight;
            vertices[3] = newBottomLeft;
            break;
          case 3: // Bottom-left
            vertices[0] = newTopLeft;
            vertices[1] = anchorPoint; // Top-right (fixed)
            vertices[2] = newBottomRight;
            vertices[3] = newBottomLeft;
            break;
        }
      }
    }
    // For other shape types, additional implementations would go here
  }
}
