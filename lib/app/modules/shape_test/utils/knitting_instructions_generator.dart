import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/new_item/controllers/new_item_wizard_controller.dart';
import 'dart:math' as math;

import '../models/shape_data.dart';
import '../models/group_shape_data.dart';

/// Utility class for generating knitting instructions from shapes
class KnittingInstructionsGenerator {
  /// Creates a transformed path for a shape based on its properties
  static Path _createShapePath(ShapeData shape) {
    // For group shapes, we need to create a combined path from all children
    if (shape is GroupShapeData) {
      Path? groupPath;

      for (final childShape in shape.childShapes) {
        final childPath = _createShapePath(childShape);

        if (groupPath == null) {
          groupPath = childPath;
        } else {
          groupPath = Path.combine(
            PathOperation.union,
            groupPath,
            childPath,
          );
        }
      }

      return groupPath ?? Path();
    }

    final path = shape.getShapePath();

    // showDialog(
    //   context: Get.context!,
    //   builder: (context) => Dialog(
    //     child: ShapePathVisualizer(shapeData: shape, path: path),
    //   ),
    // );

    return path;
  }

  static List<List<bool>> generateKnittingInstructionsNew({
    required List<ShapeData> shapes,
    required int numberOfRows,
    required int numberOfColumns,
    required double gridSize, // size of each cell in the grid in pixels (5)
    required double stitchesPerCm,
    required double rowsPerCm,
    required double gridOffsetY,
    required double gridOffsetX,
    double Function(double)? pixelsToCm,
  }) {
    final NewItemWizardController newItemWizardController =
        NewItemWizardController.to;
    if (newItemWizardController.newItem.value.knittingMachine == null) {
      return [];
    }

    if (stitchesPerCm <= 0 || rowsPerCm <= 0) {
      Get.snackbar(
        'Warning',
        'Please set your knitting gauge in the settings first',
        backgroundColor: Colors.orange,
      );
      return [];
    }

    // Initialize instructions array
    final instructions = List.generate(
      numberOfRows,
      (i) => List.generate(numberOfColumns, (j) => false),
    );

    if (shapes.isEmpty) return instructions;

    // Constants for sampling - adjusted for better precision
    const samplesPerCell = 12;
    const fillThreshold =
        0.3; // Increased threshold to ensure better shape accuracy

    // Create a combined path from all shapes
    Path? combinedPath;

    // Add each shape to the combined path
    for (ShapeData shape in shapes.reversed) {
      // Create the base shape path
      final shapePath = _createShapePath(shape);

      if (combinedPath == null) {
        combinedPath = shapePath;
      } else {
        // Combine paths using union operation
        combinedPath = Path.combine(
          PathOperation.union,
          combinedPath,
          shapePath,
        );
      }
    }
    // showDialog(
    //   context: Get.context!,
    //   builder: (context) => Dialog(
    //     child: ShapePathVisualizer(shapeData: shapes.first, path: combinedPath),
    //   ),
    // );

    if (combinedPath == null) return instructions;

    // Get combined bounds with smaller inflation to avoid capturing extra cells
    final Rect bounds = combinedPath.getBounds().inflate(gridSize * 0.5);

    // Apply accurate cm conversion if available
    double widthCm, heightCm;
    if (pixelsToCm != null) {
      // Use the provided conversion function
      widthCm = pixelsToCm(bounds.width);
      heightCm = pixelsToCm(bounds.height);
    } else {
      // Fall back to simple calculation
      widthCm = bounds.width / 20; // Assuming 20px = 1cm as fallback
      heightCm = bounds.height / 20;
    }

    // Calculate how many stitches and rows this shape will need in the real world
    final totalStitches = (widthCm * stitchesPerCm).ceil();
    final totalRows = (heightCm * rowsPerCm).ceil();

    // Log the size calculations
    debugPrint('Shape size: ${bounds.width} x ${bounds.height} pixels');
    debugPrint('Real-world size: $widthCm x $heightCm cm');
    debugPrint('Requires $totalStitches stitches and $totalRows rows');

    // Convert shape position to grid coordinates - reduced margins to prevent capturing extra cells
    final startCol =
        math.max(0, ((bounds.left - gridOffsetX) / gridSize).floor() - 1);
    final endCol = math.min(
        numberOfColumns, ((bounds.right - gridOffsetX) / gridSize).ceil() + 1);
    final startRow =
        math.max(0, ((bounds.top - gridOffsetY) / gridSize).floor() - 1);
    final endRow = math.min(
        numberOfRows, ((bounds.bottom - gridOffsetY) / gridSize).ceil() + 1);

    // Log grid boundaries for debugging
    debugPrint(
        'Grid boundaries: col $startCol to $endCol, row $startRow to $endRow');
    debugPrint('Total grid: $numberOfColumns columns, $numberOfRows rows');

    // Sample the combined path with improved boundary detection
    for (var row = startRow; row < endRow; row++) {
      for (var col = startCol; col < endCol; col++) {
        // Skip cells outside the valid grid
        if (row < 0 ||
            row >= instructions.length ||
            col < 0 ||
            col >= instructions[0].length) {
          continue;
        }

        // Calculate cell center coordinates - fixed coordinate calculation
        final centerX = col * gridSize + (gridSize / 2) + gridOffsetX;
        final centerY = row * gridSize + (gridSize / 2) + gridOffsetY;

        // If center point is in shape, mark as filled without further sampling
        if (combinedPath.contains(Offset(centerX, centerY))) {
          instructions[row][col] = true;
          continue;
        }

        // Check four corner points of the cell - fixed coordinate calculation
        final corners = [
          Offset(col * gridSize + gridOffsetX,
              row * gridSize + gridOffsetY), // Top-left
          Offset((col + 1) * gridSize + gridOffsetX,
              row * gridSize + gridOffsetY), // Top-right
          Offset(col * gridSize + gridOffsetX,
              (row + 1) * gridSize + gridOffsetY), // Bottom-left
          Offset((col + 1) * gridSize + gridOffsetX,
              (row + 1) * gridSize + gridOffsetY), // Bottom-right
        ];

        bool cornerHit = corners.any((point) => combinedPath!.contains(point));

        // If center and corners are all outside, check edges with additional points
        if (!cornerHit) {
          // Add more edge points for better detection of thin shapes
          final edges = [
            // Standard edge midpoints
            Offset(centerX, row * gridSize + gridOffsetY), // Top middle
            Offset(
                centerX, (row + 1) * gridSize + gridOffsetY), // Bottom middle
            Offset(col * gridSize + gridOffsetX, centerY), // Left middle
            Offset((col + 1) * gridSize + gridOffsetX, centerY), // Right middle

            // Additional quarter points for better edge detection
            Offset(col * gridSize + gridSize * 0.25 + gridOffsetX,
                centerY), // Left quarter
            Offset(col * gridSize + gridSize * 0.75 + gridOffsetX,
                centerY), // Right quarter
            Offset(centerX,
                row * gridSize + gridSize * 0.25 + gridOffsetY), // Top quarter
            Offset(
                centerX,
                row * gridSize +
                    gridSize * 0.75 +
                    gridOffsetY), // Bottom quarter
          ];

          bool edgeHit = edges.any((point) => combinedPath!.contains(point));

          // If no edge points hit, continue to next cell
          if (!edgeHit) continue;
        }

        // For boundary cells or close to shape, do detailed sampling
        var hitCount = 0;
        var totalSamples = samplesPerCell * samplesPerCell;

        // Detailed sampling for cells that might have partial coverage - fixed coordinate calculation
        for (var sy = 0; sy < samplesPerCell; sy++) {
          for (var sx = 0; sx < samplesPerCell; sx++) {
            final x = col * gridSize +
                ((sx + 0.5) * (gridSize / samplesPerCell)) +
                gridOffsetX;
            final y = row * gridSize +
                ((sy + 0.5) * (gridSize / samplesPerCell)) +
                gridOffsetY;

            if (combinedPath.contains(Offset(x, y))) {
              hitCount++;
            }
          }
        }

        // Calculate coverage and set stitch if threshold is met - adjusted thresholds
        final coverage = hitCount / totalSamples;

        // Only slightly reduce threshold for edge cells, but not as aggressively
        final isEdgeCell = col == 0 ||
            col == numberOfColumns - 1 ||
            row == 0 ||
            row == numberOfRows - 1;
        final effectiveThreshold =
            isEdgeCell ? fillThreshold * 0.9 : fillThreshold;

        if (coverage >= effectiveThreshold) {
          instructions[row][col] = true;
        }
      }
    }

    // Post-processing to fill isolated empty cells, but more conservatively
    for (var row = startRow + 1; row < endRow - 1; row++) {
      for (var col = startCol + 1; col < endCol - 1; col++) {
        if (!instructions[row][col] &&
            row >= 0 &&
            row < instructions.length &&
            col >= 0 &&
            col < instructions[row].length) {
          // Count filled neighbors
          int filledNeighbors = 0;
          for (var dy = -1; dy <= 1; dy++) {
            for (var dx = -1; dx <= 1; dx++) {
              if (dx == 0 && dy == 0) continue; // Skip center

              final checkRow = row + dy;
              final checkCol = col + dx;

              if (checkRow >= 0 &&
                  checkRow < instructions.length &&
                  checkCol >= 0 &&
                  checkCol < instructions[checkRow].length &&
                  instructions[checkRow][checkCol]) {
                filledNeighbors++;
              }
            }
          }

          // Require more surrounding filled cells (5+) to fill a hole
          if (filledNeighbors >= 5) {
            instructions[row][col] = true;
          }
        }
      }
    }

    // Apply optimization to remove empty rows after the last shape
    return optimizeInstructions(instructions);
  }

  // Helper method to optimize instructions
  /*
   * IMPORTANT NOTE ON ROW NUMBERING:
   * In the instructions array, index 0 represents the FIRST row generated.
   * 
   * In knitting using our standardized top-down approach:
   * - Row 1 corresponds to index 0 (first row in the array)
   * - Row N corresponds to index N-1 (last row in the array)
   * 
   * This array optimization preserves this mapping while removing any empty
   * rows that don't contribute to the pattern.
   */
  static List<List<bool>> optimizeInstructions(List<List<bool>> instructions) {
    final List<List<bool>> optimizedInstructions = [];
    var firstStitchRow = -1;
    var lastStitchRow = -1;

    // Find first and last rows with stitches
    for (var i = 0; i < instructions.length; i++) {
      if (instructions[i].contains(true)) {
        if (firstStitchRow == -1) firstStitchRow = i;
        lastStitchRow = i;
      }
    }

    if (firstStitchRow == -1) {
      return [List.generate(instructions[0].length, (j) => false)];
    }

    // Keep all rows between first and last stitch with proper spacing
    var previousStitchRow = -1;
    for (var i = firstStitchRow; i <= lastStitchRow; i++) {
      if (instructions[i].contains(true)) {
        if (previousStitchRow != -1 && i - previousStitchRow > 1) {
          final gapSize = i - previousStitchRow - 1;
          final preservedGapSize = math.max(1, gapSize ~/ 2);
          for (var j = 0; j < preservedGapSize; j++) {
            optimizedInstructions
                .add(List.generate(instructions[0].length, (j) => false));
          }
        }
        optimizedInstructions.add(instructions[i]);
        previousStitchRow = i;
      }
    }

    return optimizedInstructions;
  }

  /// Generates knitting instructions with option to use vector-based or sampling-based approach
  /// @param useVectorApproach If true, uses the more accurate vector-based intersection approach.
  ///                         If false, uses the original sampling-based approach.
  static List<List<bool>> generateKnittingInstructions({
    required List<ShapeData> shapes,
    required int numberOfRows,
    required int numberOfColumns,
    required double gridSize,
    required double stitchesPerCm,
    required double rowsPerCm,
    required double gridOffsetY,
    required double gridOffsetX,
    double Function(double)? pixelsToCm,
  }) {
    return generateKnittingInstructionsNew(
      shapes: shapes,
      numberOfRows: numberOfRows,
      numberOfColumns: numberOfColumns,
      gridSize: gridSize,
      stitchesPerCm: stitchesPerCm,
      rowsPerCm: rowsPerCm,
      gridOffsetY: gridOffsetY,
      gridOffsetX: gridOffsetX,
      pixelsToCm: pixelsToCm,
    );
  }
}
