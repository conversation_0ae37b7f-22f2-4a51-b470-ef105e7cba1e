import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Helper class for handling decimal input with international support
/// Supports both comma (,) and period (.) as decimal separators
class DecimalInputHelper {
  /// Parse a string that may contain either comma or period as decimal separator
  /// Returns null if the input is invalid
  static double? parseDecimal(String input) {
    if (input.isEmpty) return null;

    // Remove any whitespace
    final trimmed = input.trim();
    if (trimmed.isEmpty) return null;

    // Handle the case where user just typed a separator
    if (trimmed == '.' || trimmed == ',') return null;

    // Count decimal separators to detect invalid input
    final periodCount = trimmed.split('.').length - 1;
    final commaCount = trimmed.split(',').length - 1;

    // Invalid if more than one decimal separator total
    if (periodCount + commaCount > 1) return null;

    // Invalid if both separators are present
    if (periodCount > 0 && commaCount > 0) return null;

    // Normalize by replacing comma with period
    String normalized = trimmed.replaceAll(',', '.');

    // Try parsing the normalized string
    try {
      return double.parse(normalized);
    } catch (e) {
      return null;
    }
  }

  /// Parse a decimal with validation for positive values
  /// Returns null if invalid or not positive
  static double? parsePositiveDecimal(String input) {
    final value = parseDecimal(input);
    if (value == null || value <= 0) return null;
    return value;
  }

  /// Format a decimal value for display based on locale preferences
  /// If useCommaAsSeparator is null, it will auto-detect based on system locale
  static String formatDecimal(double value,
      {int decimalPlaces = 1, bool? useCommaAsSeparator}) {
    // Auto-detect decimal separator preference if not specified
    bool shouldUseComma =
        useCommaAsSeparator ?? _shouldUseCommaAsDecimalSeparator();

    // Format the number with the specified decimal places
    String formatted = value.toStringAsFixed(decimalPlaces);

    // Replace period with comma if needed
    if (shouldUseComma) {
      formatted = formatted.replaceAll('.', ',');
    }

    return formatted;
  }

  /// Auto-detect if the system locale prefers comma as decimal separator
  static bool _shouldUseCommaAsDecimalSeparator() {
    try {
      // Try to detect based on system locale
      final systemLocale = Platform.localeName;

      // List of locales that typically use comma as decimal separator
      final commaLocales = [
        'de',
        'fr',
        'es',
        'it',
        'pt',
        'ru',
        'pl',
        'nl',
        'sv',
        'da',
        'no',
        'fi',
        'cs',
        'sk',
        'hu',
        'ro',
        'bg',
        'hr',
        'sl',
        'et',
        'lv',
        'lt',
        'el',
        'tr'
      ];

      // Extract language code from locale (e.g., 'de_DE' -> 'de')
      final languageCode = systemLocale.split('_').first.toLowerCase();

      return commaLocales.contains(languageCode);
    } catch (e) {
      // If detection fails, default to period
      return false;
    }
  }

  /// Get a user-friendly error message for invalid decimal input
  static String getInvalidDecimalMessage() {
    return 'shapeEditor_propertyHud_errors_invalidDecimal'.tr;
  }

  /// Get the appropriate decimal separator for display
  static String getDecimalSeparator({bool? useCommaAsSeparator}) {
    bool shouldUseComma =
        useCommaAsSeparator ?? _shouldUseCommaAsDecimalSeparator();
    return shouldUseComma ? ',' : '.';
  }

  /// Create a helper text for input fields showing supported formats
  static String getInputHelpText() {
    return 'shapeEditor_propertyHud_decimalInputHelp'
        .tr; // "Use . or , for decimals"
  }

  /// Validate decimal input in real-time and provide user feedback
  static ValidationResult validateDecimalInput(String input,
      {bool requirePositive = false}) {
    if (input.isEmpty) {
      return ValidationResult(isValid: true, value: null, errorMessage: null);
    }

    final value = parseDecimal(input);

    if (value == null) {
      return ValidationResult(
          isValid: false,
          value: null,
          errorMessage: getInvalidDecimalMessage());
    }

    if (requirePositive && value <= 0) {
      return ValidationResult(
          isValid: false,
          value: null,
          errorMessage: 'shapeEditor_propertyHud_errors_positive'.tr);
    }

    return ValidationResult(isValid: true, value: value, errorMessage: null);
  }
}

/// Result of decimal input validation
class ValidationResult {
  final bool isValid;
  final double? value;
  final String? errorMessage;

  const ValidationResult({
    required this.isValid,
    required this.value,
    required this.errorMessage,
  });
}
