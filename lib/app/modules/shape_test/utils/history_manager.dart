import 'package:flutter/material.dart';
import 'package:xoxknit/app/modules/shape_test/models/shape_data.dart';
import 'dart:async';

/// Represents a single state in the history for undo/redo
class HistoryEntry {
  /// The shape states at this point in history
  final Map<Key, ShapeData> shapeStates;

  /// The order of shapes at this point in history
  final List<Key> shapeOrder;

  /// The selected shape indices at this point in history
  final List<int> selectedIndices;

  /// The curve mode state for each shape key
  final Map<Key, bool> curveModeStates;

  /// Name of the operation that created this history entry (for UI display)
  final String? operationName;

  /// Timestamp when this entry was created
  final DateTime timestamp;

  HistoryEntry({
    required this.shapeStates,
    required this.shapeOrder,
    required this.selectedIndices,
    required this.curveModeStates,
    this.operationName,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  /// Create a true deep copy of this history entry
  HistoryEntry clone() {
    final copiedStates = <Key, ShapeData>{};
    for (final entry in shapeStates.entries) {
      copiedStates[entry.key] = entry.value.deepCopy();
    }
    final copiedCurveModes = Map<Key, bool>.from(curveModeStates);
    return HistoryEntry(
      shapeStates: copiedStates,
      shapeOrder: List<Key>.from(shapeOrder),
      selectedIndices: List<int>.from(selectedIndices),
      curveModeStates: copiedCurveModes,
      operationName: operationName,
      timestamp: timestamp,
    );
  }
}

/// Manages the history of operations for undo/redo functionality
class HistoryManager {
  /// Maximum number of history entries to keep
  static const int _maxHistorySize = 50;

  /// List of history entries
  final List<HistoryEntry> _history = [];

  /// Current position in the history stack (-1 if empty)
  int _currentIndex = -1;

  /// Temporary storage for tracking changes between start/finish operations
  HistoryEntry? _trackingStartState;

  /// Flag to enable memory optimization techniques
  final bool _useMemoryOptimization;

  /// Garbage collection timer - used to clean up unused history entries
  Timer? _gcTimer;

  /// Create a HistoryManager with optional memory optimization
  HistoryManager({bool useMemoryOptimization = false})
      : _useMemoryOptimization = useMemoryOptimization {
    debugPrint("[HistoryManager] Initialized. Max Size: $_maxHistorySize");
    if (_useMemoryOptimization) {
      _scheduleGarbageCollection();
    }
  }

  /// Schedule periodic garbage collection to release memory from unused history entries
  void _scheduleGarbageCollection() {
    _gcTimer?.cancel();
    _gcTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _runGarbageCollection();
    });
  }

  /// Release memory from history entries far from current index
  void _runGarbageCollection() {
    if (_history.isEmpty || !_useMemoryOptimization) return;
    const nearbyRange = 5; // Keep nearby entries untouched

    for (int i = 0; i < _history.length; i++) {
      // Skip entries near current index
      if ((i - _currentIndex).abs() <= nearbyRange) continue;

      // Apply memory optimization for distant entries
      _optimizeHistoryEntry(_history[i]);
    }
  }

  /// Optimize a history entry to reduce memory usage
  /// Only keeps essential data for undo/redo operations
  void _optimizeHistoryEntry(HistoryEntry entry) {
    // This is where we would implement more aggressive
    // memory optimization techniques if needed

    // For now, this is a placeholder for future optimization
  }

  /// Check if undo operation is available
  bool get canUndo => _currentIndex >= 0;

  /// Check if redo operation is available
  bool get canRedo => _currentIndex < _history.length - 1;

  /// Expose the current index for external logic (e.g., UndoRedoManager)
  int get currentIndex => _currentIndex;

  /// Expose the history list for external logic (read-only access intended)
  List<HistoryEntry> get history => List.unmodifiable(_history);

  /// Start tracking changes for a complex operation
  void startTracking(
    Map<Key, ShapeData> shapeStates,
    List<Key> shapeOrder,
    List<int> selectedIndices,
    Map<Key, bool> curveModeStates,
  ) {
    final deepCopiedShapeStates = <Key, ShapeData>{};
    for (final entry in shapeStates.entries) {
      deepCopiedShapeStates[entry.key] = entry.value.deepCopy();
    }

    _trackingStartState = HistoryEntry(
      shapeStates: deepCopiedShapeStates,
      shapeOrder: List<Key>.from(shapeOrder),
      selectedIndices: List<int>.from(selectedIndices),
      curveModeStates: Map<Key, bool>.from(curveModeStates),
    );
  }

  /// Finish tracking and add a history entry if changes were made
  void finishTracking(
      Map<Key, ShapeData> currentShapeStates,
      List<Key> currentShapeOrder,
      List<int> currentSelectedIndices,
      Map<Key, bool> currentCurveModeStates,
      [String? operationName]) {
    if (_trackingStartState == null) {
      return;
    }

    bool hasChanges = _hasChanges(
      _trackingStartState!.shapeStates,
      currentShapeStates,
      _trackingStartState!.shapeOrder,
      currentShapeOrder,
      _trackingStartState!.selectedIndices,
      currentSelectedIndices,
      _trackingStartState!.curveModeStates,
      currentCurveModeStates,
    );

    if (hasChanges) {
      addHistoryEntry(
        currentShapeStates,
        currentShapeOrder,
        currentSelectedIndices,
        currentCurveModeStates,
        operationName,
      );
    } else {}

    _trackingStartState = null;
  }

  /// Check if there were any changes between two states
  bool _hasChanges(
    Map<Key, ShapeData> oldShapeStates,
    Map<Key, ShapeData> newShapeStates,
    List<Key> oldShapeOrder,
    List<Key> newShapeOrder,
    List<int> oldSelectedIndices,
    List<int> newSelectedIndices,
    Map<Key, bool> oldCurveModeStates,
    Map<Key, bool> newCurveModeStates,
  ) {
    // Quick structural checks first (faster)

    // Check if shape collections have different sizes
    if (oldShapeStates.length != newShapeStates.length) {
      debugPrint("  Change: Shape count diff");
      return true;
    }

    // Check if shape keys differ (addition/removal)
    if (!_haveSameKeys(oldShapeStates, newShapeStates)) {
      debugPrint("  Change: Shape keys diff");
      return true;
    }

    // Check shape order
    if (oldShapeOrder.length != newShapeOrder.length) {
      debugPrint("  Change: Shape order length diff");
      return true;
    }
    for (int i = 0; i < oldShapeOrder.length; i++) {
      if (oldShapeOrder[i] != newShapeOrder[i]) {
        debugPrint("  Change: Shape order content diff");
        return true;
      }
    }

    // Check selection
    if (oldSelectedIndices.length != newSelectedIndices.length) {
      debugPrint("  Change: Selection count diff");
      return true;
    }
    if (oldSelectedIndices.isNotEmpty || newSelectedIndices.isNotEmpty) {
      // Sort copies before comparison for more accurate matching regardless of order
      final sortedOld = List<int>.from(oldSelectedIndices)..sort();
      final sortedNew = List<int>.from(newSelectedIndices)..sort();

      if (sortedOld.length != sortedNew.length) {
        debugPrint("  Change: Sorted selection count diff");
        return true;
      } // Should be caught above, but safe check
      for (int i = 0; i < sortedOld.length; i++) {
        if (sortedOld[i] != sortedNew[i]) {
          debugPrint("  Change: Sorted selection content diff");
          return true;
        }
      }
    }

    // Check curve mode states
    if (oldCurveModeStates.length != newCurveModeStates.length) {
      debugPrint("  Change: Curve mode count diff");
      return true;
    }
    for (final key in oldCurveModeStates.keys) {
      if (oldCurveModeStates[key] != newCurveModeStates[key]) {
        debugPrint("  Change: Curve mode value diff for key $key");
        return true;
      }
    }

    // Deep comparison of shape states
    // Compare each shape's key properties to detect meaningful changes
    for (final key in oldShapeStates.keys) {
      final oldShape = oldShapeStates[key]!;
      final newShape = newShapeStates[key]!;

      // Check for meaningful changes in shape properties
      if (_hasShapeChanged(oldShape, newShape)) {
        debugPrint("  Change: Shape data diff for key $key");
        return true;
      }
    }

    return false;
  }

  /// Helper method to check if two maps have exactly the same keys
  bool _haveSameKeys(Map<Key, dynamic> map1, Map<Key, dynamic> map2) {
    if (map1.length != map2.length) return false;
    return map1.keys.every((key) => map2.containsKey(key));
  }

  /// Check if a shape has meaningfully changed between states
  bool _hasShapeChanged(ShapeData oldShape, ShapeData newShape) {
    // Type mismatch is a definite change
    if (oldShape.type != newShape.type) return true;

    // Compare core geometric properties
    if (oldShape.rotation != newShape.rotation) return true;
    if (oldShape.center != newShape.center) return true;
    if (oldShape.boundingRect != newShape.boundingRect) return true;

    // Compare vertices
    if (oldShape.vertices.length != newShape.vertices.length) return true;
    const epsilon = 0.001;
    for (int i = 0; i < oldShape.vertices.length; i++) {
      final v1 = oldShape.vertices[i];
      final v2 = newShape.vertices[i];
      if ((v1.dx - v2.dx).abs() > epsilon || (v1.dy - v2.dy).abs() > epsilon) {
        return true;
      }
    }

    // Check curve controls
    if (!_mapEquals(oldShape.curveControls, newShape.curveControls)) {
      return true;
    }

    return false;
  }

  /// Compare two maps with numeric keys and Offset values
  bool _mapEquals(Map<int, Offset> map1, Map<int, Offset> map2) {
    if (map1.length != map2.length) return false;

    const epsilon = 0.001; // Small tolerance for float comparison
    for (final key in map1.keys) {
      if (!map2.containsKey(key)) return true;

      final v1 = map1[key]!;
      final v2 = map2[key]!;
      if ((v1.dx - v2.dx).abs() > epsilon || (v1.dy - v2.dy).abs() > epsilon) {
        return true;
      }
    }

    return true;
  }

  /// Add a new history entry and trim older entries if needed
  void addHistoryEntry(Map<Key, ShapeData> shapeStates, List<Key> shapeOrder,
      List<int> selectedIndices, Map<Key, bool> curveModeStates,
      [String? operationName]) {
    debugPrint(
        "[HistoryManager] Adding history entry for: ${operationName ?? 'Unnamed'}. Current Index: $_currentIndex, History Size: ${_history.length}");
    if (_currentIndex < _history.length - 1) {
      debugPrint("  Truncating future history from index ${_currentIndex + 1}");
      _history.removeRange(_currentIndex + 1, _history.length);
    }

    // Remove the async processing - perform deep copy synchronously
    final deepCopiedShapeStates = <Key, ShapeData>{};
    for (final entry in shapeStates.entries) {
      deepCopiedShapeStates[entry.key] = entry.value.deepCopy();
    }

    final entry = HistoryEntry(
      shapeStates: deepCopiedShapeStates,
      shapeOrder: List<Key>.from(shapeOrder),
      selectedIndices: List<int>.from(selectedIndices),
      curveModeStates: Map<Key, bool>.from(curveModeStates),
      operationName: operationName,
    );

    _history.add(entry);
    _currentIndex = _history.length - 1;
    debugPrint(
        "  Added entry. New Index: $_currentIndex, New Size: ${_history.length}");

    _enforceHistorySizeLimit();
  }

  /// Enforce the maximum history size limit
  void _enforceHistorySizeLimit() {
    if (_history.length > _maxHistorySize) {
      debugPrint(
          "  History size limit exceeded ($_maxHistorySize). Removing oldest entry.");
      _history.removeAt(0);
      _currentIndex--;
      debugPrint("  Index adjusted to $_currentIndex after removal.");
    }
  }

  /// Dispose of resources
  void dispose() {
    debugPrint("[HistoryManager] Disposing...");
    _gcTimer?.cancel();
    _gcTimer = null;
    _history.clear();
    _currentIndex = -1;
    _trackingStartState = null;
    debugPrint("[HistoryManager] Disposed.");
  }

  /// Undo the last operation and return the previous state
  HistoryEntry? undo(
    Map<Key, ShapeData> currentShapeStates,
    List<Key> currentShapeOrder,
    List<int> currentSelectedIndices,
    Map<Key, bool> currentCurveModeStates,
  ) {
    debugPrint(
        "[HistoryManager] Undo requested. Current Index: $_currentIndex");
    if (!canUndo) {
      debugPrint("  Cannot undo. History is empty or at the beginning.");
      return null;
    }

    // Move back in history
    final entryToUndo = _history[_currentIndex];
    _currentIndex--;
    debugPrint("  Moved index back to $_currentIndex.");

    if (_currentIndex >= 0) {
      debugPrint("  Returning previous state (index: $_currentIndex)");
      return _history[_currentIndex];
    } else {
      debugPrint(
          "  Reached initial state (index is -1). Returning null to signify empty state.");
      return null; // Indicate we've reached the initial empty state
    }
  }

  /// Redo a previously undone operation
  HistoryEntry? redo() {
    debugPrint(
        "[HistoryManager] Redo requested. Current Index: $_currentIndex");
    if (!canRedo) {
      debugPrint("  Cannot redo. No future history available.");
      return null;
    }

    // Move forward in history
    _currentIndex++;
    debugPrint("  Moved index forward to $_currentIndex.");
    debugPrint("  Returning state at new index: $_currentIndex");
    return _history[_currentIndex];
  }

  /// Get the operation name of the last action that can be undone
  String? getLastUndoOperationName() {
    if (!canUndo) return null;
    return _history[_currentIndex].operationName;
  }

  /// Get the operation name of the next action that can be redone
  String? getNextRedoOperationName() {
    if (!canRedo || _currentIndex + 1 >= _history.length) return null;
    return _history[_currentIndex + 1].operationName;
  }

  /// Clear all history
  void clearHistory() {
    debugPrint(
        "[HistoryManager] Clearing history. Current Index: $_currentIndex");
    _history.clear();
    _currentIndex = -1;
    _trackingStartState = null;
    debugPrint("  History cleared. Index reset to -1.");
  }

  /// Cancel the current tracking operation without saving
  void cancelTracking() {
    debugPrint("[HistoryManager] Cancelling tracking...");
    _trackingStartState = null;
    debugPrint("  Tracking cancelled.");
  }
}
