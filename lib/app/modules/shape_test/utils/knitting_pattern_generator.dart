import 'dart:math' as math;

/// Utility class for converting boolean grid instructions into knitting patterns
class KnittingPatternGenerator {
  /// Generates a text-based knitting pattern from a boolean grid
  ///
  /// Parameters:
  /// - instructions: 2D boolean array where true indicates a stitch
  /// - startFromBottom: Whether to start the pattern from the bottom (default: true)
  /// - includeRowNumbers: Whether to include row numbers in the pattern (default: true)
  static String generateTextPattern({
    required List<List<bool>> instructions,
    bool startFromBottom = true,
    bool includeRowNumbers = true,
  }) {
    if (instructions.isEmpty || instructions[0].isEmpty) {
      return "No pattern available";
    }

    final buffer = StringBuffer();

    // Determine the iteration order based on startFromBottom
    final List<int> rowIndices = startFromBottom
        ? List.generate(instructions.length, (i) => instructions.length - 1 - i)
        : List.generate(instructions.length, (i) => i);

    for (final rowIndex in rowIndices) {
      final row = instructions[rowIndex];
      final displayRowNum =
          startFromBottom ? instructions.length - rowIndex : rowIndex + 1;

      if (includeRowNumbers) {
        buffer.write('Row ${displayRowNum.toString().padLeft(3)}: ');
      }

      // Find the first and last stitch in the row for optimized display
      int firstStitch = 0;
      while (firstStitch < row.length && !row[firstStitch]) {
        firstStitch++;
      }

      int lastStitch = row.length - 1;
      while (lastStitch >= 0 && !row[lastStitch]) {
        lastStitch--;
      }

      // If no stitches in row, show empty row
      if (firstStitch > lastStitch) {
        buffer.write('No stitches');
      } else {
        // For each column in the row
        for (int i = 0; i < row.length; i++) {
          // Compact representation: only show meaningful range
          if (i < firstStitch || i > lastStitch) continue;

          buffer.write(row[i] ? 'K' : '.');
        }
      }

      buffer.writeln();
    }

    return buffer.toString();
  }

  /// Generates a structured representation of the knitting pattern
  /// Returns a list of rows, where each row contains the stitch type for each needle
  static List<List<String>> generateStructuredPattern({
    required List<List<bool>> instructions,
    bool startFromBottom = true,
  }) {
    if (instructions.isEmpty) {
      return [];
    }

    final List<List<String>> pattern = [];

    // Determine the iteration order based on startFromBottom
    final List<int> rowIndices = startFromBottom
        ? List.generate(instructions.length, (i) => instructions.length - 1 - i)
        : List.generate(instructions.length, (i) => i);

    for (final rowIndex in rowIndices) {
      final row = instructions[rowIndex];
      final List<String> patternRow = [];

      for (final stitch in row) {
        patternRow.add(stitch ? 'K' : '.'); // K for knit, . for no stitch
      }

      pattern.add(patternRow);
    }

    return pattern;
  }

  /// Calculate statistics about the knitting pattern
  static Map<String, dynamic> calculatePatternStatistics(
      List<List<bool>> instructions) {
    if (instructions.isEmpty || instructions[0].isEmpty) {
      return {
        'totalRows': 0,
        'totalStitches': 0,
        'maxWidth': 0,
        'minWidth': 0,
        'averageWidth': 0,
      };
    }

    int totalStitches = 0;
    int maxWidth = 0;
    int minWidth = instructions[0].length;

    for (final row in instructions) {
      int rowStitches = row.where((stitch) => stitch).length;
      totalStitches += rowStitches;

      if (rowStitches > 0) {
        maxWidth = math.max(maxWidth, rowStitches);
        minWidth = math.min(minWidth, rowStitches > 0 ? rowStitches : minWidth);
      }
    }

    // Filter out rows with no stitches for average calculation
    final nonEmptyRows = instructions.where((row) => row.contains(true)).length;
    final averageWidth = nonEmptyRows > 0 ? totalStitches / nonEmptyRows : 0;

    return {
      'totalRows': instructions.length,
      'nonEmptyRows': nonEmptyRows,
      'totalStitches': totalStitches,
      'maxWidth': maxWidth,
      'minWidth': minWidth,
      'averageWidth': averageWidth.toStringAsFixed(1),
    };
  }
}
