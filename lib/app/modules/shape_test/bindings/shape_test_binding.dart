import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/shape_editor_controller.dart';

class ShapeTestBinding extends Bindings {
  @override
  void dependencies() {
    // Only register the controller if it's not already registered
    if (!Get.isRegistered<ShapeEditorController>()) {
      Get.put<ShapeEditorController>(
        ShapeEditorController(),
        permanent:
            true, // Make it permanent to prevent re-initialization issues
      );
    }
  }
}
