import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:async';
import 'package:xoxknit/app/modules/shape_test/models/shape_data.dart';
import 'package:xoxknit/app/modules/shape_test/utils/history_manager.dart';

/// Manages undo/redo operations and maintains transaction state
class UndoRedoManager {
  /// History manager for handling the actual state stack
  final HistoryManager _historyManager;

  /// Observable values for undo/redo button states
  final RxBool canUndo = RxBool(false);
  final RxBool canRedo = RxBool(false);

  /// Additional state for history management
  bool _isInTransaction = false;
  final List<String> _currentTransaction = [];

  /// Observable value to track the last operation performed
  final Rx<String?> lastOperation = Rx<String?>(null);

  /// Transaction start timestamp for debugging and analytics
  DateTime? _transactionStartTime;

  /// Configuration for logging verbosity
  final bool _enableDebugLogging;

  /// Constructor with configuration options
  UndoRedoManager({
    bool useMemoryOptimization = true,
    bool enableDebugLogging = false,
  })  : _historyManager =
            HistoryManager(useMemoryOptimization: useMemoryOptimization),
        _enableDebugLogging = enableDebugLogging {
    _logDebug(
        'Initializing with Memory Optimization: $useMemoryOptimization, Debug Logging: $_enableDebugLogging');
    _updateUndoRedoState(); // Initial state
  }

  /// Start tracking history for a shape manipulation
  ///
  /// This begins a transaction that can include multiple operations
  /// which will be grouped in the history stack
  void startHistoryTracking(
      Map<Key, dynamic> shapeStates,
      List<Key> shapeKeysOrder,
      List<int> selectedIndices,
      Map<Key, bool> curveModeStates,
      [String? operationName]) {
    _logDebug(
        'Attempting to start history tracking for: ${operationName ?? 'Unnamed operation'}');
    if (_isInTransaction) {
      _logDebug('Warning: Starting new transaction while one is in progress. '
          'Previous transaction (${_currentTransaction.join(", ")}) will be finalized first.');
      _finishCurrentTransaction(
        shapeStates, // Use current state to finish implicitly
        shapeKeysOrder,
        selectedIndices,
        curveModeStates,
      );
    }

    _currentTransaction.clear(); // Ensure transaction name starts fresh
    if (operationName != null) {
      _currentTransaction.add(operationName);
      _logDebug("Added '$operationName' to current transaction name.");
    }

    _isInTransaction = true;
    _transactionStartTime = DateTime.now();
    _logDebug('Transaction started: ${_currentTransaction.join(", ")}');

    try {
      final typedShapeStates = _convertToShapeDataMap(shapeStates);
      _logDebug(
          'Converted shape states. Calling historyManager.startTracking...');
      _historyManager.startTracking(
        typedShapeStates,
        List<Key>.from(shapeKeysOrder),
        List<int>.from(selectedIndices),
        curveModeStates,
      );
      _logDebug('historyManager.startTracking called successfully.');
    } catch (e, stackTrace) {
      _logError('Error starting transaction: $e', stackTrace);
      _isInTransaction = false;
      _currentTransaction.clear();
      _transactionStartTime = null;
    }
  }

  /// Finish tracking history and record if changes occurred
  ///
  /// This completes the current transaction and adds it to the history stack
  /// if there were any changes made since the transaction started
  void finishHistoryTracking(
    Map<Key, ShapeData> shapeStates,
    List<Key> shapeKeysOrder,
    List<int> selectedIndices,
    Map<Key, bool> curveModeStates,
  ) {
    _logDebug(
        'Attempting to finish history tracking for transaction: ${_currentTransaction.join(", ")}');
    if (!_isInTransaction) {
      _logDebug(
          'Warning: Attempted to finish a transaction that was not started. Ignoring.');
      return;
    }

    _finishCurrentTransaction(
      shapeStates,
      shapeKeysOrder,
      selectedIndices,
      curveModeStates,
    );
    _logDebug('finishHistoryTracking completed.');
  }

  /// Private method to finish the current transaction with error handling
  void _finishCurrentTransaction(
    Map<Key, dynamic> shapeStates,
    List<Key> shapeKeysOrder,
    List<int> selectedIndices,
    Map<Key, bool> curveModeStates,
  ) {
    _logDebug(
        'Executing _finishCurrentTransaction for: ${_currentTransaction.join(", ")}');
    final duration = _transactionStartTime != null
        ? DateTime.now().difference(_transactionStartTime!)
        : null;

    try {
      final typedShapeStates = _convertToShapeDataMap(shapeStates);
      _logDebug(
          'Converted shape states. Calling historyManager.finishTracking...');
      _historyManager.finishTracking(
        typedShapeStates,
        shapeKeysOrder,
        selectedIndices,
        curveModeStates,
        _currentTransaction.isNotEmpty ? _currentTransaction.join(", ") : null,
      );
      _logDebug('historyManager.finishTracking called.');

      if (_currentTransaction.isNotEmpty) {
        final opName = _currentTransaction.join(", ");
        lastOperation.value = opName;
        _logDebug('Set lastOperation to: $opName');
      }

      _logDebug('Transaction completed: ${_currentTransaction.join(", ")}'
          '${duration != null ? ' (Duration: ${duration.inMilliseconds}ms)' : ''}');
    } catch (e, stackTrace) {
      _logError('Error finishing transaction: $e', stackTrace);
    } finally {
      _logDebug('Updating undo/redo state and resetting transaction flags...');
      _updateUndoRedoState();
      _isInTransaction = false;
      _currentTransaction.clear();
      _transactionStartTime = null;
      _logDebug('Transaction state reset.');
    }
  }

  /// Make a snapshot of the current state for history
  ///
  /// This creates a single history entry without starting a transaction
  void addHistoryEntry(
      Map<Key, ShapeData> shapeStates,
      List<Key> shapeKeysOrder,
      List<int> selectedIndices,
      Map<Key, bool> curveModeStates,
      [String? operationName]) {
    _logDebug(
        'Attempting to add direct history entry: ${operationName ?? 'Unnamed operation'}');
    if (_isInTransaction) {
      _logDebug(
          'Warning: Adding direct entry while transaction (${_currentTransaction.join(", ")}) is active. Finalizing transaction first.');
      _finishCurrentTransaction(
          shapeStates, shapeKeysOrder, selectedIndices, curveModeStates);
    }
    try {
      _logDebug('Calling historyManager.addHistoryEntry...');
      _historyManager.addHistoryEntry(
        shapeStates,
        shapeKeysOrder,
        selectedIndices,
        curveModeStates,
        operationName,
      );
      _logDebug('historyManager.addHistoryEntry called successfully.');

      if (operationName != null) {
        lastOperation.value = operationName;
        _logDebug('Set lastOperation to: $operationName');
      }

      _logDebug('Added history entry: ${operationName ?? 'Unnamed operation'}');
    } catch (e, stackTrace) {
      _logError('Error adding history entry: $e', stackTrace);
    }

    _updateUndoRedoState();
  }

  /// Undo the last action
  ///
  /// Returns the previous state from history, or null if there is nothing to undo
  HistoryEntry? undo(
    Map<Key, ShapeData> shapeStates,
    List<Key> shapeKeysOrder,
    List<int> selectedIndices,
    Map<Key, bool> curveModeStates,
  ) {
    _logDebug('Undo requested.');
    if (_isInTransaction) {
      _logDebug(
          'Cancelling ongoing transaction (${_currentTransaction.join(", ")}) due to undo operation.');
      cancelTransaction(); // Cancel ongoing transaction before undo
    }

    HistoryEntry? previousState;
    try {
      _logDebug('Calling historyManager.undo...');
      previousState = _historyManager.undo(
        shapeStates,
        shapeKeysOrder,
        selectedIndices,
        curveModeStates,
      );
      _logDebug(
          'historyManager.undo returned ${previousState == null ? 'null (initial state)' : 'a valid state'}');

      _updateUndoRedoState(); // Update state regardless of outcome

      if (previousState != null) {
        // Determine operation name from the state *before* the undone one
        final lastValidOpIndex = _historyManager
            .currentIndex; // Index points to the state *before* the one returned
        final operationName = lastValidOpIndex >= 0
            ? _historyManager.history[lastValidOpIndex].operationName
            : null; // Should not happen if previousState is not null, but safe check

        final String msg = 'Undo: ${operationName ?? 'Original State'}';
        lastOperation.value = msg;
        _logDebug(msg);
      } else {
        // If undo returns null, it means we are at the beginning of history.
        lastOperation.value = 'At Initial State';
        _logDebug(
            'Undo: Reached beginning of history (initial state). Returning null.');
      }
    } catch (e, stackTrace) {
      _logError('Error during undo: $e', stackTrace);
      lastOperation.value = 'Undo Error';
      // Ensure state is updated even on error
      _updateUndoRedoState();
      return null; // Indicate error or inability to undo
    }

    return previousState;
  }

  /// Redo the previously undone action
  ///
  /// Returns the next state from history, or null if there is nothing to redo
  HistoryEntry? redo() {
    _logDebug('Redo requested.');
    if (_isInTransaction) {
      _logDebug(
          'Cancelling ongoing transaction (${_currentTransaction.join(", ")}) due to redo operation.');
      cancelTransaction(); // Cancel ongoing transaction before redo
    }

    HistoryEntry? nextState;
    try {
      _logDebug('Calling historyManager.redo...');
      nextState = _historyManager.redo();
      _logDebug(
          'historyManager.redo returned ${nextState == null ? 'null (no redo)' : 'a valid state'}');

      _updateUndoRedoState(); // Update state regardless of outcome

      if (nextState != null) {
        final operationName = nextState.operationName;
        final String msg = 'Redo: ${operationName ?? 'Unnamed operation'}';
        lastOperation.value = msg;
        _logDebug(msg);
      } else {
        // This case should ideally not change lastOperation, maybe set to "Nothing to Redo"?
        _logDebug('Redo: Nothing to redo.');
        // lastOperation.value remains unchanged or set explicitly
      }
    } catch (e, stackTrace) {
      _logError('Error during redo: $e', stackTrace);
      lastOperation.value = 'Redo Error';
      // Ensure state is updated even on error
      _updateUndoRedoState();
      return null; // Indicate error or inability to redo
    }

    return nextState;
  }

  /// Helper method to safely convert the dynamic map to the expected ShapeData map
  Map<Key, ShapeData> _convertToShapeDataMap(Map<Key, dynamic> map) {
    final result = <Key, ShapeData>{};
    int nonShapeDataCount = 0;

    for (final entry in map.entries) {
      if (entry.value is ShapeData) {
        result[entry.key] = entry.value as ShapeData;
      } else {
        nonShapeDataCount++;
        _logDebug(
            'Warning: Non-ShapeData value found in shape states map for key ${entry.key}. Type: ${entry.value.runtimeType}');
      }
    }
    if (nonShapeDataCount > 0) {
      _logDebug(
          'Finished conversion: Found $nonShapeDataCount non-ShapeData entries.');
    }

    return result;
  }

  /// Update the observable state for undo/redo buttons
  void _updateUndoRedoState() {
    final prevCanUndo = canUndo.value;
    final prevCanRedo = canRedo.value;
    canUndo.value = _historyManager.canUndo;
    canRedo.value = _historyManager.canRedo;
    if (prevCanUndo != canUndo.value || prevCanRedo != canRedo.value) {
      _logDebug(
          'Undo/Redo state updated: canUndo=${canUndo.value}, canRedo=${canRedo.value}');
    }
  }

  /// Get the operation name of the last action that can be undone
  String? getLastUndoOperationName() {
    final name = _historyManager.getLastUndoOperationName();
    _logDebug('GetLastUndoOperationName called, returning: ${name ?? 'null'}');
    return name;
  }

  /// Get the operation name of the next action that can be redone
  String? getNextRedoOperationName() {
    final name = _historyManager.getNextRedoOperationName();
    _logDebug('GetNextRedoOperationName called, returning: ${name ?? 'null'}');
    return name;
  }

  /// Check if there is an action that can be undone
  bool get hasUndo => canUndo.value;

  /// Check if there is an action that can be redone
  bool get hasRedo => canRedo.value;

  /// Cancel the current transaction without adding a history entry
  void cancelTransaction() {
    _logDebug(
        'Attempting to cancel transaction: ${_currentTransaction.join(", ")}');
    if (_isInTransaction) {
      _logDebug('Transaction is active. Cancelling...');

      _historyManager.cancelTracking();
      _logDebug('Called historyManager.cancelTracking.');

      _isInTransaction = false;
      _currentTransaction.clear();
      _transactionStartTime = null;
      _logDebug('Transaction state flags reset.');

      _updateUndoRedoState();
      _logDebug('Transaction cancelled successfully.');
    } else {
      _logDebug('No active transaction to cancel.');
    }
  }

  /// Clear all history
  void clearHistory() {
    _logDebug('Clearing all history requested.');
    if (_isInTransaction) {
      _logDebug(
          'Warning: Clearing history while transaction (${_currentTransaction.join(", ")}) is active. Transaction will be cancelled.');
      cancelTransaction(); // Ensure transaction is cancelled before clearing
    }
    _historyManager.clearHistory();
    _updateUndoRedoState();
    lastOperation.value = null;
    _logDebug('History cleared successfully.');
  }

  /// Log debug messages if enabled
  void _logDebug(String message) {
    return; // too many logs, disable for now
    if (_enableDebugLogging && kDebugMode) {
      debugPrint('[UndoRedoManager] $message');
    }
  }

  /// Log error messages (always enabled for critical issues)
  void _logError(String message, [StackTrace? stackTrace]) {
    debugPrint('[UndoRedoManager] ERROR: $message');
    if (stackTrace != null) {
      debugPrint(stackTrace.toString());
    }
  }

  /// Clean up resources when no longer needed
  void dispose() {
    _logDebug('Disposing UndoRedoManager...');
    _historyManager.dispose();
    canUndo.close();
    canRedo.close();
    lastOperation.close();
    _logDebug('UndoRedoManager disposed.');
  }

  // Expose internal history manager properties for undo logic adjustment
  HistoryManager get historyManager => _historyManager;
}
