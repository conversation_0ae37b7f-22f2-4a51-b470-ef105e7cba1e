import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'knitting_zone_models.dart';

/// Zone history entry for undo/redo functionality
class ZoneHistoryEntry {
  final List<KnittingZone> zones;
  final int? selectedZoneIndex;
  final String? operationName;
  final DateTime timestamp;

  ZoneHistoryEntry({
    required this.zones,
    this.selectedZoneIndex,
    this.operationName,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  ZoneHistoryEntry copyWith({
    List<KnittingZone>? zones,
    int? selectedZoneIndex,
    String? operationName,
    DateTime? timestamp,
  }) {
    return ZoneHistoryEntry(
      zones: zones ?? this.zones.map((z) => z.copyWith()).toList(),
      selectedZoneIndex: selectedZoneIndex ?? this.selectedZoneIndex,
      operationName: operationName ?? this.operationName,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'zones': zones.map((z) => z.toJson()).toList(),
      'selectedZoneIndex': selectedZoneIndex,
      'operationName': operationName,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory ZoneHistoryEntry.fromJson(Map<String, dynamic> json) {
    return ZoneHistoryEntry(
      zones: (json['zones'] as List?)
              ?.map((z) => KnittingZone.fromJson(z))
              .toList() ??
          [],
      selectedZoneIndex: json['selectedZoneIndex'],
      operationName: json['operationName'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}

/// Zone history manager for undo/redo functionality
///
/// This class provides complete history tracking for all zone operations including:
/// - Creating new zones
/// - Deleting zones
/// - Resizing zone boundaries
/// - Renaming zones
/// - Reordering zones
///
/// Features:
/// - Smart tracking that only records actual changes
/// - Configurable history size limit (default: 50 operations)
/// - Reactive observables for UI integration
/// - Detailed operation names for user feedback
/// - Start/finish tracking pattern for complex operations
/// - Cancel tracking for failed operations
///
/// Usage:
/// ```dart
/// // For simple operations
/// historyManager.addHistoryEntry(zones, selectedIndex, "Operation Name");
///
/// // For complex operations
/// historyManager.startTracking(zones, selectedIndex, "Operation Name");
/// // ... perform modifications ...
/// historyManager.finishTracking(newZones, newSelectedIndex);
///
/// // For failed operations
/// historyManager.cancelTracking();
/// ```
class ZoneHistoryManager {
  final List<ZoneHistoryEntry> _history = [];
  final List<ZoneHistoryEntry> _redoStack = [];
  final int _maxHistorySize = 50;

  // Observables for UI
  final RxBool canUndo = false.obs;
  final RxBool canRedo = false.obs;
  final Rx<String?> lastOperation = Rx<String?>(null);

  // Track if we're currently in the middle of an operation
  ZoneHistoryEntry? _trackingEntry;

  /// Start tracking a zone operation
  void startTracking(List<KnittingZone> currentZones, int? selectedIndex,
      [String? operationName]) {
    _trackingEntry = ZoneHistoryEntry(
      zones: currentZones.map((z) => z.copyWith()).toList(), // Deep copy
      selectedZoneIndex: selectedIndex,
      operationName: operationName,
    );
  }

  /// Finish tracking and add to history if changes occurred
  void finishTracking(List<KnittingZone> newZones, int? newSelectedIndex) {
    if (_trackingEntry == null) return;

    // Check if zones actually changed
    bool hasChanges = _hasZoneChanges(_trackingEntry!.zones, newZones) ||
        _trackingEntry!.selectedZoneIndex != newSelectedIndex;

    if (hasChanges) {
      _addHistoryEntry(_trackingEntry!);
    }

    _trackingEntry = null;
  }

  /// Cancel current tracking without adding to history
  void cancelTracking() {
    _trackingEntry = null;
  }

  /// Add entry directly to history (for operations that don't need tracking)
  void addHistoryEntry(List<KnittingZone> zones, int? selectedIndex,
      [String? operationName]) {
    final entry = ZoneHistoryEntry(
      zones: zones.map((z) => z.copyWith()).toList(),
      selectedZoneIndex: selectedIndex,
      operationName: operationName,
    );
    _addHistoryEntry(entry);
  }

  void _addHistoryEntry(ZoneHistoryEntry entry) {
    // Clear redo stack when new entry is added
    _redoStack.clear();

    // Add to history
    _history.add(entry);

    // Maintain size limit
    if (_history.length > _maxHistorySize) {
      _history.removeAt(0);
    }

    _updateObservables();
    debugPrint(
        '[ZoneHistoryManager] Added history entry: ${entry.operationName}');
  }

  /// Undo last operation
  ZoneHistoryEntry? undo() {
    if (_history.isEmpty) return null;

    // Move current state to redo stack first
    final currentEntry = _history.removeLast();
    _redoStack.add(currentEntry);

    // Get previous state
    ZoneHistoryEntry? previousEntry;
    if (_history.isNotEmpty) {
      previousEntry = _history.last;
    }

    _updateObservables();
    debugPrint('[ZoneHistoryManager] Undoing: ${currentEntry.operationName}');

    return previousEntry;
  }

  /// Redo previously undone operation
  ZoneHistoryEntry? redo() {
    if (_redoStack.isEmpty) return null;

    final redoEntry = _redoStack.removeLast();
    _history.add(redoEntry);

    _updateObservables();
    debugPrint('[ZoneHistoryManager] Redoing: ${redoEntry.operationName}');

    return redoEntry;
  }

  /// Get the name of the last operation that can be undone
  String? getLastUndoOperationName() {
    return _history.isNotEmpty ? _history.last.operationName : null;
  }

  /// Get the name of the next operation that can be redone
  String? getNextRedoOperationName() {
    return _redoStack.isNotEmpty ? _redoStack.last.operationName : null;
  }

  /// Clear all history
  void clearHistory() {
    _history.clear();
    _redoStack.clear();
    _trackingEntry = null;
    _updateObservables();
    debugPrint('[ZoneHistoryManager] History cleared');
  }

  /// Check if zones have changed
  bool _hasZoneChanges(
      List<KnittingZone> oldZones, List<KnittingZone> newZones) {
    if (oldZones.length != newZones.length) return true;

    for (int i = 0; i < oldZones.length; i++) {
      final oldZone = oldZones[i];
      final newZone = newZones[i];

      // Check basic properties
      if (oldZone.name != newZone.name ||
          oldZone.startNeedle != newZone.startNeedle ||
          oldZone.endNeedle != newZone.endNeedle ||
          oldZone.startRow != newZone.startRow ||
          oldZone.endRow != newZone.endRow) {
        return true;
      }

      // Check instructions
      if (oldZone.instructions.length != newZone.instructions.length) {
        return true;
      }

      for (int r = 0; r < oldZone.instructions.length; r++) {
        if (oldZone.instructions[r].length != newZone.instructions[r].length) {
          return true;
        }
        for (int c = 0; c < oldZone.instructions[r].length; c++) {
          if (oldZone.instructions[r][c] != newZone.instructions[r][c]) {
            return true;
          }
        }
      }
    }

    return false;
  }

  void _updateObservables() {
    canUndo.value = _history.isNotEmpty;
    canRedo.value = _redoStack.isNotEmpty;
    lastOperation.value = getLastUndoOperationName();
  }

  /// Get history size for debugging
  int get historySize => _history.length;
  int get redoStackSize => _redoStack.length;
}
