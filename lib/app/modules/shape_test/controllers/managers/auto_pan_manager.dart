import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';

/// Manager for auto-panning functionality when dragging shapes beyond viewport bounds
///
/// This class provides smooth, continuous panning when shapes are dragged beyond
/// the visible viewport edges, following professional design software standards.
class AutoPanManager {
  // Animation and timing constants
  static const double _panSpeed = 200.0; // pixels per second
  static const double _edgeThreshold =
      50.0; // pixels from edge to trigger panning
  static const double _maxPanSpeed = 400.0; // maximum pan speed
  static const Duration _panInterval = Duration(milliseconds: 16); // ~60fps

  // State tracking
  Timer? _panTimer;
  bool _isPanning = false;
  Offset _currentPanVelocity = Offset.zero;

  // Callback for updating pan offset
  final Function(Offset newPanOffset) onPanUpdate;

  AutoPanManager({required this.onPanUpdate});

  /// Check if a shape position requires auto-panning and start if needed
  ///
  /// [shapeGlobalBounds] - The global bounds of the shape being dragged
  /// [viewportSize] - The size of the current viewport
  /// [currentPanOffset] - The current pan offset
  /// [currentZoom] - The current zoom scale
  void checkAndStartAutoPan({
    required Rect shapeGlobalBounds,
    required Size viewportSize,
    required Offset currentPanOffset,
    required double currentZoom,
  }) {
    // Calculate the viewport bounds in global coordinates
    final viewportBounds = Rect.fromLTWH(
      -currentPanOffset.dx / currentZoom,
      -currentPanOffset.dy / currentZoom,
      viewportSize.width / currentZoom,
      viewportSize.height / currentZoom,
    );

    // Calculate how far the shape extends beyond viewport edges
    final double leftOverflow = viewportBounds.left - shapeGlobalBounds.left;
    final double rightOverflow = shapeGlobalBounds.right - viewportBounds.right;
    final double topOverflow = viewportBounds.top - shapeGlobalBounds.top;
    final double bottomOverflow =
        shapeGlobalBounds.bottom - viewportBounds.bottom;

    // Calculate required pan velocity based on overflow
    double panX = 0.0;
    double panY = 0.0;

    // Horizontal panning
    if (leftOverflow > 0) {
      // Shape is beyond left edge, pan left (positive X to move viewport left)
      panX = _calculatePanSpeed(leftOverflow);
    } else if (rightOverflow > 0) {
      // Shape is beyond right edge, pan right (negative X to move viewport right)
      panX = -_calculatePanSpeed(rightOverflow);
    }

    // Vertical panning
    if (topOverflow > 0) {
      // Shape is beyond top edge, pan up (positive Y to move viewport up)
      panY = _calculatePanSpeed(topOverflow);
    } else if (bottomOverflow > 0) {
      // Shape is beyond bottom edge, pan down (negative Y to move viewport down)
      panY = -_calculatePanSpeed(bottomOverflow);
    }

    final newVelocity = Offset(panX, panY);

    // Start panning if velocity is non-zero, stop if zero
    if (newVelocity != Offset.zero) {
      _startPanning(newVelocity);
    } else {
      stopAutoPan();
    }
  }

  /// Calculate pan speed based on distance from viewport edge
  double _calculatePanSpeed(double overflow) {
    // Use exponential curve for more natural feel
    final normalizedDistance = math.min(overflow / _edgeThreshold, 3.0);
    return math.min(
        _panSpeed * math.pow(normalizedDistance, 1.5), _maxPanSpeed);
  }

  /// Start continuous panning with the given velocity
  void _startPanning(Offset velocity) {
    _currentPanVelocity = velocity;

    // Don't start a new timer if already panning with same velocity
    if (_isPanning && _currentPanVelocity == velocity) {
      return;
    }

    // Stop existing timer
    _panTimer?.cancel();

    _isPanning = true;

    // Start new panning timer
    _panTimer = Timer.periodic(_panInterval, (timer) {
      if (!_isPanning) {
        timer.cancel();
        return;
      }

      // Calculate pan delta for this frame
      final deltaTime = _panInterval.inMilliseconds / 1000.0;
      final panDelta = _currentPanVelocity * deltaTime;

      // Apply the pan update
      onPanUpdate(panDelta);
    });
  }

  /// Stop auto-panning
  void stopAutoPan() {
    _isPanning = false;
    _currentPanVelocity = Offset.zero;
    _panTimer?.cancel();
    _panTimer = null;
  }

  /// Check if auto-panning is currently active
  bool get isAutoPanning => _isPanning;

  /// Get current pan velocity
  Offset get currentVelocity => _currentPanVelocity;

  /// Dispose of resources
  void dispose() {
    stopAutoPan();
  }
}
