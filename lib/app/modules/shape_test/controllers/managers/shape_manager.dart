// shape_manager.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For HapticFeedback
import 'package:get/get.dart';
import 'dart:math' as math;

import '../shape_editor_controller.dart';
import '../../models/shape_data.dart';
import '../../models/group_shape_data.dart';
import '../../views/transformable_shape.dart';
import '../../utils/geometry_utils.dart';
import '../../constants/grid_constants.dart';
import '../../handlers/shape_manipulation_handlers.dart';

/// Manages shape states and operations in the shape test controller
class ShapeManager {
  static final ShapeManager _instance = ShapeManager._internal();
  factory ShapeManager() => _instance;
  ShapeManager._internal();

  final RxList<TransformableShape> shapes = <TransformableShape>[].obs;
  final RxMap<Key, ShapeData> shapeStates = <Key, ShapeData>{}.obs;
  final List<Key> shapeKeysOrder = [];
  // Track the last added shape type for the add button icon
  ShapeType? lastAddedShapeType;

  // Creates a shape with the given type and adds it to the shapes list
  TransformableShape addShape(ShapeType type,
      {required BoxConstraints constraints,
      required double screenWidth,
      bool placeAtCenter = true,
      required double currentZoom,
      required Offset currentPanOffset}) {
    // Generate a unique key for the new shape
    final shapeKey = GlobalKey();
    // _keyCounter++;

    // Calculate the center of the VISIBLE area in canvas coordinates
    final viewCenterX = (Get.width / 2 - currentPanOffset.dx) / currentZoom;
    final viewCenterY = (Get.height / 2 - currentPanOffset.dy) / currentZoom;

    // Calculate the CANVAS center line X coordinate (for mirror mode)
    final canvasCenterLineX =
        (screenWidth / 2 - currentPanOffset.dx) / currentZoom;

    // Size of the shape (consider making this dynamic or based on zoom?)
    final shapeWidth = 150.0 / currentZoom; // Adjust size based on zoom?
    final shapeHeight = 150.0 / currentZoom; // Adjust size based on zoom?

    // Position calculation
    double leftX, topY;

    if (placeAtCenter) {
      // Center the shape in the visible viewport when not in mirror mode
      leftX = viewCenterX - shapeWidth / 2;
      topY = viewCenterY - shapeHeight / 2;
    } else {
      // In mirror mode, position the left edge exactly at the canvas center line
      leftX = canvasCenterLineX;
      // Center vertically in the visible viewport
      topY = viewCenterY - shapeHeight / 2;
    }

    // Create the rectangle from left-top coordinates
    final rect = Rect.fromLTWH(
      leftX,
      topY,
      shapeWidth,
      shapeHeight,
    );

    // Create the initial shape data based on type and rect
    ShapeData initialShapeData;
    switch (type) {
      case ShapeType.rectangle:
        initialShapeData = ShapeData.rectangle(rect);
        break;
      case ShapeType.triangle:
        initialShapeData = ShapeData.triangle(rect);
        break;
      case ShapeType.rightTriangle:
        initialShapeData = ShapeData.rightTriangle(rect);
        break;
      case ShapeType.trapezoid:
        initialShapeData = ShapeData.trapezoid(rect);
        break;
      default:
        initialShapeData = ShapeData.rectangle(rect);
    }

    // Store this shape data in our state map immediately
    shapeStates[shapeKey] = initialShapeData;

    // Create extended constraints to match the grid's extended height
    // This ensures the shape can be manipulated throughout the entire grid area
    final extendedConstraints = BoxConstraints(
      minWidth: constraints.minWidth,
      maxWidth: constraints.maxWidth,
      minHeight: constraints.minHeight,
      maxHeight: GridConstants.getExtendedHeight(Get.height),
    );

    // Create the shape with explicit shape data and extended constraints
    final shape = TransformableShape(
      key: shapeKey,
      constraints: extendedConstraints, // Use extended constraints
      initialShapeType: type,
      initialRect: rect,
      initialShapeData: initialShapeData,
      selected: true, // Set the shape as selected when created
    );

    // Store shape
    shapes.add(shape);
    shapeKeysOrder.add(shapeKey);

    // Remember the last shape type for the toolbar icon
    lastAddedShapeType = type;

    return shape;
  }

  // Deletes shapes with the given keys
  void deleteShapes(List<Key> keysToDelete) {
    for (final key in keysToDelete) {
      // Remove shape from main collection
      for (int i = 0; i < shapes.length; i++) {
        if (shapes[i].key == key) {
          shapes.removeAt(i);
          break;
        }
      }

      // Remove from state collections
      shapeStates.remove(key);
      shapeKeysOrder.remove(key);
    }
  }

  /// Removes a shape and its associated state by key.
  void removeShape(Key key) {
    // Remove from the widget list
    shapes.removeWhere((shape) => shape.key == key);
    // Remove from the state map
    shapeStates.remove(key);
    // Remove from the order list
    shapeKeysOrder.remove(key);
    // Optionally, remove from curveModeStates if managed here (though likely in UIStateManager)
    // curveModeStates.remove(key);
    print("[ShapeManager] Removed shape with key: $key");
  }

  // Updates the shape state
  void saveShapeState(Key shapeKey, ShapeData shapeData) {
    shapeStates[shapeKey] = shapeData;

    // Force UI update to reflect shape changes
    try {
      if (Get.isRegistered<ShapeEditorController>()) {
        Get.find<ShapeEditorController>().update();
      }
    } catch (_) {
      // Safely handle case where controller might not be ready yet
    }
  }

  // Gets the shape state for a specific key
  ShapeData? getShapeState(Key? key) {
    if (key == null) return null;
    return shapeStates[key];
  }

  /// Updates the bounding rectangle and center of a shape based on its current state.
  /// Uses the accurate calculation method that considers curves.
  ShapeData updateBoundingRect(ShapeData shapeData) {
    // Calculate the accurate bounding box using the utility function
    final dynamic boundsResult =
        GeometryUtils.calculateAccurateBoundingRect(shapeData);

    // Handle the result based on its type
    if (boundsResult is Rect) {
      // For regular shapes or if the utility returned a simple Rect
      return shapeData.copyWith(
        boundingRect: boundsResult,
        center: boundsResult.center,
      );
    } else if (boundsResult is GroupBoundsData && shapeData is GroupShapeData) {
      // For GroupShapeData, update using GroupBoundsData properties
      return shapeData.copyWithChildren(
        boundingRect: boundsResult.bounds,
        center: boundsResult.center,
        vertices: boundsResult.vertices, // Update group vertices as well
      );
    } else {
      // Fallback: If the result is unexpected, return the original data
      // Or potentially use the old vertex-based calculation as a last resort
      print(
          "Warning: Unexpected result from calculateAccurateBoundingRect in ShapeManager.updateBoundingRect. Falling back.");
      // Fallback to old method just in case
      double minX = double.infinity;
      double minY = double.infinity;
      double maxX = -double.infinity;
      double maxY = -double.infinity;

      for (final vertex in shapeData.vertices) {
        minX = math.min(minX, vertex.dx);
        minY = math.min(minY, vertex.dy);
        maxX = math.max(maxX, vertex.dx);
        maxY = math.max(maxY, vertex.dy);
      }

      const minSize = 20.0;
      final width = math.max(maxX - minX, minSize);
      final height = math.max(maxY - minY, minSize);
      final fallbackRect = Rect.fromLTWH(minX, minY, width, height);

      if (shapeData is GroupShapeData) {
        return shapeData.copyWithChildren(
          boundingRect: fallbackRect,
          center: fallbackRect.center,
        );
      }
      return shapeData.copyWith(
        boundingRect: fallbackRect,
        center: fallbackRect.center,
      );
    }
  }

  Path getShapePath(Key shapeKey) {
    final shape = shapes.firstWhere((shape) => shape.key == shapeKey);
    return shape.getShapePath(Get.context!);
  }

  // Creates a duplicate of a shape with an offset
  ShapeData createOffsetShapeData(ShapeData original, double dx, double dy) {
    // For group shapes, we need to handle the child shapes
    if (original is GroupShapeData) {
      final offsetChildren = <ShapeData>[];

      // Offset each child shape
      for (final child in original.childShapes) {
        offsetChildren.add(createOffsetShapeData(child, dx, dy));
      }

      // Create a new group shape with offset vertices and children
      return original.copyWithChildren(
        childShapes: offsetChildren,
        vertices:
            original.vertices.map((v) => Offset(v.dx + dx, v.dy)).toList(),
        boundingRect: Rect.fromLTWH(
          original.boundingRect.left + dx,
          original.boundingRect.top + dy,
          original.boundingRect.width,
          original.boundingRect.height,
        ),
        center: Offset(original.center.dx + dx, original.center.dy + dy),
      );
    }

    // For regular shapes, just offset the vertices and bounding rect
    return original.copyWith(
      vertices:
          original.vertices.map((v) => Offset(v.dx + dx, v.dy + dy)).toList(),
      boundingRect: Rect.fromLTWH(
        original.boundingRect.left + dx,
        original.boundingRect.top + dy,
        original.boundingRect.width,
        original.boundingRect.height,
      ),
      center: Offset(original.center.dx + dx, original.center.dy + dy),
    );
  }

  // Saves the state for all shapes
  void saveAllShapesState() {
    for (final shape in shapes) {
      if (shape.key != null) {
        final data = shape.getShapeData(Get.context!);
        if (data != null) {
          shapeStates[shape.key!] = data;
        }
      }
    }
  }

  // Clear all shapes
  void resetShapes() {
    shapes.clear();
    shapeStates.clear();
    shapeKeysOrder.clear();
  }

  // Handle a drag event for a specific shape by key
  void handleShapeDrag(
    Key shapeKey,
    DragUpdateDetails details, {
    // --- Added for shape snapping ---
    required Map<Key, ShapeData> allShapeStates,
    required Key currentShapeKey,
    double? snapThreshold, // Optional threshold override
    // --- End Added ---
  }) {
    // Get the shape by key
    final shapeData = shapeStates[shapeKey];
    if (shapeData == null) return;

    // Find the TransformableShape instance with this key
    final shapeIndex = shapes.indexWhere((shape) => shape.key == shapeKey);
    if (shapeIndex == -1) return;

    final shape = shapes[shapeIndex];

    // Get controller reference
    final controller = Get.find<ShapeEditorController>();

    // Different handling for mirror mode (Mirror mode probably shouldn't snap to other shapes)
    if (controller.isMirrorModeActive.value) {
      _handleShapeDragInMirrorMode(shapeKey, details, shape, shapeData);
    } else {
      // Pass snapping parameters to the normal drag handler
      _handleNormalShapeDrag(
        shapeKey,
        details,
        shape,
        shapeData,
        allShapeStates,
        currentShapeKey,
        snapThreshold,
      );
    }
  }

  // Handle shape dragging in normal mode
  void _handleNormalShapeDrag(
    Key shapeKey,
    DragUpdateDetails details,
    TransformableShape shape,
    ShapeData shapeData,
    Map<Key, ShapeData> allShapeStates,
    Key currentShapeKey,
    double? snapThreshold,
  ) {
    final controller = Get.find<ShapeEditorController>();
    final size = Get.size;
    BoxConstraints constraints = shape.constraints;

    // --- Use ShapeManipulationHandlers.handleShapeDrag ---
    final result = ShapeManipulationHandlers.handleShapeDrag(
      shapeData: shapeData,
      delta: details.delta,
      constraints: constraints,
      allShapeStates: allShapeStates,
      currentShapeKey: shapeKey,
      // Add snap function and global position
      getSnapInfo: (
          {required ShapeData originalShapeData,
          required ShapeData potentialShapeData,
          required Offset globalPointerPosition,
          required Key shapeKey}) {
        return controller.snappingManager.calculateDragSnap(
          originalShapeData: originalShapeData,
          potentialShapeData: potentialShapeData,
          globalPointerPosition: globalPointerPosition,
          shapeKey: shapeKey,
          allShapeStates: allShapeStates,
          constraints: constraints,
          isCenterSnapEnabled: controller.snapToCenter.value,
          isShapeSnapEnabled: true,
          // Professional snap parameters for smooth, predictable behavior
          shapeSnapThreshold: 12.0, // Reduced for less aggressive snapping
          centerSnapThreshold: 16.0, // Slightly higher for center line priority
          gridSnapThreshold: 8.0, // Reduced for lighter grid snapping
        );
      },
      globalPointerPosition: details.globalPosition,
    );

    final updatedShapeData = result.shapeData;
    final snapInfo = result.snapInfo;

    // Update controller with the latest snap info
    controller.setActiveSnapInfo(snapInfo);

    // If the data didn't change, do nothing further
    if (updatedShapeData == shapeData) {
      return;
    }

    // --- Handle Snapping Indicators (Grid/Center Snapping Only for now) ---
    // Shape snap indicators will be handled by the new SnapLinePainter
    if (snapInfo == null) {
      // Check for grid/center snapping if no shape snap occurred
      final finalCenter = updatedShapeData.center;
      final snappedCenter =
          controller.snappingManager.snapPoint(finalCenter, size);
      final didGridSnap = (snappedCenter - finalCenter).distance > 0.1;

      if (didGridSnap) {
        final screenCenter = size.width / 2;
        final horizontalCenterLine = size.height / 2;
        final centerSnapPrecision = 2.0;
        final isSnappedToVerticalCenter =
            (snappedCenter.dx - screenCenter).abs() < centerSnapPrecision;
        final isSnappedToHorizontalCenter =
            (snappedCenter.dy - horizontalCenterLine).abs() <
                centerSnapPrecision;

        if (isSnappedToVerticalCenter || isSnappedToHorizontalCenter) {
          controller.addSnapIndicator(snappedCenter, isCenterSnap: true);

          // Add haptic feedback for tactile feel on center snaps
          HapticFeedback.mediumImpact();
        } else {
          controller.addSnapIndicator(snappedCenter);

          // Add light haptic feedback for tactile feel on grid snaps
          HapticFeedback.lightImpact();
        }
      }
    } else {
      // For shape snaps, provide medium haptic feedback
      HapticFeedback.mediumImpact();
    }
    // --- End Indicator Handling ---

    // --- Save and Update UI ---
    shapeStates[shapeKey] = updatedShapeData;

    final newShape = TransformableShape(
      key: shapeKey,
      constraints: shape.constraints,
      initialShapeType: shape.initialShapeType,
      initialRect: shape.initialRect,
      selected: true,
      initialShapeData: updatedShapeData,
      initialCurveMode: controller.isCurveModeActive() &&
          controller.selectedIndices.isNotEmpty &&
          controller.selectedIndices.first ==
              shapes.indexWhere((s) => s.key == shapeKey),
    );

    final index = shapes.indexWhere((s) => s.key == shapeKey);
    if (index >= 0) {
      shapes[index] = newShape;
      controller.update();
    }
  }

  // Handle shape dragging in mirror mode
  void _handleShapeDragInMirrorMode(Key shapeKey, DragUpdateDetails details,
      TransformableShape shape, ShapeData shapeData) {
    // Get controller reference
    final controller = Get.find<ShapeEditorController>();
    final screenCenter = Get.width / 2;

    // Find leftmost point of the shape
    double leftmostX = double.infinity;
    for (final vertex in shapeData.vertices) {
      leftmostX = math.min(leftmostX, vertex.dx);
    }

    // Calculate allowed movement
    double allowedDx = details.delta.dx;
    double allowedDy = details.delta.dy;

    // In mirror mode, constrain horizontal movement to keep left edge at center line
    // This ensures the original shape stays on the RIGHT side of the center line
    if (details.delta.dx != 0) {
      // If leftmost point would go beyond center, restrict movement left
      if (leftmostX + details.delta.dx < screenCenter) {
        allowedDx = screenCenter - leftmostX;
      }

      // Also ensure the shape stays anchored to the center line
      // If moving right (away from center line), check if the shape is already at the center line
      if (details.delta.dx > 0 && (leftmostX - screenCenter).abs() < 0.5) {
        // Don't allow movement away from the center line
        allowedDx = 0;
      }
    }

    // Get the constraints from the shape
    BoxConstraints constraints = shape.constraints;

    // Check Y movement
    if (details.delta.dy != 0) {
      double minAllowedDy = -double.infinity;
      double maxAllowedDy = double.infinity;

      for (final vertex in shapeData.vertices) {
        if (details.delta.dy > 0) {
          maxAllowedDy =
              math.min(maxAllowedDy, constraints.maxHeight - vertex.dy);
        } else {
          minAllowedDy = math.max(minAllowedDy, -vertex.dy);
        }
      }

      allowedDy = details.delta.dy.clamp(minAllowedDy, maxAllowedDy);
    }

    // Apply the allowed movement
    if (allowedDx != 0 || allowedDy != 0) {
      final constrainedVertices = shapeData.vertices
          .map((vertex) => vertex.translate(allowedDx, allowedDy))
          .toList();

      // Calculate the final translation delta applied
      final translationDelta = Offset(allowedDx, allowedDy);

      // --- Transform child shapes if it's a group ---
      List<ShapeData>? transformedChildren;
      if (shapeData is GroupShapeData) {
        transformedChildren =
            shapeData.transformChildShapes(translation: translationDelta);
      }
      // ---------------------------------------------

      // Create updated shape data
      ShapeData updatedShapeData = shapeData.copyWith(
        vertices: constrainedVertices,
        center: shapeData.center
            .translate(allowedDx, allowedDy), // Use ALLOWED delta
      );
      if (updatedShapeData is GroupShapeData && transformedChildren != null) {
        updatedShapeData =
            updatedShapeData.copyWithChildren(childShapes: transformedChildren);
      }

      // Update the bounding rect
      final updatedShapeData2 = updateBoundingRect(updatedShapeData);

      // Save the new state
      shapeStates[shapeKey] = updatedShapeData2;

      // Create a new shape instance with the updated data
      final newShape = TransformableShape(
        key: shapeKey,
        constraints: shape.constraints,
        initialShapeType: shape.initialShapeType,
        initialRect: shape.initialRect,
        selected: true,
        initialShapeData: updatedShapeData2,
      );

      // Find the shape index and replace it
      final index = shapes.indexWhere((s) => s.key == shapeKey);
      if (index >= 0) {
        shapes[index] = newShape;
      }

      // Update the mirrored shape if this is an original shape
      if (!controller.isShapeMirrored(shapeKey)) {
        // If this is an original shape with a mirror pair, notify the mirror mode manager
        final mirrorKey = controller.getMirroredPair(shapeKey);
        if (mirrorKey != null) {
          // Force an immediate update of the mirrored shape
          controller.handleShapeTransformation(shapeKey);

          // Show center line snap indicator in mirror mode (key visual feature)
          // Add multiple indicators to show the center line more prominently
          const int indicatorCount = 5;
          const double indicatorSpacing = 80.0;
          final centerY = updatedShapeData2.center.dy;
          final screenCenter = Get.width / 2;

          // Add strong center indicator at the shape's center level
          controller.addSnapIndicator(Offset(screenCenter, centerY),
              isCenterSnap: true);

          // Add additional indicators along the center line
          for (int i = 1; i <= indicatorCount; i++) {
            // Add indicators above and below
            final aboveY = centerY - (i * indicatorSpacing);
            final belowY = centerY + (i * indicatorSpacing);

            if (aboveY > 0) {
              controller.addSnapIndicator(Offset(screenCenter, aboveY),
                  isCenterSnap: true);
            }

            if (belowY < Get.height) {
              controller.addSnapIndicator(Offset(screenCenter, belowY),
                  isCenterSnap: true);
            }
          }
        }
      }
    }
  }
}
