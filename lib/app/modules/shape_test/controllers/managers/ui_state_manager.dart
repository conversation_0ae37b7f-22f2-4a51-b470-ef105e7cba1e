import 'package:flutter/material.dart';
import 'package:get/get.dart';

class UIStateManager {
  final RxBool isToolbarExpanded = RxBool(true);
  final RxBool isShapeMenuOpen = RxBool(true);
  final RxBool hasShownToolbarHint = RxBool(false);

  // State for curve mode
  final Map<Key, bool> curveModeStates = <Key, bool>{};

  // Initialize UI state, check if we need to show toolbar hint
  void initialize() {
    // Always ensure toolbar and shape menu are expanded on startup
    isToolbarExpanded.value = true;
    isShapeMenuOpen.value = true;
  }

  // Method to toggle shape menu open/closed state
  void toggleShapeMenu() {
    isShapeMenuOpen.value = !isShapeMenuOpen.value;
  }

  // Method to toggle toolbar expansion state
  void toggleToolbarExpanded() {
    isToolbarExpanded.value = !isToolbarExpanded.value;
    // If collapsing the toolbar, also close the shape menu if it's open
    if (!isToolbarExpanded.value && isShapeMenuOpen.value) {
      isShapeMenuOpen.value = false;
    }
  }

  // Check if curve mode is active for a shape
  bool isCurveModeActive(Key? shapeKey) {
    if (shapeKey == null) return false;
    return curveModeStates[shapeKey] ?? false;
  }

  // Toggle curve mode for a shape
  void toggleCurveMode(Key shapeKey, bool value) {
    curveModeStates[shapeKey] = value;
  }
}
