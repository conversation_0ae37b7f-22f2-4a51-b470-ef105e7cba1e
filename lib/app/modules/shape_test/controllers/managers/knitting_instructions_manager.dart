import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/new_item/controllers/new_item_wizard_controller.dart';
import '../shape_editor_controller.dart';
import 'knitting_zone_models.dart';
import 'zone_history_manager.dart';
import 'zone_persistence_service.dart';
import 'zone_generation_service.dart';
import 'instruction_generation_service.dart';

/// Manager class for handling knitting instruction generation
class KnittingInstructionsManager {
  // Reference to the parent controller
  final ShapeEditorController _controller;

  // Services
  late final ZonePersistenceService _persistenceService;
  late final ZoneGenerationService _generationService;
  late final InstructionGenerationService _instructionService;
  final ZoneHistoryManager _zoneHistoryManager = ZoneHistoryManager();

  // Current instruction grid
  final Rx<List<List<bool>>> currentInstructions = Rx<List<List<bool>>>([]);

  final Rx<List<KnittingZone>> knittingZones = Rx<List<KnittingZone>>([]);

  // Statistics about the current pattern
  final Rx<Map<String, dynamic>> patternStatistics =
      Rx<Map<String, dynamic>>({});

  // Flag to indicate if instructions are being generated
  final RxBool isGeneratingInstructions = false.obs;

  // Pattern options - standardize on top-down only
  final RxBool startFromBottom =
      false.obs; // Set to false and keep for compatibility
  final RxBool includeRowNumbers = true.obs;

  KnittingInstructionsManager(this._controller) {
    // Initialize services
    _persistenceService = ZonePersistenceService();
    _generationService = ZoneGenerationService();
    _instructionService = InstructionGenerationService(_controller);
  }

  // Getters for zone history functionality
  ZoneHistoryManager get zoneHistoryManager => _zoneHistoryManager;
  RxBool get canUndoZones => _zoneHistoryManager.canUndo;
  RxBool get canRedoZones => _zoneHistoryManager.canRedo;
  Rx<String?> get lastZoneOperation => _zoneHistoryManager.lastOperation;

  /// Load saved zones from wizard state if they match the current instructions
  Future<bool> _loadSavedZonesIfCompatible(
      List<List<bool>> newInstructions) async {
    final loadedZones =
        await _persistenceService.loadSavedZonesIfCompatible(newInstructions);
    if (loadedZones != null) {
      knittingZones.value = loadedZones;
      return true;
    }
    return false;
  }

  /// Save zones to wizard state with instruction hash (dual storage approach)
  Future<void> saveZonesToWizardState() async {
    await _persistenceService.saveZonesToWizardState(
        knittingZones.value, currentInstructions.value);
  }

  /// Generate knitting instructions from the current shapes
  Future<List<List<bool>>> generateInstructions() async {
    isGeneratingInstructions.value = true;

    try {
      // Generate the instructions using the service
      final newInstructions = await _instructionService.generateInstructions();

      // Update current instructions
      currentInstructions.value = newInstructions;

      // Calculate pattern statistics
      patternStatistics.value =
          _instructionService.calculatePatternStatistics(newInstructions);

      final newItemWizardController = NewItemWizardController.to;
      if (newItemWizardController.currentKnittingRow.value == 10000) {
        newItemWizardController.currentKnittingRow.value =
            newInstructions.length - 1;
      }

      // // Try to load saved zones first, only generate new ones if needed
      // final loadedSavedZones =
      //     await _loadSavedZonesIfCompatible(newInstructions);

      // if (!loadedSavedZones) {
      //   // Clear zone history before generating new zones
      //   _zoneHistoryManager.clearHistory();

      //   // Generate new zones and save them
      //   debugPrint('[KnittingInstructionsManager] Generating new zones');
      //   knittingZones.value = getKnittingZones();

      //   // Save the newly generated zones
      //   await saveZonesToWizardState();
      // }

      knittingZones.value = getKnittingZones();

      // Save the newly generated zones
      // await saveZonesToWizardState();

      return currentInstructions.value;
    } catch (e, stackTrace) {
      debugPrint('Error generating knitting instructions: $e');
      debugPrint('Stack trace: $stackTrace');
      return [];
    } finally {
      isGeneratingInstructions.value = false;
    }
  }

  /// Get a text representation of the current knitting pattern
  String getTextPattern() {
    return _instructionService.getTextPattern(
        currentInstructions.value, includeRowNumbers.value);
  }

  /// Get a structured representation of the current knitting pattern
  List<List<String>> getStructuredPattern() {
    return _instructionService.getStructuredPattern(currentInstructions.value);
  }

  /// Toggle whether to include row numbers in the pattern text
  void toggleRowNumbers() {
    includeRowNumbers.value = !includeRowNumbers.value;
  }

  /// Get the knitting zones from the current instructions using an improved algorithm
  /// that handles contiguous and discontinuous rows appropriately
  List<KnittingZone> getKnittingZones({bool startFromRight = true}) {
    return _generationService.generateKnittingZones(currentInstructions.value,
        startFromRight: startFromRight);
  }

  /// Get all zones with trimmed instructions for display purposes
  /// This removes padding spaces (false values) while maintaining position information
  List<Map<String, dynamic>> getTrimmedKnittingZones() {
    return _generationService.getTrimmedKnittingZones(knittingZones.value);
  }
}
