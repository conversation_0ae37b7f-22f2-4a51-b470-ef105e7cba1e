import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/new_item/controllers/new_item_wizard_controller.dart';
import 'knitting_zone_models.dart';
import 'dart:convert' show utf8;
import 'package:crypto/crypto.dart';

/// Service for handling zone persistence operations
class ZonePersistenceService {
  /// Calculate a hash of the instruction pattern for comparison
  String calculateInstructionsHash(List<List<bool>> instructions) {
    if (instructions.isEmpty) return '';

    // Convert instructions to a string representation
    final buffer = StringBuffer();
    for (final row in instructions) {
      buffer.write(row.map((cell) => cell ? '1' : '0').join(''));
    }

    // Calculate MD5 hash
    final bytes = utf8.encode(buffer.toString());
    final digest = md5.convert(bytes);
    return digest.toString();
  }

  /// Load saved zones from wizard state if they match the current instructions
  Future<List<KnittingZone>?> loadSavedZonesIfCompatible(
      List<List<bool>> newInstructions) async {
    try {
      if (!Get.isRegistered<NewItemWizardController>()) {
        return null;
      }

      final wizardController = Get.find<NewItemWizardController>();
      if (wizardController.currentStateId.value == null) {
        return null;
      }

      final state = await wizardController.wizardStateService
          .loadWizardState(wizardController.currentStateId.value!);

      if (state?.instructionsHash == null) {
        debugPrint('[ZonePersistenceService] No saved hash found');
        return null;
      }

      // Calculate hash of new instructions
      final newHash = calculateInstructionsHash(newInstructions);

      // Check if instructions have changed
      if (state!.instructionsHash != newHash) {
        debugPrint(
            '[ZonePersistenceService] Instructions changed, regenerating zones');
        debugPrint('  Old hash: ${state.instructionsHash}');
        debugPrint('  New hash: $newHash');
        return null;
      }

      // Instructions match, try to load saved zones
      // First try to load full zones from local storage
      if (state.savedKnittingZones != null &&
          state.savedKnittingZones!.isNotEmpty) {
        debugPrint(
            '[ZonePersistenceService] Loading full zones from local storage (${state.savedKnittingZones!.length} zones)');

        // Verify zones are still valid for the current instructions
        if (validateZonesAgainstInstructions(
            state.savedKnittingZones!, newInstructions)) {
          debugPrint('[ZonePersistenceService] Loaded full zones are valid');
          return List<KnittingZone>.from(state.savedKnittingZones!);
        } else {
          debugPrint('[ZonePersistenceService] Loaded full zones are invalid');
        }
      }

      // If full zones aren't available or invalid, try to reconstruct from metadata
      if (state.savedZoneMetadata != null &&
          state.savedZoneMetadata!.isNotEmpty) {
        debugPrint(
            '[ZonePersistenceService] Reconstructing zones from metadata (${state.savedZoneMetadata!.length} zones)');

        final reconstructedZones = reconstructZonesFromMetadata(
            state.savedZoneMetadata!, newInstructions);
        if (reconstructedZones.isNotEmpty) {
          debugPrint(
              '[ZonePersistenceService] Successfully reconstructed zones from metadata');
          return reconstructedZones;
        } else {
          debugPrint(
              '[ZonePersistenceService] Failed to reconstruct zones from metadata');
        }
      }

      debugPrint('[ZonePersistenceService] No valid saved zones found');
      return null;
    } catch (e) {
      debugPrint('[ZonePersistenceService] Error loading saved zones: $e');
      return null;
    }
  }

  /// Reconstruct full zones from lightweight metadata and current instructions
  List<KnittingZone> reconstructZonesFromMetadata(
      List<ZoneMetadata> metadata, List<List<bool>> instructions) {
    final reconstructedZones = <KnittingZone>[];

    for (final meta in metadata) {
      try {
        // Validate boundaries
        if (meta.startRow < 0 ||
            meta.endRow >= instructions.length ||
            meta.startNeedle < 0 ||
            meta.endNeedle >=
                (instructions.isNotEmpty ? instructions[0].length : 0)) {
          debugPrint(
              '[ZonePersistenceService] Invalid zone boundaries for ${meta.name}');
          continue;
        }

        // Extract zone instructions from the full pattern
        final zoneInstructions = extractZoneInstructions(instructions, meta);

        if (zoneInstructions.isNotEmpty) {
          final zone = KnittingZone(
            name: meta.name,
            instructions: zoneInstructions,
            config: meta.config,
            startNeedle: meta.startNeedle,
            endNeedle: meta.endNeedle,
            startRow: meta.startRow,
            endRow: meta.endRow,
          );
          reconstructedZones.add(zone);
        } else {
          debugPrint(
              '[ZonePersistenceService] No valid instructions found for zone ${meta.name}');
        }
      } catch (e) {
        debugPrint(
            '[ZonePersistenceService] Error reconstructing zone ${meta.name}: $e');
      }
    }

    return reconstructedZones;
  }

  /// Extract zone instructions from the full pattern based on metadata
  List<List<bool>> extractZoneInstructions(
      List<List<bool>> fullInstructions, ZoneMetadata metadata) {
    final zoneInstructions = <List<bool>>[];

    for (int row = metadata.startRow; row <= metadata.endRow; row++) {
      if (row >= 0 && row < fullInstructions.length) {
        final fullRow = fullInstructions[row];
        final zoneRow = <bool>[];

        for (int col = metadata.startNeedle; col <= metadata.endNeedle; col++) {
          if (col >= 0 && col < fullRow.length) {
            zoneRow.add(fullRow[col]);
          } else {
            zoneRow.add(false); // Pad with false if out of bounds
          }
        }

        zoneInstructions.add(zoneRow);
      } else {
        // Create empty row if out of bounds
        final emptyRow = List<bool>.filled(
            metadata.endNeedle - metadata.startNeedle + 1, false);
        zoneInstructions.add(emptyRow);
      }
    }

    return zoneInstructions;
  }

  /// Validate that zones are compatible with the current instructions
  bool validateZonesAgainstInstructions(
      List<KnittingZone> zones, List<List<bool>> instructions) {
    if (zones.isEmpty || instructions.isEmpty) return zones.isEmpty;

    for (final zone in zones) {
      // Check bounds
      if (zone.startRow < 0 ||
          zone.endRow >= instructions.length ||
          zone.startNeedle < 0 ||
          zone.endNeedle >= instructions[0].length) {
        return false;
      }

      // Check that zone instructions match the global instructions in the zone's area
      for (int localRow = 0; localRow < zone.instructions.length; localRow++) {
        final globalRow = zone.startRow + localRow;
        if (globalRow >= instructions.length) return false;

        for (int localCol = 0;
            localCol < zone.instructions[localRow].length;
            localCol++) {
          final globalCol = zone.startNeedle + localCol;
          if (globalCol >= instructions[globalRow].length) return false;

          // If zone has a stitch, the global pattern should also have a stitch there
          if (zone.instructions[localRow][localCol] &&
              !instructions[globalRow][globalCol]) {
            return false;
          }
        }
      }
    }

    return true;
  }

  /// Save zones to wizard state with instruction hash (dual storage approach)
  Future<void> saveZonesToWizardState(
      List<KnittingZone> zones, List<List<bool>> currentInstructions) async {
    try {
      if (!Get.isRegistered<NewItemWizardController>()) {
        return;
      }

      final wizardController = Get.find<NewItemWizardController>();
      if (wizardController.currentStateId.value == null) {
        return;
      }

      final state = await wizardController.wizardStateService
          .loadWizardState(wizardController.currentStateId.value!);

      if (state == null) {
        return;
      }

      // Calculate current instructions hash
      final instructionsHash = calculateInstructionsHash(currentInstructions);

      // Create lightweight metadata for Firestore
      final zoneMetadata =
          zones.map((zone) => ZoneMetadata.fromZone(zone)).toList();

      // Save with dual approach:
      // - Full zones for local storage (faster access)
      // - Lightweight metadata for Firestore (avoids size limits)
      final updatedState = state.copyWith(
        savedKnittingZones: List<KnittingZone>.from(zones), // Local storage
        savedZoneMetadata: zoneMetadata, // Firestore storage
        instructionsHash: instructionsHash,
        lastModified: DateTime.now(),
      );

      await wizardController.wizardStateService.saveWizardState(updatedState);
      debugPrint(
          '[ZonePersistenceService] Saved ${zones.length} zones with dual storage approach. Hash: $instructionsHash');
    } catch (e) {
      debugPrint('[ZonePersistenceService] Error saving zones: $e');
    }
  }
}
