import 'knitting_zone_models.dart';
import 'dart:math' as math;

/// Service for generating knitting zones from instruction patterns
class ZoneGenerationService {
  /// Get the knitting zones from the current instructions using an improved algorithm
  /// that handles contiguous and discontinuous rows appropriately
  List<KnittingZone> generateKnittingZones(List<List<bool>> instructions,
      {bool startFromRight = true}) {
    final List<List<bool>> workingInstructions =
        List.generate(instructions.length, (r) => List.from(instructions[r]));

    List<KnittingZone> knittingZones = [];
    int zoneCount = 1;

    // Process the pattern from bottom to top using the new algorithm
    _processPatternByContiguity(workingInstructions, knittingZones, zoneCount);

    // Sort zones from bottom right to top left and renumber them
    _sortAndRenumberZones(knittingZones);

    return knittingZones;
  }

  /// Process the pattern by analyzing contiguity patterns from bottom to top
  void _processPatternByContiguity(
    List<List<bool>> workingInstructions,
    List<KnittingZone> knittingZones,
    int zoneCount,
  ) {
    if (!_hasStitches(workingInstructions)) return;

    // Find the bottommost row with stitches
    int bottomRowIndex = -1;
    for (int row = workingInstructions.length - 1; row >= 0; row--) {
      if (workingInstructions[row].contains(true)) {
        bottomRowIndex = row;
        break;
      }
    }

    if (bottomRowIndex == -1) return; // No stitches found

    // Determine if the bottom row is contiguous
    bool isBottomRowContiguous =
        _isRowContiguous(workingInstructions[bottomRowIndex]);

    // Scan upward until we find a row with different contiguity pattern or no stitches
    int endOfBlock = bottomRowIndex;
    int startOfBlock = bottomRowIndex;

    for (int row = bottomRowIndex - 1; row >= 0; row--) {
      final rowData = workingInstructions[row];

      if (!rowData.contains(true)) {
        // No stitches in this row, marks the end of the current block
        endOfBlock = row + 1;
        break;
      }

      bool isCurrentRowContiguous = _isRowContiguous(rowData);

      if (isCurrentRowContiguous != isBottomRowContiguous) {
        // Contiguity pattern changed, marks the end of the current block
        endOfBlock = row + 1;
        break;
      }

      startOfBlock = row;
    }

    // If we reached the top without a change in contiguity pattern, set endOfBlock to top
    if (endOfBlock == bottomRowIndex) {
      endOfBlock = startOfBlock;
    }

    // Extract the block from startOfBlock to bottomRowIndex
    List<List<bool>> blockInstructions =
        _extractBlock(workingInstructions, startOfBlock, bottomRowIndex);

    // Process this block based on its contiguity
    if (isBottomRowContiguous) {
      // For contiguous blocks, create a single zone
      _createZoneFromBlock(blockInstructions, knittingZones, zoneCount,
          startOfBlock, bottomRowIndex);
      zoneCount++;
    } else {
      // For discontinuous blocks, split into multiple zones by columns
      _splitDiscontinuousBlockIntoZones(blockInstructions, knittingZones,
          zoneCount, startOfBlock, bottomRowIndex);
      zoneCount += _countDistinctColumnGroups(blockInstructions);
    }

    // Recursively process the rest of the pattern (if any)
    if (startOfBlock > 0) {
      _processPatternByContiguity(
          workingInstructions, knittingZones, zoneCount);
    }
  }

  // Helper function to generate letter-based zone names (A, B, C, ..., Z, AA, AB, etc.)
  String _generateZoneName(int index) {
    String result = '';
    int columnNumber = index + 1; // Convert 0-based to 1-based

    while (columnNumber > 0) {
      columnNumber--; // Make it 0-based for this iteration
      result = String.fromCharCode(65 + (columnNumber % 26)) + result;
      columnNumber = columnNumber ~/ 26;
    }

    return 'Zone $result';
  }

  // Helper function to check if a name matches the default pattern (Zone A, Zone B, etc.)
  bool isDefaultZoneName(String name) {
    final pattern = RegExp(r'^Zone [A-Z]+$');
    return pattern.hasMatch(name);
  }

  /// Sort zones from bottom right to top left and renumber them starting from A
  void _sortAndRenumberZones(List<KnittingZone> zones) {
    if (zones.isEmpty) return;

    // Sort from bottom to top (higher endRow to lower endRow)
    // For same endRow, sort from right to left (higher endNeedle to lower endNeedle)
    zones.sort((a, b) {
      // First compare by bottom row (endRow) - sort from bottom to top
      int endRowComparison = b.endRow.compareTo(a.endRow);
      if (endRowComparison != 0) {
        return endRowComparison;
      }

      // If they have the same bottom row, compare by right edge (endNeedle) - sort from right to left
      return b.endNeedle.compareTo(a.endNeedle);
    });

    // Rename zones using letters (only for default names, preserve custom names)
    for (int i = 0; i < zones.length; i++) {
      // Only rename zones that have default names, preserve custom names
      if (isDefaultZoneName(zones[i].name)) {
        zones[i] = zones[i].copyWith(name: _generateZoneName(i));
      }
    }
  }

  /// Check if a row is contiguous (all true values form a single block with no gaps)
  bool _isRowContiguous(List<bool> row) {
    int firstTrue = -1;
    int lastTrue = -1;

    for (int i = 0; i < row.length; i++) {
      if (row[i]) {
        if (firstTrue == -1) firstTrue = i;
        lastTrue = i;
      }
    }

    // If no true values or only one true value, it's contiguous
    if (firstTrue == -1 || firstTrue == lastTrue) return true;

    // Check if there are any false values between the first and last true values
    for (int i = firstTrue; i <= lastTrue; i++) {
      if (!row[i]) return false;
    }

    return true;
  }

  /// Extract a block of instructions from startRow to endRow
  List<List<bool>> _extractBlock(
      List<List<bool>> instructions, int startRow, int endRow) {
    final List<List<bool>> block = [];

    for (int row = startRow; row <= endRow; row++) {
      // Create a copy of the row
      final rowData = List<bool>.from(instructions[row]);
      block.add(rowData);

      // Clear the row in the original instructions
      for (int col = 0; col < instructions[row].length; col++) {
        if (instructions[row][col]) {
          instructions[row][col] = false;
        }
      }
    }

    return block;
  }

  /// Create a zone from a contiguous block
  void _createZoneFromBlock(
      List<List<bool>> blockInstructions,
      List<KnittingZone> knittingZones,
      int zoneCount,
      int globalStartRow,
      int globalEndRow) {
    if (blockInstructions.isEmpty) return;

    // Minimum thresholds for creating a zone
    const int minRows = 3; // Minimum number of rows required
    const int minStitches = 5; // Minimum total stitches required

    // Check if we have enough rows
    if (globalEndRow - globalStartRow + 1 < minRows) {
      // Skip tiny zones with just 1-2 rows
      return;
    }

    // Find the min and max columns of the content within the block (these are global indices)
    int globalMinCol =
        blockInstructions.isNotEmpty ? blockInstructions[0].length : 0;
    int globalMaxCol = 0;
    bool hasContent = false;
    int totalStitches = 0;

    for (final row in blockInstructions) {
      for (int col = 0; col < row.length; col++) {
        if (row[col]) {
          totalStitches++;
          hasContent = true;
          globalMinCol = math.min(globalMinCol, col);
          globalMaxCol = math.max(globalMaxCol, col);
        }
      }
    }

    // Check if this zone has the minimum required stitches
    if (!hasContent || totalStitches < minStitches) return;

    // Create CROPPED instructions for the zone based on globalMinCol and globalMaxCol
    List<List<bool>> croppedZoneInstructions = [];
    if (globalMinCol <= globalMaxCol) {
      // Ensure valid column range
      for (final fullWidthRow in blockInstructions) {
        // Sublist from globalMinCol to globalMaxCol+1 from the fullWidthRow
        // Ensure sublist range is valid for each row, though it should be if hasContent is true
        // and globalMinCol/globalMaxCol were derived from these rows.
        croppedZoneInstructions
            .add(fullWidthRow.sublist(globalMinCol, globalMaxCol + 1));
      }
    } else {
      // This case should ideally not be reached if hasContent is true.
      // If it is, it implies an issue with min/max col calculation or empty block.
      // For safety, if croppedZoneInstructions would be empty or invalid, return.
      return;
    }

    // Ensure croppedZoneInstructions is not empty if we proceed
    if (croppedZoneInstructions.isEmpty ||
        croppedZoneInstructions.every((row) => row.isEmpty)) {
      return; // Don't create a zone with no instruction data
    }

    final zone = KnittingZone(
      name: _generateZoneName(zoneCount - 1), // Convert to 0-based index
      instructions: croppedZoneInstructions, // Pass CROPPED instructions
      startNeedle: globalMinCol, // Global start needle for this zone
      endNeedle: globalMaxCol, // Global end needle for this zone
      startRow: globalStartRow, // Global start row for this zone
      endRow: globalEndRow, // Global end row for this zone
    );

    knittingZones.add(zone);
  }

  /// Split a discontinuous block into multiple zones by column grouping
  void _splitDiscontinuousBlockIntoZones(
      List<List<bool>> blockInstructions,
      List<KnittingZone> knittingZones,
      int zoneCount,
      int startRow,
      int endRow) {
    if (blockInstructions.isEmpty) return;

    // Minimum thresholds for creating a zone
    const int minRows = 3; // Minimum number of rows required
    const int minStitches = 5; // Minimum total stitches required

    // Check if we have enough rows
    if (endRow - startRow + 1 < minRows) {
      // Skip tiny zones with just 1-2 rows
      return;
    }

    // Identify distinct column groups in the block
    final List<Map<String, int>> columnGroups =
        _identifyColumnGroups(blockInstructions);

    // Create a zone for each column group
    for (int i = 0; i < columnGroups.length; i++) {
      final group = columnGroups[i];
      final int minCol = group['start']!;
      final int maxCol = group['end']!;

      // Extract this column group as a separate block
      final List<List<bool>> zoneInstructions = [];
      int totalStitches = 0;

      for (final row in blockInstructions) {
        final List<bool> zoneRow = List.filled(maxCol - minCol + 1, false);

        for (int col = minCol; col <= maxCol; col++) {
          if (col < row.length && row[col]) {
            zoneRow[col - minCol] = true;
            totalStitches++;
          }
        }

        zoneInstructions.add(zoneRow);
      }

      // Check if this zone has the minimum required stitches
      if (totalStitches < minStitches) continue;

      // Create a zone if it has any stitches
      if (_hasStitches(zoneInstructions)) {
        final zone = KnittingZone(
          name:
              _generateZoneName(zoneCount + i - 1), // Convert to 0-based index
          instructions: zoneInstructions,
          startNeedle: minCol,
          endNeedle: maxCol,
          startRow: startRow,
          endRow: endRow,
        );

        knittingZones.add(zone);
      }
    }
  }

  /// Identify distinct column groups in a discontinuous block
  List<Map<String, int>> _identifyColumnGroups(
      List<List<bool>> blockInstructions) {
    final List<Map<String, int>> groups = [];

    // Create a merged row representing all columns that have stitches in any row
    final List<bool> mergedRow =
        List.filled(blockInstructions[0].length, false);

    for (final row in blockInstructions) {
      for (int col = 0; col < row.length; col++) {
        if (row[col]) {
          mergedRow[col] = true;
        }
      }
    }

    // Identify continuous groups in the merged row
    int startCol = -1;

    for (int col = 0; col < mergedRow.length; col++) {
      if (mergedRow[col]) {
        if (startCol == -1) {
          startCol = col;
        }
      } else {
        if (startCol != -1) {
          groups.add({'start': startCol, 'end': col - 1});
          startCol = -1;
        }
      }
    }

    // Don't forget the last group if it extends to the end
    if (startCol != -1) {
      groups.add({'start': startCol, 'end': mergedRow.length - 1});
    }

    return groups;
  }

  /// Count the number of distinct column groups in a block
  int _countDistinctColumnGroups(List<List<bool>> blockInstructions) {
    return _identifyColumnGroups(blockInstructions).length;
  }

  // Helper method to check if there are any stitches left in the working instructions
  bool _hasStitches(List<List<bool>> instructions) {
    for (final row in instructions) {
      if (row.contains(true)) {
        return true;
      }
    }
    return false;
  }

  /// Get all zones with trimmed instructions for display purposes
  /// This removes padding spaces (false values) while maintaining position information
  List<Map<String, dynamic>> getTrimmedKnittingZones(List<KnittingZone> zones) {
    final result = <Map<String, dynamic>>[];

    for (int i = 0; i < zones.length; i++) {
      final zone = zones[i];
      final trimmed = zone.getTrimmedInstructions();

      result.add({
        'name': zone.name,
        'instructions': trimmed['instructions'],
        'config': zone.config.value.toJson(),
        // Original position data
        'startNeedle': zone.startNeedle,
        'endNeedle': zone.endNeedle,
        'startRow': zone.startRow,
        'endRow': zone.endRow,
        // Trim information
        'leftTrim': trimmed['leftTrim'],
        'rightTrim': trimmed['rightTrim'],
        'topTrim': trimmed['topTrim'],
        'bottomTrim': trimmed['bottomTrim'],
        // Calculate actual needle positions after trimming
        'displayStartNeedle': zone.startNeedle + trimmed['leftTrim'],
        'displayEndNeedle': zone.endNeedle - trimmed['rightTrim'],
        'displayStartRow': zone.startRow + trimmed['topTrim'],
        'displayEndRow': zone.endRow - trimmed['bottomTrim'],
      });
    }

    return result;
  }
}
