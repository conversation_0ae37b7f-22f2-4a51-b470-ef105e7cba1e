// selection_manager.dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/shape_manager.dart';
import 'package:xoxknit/app/modules/shape_test/models/shape_data.dart';
import 'package:xoxknit/app/modules/shape_test/views/transformable_shape.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/shape_editor_controller.dart';
import 'package:xoxknit/app/modules/shape_test/utils/geometry_utils.dart';

class SelectionManager {
  final ShapeManager shapeManager;
  final RxList<int> selectedIndices = <int>[].obs;
  final RxBool isMultiSelecting = RxBool(false);
  final isMultiSelectionMode = false.obs;

  // Flag to track if a drag started on a selected shape
  bool isValidDrag = false;

  // Flag to track if we're interacting with a handle
  bool isHandleInteraction = false;

  SelectionManager(this.shapeManager);

  // Selects a shape by its index
  void selectShapeByIndex(int index) {
    if (index < 0 || index >= shapeManager.shapes.length) return;

    selectedIndices.clear();
    selectedIndices.add(index);
    updateShapeSelectionState();
  }

  // Selects a shape object
  void selectShape(TransformableShape shape) {
    // Find the index of the clicked shape
    int clickedIndex = -1;
    for (int i = 0; i < shapeManager.shapes.length; i++) {
      if (shapeManager.shapes[i].key == shape.key) {
        clickedIndex = i;
        break;
      }
    }

    if (clickedIndex == -1) return;

    // If shift is held down, add to multi-selection
    if (isMultiSelecting.value) {
      toggleShapeSelection(
          clickedIndex, !selectedIndices.contains(clickedIndex));
      return;
    }

    // Otherwise, deselect all and select only this shape
    selectedIndices.clear();

    // Move shape to the top (end of the list)
    if (clickedIndex < shapeManager.shapes.length - 1) {
      final selectedShape = shapeManager.shapes[clickedIndex];
      shapeManager.shapes.removeAt(clickedIndex);
      shapeManager.shapes.add(selectedShape);
      clickedIndex = shapeManager.shapes.length - 1;
    }

    // Update the selected indices
    selectedIndices.add(clickedIndex);

    // Update shape selection state
    updateShapeSelectionState();
  }

  // Toggles selection of a shape by index
  void toggleShapeSelection(int index, bool selected) {
    if (index < 0 || index >= shapeManager.shapes.length) return;

    // Update the selected indices
    if (selected) {
      if (!selectedIndices.contains(index)) {
        selectedIndices.add(index);
      }
    } else {
      selectedIndices.remove(index);
    }

    // Update shape selection state
    updateShapeSelectionState();
  }

  // Deselects all shapes
  void deselectAllShapes() {
    // Only do something if there are shapes selected
    if (selectedIndices.isNotEmpty) {
      // Clear the selected indices
      selectedIndices.clear();

      // Update shape selection state
      updateShapeSelectionState();
    }
  }

  // Updates all shapes to reflect the current selection state
  void updateShapeSelectionState({Map<Key, bool>? curveModeStates}) {
    final List<TransformableShape> updatedShapes = [];

    // Check if we're transitioning from single-select to multi-select mode
    bool isMultiSelect = selectedIndices.length > 1;

    // If we're in multi-select mode, turn off curve mode for all shapes
    if (isMultiSelect && curveModeStates != null) {
      for (final entry in curveModeStates.entries.toList()) {
        if (entry.value) {
          // Get the shape with this key
          for (int i = 0; i < shapeManager.shapes.length; i++) {
            if (shapeManager.shapes[i].key == entry.key) {
              shapeManager.shapes[i]
                  .toggleCurveMode(); // Turn off curve mode visually
              curveModeStates[entry.key] = false; // Update state in controller
              break;
            }
          }
        }
      }
    }

    for (int i = 0; i < shapeManager.shapes.length; i++) {
      final currentShape = shapeManager.shapes[i];
      final isSelected = selectedIndices.contains(i);

      // Force rebuild of all selected shapes if we're in multi-select mode
      // This ensures rotation handles disappear on previously selected shapes
      if ((isSelected != currentShape.selected) ||
          (isSelected && isMultiSelect)) {
        updatedShapes.add(TransformableShape(
          key: currentShape.key,
          constraints: currentShape.constraints,
          initialShapeType: currentShape.initialShapeType,
          initialRect: currentShape.initialRect,
          selected: isSelected,
          initialShapeData: shapeManager.getShapeState(currentShape.key),
          initialCurveMode: curveModeStates?[currentShape.key],
        ));
      } else {
        updatedShapes.add(currentShape);
      }
    }

    // Replace the shapes list with updated shapes
    shapeManager.shapes.clear();
    shapeManager.shapes.addAll(updatedShapes);

    // Force UI update to reflect selection changes
    Get.find<ShapeEditorController>().update();
  }

  // Creates a path for hit testing a shape
  Path createHitTestPath(ShapeData shapeData) {
    // Use GeometryUtils to create an accurate path that properly accounts for curves
    // This ensures hit testing is consistent with the actual shape rendering
    final path = GeometryUtils.buildShapePath(shapeData);

    // Apply rotation transformation
    final matrix = Matrix4.identity()
      ..translate(shapeData.center.dx, shapeData.center.dy)
      ..rotateZ(shapeData.rotation)
      ..translate(-shapeData.center.dx, -shapeData.center.dy);

    return path.transform(matrix.storage);
  }

  // Helper to find a widget's BuildContext
  BuildContext? findContext(BuildContext context, Widget target) {
    BuildContext? result;
    final Key? targetKey = target.key;

    void visitor(Element element) {
      // Check if the widget is the exact target OR if it has the same key
      // This makes the search more robust, especially in mirror mode
      if (element.widget == target ||
          (targetKey != null && element.widget.key == targetKey)) {
        result = element;
        return;
      }

      // If this is a TransformableShape, do a more detailed comparison
      if (targetKey != null &&
          element.widget is TransformableShape &&
          target is TransformableShape) {
        final elementShape = element.widget;
        final targetShape = target;

        // Compare by key
        if (elementShape.key == targetShape.key) {
          result = element;
          return;
        }
      }

      element.visitChildren(visitor);
    }

    if (context is Element) {
      visitor(context);
    }

    return result;
  }
}
