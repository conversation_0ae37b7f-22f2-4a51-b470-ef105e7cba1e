import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/views/transformable_shape.dart';
import '../../models/shape_data.dart';
import '../../models/group_shape_data.dart';

import 'shape_manager.dart';
import 'selection_manager.dart';
import '../shape_editor_controller.dart';

/// Enhanced implementation of the mirror mode functionality
/// Addresses issues with the original implementation and provides a more robust solution
class EnhancedMirrorModeManager {
  // References to other managers for coordination
  final ShapeManager shapeManager;
  final SelectionManager selectionManager;
  final ShapeEditorController controller;

  // Mirror mode state
  final RxBool isMirrorModeActive = RxBool(false);

  // Map to track mirrored shape pairs (original key -> mirrored shape key)
  final Map<Key, Key> mirroredPairs = <Key, Key>{};

  // Set of shapes created in the current mirror mode session
  final Set<Key> shapesCreatedInCurrentSession = <Key>{};

  // Key of the active original shape in mirror mode (the one that can be manipulated)
  Rx<Key?> activeOriginalShapeKey = Rx<Key?>(null);

  // Screen center (vertical line) - cached for performance
  double _screenCenter = 0.0;

  // Constructor
  EnhancedMirrorModeManager(
      this.shapeManager, this.selectionManager, this.controller) {
    // Update screen center whenever screen size changes
    _updateScreenCenter();

    // Listen for selection changes to enforce mirror mode constraints
    ever(selectionManager.selectedIndices, _enforceSelectionConstraints);
  }

  // Update the screen center value
  void _updateScreenCenter() {
    // The true center of the grid is at screen width / 2
    // But we need to adjust for pan offset
    _screenCenter = Get.width / 2;

    // If the controller has zoom or pan, we should account for that
    // Note: For now we're maintaining a fixed screen-centered vertical line
    // that doesn't move with pan, as that seems to be the expected behavior
  }

  /// Toggle mirror mode on/off
  void toggleMirrorMode() {
    final wasActive = isMirrorModeActive.value;
    isMirrorModeActive.value = !wasActive;

    // If turning off mirror mode
    if (wasActive) {
      // --- Group Mirrored Pairs ---
      // Create a copy of the pairs before clearing
      final Map<Key, Key> pairsToGroup = Map.from(mirroredPairs);
      // ----------------------------

      // Keep all shapes but clear the session data
      mirroredPairs.clear();
      shapesCreatedInCurrentSession.clear();
      activeOriginalShapeKey.value = null;

      // --- Call Controller to Group ---
      if (pairsToGroup.isNotEmpty) {
        // Use WidgetsBinding to ensure this runs after the current frame
        // Avoids potential state conflicts during build/layout phases
        WidgetsBinding.instance.addPostFrameCallback((_) {
          controller.groupMirroredPairs(pairsToGroup);
        });
      }
      // --------------------------------
    } else {
      // If turning on mirror mode
      // Clear any existing selections
      selectionManager.selectedIndices.clear();
      selectionManager.updateShapeSelectionState();

      // Update screen center in case screen size has changed
      _updateScreenCenter();

      // Show a helpful message about mirror mode
      // Get.snackbar(
      //   'Mirror Mode Enabled',
      //   'You can add one shape on the right side of the center line, which will be mirrored on the left side. Both shapes will be anchored to the center line.',
      //   snackPosition: SnackPosition.BOTTOM,
      //   duration: const Duration(seconds: 3),
      //   backgroundColor: Colors.blue.shade700,
      //   colorText: Colors.white,
      // );
    }
  }

  /// Check if a shape is the mirrored copy (not the original)
  bool isShapeMirrored(Key shapeKey) {
    return mirroredPairs.values.contains(shapeKey);
  }

  /// Check if a shape is the original (not the mirrored copy)
  bool isShapeOriginal(Key shapeKey) {
    return mirroredPairs.keys.contains(shapeKey);
  }

  /// Get the mirrored pair for a shape (works in both directions)
  Key? getMirroredPair(Key shapeKey) {
    // If this is an original shape, return its mirror
    if (mirroredPairs.containsKey(shapeKey)) {
      return mirroredPairs[shapeKey];
    }

    // If this is a mirrored shape, find its original
    for (final entry in mirroredPairs.entries) {
      if (entry.value == shapeKey) {
        return entry.key;
      }
    }

    // No pair found
    return null;
  }

  /// Create a mirrored shape for the given original shape
  /// Returns the key of the created mirrored shape
  Key createMirroredShape(
      Key originalKey, ShapeData originalData, BoxConstraints constraints) {
    // Find the leftmost vertex of the original shape
    double leftmostX = double.infinity;
    for (final vertex in originalData.vertices) {
      if (vertex.dx < leftmostX) {
        leftmostX = vertex.dx;
      }
    }

    // Calculate how much to shift to ensure the leftmost vertex is exactly at the center line
    final shiftAmount = _screenCenter - leftmostX;

    // Create shifted vertices and adjusted shape data
    ShapeData constrainedOriginalData;
    if (shiftAmount.abs() > 0.001) {
      // Only adjust if not already at center line (with small tolerance)
      final shiftedVertices = originalData.vertices.map((vertex) {
        return Offset(vertex.dx + shiftAmount, vertex.dy);
      }).toList();

      // Calculate new bounding rect
      final shiftedRect = Rect.fromLTRB(
        originalData.boundingRect.left + shiftAmount,
        originalData.boundingRect.top,
        originalData.boundingRect.right + shiftAmount,
        originalData.boundingRect.bottom,
      );

      // Calculate new center
      final shiftedCenter = Offset(
        originalData.center.dx + shiftAmount,
        originalData.center.dy,
      );

      // Create adjusted shape data
      constrainedOriginalData = ShapeData(
        type: originalData.type,
        vertices: shiftedVertices,
        boundingRect: shiftedRect,
        center: shiftedCenter,
        rotation: originalData.rotation,
        curveControls: originalData.curveControls,
      );
    } else {
      constrainedOriginalData = originalData;
    }

    // Save the constrained original data
    shapeManager.shapeStates[originalKey] = constrainedOriginalData;

    // Create the mirrored shape data
    final mirroredData = mirrorShapeData(constrainedOriginalData);

    // Create a key for the mirrored shape
    final mirrorKey = GlobalKey();

    // Store the mirrored shape data
    shapeManager.shapeStates[mirrorKey] = mirroredData;

    // Create the mirrored shape widget
    final mirroredShape = TransformableShape(
      key: mirrorKey,
      constraints: constraints,
      initialShapeType: constrainedOriginalData.type,
      initialShapeData: mirroredData,
      selected: false,
    );

    // Add the mirrored shape to the shape manager
    shapeManager.shapes.add(mirroredShape);
    shapeManager.shapeKeysOrder.add(mirrorKey);

    // Register the pair
    mirroredPairs[originalKey] = mirrorKey;

    // Add both shapes to the current session
    shapesCreatedInCurrentSession.add(originalKey);
    shapesCreatedInCurrentSession.add(mirrorKey);

    // Set the active original shape
    activeOriginalShapeKey.value = originalKey;

    return mirrorKey;
  }

  /// Update the mirrored shape when the original changes
  void updateMirroredShape(Key originalKey) {
    // Get the mirrored shape key
    final mirrorKey = mirroredPairs[originalKey];
    if (mirrorKey == null) return;

    // Get the original shape data
    final originalData = shapeManager.shapeStates[originalKey];
    if (originalData == null) return;

    // Ensure the original data is properly positioned on the right side
    final constrainedOriginalData = _constrainToRightSide(originalData);

    // If the original data needed to be constrained, update it
    if (constrainedOriginalData != originalData) {
      shapeManager.shapeStates[originalKey] = constrainedOriginalData;
    }

    // Create the mirrored shape data
    final mirroredData = mirrorShapeData(constrainedOriginalData);

    // Update the mirrored shape data
    shapeManager.shapeStates[mirrorKey] = mirroredData;

    // Force a rebuild of the mirrored shape
    _rebuildShape(mirrorKey);
  }

  /// Mirror a shape data horizontally across the screen center
  /// Ensures the rightmost vertex of the mirrored shape is exactly at the center line
  ShapeData mirrorShapeData(ShapeData originalData) {
    // First, ensure the original shape is properly constrained to the right side
    // with its leftmost vertex at the center line
    final constrainedOriginalData = _constrainToRightSide(originalData);

    // Create new mirrored vertices by reflecting across the center line
    final mirroredVertices = <Offset>[];
    for (final vertex in constrainedOriginalData.vertices) {
      // Calculate distance from center line
      final distanceFromCenter = vertex.dx - _screenCenter;

      // Place mirrored vertex the same distance on the other side
      mirroredVertices
          .add(Offset(_screenCenter - distanceFromCenter, vertex.dy));
    }

    // Calculate the mirrored bounding rectangle
    final mirroredRect = Rect.fromLTRB(
      _screenCenter * 2 - constrainedOriginalData.boundingRect.right,
      constrainedOriginalData.boundingRect.top,
      _screenCenter * 2 - constrainedOriginalData.boundingRect.left,
      constrainedOriginalData.boundingRect.bottom,
    );

    // Calculate the mirrored center point
    final mirroredCenter = Offset(
      _screenCenter * 2 - constrainedOriginalData.center.dx,
      constrainedOriginalData.center.dy,
    );

    // Mirror the curve controls if any
    final mirroredCurveControls = <int, Offset>{};
    for (final entry in constrainedOriginalData.curveControls.entries) {
      final controlPoint = entry.value;
      // For a proper mirror reflection, negate the X component of the control offset
      // The Y component remains unchanged
      mirroredCurveControls[entry.key] = Offset(
        -controlPoint.dx, // Negate X to make curves properly mirror
        controlPoint.dy, // Keep Y the same
      );
    }

    // Handle group shapes specially
    if (constrainedOriginalData is GroupShapeData) {
      final groupData = constrainedOriginalData;
      final mirroredChildren = <ShapeData>[];

      // Mirror each child shape
      for (final childShape in groupData.childShapes) {
        final mirroredChild = mirrorShapeData(childShape);
        mirroredChildren.add(mirroredChild);
      }

      // Create a new group shape with mirrored data
      return GroupShapeData(
        vertices: mirroredVertices,
        boundingRect: mirroredRect,
        center: mirroredCenter,
        rotation: -constrainedOriginalData
            .rotation, // Negate rotation for mirror effect
        visualRotation: -constrainedOriginalData.visualRotation,
        curveControls: mirroredCurveControls,
        childShapes: mirroredChildren,
        originalKeys: groupData.originalKeys,
      );
    }

    // For regular shapes, create a new shape with mirrored data
    return ShapeData(
      type: constrainedOriginalData.type,
      vertices: mirroredVertices,
      boundingRect: mirroredRect,
      center: mirroredCenter,
      rotation: -constrainedOriginalData
          .rotation, // Negate rotation for mirror effect
      curveControls: mirroredCurveControls,
      visualRotation: -constrainedOriginalData.visualRotation,
    );
  }

  /// Constrain a shape to stay on the right side of the center line
  /// and ensure the leftmost vertex is exactly at the center line
  ShapeData _constrainToRightSide(ShapeData shapeData) {
    // Find the leftmost vertex
    double leftmostX = double.infinity;
    for (final vertex in shapeData.vertices) {
      if (vertex.dx < leftmostX) {
        leftmostX = vertex.dx;
      }
    }

    // Calculate how much to shift right to align with center line
    // Using zero tolerance to ensure strict alignment to center line
    final shiftAmount = (_screenCenter - leftmostX);

    // Always apply the shift to ensure strict alignment to center
    // Create shifted vertices
    final shiftedVertices = shapeData.vertices
        .map((vertex) => Offset(vertex.dx + shiftAmount, vertex.dy))
        .toList();

    // Calculate new bounding rect
    final shiftedRect = Rect.fromLTRB(
      shapeData.boundingRect.left + shiftAmount,
      shapeData.boundingRect.top,
      shapeData.boundingRect.right + shiftAmount,
      shapeData.boundingRect.bottom,
    );

    // Calculate new center
    final shiftedCenter = Offset(
      shapeData.center.dx + shiftAmount,
      shapeData.center.dy,
    );

    // --- Handle curve controls and prevent them from crossing the middle line ---
    final shiftedControls = <int, Offset>{};
    bool curvesConstrained = false;

    for (final entry in shapeData.curveControls.entries) {
      final edgeIndex = entry.key;
      final controlOffset = entry.value;

      if (controlOffset == Offset.zero) {
        shiftedControls[edgeIndex] = controlOffset;
        continue;
      }

      // We need to check if this control point will cause the curve to cross the center line

      // Get the edge vertices (using the shifted vertices)
      final startPoint = shiftedVertices[edgeIndex];
      final endPoint =
          shiftedVertices[(edgeIndex + 1) % shiftedVertices.length];
      final edgeMidpoint = Offset(
        (startPoint.dx + endPoint.dx) / 2,
        (startPoint.dy + endPoint.dy) / 2,
      );

      // Calculate the actual control point position
      final controlPoint = edgeMidpoint + controlOffset;

      // If the control point is left of the center line, adjust it
      if (controlPoint.dx < _screenCenter) {
        // Calculate how far to move the control point right
        final controlAdjustX = _screenCenter - controlPoint.dx + 0.1;

        // Create a new control offset that doesn't cross the center line
        final adjustedOffset =
            Offset(controlOffset.dx + controlAdjustX, controlOffset.dy);

        shiftedControls[edgeIndex] = adjustedOffset;
        curvesConstrained = true;
      } else {
        // No adjustment needed for this control point
        shiftedControls[edgeIndex] = controlOffset;
      }
    }

    // Handle group shapes specially
    if (shapeData is GroupShapeData) {
      final groupData = shapeData;
      final shiftedChildren = <ShapeData>[];

      // Shift each child shape
      for (final childShape in groupData.childShapes) {
        final shiftedChild = _shiftShapeData(childShape, shiftAmount, 0);
        // Also ensure each child's curves don't cross the center line
        final constrainedChild = _constrainToRightSide(shiftedChild);
        shiftedChildren.add(constrainedChild);
      }

      // Create a new group shape with shifted and constrained data
      return GroupShapeData(
        vertices: shiftedVertices,
        boundingRect: shiftedRect,
        center: shiftedCenter,
        rotation: shapeData.rotation,
        visualRotation: shapeData.visualRotation,
        curveControls: shiftedControls,
        childShapes: shiftedChildren,
        originalKeys: groupData.originalKeys,
      );
    }

    // For regular shapes, create a new shape with shifted and constrained data
    return ShapeData(
      type: shapeData.type,
      vertices: shiftedVertices,
      boundingRect: shiftedRect,
      center: shiftedCenter,
      rotation: shapeData.rotation,
      visualRotation: shapeData.visualRotation,
      curveControls: shiftedControls,
    );
  }

  /// Helper method to shift a shape by a given amount
  ShapeData _shiftShapeData(ShapeData shapeData, double dx, double dy) {
    // Create shifted vertices
    final shiftedVertices = shapeData.vertices.map((vertex) {
      return Offset(vertex.dx + dx, vertex.dy + dy);
    }).toList();

    // Calculate new bounding rect
    final shiftedRect = Rect.fromLTRB(
      shapeData.boundingRect.left + dx,
      shapeData.boundingRect.top + dy,
      shapeData.boundingRect.right + dx,
      shapeData.boundingRect.bottom + dy,
    );

    // Calculate new center
    final shiftedCenter = Offset(
      shapeData.center.dx + dx,
      shapeData.center.dy + dy,
    );

    // Shift curve controls
    final shiftedControls = <int, Offset>{};
    for (final entry in shapeData.curveControls.entries) {
      shiftedControls[entry.key] = Offset(
        entry.value.dx + dx,
        entry.value.dy + dy,
      );
    }

    // Handle group shapes specially
    if (shapeData is GroupShapeData) {
      final groupData = shapeData;
      final shiftedChildren = <ShapeData>[];

      // Shift each child shape
      for (final childShape in groupData.childShapes) {
        final shiftedChild = _shiftShapeData(childShape, dx, dy);
        shiftedChildren.add(shiftedChild);
      }

      // Create a new group shape with shifted data
      return GroupShapeData(
        vertices: shiftedVertices,
        boundingRect: shiftedRect,
        center: shiftedCenter,
        rotation: shapeData.rotation,
        visualRotation: shapeData.visualRotation,
        curveControls: shiftedControls,
        childShapes: shiftedChildren,
        originalKeys: groupData.originalKeys,
      );
    }

    // For regular shapes, create a new shape with shifted data
    return ShapeData(
      type: shapeData.type,
      vertices: shiftedVertices,
      boundingRect: shiftedRect,
      center: shiftedCenter,
      rotation: shapeData.rotation,
      visualRotation: shapeData.visualRotation,
      curveControls: shiftedControls,
    );
  }

  /// Enforce selection constraints for mirror mode
  /// This ensures only the original shape can be selected in mirror mode
  void _enforceSelectionConstraints(List<int> selectedIndices) {
    // Only apply constraints if mirror mode is active
    if (!isMirrorModeActive.value) return;

    // If no shapes are selected, nothing to do
    if (selectedIndices.isEmpty) return;

    // Check each selected shape
    final List<int> indicesToRemove = [];

    for (final index in selectedIndices) {
      // Skip if index is out of bounds
      if (index < 0 || index >= shapeManager.shapes.length) continue;

      final shape = shapeManager.shapes[index];
      final shapeKey = shape.key!;

      // If this is a mirrored shape, deselect it
      if (isShapeMirrored(shapeKey)) {
        indicesToRemove.add(index);
        continue;
      }

      // If we have an active original shape and this is not it, deselect it
      if (activeOriginalShapeKey.value != null &&
          shapeKey != activeOriginalShapeKey.value &&
          shapesCreatedInCurrentSession
              .contains(activeOriginalShapeKey.value)) {
        indicesToRemove.add(index);
        continue;
      }

      // If this is a shape not created in the current session, deselect it
      if (!shapesCreatedInCurrentSession.contains(shapeKey)) {
        indicesToRemove.add(index);
        continue;
      }

      // This must be a valid original shape, make it the active one
      activeOriginalShapeKey.value = shapeKey;
    }

    // Remove invalid selections
    if (indicesToRemove.isNotEmpty) {
      for (final index in indicesToRemove) {
        selectionManager.selectedIndices.remove(index);
      }

      // Update the selection state
      selectionManager.updateShapeSelectionState();
    }
  }

  /// Force a rebuild of a shape
  void _rebuildShape(Key shapeKey) {
    // Find the shape index
    int shapeIndex = -1;
    for (int i = 0; i < shapeManager.shapes.length; i++) {
      if (shapeManager.shapes[i].key == shapeKey) {
        shapeIndex = i;
        break;
      }
    }

    // If shape not found, nothing to do
    if (shapeIndex == -1) return;

    // Get the current shape
    final currentShape = shapeManager.shapes[shapeIndex];

    // Get the shape data
    final shapeData = shapeManager.shapeStates[shapeKey];
    if (shapeData == null) return;

    // Create a new shape with the same properties but updated data
    final newShape = TransformableShape(
      key: shapeKey,
      constraints: currentShape.constraints,
      initialShapeType: shapeData.type,
      initialShapeData: shapeData,
      selected: currentShape.selected,
    );

    // Replace the shape in the list using a post-frame callback to avoid build-time updates
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Verify the index is still valid (shape might have been removed)
      if (shapeIndex >= 0 && shapeIndex < shapeManager.shapes.length) {
        // Make sure the key still matches (shapes might have been rearranged)
        if (shapeManager.shapes[shapeIndex].key == shapeKey) {
          shapeManager.shapes[shapeIndex] = newShape;
        }
      }
    });
  }

  /// Update the mirrored shape data without necessarily rebuilding the widget
  /// This is optimized for frequent updates during drag operations
  void updateMirroredShapeData(Key originalKey) {
    // Get the mirrored shape key
    final mirrorKey = mirroredPairs[originalKey];
    if (mirrorKey == null) return;

    // Get the original shape data
    final originalData = shapeManager.shapeStates[originalKey];
    if (originalData == null) return;

    // Ensure the original data is properly positioned on the right side
    final constrainedOriginalData = _constrainToRightSide(originalData);

    // If the original data needed to be constrained, update it
    if (constrainedOriginalData != originalData) {
      shapeManager.shapeStates[originalKey] = constrainedOriginalData;
    }

    // Create the mirrored shape data
    final mirroredData = mirrorShapeData(constrainedOriginalData);

    // Update the mirrored shape data without forcing a rebuild
    shapeManager.shapeStates[mirrorKey] = mirroredData;

    // We won't force a rebuild here, just update the data
    // This is more efficient for frequent updates during drag operations
  }

  /// Constrain a shape position during active drag operations
  /// Returns the constrained shape data if changes were needed, otherwise returns null
  ShapeData? constrainPositionDuringDrag(Key shapeKey, ShapeData currentData) {
    // Only apply constraints in mirror mode for original shapes
    if (!isMirrorModeActive.value) {
      return null;
    }

    // Get the constrained shape data - this enforces the center line constraint
    final constrainedData = _constrainToRightSide(currentData);

    // Always return the constrained data in mirror mode to ensure strict enforcement
    // even if the change is minimal
    return constrainedData;
  }

  /// Apply constraints to an original shape to ensure it's properly positioned
  /// Returns the constrained shape data
  ShapeData applyOriginalShapeConstraints(Key shapeKey, ShapeData shapeData) {
    // If not in mirror mode, return the original data
    if (!isMirrorModeActive.value) return shapeData;

    // Ensure the shape is properly positioned on the right side
    final constrainedData = _constrainToRightSide(shapeData);

    // Update the shape data in the manager
    shapeManager.shapeStates[shapeKey] = constrainedData;

    return constrainedData;
  }

  /// Handle shape deletion in mirror mode
  /// Returns the mirrored shape key (if any) that also needs to be deleted
  Key? handleShapeDeletion(Key shapeKey) {
    // If not in mirror mode, nothing to do
    if (!isMirrorModeActive.value) return null;

    Key? mirrorKeyToDelete;

    // Check if this is an original shape with a mirror
    if (isShapeOriginal(shapeKey)) {
      // Get the mirrored shape key
      final mirrorKey = mirroredPairs[shapeKey];
      if (mirrorKey != null) {
        // Remove the mirrored shape from the session
        shapesCreatedInCurrentSession.remove(mirrorKey);

        // Remove the pair
        mirroredPairs.remove(shapeKey);

        // If this was the active shape, clear it
        if (activeOriginalShapeKey.value == shapeKey) {
          activeOriginalShapeKey.value = null;
        }

        // Return the mirror key so it can be deleted from the UI
        mirrorKeyToDelete = mirrorKey;

        // Remove state data for the mirrored shape
        shapeManager.shapeStates.remove(mirrorKey);
        shapeManager.shapeKeysOrder.remove(mirrorKey);
      }
    } else if (isShapeMirrored(shapeKey)) {
      // If deleting a mirrored shape directly, find and remove its original too
      Key? originalKey;
      for (final entry in mirroredPairs.entries) {
        if (entry.value == shapeKey) {
          originalKey = entry.key;
          break;
        }
      }

      if (originalKey != null) {
        // Remove the original shape from the session
        shapesCreatedInCurrentSession.remove(originalKey);

        // Remove the pair
        mirroredPairs.remove(originalKey);

        // If this was the active shape, clear it
        if (activeOriginalShapeKey.value == originalKey) {
          activeOriginalShapeKey.value = null;
        }

        // Return the original key so it can be deleted from the UI
        mirrorKeyToDelete = originalKey;

        // Remove state data for the original shape
        shapeManager.shapeStates.remove(originalKey);
        shapeManager.shapeKeysOrder.remove(originalKey);
      }
    }

    // Remove the shape from the session
    shapesCreatedInCurrentSession.remove(shapeKey);

    return mirrorKeyToDelete;
  }

  /// Check if a shape can be selected in mirror mode
  bool canSelectShape(Key shapeKey) {
    // If not in mirror mode, any shape can be selected
    if (!isMirrorModeActive.value) return true;

    // Mirrored shapes can never be selected
    if (isShapeMirrored(shapeKey)) return false;

    // If we have an active shape and this is not it, it can't be selected
    if (activeOriginalShapeKey.value != null &&
        shapeKey != activeOriginalShapeKey.value &&
        shapesCreatedInCurrentSession.contains(activeOriginalShapeKey.value)) {
      return false;
    }

    // If this is a shape not created in the current session, it can't be selected
    if (!shapesCreatedInCurrentSession.contains(shapeKey)) {
      return false;
    }

    // Otherwise, it can be selected
    return true;
  }

  /// Handle shape transformation in mirror mode
  /// This should be called whenever an original shape is transformed
  void handleShapeTransformation(Key originalKey) {
    // If not in mirror mode, nothing to do
    if (!isMirrorModeActive.value) return;

    // If this is not an original shape, nothing to do
    if (!isShapeOriginal(originalKey)) return;

    // Use the optimized update method for real-time updates during drag
    // This updates the data but doesn't immediately force a widget rebuild
    updateMirroredShapeData(originalKey);

    // Also trigger a full rebuild when appropriate (less frequently)
    // This ensures the UI eventually gets fully updated
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Get the mirrored shape key
      final mirrorKey = mirroredPairs[originalKey];
      if (mirrorKey != null) {
        // Force a rebuild of the mirrored shape
        _rebuildShape(mirrorKey);
      }
    });
  }

  /// Clear all mirror mode state
  void clearMirrorState() {
    mirroredPairs.clear();
    shapesCreatedInCurrentSession.clear();
    activeOriginalShapeKey.value = null;
  }
}
