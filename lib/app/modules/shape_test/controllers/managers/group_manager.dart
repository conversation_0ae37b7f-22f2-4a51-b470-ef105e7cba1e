import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/selection_manager.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/shape_manager.dart';
import 'package:xoxknit/app/modules/shape_test/models/group_shape_data.dart';
import 'package:xoxknit/app/modules/shape_test/models/shape_data.dart';
import 'package:xoxknit/app/modules/shape_test/views/transformable_shape.dart';
import 'package:xoxknit/app/modules/shape_test/constants/grid_constants.dart';
import '../../utils/geometry_utils.dart';

class GroupManager {
  final ShapeManager shapeManager;
  final SelectionManager selectionManager;

  GroupManager(this.shapeManager, this.selectionManager);

  // Groups selected shapes, flattening any existing groups
  bool groupSelectedShapes() {
    // Check if grouping is possible
    if (!canGroupSelectedShapes()) return false;

    // Get the selected indices as a List, sorted by index
    final List<int> indices = selectionManager.selectedIndices.toList()
      ..sort((a, b) => a.compareTo(b));

    // --- Flattened Lists ---
    final List<ShapeData> allChildShapeData = []; // Holds individual ShapeData
    final List<Key> allChildKeys =
        []; // Holds keys corresponding to allChildShapeData
    final List<Key> keysToRemove =
        []; // Holds keys of top-level items being grouped

    // Iterate through selected top-level shapes
    for (final index in indices) {
      if (index >= 0 && index < shapeManager.shapes.length) {
        final shape = shapeManager.shapes[index];
        final Key? shapeKey = shape.key;
        if (shapeKey == null) continue;

        keysToRemove.add(shapeKey); // Add top-level key for removal later

        final data = shapeManager.shapeStates[shapeKey];
        if (data == null) continue;

        // --- Flattening Logic ---
        if (data is GroupShapeData) {
          // If it's a group, add its children
          for (int i = 0; i < data.childShapes.length; i++) {
            final childData = data.childShapes[i];
            final childKey = data.originalKeys[i]; // Use the original key
            if (childKey != null) {
              allChildShapeData.add(childData);
              allChildKeys.add(childKey);
            }
          }
        } else {
          // If it's a regular shape, add it directly
          allChildShapeData.add(data);
          allChildKeys.add(shapeKey);
        }
        // ------------------------
      }
    }

    // Ensure we have something to group after flattening
    if (allChildShapeData.isEmpty || allChildKeys.isEmpty) {
      debugPrint("[GroupManager] No valid shapes found after flattening.");
      return false;
    }

    // Create a group data using the flattened lists
    final groupData =
        GroupShapeData.fromShapes(allChildShapeData, allChildKeys);

    // Create a key for the group
    final groupKey = GlobalKey();
    // Ensure the new group key is tracked
    // shapeManager.shapeKeysOrder.add(groupKey); // Add later, after removing old ones

    // Save the group state
    shapeManager.saveShapeState(groupKey, groupData);

    // Create a new TransformableShape for the group
    final newGroupShape = TransformableShape(
      key: groupKey,
      constraints: BoxConstraints(
        maxWidth: Get.width,
        maxHeight: GridConstants.getExtendedHeight(Get.height),
      ),
      initialShapeType: ShapeType.group,
      initialShapeData: groupData,
      selected: true, // Select the new group
    );

    // Remove original selected shapes (including flattened groups)
    shapeManager.shapes.removeWhere((shape) {
      final key = shape.key;
      return key != null && keysToRemove.contains(key);
    });

    // Remove shape keys from tracking
    for (final key in keysToRemove) {
      shapeManager.shapeKeysOrder.remove(key);
      // Also remove the state of the items being removed
      shapeManager.shapeStates.remove(key);
    }

    // Add the new group shape and its key
    shapeManager.shapes.add(newGroupShape);
    shapeManager.shapeKeysOrder.add(groupKey); // Add key *after* removal

    // Update selection to just the new group
    selectionManager.selectedIndices.clear();
    // Find the index of the newly added group
    int groupIndex = shapeManager.shapes.indexWhere((s) => s.key == groupKey);
    if (groupIndex != -1) {
      selectionManager.selectedIndices.add(groupIndex);
    } else {
      // Fallback if index isn't found (shouldn't happen)
      selectionManager.selectedIndices.add(shapeManager.shapes.length - 1);
    }

    return true;
  }

  // Ungroups a selected group shape
  bool ungroupSelectedShape() {
    // Check if ungrouping is possible
    if (!canUngroupSelectedShape()) return false;

    // Get the selected group shape
    final index = selectionManager.selectedIndices.first;
    final groupShape = shapeManager.shapes[index];
    final Key? groupKey = groupShape.key;
    if (groupKey == null) return false;

    // Get the group data
    final groupData = shapeManager.shapeStates[groupKey];
    if (groupData == null || groupData is! GroupShapeData) return false;

    // The group's children data and keys
    final childShapes = groupData.childShapes;
    final originalKeys = groupData.originalKeys;

    if (childShapes.isEmpty ||
        originalKeys.isEmpty ||
        originalKeys.length != childShapes.length) {
      return false;
    }

    // List to collect individual shapes after ungrouping
    final List<TransformableShape> newShapes = [];
    // final List<int> newSelectedIndices = [];

    // Create individual shapes from group children
    for (int i = 0; i < originalKeys.length; i++) {
      final childKey = originalKeys[i];
      if (childKey == null) continue;

      final childShape = childShapes[i];

      // --- Recalculate Bounding Box ---
      // Ensure the bounding box reflects the actual vertices
      // Assume childShape.vertices are in the correct world coordinates
      // final recalculatedBounds = GeometryUtils.calculateBoundingRect(childShape.vertices);
      // Use the new accurate method that considers curves
      final recalculatedBounds =
          GeometryUtils.calculateAccurateBoundingRect(childShape);
      final updatedChildShape =
          childShape.copyWith(boundingRect: recalculatedBounds);
      // --------------------------------

      // Save the child shape state with the updated bounds
      shapeManager.saveShapeState(childKey, updatedChildShape);
      // Add key *before* creating the widget if relying on order
      shapeManager.shapeKeysOrder.add(childKey);

      // Create a new TransformableShape for each child using the updated data
      final shape = TransformableShape(
        key: childKey,
        constraints: BoxConstraints(
          maxWidth: Get.width,
          maxHeight: GridConstants.getExtendedHeight(Get.height),
        ),
        initialShapeType: updatedChildShape.type, // Use updated shape
        selected: true,
        initialShapeData:
            updatedChildShape, // Use updated shape with correct bounds
        initialCurveMode: false,
      );

      newShapes.add(shape);
    }

    // Remove the group shape
    shapeManager.shapes.removeAt(index);
    shapeManager.shapeStates.remove(groupKey);
    shapeManager.shapeKeysOrder.remove(groupKey);

    // Add all the individual shapes
    shapeManager.shapes.addAll(newShapes);

    // Select all the individual shapes
    selectionManager.selectedIndices.clear();
    for (int i = 0; i < newShapes.length; i++) {
      selectionManager.selectedIndices
          .add(shapeManager.shapes.length - newShapes.length + i);
    }

    return true;
  }

  // Check if the currently selected shapes can be grouped
  bool canGroupSelectedShapes() {
    return selectionManager.selectedIndices.length >= 2;
  }

  // Check if the currently selected shape can be ungrouped
  bool canUngroupSelectedShape() {
    if (selectionManager.selectedIndices.length != 1) return false;

    final index = selectionManager.selectedIndices.first;
    if (index < 0 || index >= shapeManager.shapes.length) return false;

    final shape = shapeManager.shapes[index];
    final Key? shapeKey = shape.key;
    if (shapeKey == null) return false;

    final shapeData = shapeManager.shapeStates[shapeKey];
    return shapeData is GroupShapeData;
  }

  /// Groups shapes specified by their keys, flattening any existing groups within the selection
  /// Returns true if grouping was successful, false otherwise.
  bool groupShapesByKey(
      List<Key> keysToGroup, RxList<TransformableShape> currentShapes) {
    // Validate keys
    if (keysToGroup.length < 2) {
      debugPrint("[GroupManager] Need at least two keys to group.");
      return false;
    }

    final List<ShapeData> allChildShapeData = []; // Holds individual ShapeData
    final List<Key> allChildKeys =
        []; // Holds keys corresponding to allChildShapeData
    final List<Key> keysToRemove =
        []; // Holds keys of top-level items being grouped
    final List<TransformableShape> shapesToRemove =
        []; // Holds widgets to remove

    // Iterate through provided keys
    for (final key in keysToGroup) {
      // Find the corresponding shape widget to potentially get constraints
      final shapeWidget = currentShapes.firstWhereOrNull((s) => s.key == key);
      if (shapeWidget == null) {
        debugPrint("[GroupManager] Could not find shape widget for key: $key");
        continue; // Skip if widget not found
      }

      final data = shapeManager.shapeStates[key];
      if (data == null) {
        debugPrint("[GroupManager] Could not find shape data for key: $key");
        continue; // Skip if data not found
      }

      keysToRemove.add(key); // Mark this top-level key for removal
      shapesToRemove.add(shapeWidget); // Mark this widget for removal

      // --- Flattening Logic ---
      if (data is GroupShapeData) {
        // If it's a group, add its children
        for (int i = 0; i < data.childShapes.length; i++) {
          final childData = data.childShapes[i];
          final childKey = data.originalKeys[i]; // Use the original key
          if (childKey != null) {
            allChildShapeData.add(childData);
            allChildKeys.add(childKey);
          }
        }
      } else {
        // If it's a regular shape, add it directly
        allChildShapeData.add(data);
        allChildKeys.add(key);
      }
      // ------------------------
    }

    // Ensure we have something to group after flattening and validation
    if (allChildShapeData.isEmpty ||
        allChildKeys.isEmpty ||
        keysToRemove.length < 2) {
      debugPrint(
          "[GroupManager] Not enough valid shapes found after flattening and validation.");
      return false;
    }

    // Create a group data using the flattened lists
    final groupData =
        GroupShapeData.fromShapes(allChildShapeData, allChildKeys);

    // Create a key for the group
    final groupKey = GlobalKey();

    // Save the group state *before* modifying lists
    shapeManager.saveShapeState(groupKey, groupData);

    // Create a new TransformableShape for the group
    // Use generic constraints, similar to groupSelectedShapes
    final newGroupShape = TransformableShape(
      key: groupKey,
      constraints: BoxConstraints(
        maxWidth: Get.width,
        maxHeight: GridConstants.getExtendedHeight(Get.height),
      ),
      initialShapeType: ShapeType.group,
      initialShapeData: groupData,
      selected:
          true, // Select the new group by default (controller can override)
    );

    // --- Update State ---
    // Remove original shapes from the manager's list
    currentShapes.removeWhere((shape) => keysToRemove.contains(shape.key));

    // Remove shape keys and states from the manager
    for (final key in keysToRemove) {
      shapeManager.shapeKeysOrder.remove(key);
      shapeManager.shapeStates.remove(key);
    }

    // Add the new group shape widget and its key
    currentShapes.add(newGroupShape);
    shapeManager.shapeKeysOrder.add(groupKey); // Add key *after* removal

    // Note: Selection update is handled by the caller (ShapeEditorController)
    debugPrint(
        "[GroupManager] Successfully grouped ${keysToRemove.length} shapes by key into group $groupKey.");
    return true;
  }
}
