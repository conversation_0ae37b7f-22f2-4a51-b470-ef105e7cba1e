import 'package:get/get.dart';
import 'dart:math' as math;

/// represents an area of the pattern which can be knitted continuously from bottom to top
/// it is a list of rows, each row is a list of stitches
class KnittingZone {
  final String name;
  final List<List<bool>> instructions;
  final Rx<KnittingZoneConfig> config;

  // Position information relative to the full pattern
  final int startNeedle; // Leftmost needle position in the full pattern
  final int endNeedle; // Rightmost needle position in the full pattern
  final int startRow; // Top row position in the full pattern
  final int endRow; // Bottom row position in the full pattern

  KnittingZone({
    required this.name,
    required this.instructions,
    KnittingZoneConfig? config,
    this.startNeedle = 0,
    this.endNeedle = 0,
    this.startRow = 0,
    this.endRow = 0,
  }) : config = Rx<KnittingZoneConfig>(config ?? KnittingZoneConfig());

  void appendRow(List<bool> row) {
    instructions.insert(0, row);
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'instructions': instructions,
      'config': config.value.toJson(),
      'startNeedle': startNeedle,
      'endNeedle': endNeedle,
      'startRow': startRow,
      'endRow': endRow,
    };
  }

  factory KnittingZone.fromJson(Map<String, dynamic> json) {
    return KnittingZone(
      name: json['name'] ?? '',
      instructions: (json['instructions'] as List?)
              ?.map((row) => (row as List).cast<bool>())
              .toList() ??
          [],
      config: json['config'] != null
          ? KnittingZoneConfig.fromJson(json['config'])
          : null,
      startNeedle: json['startNeedle'] ?? 0,
      endNeedle: json['endNeedle'] ?? 0,
      startRow: json['startRow'] ?? 0,
      endRow: json['endRow'] ?? 0,
    );
  }

  /// Returns a trimmed version of instructions with padding spaces (false values) removed
  /// This is useful for display purposes while keeping original position data
  Map<String, dynamic> getTrimmedInstructions() {
    if (instructions.isEmpty) {
      return {
        'instructions': <List<bool>>[],
        'leftTrim': 0,
        'rightTrim': 0,
        'topTrim': 0,
        'bottomTrim': 0
      };
    }

    // Find the leftmost and rightmost columns that contain true values
    int leftBound = instructions[0].length;
    int rightBound = 0;

    // Also find top and bottom rows with content
    int topBound = -1;
    int bottomBound = -1;

    // Find the boundaries of the actual content
    for (int rowIndex = 0; rowIndex < instructions.length; rowIndex++) {
      final row = instructions[rowIndex];
      bool rowHasStitches = false;

      for (int colIndex = 0; colIndex < row.length; colIndex++) {
        if (row[colIndex]) {
          rowHasStitches = true;
          leftBound = math.min(leftBound, colIndex);
          rightBound = math.max(rightBound, colIndex);
        }
      }

      if (rowHasStitches) {
        if (topBound == -1) topBound = rowIndex;
        bottomBound = rowIndex;
      }
    }

    // Guard against empty zones
    if (leftBound > rightBound || topBound == -1) {
      return {
        'instructions': <List<bool>>[],
        'leftTrim': 0,
        'rightTrim': 0,
        'topTrim': 0,
        'bottomTrim': 0
      };
    }

    // Extract the trimmed instructions
    final trimmedInstructions = <List<bool>>[];
    for (int rowIndex = topBound; rowIndex <= bottomBound; rowIndex++) {
      final row = instructions[rowIndex];
      final trimmedRow = row.sublist(leftBound, rightBound + 1);
      trimmedInstructions.add(trimmedRow);
    }

    return {
      'instructions': trimmedInstructions,
      'leftTrim': leftBound,
      'rightTrim': instructions[0].length - rightBound - 1,
      'topTrim': topBound,
      'bottomTrim': instructions.length - bottomBound - 1
    };
  }

  KnittingZone copyWith({
    String? name,
    List<List<bool>>? instructions,
    KnittingZoneConfig? config,
    int? startNeedle,
    int? endNeedle,
    int? startRow,
    int? endRow,
  }) {
    return KnittingZone(
      name: name ?? this.name,
      instructions: instructions ?? this.instructions,
      config: config ?? this.config.value,
      startNeedle: startNeedle ?? this.startNeedle,
      endNeedle: endNeedle ?? this.endNeedle,
      startRow: startRow ?? this.startRow,
      endRow: endRow ?? this.endRow,
    );
  }
}

/// Configuration for a knitting zone
class KnittingZoneConfig {
  final RxBool autoControlCarriagePosition;
  final RxBool autoAdjustAsymmetricalRows;
  final RxBool autoControlIncreaseDecrease;
  final RxString carriagePosition; // "left" or "right"
  final RxBool isAsymmetrical; // yes or no
  final RxString asymmetricalDirection; // "inc" or "dec"
  final RxInt increaseBy;
  final RxInt increaseEvery;
  final RxInt decreaseBy;
  final RxInt decreaseEvery;
  final RxBool isEmpty; // If this is an empty zone
  final RxString finishingMethod; // "bind off" or "use scrap"

  KnittingZoneConfig({
    bool autoControlCarriagePosition = true,
    bool autoAdjustAsymmetricalRows = true,
    bool autoControlIncreaseDecrease = true,
    String carriagePosition = "left",
    bool isAsymmetrical = false,
    String asymmetricalDirection = "inc",
    int increaseBy = 0,
    int increaseEvery = 0,
    int decreaseBy = 0,
    int decreaseEvery = 0,
    bool isEmpty = false,
    String finishingMethod = "bind off",
  })  : autoControlCarriagePosition = autoControlCarriagePosition.obs,
        autoAdjustAsymmetricalRows = autoAdjustAsymmetricalRows.obs,
        autoControlIncreaseDecrease = autoControlIncreaseDecrease.obs,
        carriagePosition = carriagePosition.obs,
        isAsymmetrical = isAsymmetrical.obs,
        asymmetricalDirection = asymmetricalDirection.obs,
        increaseBy = increaseBy.obs,
        increaseEvery = increaseEvery.obs,
        decreaseBy = decreaseBy.obs,
        decreaseEvery = decreaseEvery.obs,
        isEmpty = isEmpty.obs,
        finishingMethod = finishingMethod.obs;

  Map<String, dynamic> toJson() {
    return {
      'autoControlCarriagePosition': autoControlCarriagePosition.value,
      'autoAdjustAsymmetricalRows': autoAdjustAsymmetricalRows.value,
      'autoControlIncreaseDecrease': autoControlIncreaseDecrease.value,
      'carriagePosition': carriagePosition.value,
      'isAsymmetrical': isAsymmetrical.value,
      'asymmetricalDirection': asymmetricalDirection.value,
      'increaseBy': increaseBy.value,
      'increaseEvery': increaseEvery.value,
      'decreaseBy': decreaseBy.value,
      'decreaseEvery': decreaseEvery.value,
      'isEmpty': isEmpty.value,
      'finishingMethod': finishingMethod.value,
    };
  }

  factory KnittingZoneConfig.fromJson(Map<String, dynamic> json) {
    return KnittingZoneConfig(
      autoControlCarriagePosition: json['autoControlCarriagePosition'] ?? true,
      autoAdjustAsymmetricalRows: json['autoAdjustAsymmetricalRows'] ?? true,
      autoControlIncreaseDecrease: json['autoControlIncreaseDecrease'] ?? true,
      carriagePosition: json['carriagePosition'] ?? "left",
      isAsymmetrical: json['isAsymmetrical'] ?? false,
      asymmetricalDirection: json['asymmetricalDirection'] ?? "inc",
      increaseBy: json['increaseBy'] ?? 0,
      increaseEvery: json['increaseEvery'] ?? 0,
      decreaseBy: json['decreaseBy'] ?? 0,
      decreaseEvery: json['decreaseEvery'] ?? 0,
      isEmpty: json['isEmpty'] ?? false,
      finishingMethod: json['finishingMethod'] ?? "bind off",
    );
  }

  KnittingZoneConfig copyWith({
    bool? autoControlCarriagePosition,
    bool? autoAdjustAsymmetricalRows,
    bool? autoControlIncreaseDecrease,
    String? carriagePosition,
    bool? isAsymmetrical,
    String? asymmetricalDirection,
    int? increaseBy,
    int? increaseEvery,
    int? decreaseBy,
    int? decreaseEvery,
    bool? isEmpty,
    String? finishingMethod,
  }) {
    return KnittingZoneConfig(
      autoControlCarriagePosition:
          autoControlCarriagePosition ?? this.autoControlCarriagePosition.value,
      autoAdjustAsymmetricalRows:
          autoAdjustAsymmetricalRows ?? this.autoAdjustAsymmetricalRows.value,
      autoControlIncreaseDecrease:
          autoControlIncreaseDecrease ?? this.autoControlIncreaseDecrease.value,
      carriagePosition: carriagePosition ?? this.carriagePosition.value,
      isAsymmetrical: isAsymmetrical ?? this.isAsymmetrical.value,
      asymmetricalDirection:
          asymmetricalDirection ?? this.asymmetricalDirection.value,
      increaseBy: increaseBy ?? this.increaseBy.value,
      increaseEvery: increaseEvery ?? this.increaseEvery.value,
      decreaseBy: decreaseBy ?? this.decreaseBy.value,
      decreaseEvery: decreaseEvery ?? this.decreaseEvery.value,
      isEmpty: isEmpty ?? this.isEmpty.value,
      finishingMethod: finishingMethod ?? this.finishingMethod.value,
    );
  }
}

/// Lightweight zone metadata for Firestore storage (without large instruction arrays)
class ZoneMetadata {
  final String name;
  final int startNeedle;
  final int endNeedle;
  final int startRow;
  final int endRow;
  final KnittingZoneConfig config;

  ZoneMetadata({
    required this.name,
    required this.startNeedle,
    required this.endNeedle,
    required this.startRow,
    required this.endRow,
    required this.config,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'startNeedle': startNeedle,
      'endNeedle': endNeedle,
      'startRow': startRow,
      'endRow': endRow,
      'config': config.toJson(),
    };
  }

  factory ZoneMetadata.fromJson(Map<String, dynamic> json) {
    return ZoneMetadata(
      name: json['name'] ?? '',
      startNeedle: json['startNeedle'] ?? 0,
      endNeedle: json['endNeedle'] ?? 0,
      startRow: json['startRow'] ?? 0,
      endRow: json['endRow'] ?? 0,
      config: json['config'] != null
          ? KnittingZoneConfig.fromJson(json['config'])
          : KnittingZoneConfig(),
    );
  }

  /// Create metadata from a full KnittingZone
  factory ZoneMetadata.fromZone(KnittingZone zone) {
    return ZoneMetadata(
      name: zone.name,
      startNeedle: zone.startNeedle,
      endNeedle: zone.endNeedle,
      startRow: zone.startRow,
      endRow: zone.endRow,
      config: zone.config.value,
    );
  }
}
