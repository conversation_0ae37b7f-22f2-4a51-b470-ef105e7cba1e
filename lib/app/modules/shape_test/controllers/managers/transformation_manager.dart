import 'package:flutter/material.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/shape_manager.dart';
import 'package:xoxknit/app/modules/shape_test/models/group_shape_data.dart';
import 'package:xoxknit/app/modules/shape_test/models/shape_data.dart';

class TransformationManager {
  final ShapeManager shapeManager;

  TransformationManager(this.shapeManager);

  // Flips a shape horizontally
  ShapeData flipShapeHorizontally(ShapeData shapeData) {
    // Special handling for group shapes
    if (shapeData is GroupShapeData) {
      // First, flip the group's bounding shape vertices
      final flippedVertices = <Offset>[];
      for (final vertex in shapeData.vertices) {
        // Calculate horizontal reflection across the center
        final dx = shapeData.center.dx - (vertex.dx - shapeData.center.dx);
        flippedVertices.add(Offset(dx, vertex.dy));
      }

      // Now flip each child shape horizontally
      final flippedChildShapes = <ShapeData>[];
      for (final childShape in shapeData.childShapes) {
        // Calculate how to flip the child relative to the group's center
        final flippedChildVertices = <Offset>[];
        for (final vertex in childShape.vertices) {
          // Flip horizontally across the group's center
          final dx = shapeData.center.dx - (vertex.dx - shapeData.center.dx);
          flippedChildVertices.add(Offset(dx, vertex.dy));
        }

        // Create a new map of curve controls by flipping them horizontally
        final flippedCurveControls = <int, Offset>{};
        for (final entry in childShape.curveControls.entries) {
          if (entry.value != Offset.zero) {
            // Flip the control offset horizontally
            flippedCurveControls[entry.key] =
                Offset(-entry.value.dx, entry.value.dy);
          }
        }

        // Calculate the new center for the child shape
        final childCenterDx =
            shapeData.center.dx - (childShape.center.dx - shapeData.center.dx);
        final flippedChildCenter = Offset(childCenterDx, childShape.center.dy);

        // Create updated child shape data
        final updatedChildShape = childShape.copyWith(
          vertices: flippedChildVertices,
          curveControls: flippedCurveControls,
          center: flippedChildCenter,
        );

        flippedChildShapes.add(updatedChildShape);
      }

      // Create updated group shape data with flipped vertices and children
      return shapeData.copyWithChildren(
        vertices: flippedVertices,
        childShapes: flippedChildShapes,
      );
    }

    // Normal handling for regular shapes
    // Create a new list of vertices by reflecting each vertex horizontally
    final flippedVertices = <Offset>[];
    for (final vertex in shapeData.vertices) {
      // Calculate horizontal reflection across the center
      final dx = shapeData.center.dx - (vertex.dx - shapeData.center.dx);
      flippedVertices.add(Offset(dx, vertex.dy));
    }

    // Create a new map of curve controls by flipping them horizontally
    final flippedCurveControls = <int, Offset>{};
    for (final entry in shapeData.curveControls.entries) {
      if (entry.value != Offset.zero) {
        // Flip the control offset horizontally
        flippedCurveControls[entry.key] =
            Offset(-entry.value.dx, entry.value.dy);
      }
    }

    // Create updated shape data
    final updatedShapeData = shapeData.copyWith(
      vertices: flippedVertices,
      curveControls: flippedCurveControls,
    );

    // Update the bounding rectangle and center
    return shapeManager.updateBoundingRect(updatedShapeData);
  }

  // Flips a shape vertically
  ShapeData flipShapeVertically(ShapeData shapeData) {
    // Special handling for group shapes
    if (shapeData is GroupShapeData) {
      // First, flip the group's bounding shape vertices
      final flippedVertices = <Offset>[];
      for (final vertex in shapeData.vertices) {
        // Calculate vertical reflection across the center
        final dy = shapeData.center.dy - (vertex.dy - shapeData.center.dy);
        flippedVertices.add(Offset(vertex.dx, dy));
      }

      // Now flip each child shape vertically
      final flippedChildShapes = <ShapeData>[];
      for (final childShape in shapeData.childShapes) {
        // Calculate how to flip the child relative to the group's center
        final flippedChildVertices = <Offset>[];
        for (final vertex in childShape.vertices) {
          // Flip vertically across the group's center
          final dy = shapeData.center.dy - (vertex.dy - shapeData.center.dy);
          flippedChildVertices.add(Offset(vertex.dx, dy));
        }

        // Create a new map of curve controls by flipping them vertically
        final flippedCurveControls = <int, Offset>{};
        for (final entry in childShape.curveControls.entries) {
          if (entry.value != Offset.zero) {
            // Flip the control offset vertically
            flippedCurveControls[entry.key] =
                Offset(entry.value.dx, -entry.value.dy);
          }
        }

        // Calculate the new center for the child shape
        final childCenterDy =
            shapeData.center.dy - (childShape.center.dy - shapeData.center.dy);
        final flippedChildCenter = Offset(childShape.center.dx, childCenterDy);

        // Create updated child shape data
        final updatedChildShape = childShape.copyWith(
          vertices: flippedChildVertices,
          curveControls: flippedCurveControls,
          center: flippedChildCenter,
        );

        flippedChildShapes.add(updatedChildShape);
      }

      // Create updated group shape data with flipped vertices and children
      return shapeData.copyWithChildren(
        vertices: flippedVertices,
        childShapes: flippedChildShapes,
      );
    }

    // Normal handling for regular shapes
    // Create a new list of vertices by reflecting each vertex vertically
    final flippedVertices = <Offset>[];
    for (final vertex in shapeData.vertices) {
      // Calculate vertical reflection across the center
      final dy = shapeData.center.dy - (vertex.dy - shapeData.center.dy);
      flippedVertices.add(Offset(vertex.dx, dy));
    }

    // Create a new map of curve controls by flipping them vertically
    final flippedCurveControls = <int, Offset>{};
    for (final entry in shapeData.curveControls.entries) {
      if (entry.value != Offset.zero) {
        // Flip the control offset vertically
        flippedCurveControls[entry.key] =
            Offset(entry.value.dx, -entry.value.dy);
      }
    }

    // Create updated shape data
    final updatedShapeData = shapeData.copyWith(
      vertices: flippedVertices,
      curveControls: flippedCurveControls,
    );

    // Update the bounding rectangle and center
    return shapeManager.updateBoundingRect(updatedShapeData);
  }
}
