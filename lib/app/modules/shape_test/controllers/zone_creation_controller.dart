import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:math' as math;
import 'package:xoxknit/app/modules/shape_test/controllers/zones_editor_controller.dart';
import 'package:xoxknit/app/modules/shape_test/models/cell_dimensions.dart';

class ZoneCreationController extends GetxController {
  // For polygon selection
  final RxList<Offset> polygonVertices = <Offset>[].obs;
  final RxBool isPolygonClosed = false.obs;
  final RxBool isDraggingLast = false.obs;

  // For dragging any point
  final Rx<int?> draggedPointIndex = Rx<int?>(null);

  // For alignment indicators
  final Rx<int?> horizontalAlignedIndex = Rx<int?>(null);
  final Rx<int?> verticalAlignedIndex = Rx<int?>(null);
  final Rx<double?> alignmentPosition = Rx<double?>(null);
  final RxBool isHorizontalAlignment = false.obs;

  // Threshold for alignment detection (in pixels)
  final double alignmentThreshold = 3.0;

  // Reference to parent controller
  final ZonesEditorController editorController;

  ZoneCreationController({required this.editorController});

  // Add a vertex to the polygon at the given position
  void addVertex(Offset position) {
    // Debug output for adding a vertex
    debugPrint('Adding vertex at $position');

    // Always create a new list to ensure reactivity
    final List<Offset> newVertices = List<Offset>.from(polygonVertices);

    if (isPolygonClosed.value) {
      // If polygon is closed, start a new one
      newVertices.clear();
      isPolygonClosed.value = false;

      // Reset alignment indicators
      _clearAlignmentIndicators();
    }

    newVertices.add(position);
    polygonVertices.value = newVertices; // Assign to .value to trigger update

    // Force a UI refresh
    update();

    // Log after adding
    debugPrint('Vertices count after add: ${polygonVertices.length}');
  }

  // Update the last vertex position
  void updateLastVertex(Offset position) {
    if (polygonVertices.isNotEmpty && !isPolygonClosed.value) {
      final vertices = List<Offset>.from(polygonVertices);
      vertices[vertices.length - 1] = position;
      polygonVertices.value = vertices;

      // Check for alignments
      _checkPointAlignments(vertices.length - 1, position);

      // Force a UI refresh
      update();
    }
  }

  // Update any vertex position by index
  void updateVertexPosition(int index, Offset position) {
    if (index >= 0 && index < polygonVertices.length) {
      final vertices = List<Offset>.from(polygonVertices);
      vertices[index] = position;
      polygonVertices.value = vertices;

      // Check for alignments with this point
      _checkPointAlignments(index, position);

      // Force a UI refresh
      update();
    }
  }

  // Method to check if the current point aligns with any other points
  void _checkPointAlignments(int currentIndex, Offset position) {
    // Clear previous alignments
    _clearAlignmentIndicators();

    // No need to check if we only have one point
    if (polygonVertices.length <= 1) return;

    // Check alignment with all other vertices
    for (int i = 0; i < polygonVertices.length; i++) {
      // Skip comparing with itself
      if (i == currentIndex) continue;

      final otherPoint = polygonVertices[i];

      // Check for horizontal alignment (y values match)
      if ((position.dy - otherPoint.dy).abs() < alignmentThreshold) {
        horizontalAlignedIndex.value = i;
        isHorizontalAlignment.value = true;
        alignmentPosition.value = otherPoint.dy;
        break; // Only show one alignment at a time
      }

      // Check for vertical alignment (x values match)
      if ((position.dx - otherPoint.dx).abs() < alignmentThreshold) {
        verticalAlignedIndex.value = i;
        isHorizontalAlignment.value = false;
        alignmentPosition.value = otherPoint.dx;
        break; // Only show one alignment at a time
      }
    }

    // Force UI update
    update();
  }

  // Clear alignment indicators
  void _clearAlignmentIndicators() {
    horizontalAlignedIndex.value = null;
    verticalAlignedIndex.value = null;
    alignmentPosition.value = null;
    update();
  }

  // Update dragging state
  void startDraggingPoint(int index) {
    draggedPointIndex.value = index;
  }

  // Stop dragging
  void stopDragging() {
    draggedPointIndex.value = null;
    _clearAlignmentIndicators(); // Clear alignment indicators when dragging stops
  }

  // Undo the last point
  void undoLastPoint() {
    if (polygonVertices.isNotEmpty) {
      final vertices = List<Offset>.from(polygonVertices);
      vertices.removeLast();
      polygonVertices.value = vertices;

      // Ensure we're not showing closed state if we undo back to only 2 points
      if (vertices.length < 3) {
        isPolygonClosed.value = false;
      }

      // Clear alignments when points change
      _clearAlignmentIndicators();

      // Force a UI refresh
      update();
    }
  }

  // Close the polygon
  void closePolygon() {
    if (polygonVertices.length >= 3) {
      isPolygonClosed.value = true;

      // Clear alignments when we close
      _clearAlignmentIndicators();

      // Force a UI refresh
      update();
    }
  }

  // Apply the polygonal selection to update the grid
  void applyPolygonSelection(List<List<bool>> fullPattern,
      CellDimensions dimensions, Size canvasSize) {
    if (polygonVertices.length < 3 || !isPolygonClosed.value) {
      return; // Need at least 3 points and a closed polygon
    }

    // Clear any previous selection
    final numRows = fullPattern.length;
    final numCols = fullPattern[0].length;
    final List<List<bool>> newSelectionGrid =
        List.generate(numRows, (i) => List.filled(numCols, false));

    // Create a path from the polygon
    final path = Path();
    path.moveTo(polygonVertices[0].dx, polygonVertices[0].dy);
    for (int i = 1; i < polygonVertices.length; i++) {
      path.lineTo(polygonVertices[i].dx, polygonVertices[i].dy);
    }
    path.close();

    // Find bounding box of the polygon to optimize the checking
    double minX = double.infinity;
    double maxX = -double.infinity;
    double minY = double.infinity;
    double maxY = -double.infinity;

    for (final vertex in polygonVertices) {
      minX = math.min(minX, vertex.dx);
      maxX = math.max(maxX, vertex.dx);
      minY = math.min(minY, vertex.dy);
      maxY = math.max(maxY, vertex.dy);
    }

    // Convert the bounding box to grid coordinates
    final startRow = math.max(
        0,
        dimensions.getRowFromY(minY) -
            1); // Expand slightly to ensure we catch cells on the edges
    final endRow = math.min(numRows - 1, dimensions.getRowFromY(maxY) + 1);
    final startCol =
        math.max(0, dimensions.getColFromX(minX) - 1); // Expand slightly
    final endCol = math.min(numCols - 1, dimensions.getColFromX(maxX) + 1);

    // For each cell in the bounding box, check if cell center is inside polygon
    for (int row = startRow; row <= endRow; row++) {
      for (int col = startCol; col <= endCol; col++) {
        // Only consider cells that have stitches in the pattern
        if (fullPattern[row][col]) {
          final cellCenterX = dimensions.offsetX +
              (col * dimensions.cellWidth) +
              (dimensions.cellWidth / 2);
          final cellCenterY = dimensions.offsetY +
              (row * dimensions.cellHeight) +
              (dimensions.cellHeight / 2);

          if (path.contains(Offset(cellCenterX, cellCenterY))) {
            newSelectionGrid[row][col] = true;
          }
        }
      }
    }

    // Check if any cells were selected
    bool anySelected = false;
    for (var row in newSelectionGrid) {
      if (row.contains(true)) {
        anySelected = true;
        break;
      }
    }

    if (!anySelected) {
      // Show a notification if no stitches were selected
      Get.snackbar(
        'No Stitches Selected',
        'The selected area does not contain any stitches.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
      return;
    }

    // Store the bounding box in the editor controller for later use
    editorController.pendingZoneBoundingBox['minRow'] = startRow;
    editorController.pendingZoneBoundingBox['maxRow'] = endRow;
    editorController.pendingZoneBoundingBox['minCol'] = startCol;
    editorController.pendingZoneBoundingBox['maxCol'] = endCol;

    // Update the selection in the editor controller
    editorController.updateEntireSelection(newSelectionGrid);
  }

  // Method to snap to alignment
  void snapToAlignment() {
    if (draggedPointIndex.value != null && alignmentPosition.value != null) {
      final index = draggedPointIndex.value!;
      final currentPoint = polygonVertices[index];

      // Create a new point that snaps to the alignment line
      Offset snappedPoint;
      if (isHorizontalAlignment.value) {
        // For horizontal alignment, keep X but snap Y
        snappedPoint = Offset(currentPoint.dx, alignmentPosition.value!);
      } else {
        // For vertical alignment, keep Y but snap X
        snappedPoint = Offset(alignmentPosition.value!, currentPoint.dy);
      }

      // Update the point position with the snapped position
      updateVertexPosition(index, snappedPoint);
    }
  }

  // Reset the polygon creation state
  void reset() {
    polygonVertices.clear();
    isPolygonClosed.value = false;
    isDraggingLast.value = false;
    draggedPointIndex.value = null;
    _clearAlignmentIndicators();
    update();
  }

  @override
  void onInit() {
    super.onInit();

    // Add workers to ensure editor controller events trigger updates
    ever(editorController.isCreatingNewZone, (_) {
      debugPrint(
          'Creation mode changed: ${editorController.isCreatingNewZone.value}');
      if (editorController.isCreatingNewZone.value) {
        // Clear any existing polygon when entering creation mode
        reset();
      }
      update();
    });

    ever(editorController.newZoneSelection, (_) => update());

    // Listen to our own observable lists
    ever(polygonVertices, (_) {
      debugPrint('Vertices changed: ${polygonVertices.length} points');
      update();
    });
  }
}
