import 'package:get/get.dart';
import 'package:xoxknit/app/services/knitting/knitting_settings_service.dart';

import '../controllers/settings_controller.dart';

class SettingsBinding extends Bindings {
  @override
  void dependencies() {
    if (!Get.isRegistered<KnittingSettingsService>()) {
      Get.put(KnittingSettingsService());
    }
    Get.lazyPut<SettingsController>(
      () => SettingsController(),
    );
  }
}
