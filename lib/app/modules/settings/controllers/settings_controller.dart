import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:xoxknit/app/data/models/knitting_machine_model.dart';
import 'package:xoxknit/app/data/models/user_knitting_machine_model.dart';
import 'package:xoxknit/app/modules/home/<USER>/home_controller.dart';
import 'package:xoxknit/app/services/knitting/knitting_settings_service.dart';
import 'package:xoxknit/app/services/locale_service.dart';
import '../../../core/theme/theme_controller.dart';
import 'package:xoxknit/app/services/auth_service.dart';
import 'package:xoxknit/app/services/wizard_state_service.dart';
import 'package:xoxknit/app/routes/app_pages.dart';
import 'package:xoxknit/app/components/app_loader.dart';

class SettingsController extends GetxController {
  final ThemeController _themeController = ThemeController.to;
  final RxBool isDarkMode = ThemeController.to.isDarkMode.obs;
  final RxString selectedLanguage = LocaleService.to.currentLanguage.obs;
  final RxString selectedUnit = 'cm'.obs;
  final RxString selectedCurrency = 'EUR'.obs;

  final RxList<KnittingMachineModel> selectedKnittingMachines =
      <KnittingMachineModel>[].obs;

  final RxBool isDeletingAccount = false.obs;

  @override
  void onInit() {
    super.onInit();

    if (!Get.isRegistered<KnittingSettingsService>()) {
      Get.put(KnittingSettingsService());
    }

    Future.delayed(Duration.zero, () {
      final settingsService = KnittingSettingsService.to;

      selectedKnittingMachines.value = settingsService.userMachines;
    });
  }

  void toggleTheme(bool value) {
    isDarkMode.value = value;
    _themeController.toggleTheme();
    Future.delayed(Duration(milliseconds: 500), () {
      HomeController.to.updatePage();
    });
  }

  void setLanguage(String languageCode, String countryCode) {
    LocaleService.to.changeLocale(languageCode, countryCode);
  }

  void setUnit(String unit) {
    selectedUnit.value = unit;
    // TODO: Implement unit change logic
  }

  void setCurrency(String currency) {
    selectedCurrency.value = currency;
    // TODO: Implement currency change logic
  }

  // Handle account deletion process
  void initiateAccountDeletion() {
    Get.showOverlay(
      asyncFunction: () async {
        try {
          isDeletingAccount.value = true;

          final authService = AuthService.to;
          final knittingSettingsService = KnittingSettingsService.to;
          final wizardStateService = WizardStateService.to;

          // 1. Delete all user created items (wizard states)
          final userItems = await wizardStateService.listWizardStates();
          final archivedItems =
              await wizardStateService.listWizardStates(archived: true);

          // Combine both active and archived items
          final allItems = [...userItems, ...archivedItems];

          // Delete each item
          for (final item in allItems) {
            await wizardStateService.deleteWizardState(item.id);
          }

          // 2. Delete user knitting machines and settings
          await knittingSettingsService
              .clearLastValues(); // Now returns a Future

          // Clear user machines
          final emptyMachines = <UserKnittingMachineModel>[];
          await knittingSettingsService.updateUserMachinePrefs(emptyMachines);

          // 3. Delete user account
          await authService.deleteUserAccount();

          // Account deleted successfully
          Get.rootDelegate.offNamed(Routes.LOGIN);

          // Show confirmation
          Get.snackbar(
            'settings_accountManagement_deleteAccount_successTitle'.tr,
            'settings_accountManagement_deleteAccount_successMessage'.tr,
            snackPosition: SnackPosition.BOTTOM,
            duration: const Duration(seconds: 5),
          );
        } catch (e) {
          // Show error
          Get.snackbar(
            'common_error'.tr,
            'settings_accountManagement_deleteAccount_errorMessage'.tr,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red.withOpacity(0.8),
            colorText: Colors.white,
          );
        } finally {
          isDeletingAccount.value = false;
        }
      },
      loadingWidget: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AppLoader(
              color: Get.theme.colorScheme.onPrimary,
              size: 50,
            ),
            const SizedBox(height: 16),
            Text(
              'settings_accountManagement_deleteAccount_deletingMessage'.tr,
              style: TextStyle(color: Get.theme.colorScheme.onPrimary),
            ),
          ],
        ),
      ),
    );
  }
}
