import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/components/settings_section.dart';
import 'package:xoxknit/app/components/settings_tile.dart';
import 'package:xoxknit/app/routes/app_pages.dart';
import 'package:xoxknit/app/services/locale_service.dart';
import '../../../core/theme/app_colors.dart';
import '../controllers/settings_controller.dart';

class SettingsView extends GetView<SettingsController> {
  const SettingsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('settings_title'.tr),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildKnittingMachinesSection(),
          const SizedBox(height: 24),
          _buildAppearanceSection(),
          const SizedBox(height: 24),
          _buildLanguageSection(),
          const SizedBox(height: 24),
          _buildMeasurementSection(),
          const SizedBox(height: 24),
          _buildCurrencySection(),
          const SizedBox(height: 24),
          _buildAccountManagementSection(),
        ],
      ),
    );
  }

  Widget _buildKnittingMachinesSection() {
    return SettingsSection(
      title: 'settings_sections_knittingMachines'.tr,
      children: [
        SettingsTile(
          title: 'settings_knittingMachines_title'.tr,
          subtitle: 'settings_knittingMachines_subtitle'.tr,
          onTap: () =>
              Get.rootDelegate.toNamed(Routes.SETTINGS + Routes.USER_MACHINES),
          trailing: const Icon(Icons.chevron_right),
        ),
      ],
    );
  }

  Widget _buildAppearanceSection() {
    return SettingsSection(
      title: 'settings_sections_appearance'.tr,
      children: [
        Obx(() => SettingsTile(
              title: 'settings_appearance_darkMode_title'.tr,
              subtitle: 'settings_appearance_darkMode_subtitle'.tr,
              trailing: Switch(
                value: controller.isDarkMode.value,
                onChanged: controller.toggleTheme,
                activeColor: Get.theme.colorScheme.primary,
              ),
            )),
      ],
    );
  }

  Widget _buildLanguageSection() {
    return SettingsSection(
      title: 'settings_sections_language'.tr,
      children: [
        Obx(() => SettingsTile(
              title: 'settings_language_title'.tr,
              subtitle: LocaleService.to
                      .getLanguageName(controller.selectedLanguage.value) ??
                  'settings_language_subtitle'.tr,
              onTap: () => _showLanguageDialog(),
              trailing: const Icon(Icons.chevron_right),
            )),
      ],
    );
  }

  Widget _buildMeasurementSection() {
    return SettingsSection(
      title: 'settings_sections_measurement'.tr,
      children: [
        SettingsTile(
          title: 'settings_measurement_title'.tr,
          subtitle: 'settings_measurement_subtitle'.tr,
          onTap: () => _showUnitDialog(),
          trailing: const Icon(Icons.chevron_right),
        ),
      ],
    );
  }

  Widget _buildCurrencySection() {
    return SettingsSection(
      title: 'settings_sections_currency'.tr,
      children: [
        Obx(() => SettingsTile(
              title: 'settings_currency_title'.tr,
              subtitle: controller.selectedCurrency.value,
              onTap: () => _showCurrencyDialog(),
              trailing: const Icon(Icons.chevron_right),
            )),
      ],
    );
  }

  Widget _buildAccountManagementSection() {
    return SettingsSection(
      title: 'settings_sections_accountManagement'.tr,
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.red.withOpacity(0.1),
            border: Border.all(
              color: Colors.red.withOpacity(0.2),
              width: 1.0,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: SettingsTile(
            title: 'settings_accountManagement_deleteAccount_title'.tr,
            subtitle: 'settings_accountManagement_deleteAccount_subtitle'.tr,
            trailing: Container(
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.15),
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.all(8),
              child:
                  const Icon(Icons.delete_forever, color: Colors.red, size: 28),
            ),
            onTap: () => _showDeleteAccountConfirmation(),
          ),
        ),
      ],
    );
  }

  void _showLanguageDialog() {
    Get.dialog(
      AlertDialog(
        title: Text('settings_language_dialogTitle'.tr),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children:
              List.generate(LocaleService.to.supportedLocales.length, (index) {
            final locale = LocaleService.to.supportedLocales[index];
            return ListTile(
              title: Text(locale['name']!),
              trailing: Text(
                locale['flag']!,
              ),
              onTap: () {
                controller.setLanguage(locale['code']!, locale['country']!);
                Get.back();
              },
            );
          }),
        ),
      ),
    );
  }

  void _showUnitDialog() {
    Get.dialog(
      AlertDialog(
        title: Text('settings_measurement_dialogTitle'.tr),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: Text('settings_measurement_centimeters'.tr),
              onTap: () {
                controller.setUnit('cm');
                Get.back();
              },
            ),
            ListTile(
              title: Text('settings_measurement_inches'.tr),
              onTap: () {
                controller.setUnit('in');
                Get.back();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showCurrencyDialog() {
    Get.dialog(
      AlertDialog(
        title: Text('settings_currency_dialogTitle'.tr),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: Text('settings_currency_eur'.tr),
              onTap: () {
                controller.setCurrency('EUR');
                Get.back();
              },
            ),
            ListTile(
              title: Text('settings_currency_usd'.tr),
              onTap: () {
                controller.setCurrency('USD');
                Get.back();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteAccountConfirmation() {
    final TextEditingController confirmController = TextEditingController();
    final RxBool isConfirmEnabled = false.obs;

    // Validation listener
    void validateConfirmInput() {
      isConfirmEnabled.value = confirmController.text.toUpperCase() == 'DELETE';
    }

    // Add listener
    confirmController.addListener(validateConfirmInput);

    Get.dialog(
      AlertDialog(
        title: Text('settings_accountManagement_deleteAccount_confirmTitle'.tr),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('settings_accountManagement_deleteAccount_confirmMessage'.tr),
            const SizedBox(height: 24),
            Text(
              'settings_accountManagement_deleteAccount_typeToConfirm'.tr,
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: confirmController,
              decoration: InputDecoration(
                hintText: 'DELETE',
                border: OutlineInputBorder(),
              ),
              onChanged: (_) => validateConfirmInput(),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              confirmController.dispose();
              Get.back();
            },
            child: Text('common_cancel'.tr),
          ),
          Obx(() => TextButton(
                style: TextButton.styleFrom(
                  foregroundColor: Colors.red,
                ),
                onPressed: isConfirmEnabled.value
                    ? () {
                        confirmController.dispose();
                        Get.back();
                        controller.initiateAccountDeletion();
                      }
                    : null,
                child:
                    Text('settings_accountManagement_deleteAccount_confirm'.tr),
              )),
        ],
      ),
    );
  }
}
