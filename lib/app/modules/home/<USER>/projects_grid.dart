import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/routes/app_pages.dart';

class ProjectsGrid extends GetResponsiveView {
  ProjectsGrid({super.key});

  List<Map<String, dynamic>> get _projects => [
        {
          'title': 'home_projectsGrid_newItem'.tr,
          'icon': Icons.add_box_outlined,
          'onTap': () {
            Get.rootDelegate.toNamed(
              Routes.MY_ITEMS + Routes.NEW_ITEM_WIZARD,
              arguments: {'from': 'home'},
            );
          },
          'isComingSoon': false,
        },
        {
          'title': 'home_projectsGrid_myItems'.tr,
          'icon': Icons.inventory_2_outlined,
          'onTap': () {
            Get.rootDelegate.toNamed(Routes.MY_ITEMS);
          },
          'isComingSoon': false,
        },
        {
          'title': 'home_projectsGrid_xoxknitItems'.tr,
          'icon': Icons.shopping_bag_outlined,
          'onTap': () {},
          'isComingSoon': true,
        },
      ];

  @override
  Widget? builder() {
    // Calculate grid properties based on screen size
    int crossAxisCount;
    double horizontalPadding;
    double childAspectRatio;

    if (screen.isDesktop) {
      crossAxisCount = 3;
      horizontalPadding = 24.0;
      childAspectRatio = 1.4;
    } else if (screen.isTablet) {
      crossAxisCount = 2;
      horizontalPadding = 30.0;
      childAspectRatio = 1.6;
    } else {
      // Phone
      crossAxisCount = 1;
      horizontalPadding = 16.0;
      childAspectRatio = 2.0;
    }

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Get.theme.colorScheme.surfaceContainerHighest,
            Get.theme.colorScheme.surfaceContainerHighest,
          ],
        ),
      ),
      child: Padding(
        padding:
            EdgeInsets.symmetric(horizontal: horizontalPadding, vertical: 16.0),
        child: GridView.builder(
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            mainAxisSpacing: 42,
            crossAxisSpacing: 16,
            childAspectRatio: childAspectRatio,
          ),
          itemCount: _projects.length,
          itemBuilder: (context, index) {
            final project = _projects[index];
            return _buildGridItem(
              project['title'],
              project['icon'],
              project['onTap'],
              isComingSoon: project['isComingSoon'],
            );
          },
        ),
      ),
    );
  }

  Widget _buildGridItem(String title, IconData icon, VoidCallback onTap,
      {bool isComingSoon = false}) {
    final textColor = isComingSoon
        ? Get.theme.colorScheme.primary
        : Get.theme.colorScheme.onPrimary;

    return Hero(
      tag: title,
      child: Material(
        color: Colors.transparent,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              Positioned.fill(
                child: Card(
                  elevation: 4,
                  margin: EdgeInsets.zero,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                    side: isComingSoon
                        ? BorderSide(
                            color: Get.theme.colorScheme.primary, width: 3)
                        : BorderSide.none,
                  ),
                  child: InkWell(
                    onTap: onTap,
                    borderRadius: BorderRadius.circular(16),
                    splashColor: Get.theme.colorScheme.primary.withOpacity(0.3),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        gradient: isComingSoon
                            ? null
                            : LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Get.theme.colorScheme.primary,
                                  Get.theme.colorScheme.primary
                                      .withOpacity(0.8),
                                ],
                              ),
                        color:
                            isComingSoon ? Get.theme.colorScheme.surface : null,
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              icon,
                              size: screen.isPhone ? 48 : 60,
                              color: textColor,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              title,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: screen.isPhone ? 18 : 20,
                                color: textColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              if (isComingSoon)
                Positioned(
                  top: 50,
                  left: -20,
                  child: Container(
                    decoration: BoxDecoration(
                        color: Get.theme.colorScheme.primary,
                        borderRadius: BorderRadius.circular(16)),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 1,
                    ),
                    transform: Matrix4.rotationZ(-pi / 5),
                    child: Text(
                      'home_projectsGrid_comingSoon'.tr,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Get.theme.colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
