import 'package:animations/animations.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:xoxknit/app/components/app_drawer.dart';
import 'package:xoxknit/app/core/theme/theme_controller.dart';
import 'package:xoxknit/app/modules/home/<USER>/projects_grid.dart';

import '../../../components/language_switcher.dart';
import '../controllers/home_controller.dart';

class HomeView extends GetResponsiveView<HomeController> {
  HomeView({super.key});

  @override
  Widget builder() {
    return GetBuilder<HomeController>(
        id: "home",
        builder: (homeController) => Scaffold(
              appBar: _buildAppBar(),
              drawer: screen.isPhone || screen.isTablet ? AppDrawer() : null,
              body: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Get.theme.colorScheme.surface,
                      Get.theme.colorScheme.surface.withOpacity(0.9),
                    ],
                  ),
                ),
                child: Safe<PERSON>rea(
                  child: Row(
                    children: [
                      if (!screen.isPhone && !screen.isTablet)
                        _buildAnimatedDrawer(),
                      Expanded(
                        child: Column(
                          children: [
                            Expanded(
                              child: PageTransitionSwitcher(
                                transitionBuilder: (child, primaryAnimation,
                                    secondaryAnimation) {
                                  return FadeThroughTransition(
                                    animation: primaryAnimation,
                                    secondaryAnimation: secondaryAnimation,
                                    child: child,
                                  );
                                },
                                child: ProjectsGrid(),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ));
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: Get.theme.colorScheme.surface,
      title: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            'assets/images/logo_white.png',
            height: 32,
            // If you don't have this asset, remove this Image widget
            errorBuilder: (context, error, stackTrace) => Text(''.tr,
                style: TextStyle(color: Get.theme.colorScheme.primary)),
          ),
          const SizedBox(width: 8),
          Text(
            'home_title'.tr,
            style: TextStyle(
              color: Get.theme.colorScheme.primary,
              fontWeight: FontWeight.bold,
              fontSize: screen.isPhone ? 20 : 22,
            ),
          ),
        ],
      ),
      centerTitle: true,
      leading: screen.isPhone || screen.isTablet
          ? Builder(builder: (context) {
              return IconButton(
                icon: Icon(Icons.menu, color: Get.theme.colorScheme.primary),
                onPressed: () => Scaffold.of(context).openDrawer(),
              );
            })
          : null,
      actions: [
        LanguageSwitcher(),
        const SizedBox(width: 8),
      ],
    );
  }

  Widget _buildAnimatedDrawer() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: 250,
      decoration: BoxDecoration(
        color: Get.theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Get.theme.colorScheme.shadow.withOpacity(0.05),
            blurRadius: 10,
            spreadRadius: 0,
          ),
        ],
      ),
      child: const AppDrawer(),
    );
  }
}
