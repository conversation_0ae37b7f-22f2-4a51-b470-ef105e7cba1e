import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/components/calculator_tab_bar.dart';

class HomeController extends GetxController {
  static HomeController get to => Get.find();
  static const List<CalculatorTab> tabs = [
    CalculatorTab(
      label: 'Make Stuff',
      icon: Icons.calculate_outlined,
    ),
    CalculatorTab(label: 'Courses', icon: Icons.waves_outlined),
    CalculatorTab(
      label: 'Shop',
      icon: Icons.image,
    ),
  ];

  final _selectedIndex = 0.obs;
  int get selectedIndex => _selectedIndex.value;

  void changeTab(int index) {
    _selectedIndex.value = index;
  }

  void updatePage() {
    update(["home"]);
  }
}
