import 'dart:math' as math;
import 'dart:ui';

import '../../../core/utils/logger.dart';

/// Utility functions for knitting pattern analysis and manipulation
class KnittingUtils {
  /// Check if two rows are similar (exact match)
  static bool rowsAreSimilar(List<bool> row1, List<bool> row2) {
    if (row1.length != row2.length) return false;

    for (int i = 0; i < row1.length; i++) {
      if (row1[i] != row2[i]) return false;
    }

    return true;
  }

  /// Find the consecutive identical rows from a starting position
  static int findRepeatingRows(List<List<bool>> pattern, int startRow) {
    int repeatCount = 1;
    final baseRow = pattern[startRow];

    for (int i = startRow + 1; i < pattern.length; i++) {
      if (rowsAreSimilar(baseRow, pattern[i])) {
        repeatCount++;
      } else {
        break;
      }
    }

    return repeatCount;
  }

  /// Find all continuous sections of stitches in a row
  static List<StitchRange> findStitchRanges(List<bool> row) {
    final List<StitchRange> ranges = [];

    int? startNeedle;

    for (int i = 0; i < row.length; i++) {
      if (row[i]) {
        // Stitch exists at this position
        startNeedle ??= i;
      } else {
        // No stitch here
        if (startNeedle != null) {
          // End of a range
          ranges.add(StitchRange(startNeedle, i - 1));
          startNeedle = null;
        }
      }
    }

    // Handle case where range extends to the end of the row
    if (startNeedle != null) {
      ranges.add(StitchRange(startNeedle, row.length - 1));
    }

    return ranges;
  }

  /// Format needle indices into human-readable L/R notation
  static String formatNeedleNumber(int needleIndex, int needleCount,
      {bool useLRNotation = true}) {
    if (!useLRNotation) return needleIndex.toString();

    // Calculate center needle for L/R notation
    final centerNeedle = needleCount ~/ 2;

    // No center needle "C", only L1 and R1
    if (needleIndex < centerNeedle) {
      return 'L${centerNeedle - needleIndex}';
    } else {
      return 'R${needleIndex - centerNeedle + 1}';
    }
  }

  /// Format a list of stitch ranges into human-readable text
  static String formatRangesText(List<StitchRange> ranges, int needleCount,
      {bool useLRNotation = true}) {
    if (ranges.isEmpty) return "No stitches on this row";

    final List<String> rangeTexts = [];

    for (final range in ranges) {
      final startNeedle = formatNeedleNumber(range.startNeedle, needleCount,
          useLRNotation: useLRNotation);
      final endNeedle = formatNeedleNumber(range.endNeedle, needleCount,
          useLRNotation: useLRNotation);

      if (range.startNeedle == range.endNeedle) {
        rangeTexts.add("Knit needle $startNeedle");
      } else {
        rangeTexts.add("Knit needles $startNeedle to $endNeedle");
      }
    }

    return rangeTexts.join(", then ");
  }

  /// Calculate the maximum row width for a knitting pattern
  static int calculateMaxWidth(List<List<bool>> pattern) {
    int maxWidth = 0;
    for (final row in pattern) {
      int rowWidth = 0;
      for (int i = 0; i < row.length; i++) {
        if (row[i]) rowWidth = i + 1;
      }
      maxWidth = math.max(maxWidth, rowWidth);
    }
    return maxWidth;
  }

  /// Calculate whether a row has discontinuous stitches
  static bool hasDiscontinuousStitches(List<bool> row) {
    return findStitchRanges(row).length > 1;
  }

  /// Get the stitch pattern as a compact string for debugging/comparison
  static String getPatternString(List<bool> row) {
    return row.map((b) => b ? 'K' : '.').join();
  }

  static void displayShapePath(Path path) {
    final logger = AppLogger();

    logger.debug('---------- SHAPE PATH VISUALIZATION ----------');

    // Get the path's bounds
    final bounds = path.getBounds();
    logger.debug('Path Bounds: ${bounds.toString()}');

    // Create a visual grid representation
    final width = bounds.width;
    final height = bounds.height;
    final gridSize = 100; // Size of the visual grid

    // Create a grid of characters
    final List<List<String>> grid = List.generate(
      gridSize,
      (i) => List.generate(gridSize, (j) => ' '),
    );

    // Draw the path points
    final points = <Offset>[];
    path.computeMetrics().forEach((metric) {
      for (var i = 0; i <= 100; i++) {
        final pos = metric.getTangentForOffset(metric.length * i / 100);
        if (pos != null) {
          points.add(pos.position);
        }
      }
    });

    // Map points to grid
    for (final point in points) {
      final x = ((point.dx - bounds.left) / width * (gridSize - 1)).round();
      final y = ((point.dy - bounds.top) / height * (gridSize - 1)).round();
      if (x >= 0 && x < gridSize && y >= 0 && y < gridSize) {
        grid[y][x] = '█';
      }
    }

    // Draw the grid
    logger.debug('Path Visualization (${gridSize}x$gridSize):');
    for (final row in grid) {
      logger.debug('|${row.join('')}|');
    }

    // Log path statistics
    logger.debug('Path Statistics:');
    logger.debug('- Number of points: ${points.length}');
    logger.debug('- Width: ${width.toStringAsFixed(2)}');
    logger.debug('- Height: ${height.toStringAsFixed(2)}');

    logger.debug('---------- END OF PATH VISUALIZATION ----------');
  }

  static void logInstructions(List<List<bool>> instructions) {
    final logger = AppLogger();

    logger.debug('---------- KNITTING PATTERN VISUALIZATION ----------');

    // If no instructions, show a message
    if (instructions.isEmpty) {
      logger.debug('No instructions generated.');
      return;
    }

    // Calculate pattern dimensions
    final rows = instructions.length;
    final cols = instructions[0].length;

    logger.debug('Pattern size: $rows rows × $cols columns');

    // Create header with column numbers (every 5 columns)
    String header = '    ';
    for (var col = 0; col < cols; col++) {
      if (col % 5 == 0) {
        // Add column number markers every 5 columns
        var marker = col.toString();
        // Pad with spaces to align properly
        while (marker.length < 5 && col + marker.length < cols) {
          marker += ' ';
        }
        header += marker;
      } else {
        header += ' ';
      }
    }
    logger.debug(header);

    // Create visual representation of the pattern
    for (var row = 0; row < rows; row++) {
      // Row number with padding for alignment
      String rowStr = '${row.toString().padLeft(3)} |';

      // Convert booleans to visual characters
      for (var col = 0; col < instructions[row].length; col++) {
        rowStr += instructions[row][col] ? '█' : ' ';
      }

      // Add right border
      rowStr += '|';

      // Log the row
      logger.debug(rowStr);
    }

    // Bottom border
    String footer = '    ';
    for (var col = 0; col < cols; col++) {
      footer += '-';
    }
    logger.debug(footer);
    logger.debug('---------- END OF PATTERN ----------');
  }
}

/// Represents a range of continuous stitches
class StitchRange {
  final int startNeedle;
  final int endNeedle;

  StitchRange(this.startNeedle, this.endNeedle);

  @override
  String toString() => 'StitchRange($startNeedle-$endNeedle)';
}
