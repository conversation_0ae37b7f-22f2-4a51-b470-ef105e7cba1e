import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/knitting_instructions_manager.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/knitting_zone_models.dart';
import 'knitting_utils.dart' as utils;

/// A utility class for processing and applying knitting zone configurations
class ZoneConfigurationProcessor {
  /// Processes a knitting zone's instructions based on its configuration
  /// Returns the modified instructions
  static List<List<bool>> applyZoneConfiguration(KnittingZone zone) {
    if (zone.instructions.isEmpty) return [];

    List<List<bool>> processedInstructions =
        List.from(zone.instructions.map((row) => List<bool>.from(row)));
    final config = zone.config.value;

    // Apply asymmetrical rows adjustment if needed
    if (!config.autoAdjustAsymmetricalRows.value) {
      processedInstructions = _applyAsymmetricalAdjustment(
        processedInstructions,
        config.asymmetricalDirection.value,
      );
    }

    // Apply manual increases/decreases if auto control is disabled
    if (!config.autoControlIncreaseDecrease.value) {
      processedInstructions = _applyManualIncreasesDecreases(
        processedInstructions,
        config.increaseBy.value,
        config.increaseEvery.value,
        config.decreaseBy.value,
        config.decreaseEvery.value,
      );
    }

    return processedInstructions;
  }

  /// Applies asymmetrical adjustment to rows
  /// If direction is "inc", adds a stitch on the shorter side
  /// If direction is "dec", removes a stitch from the longer side
  static List<List<bool>> _applyAsymmetricalAdjustment(
    List<List<bool>> instructions,
    String direction,
  ) {
    List<List<bool>> result = [];

    for (final row in instructions) {
      // Find the active stitch ranges
      final List<utils.StitchRange> ranges =
          utils.KnittingUtils.findStitchRanges(row);

      // If it's a single continuous range, check for asymmetry
      if (ranges.length == 1) {
        final range = ranges[0];

        // Calculate the center of the active stitches, not the entire row
        final stitchCenter = (range.startNeedle + range.endNeedle) / 2.0;

        // Calculate left and right counts from the stitch center
        final leftCount = (stitchCenter - range.startNeedle).ceil();
        final rightCount = (range.endNeedle - stitchCenter).ceil();

        // Check if asymmetrical (difference of exactly 1)
        if ((leftCount - rightCount).abs() == 1) {
          List<bool> adjustedRow = List<bool>.from(row);

          if (direction == "inc") {
            // Add a stitch to the shorter side
            if (leftCount < rightCount) {
              // Add to left side if it's shorter
              if (range.startNeedle > 0) {
                adjustedRow[range.startNeedle - 1] = true;
              }
            } else {
              // Add to right side if it's shorter
              if (range.endNeedle < row.length - 1) {
                adjustedRow[range.endNeedle + 1] = true;
              }
            }
          } else if (direction == "dec") {
            // Remove a stitch from the longer side
            if (leftCount > rightCount) {
              // Remove from left side if it's longer
              adjustedRow[range.startNeedle] = false;
            } else {
              // Remove from right side if it's longer
              adjustedRow[range.endNeedle] = false;
            }
          }

          result.add(adjustedRow);
          continue;
        }
      }

      // If not asymmetrical or has multiple ranges, keep row as is
      result.add(List<bool>.from(row));
    }

    return result;
  }

  /// Applies manual increases and decreases based on configured intervals
  static List<List<bool>> _applyManualIncreasesDecreases(
    List<List<bool>> instructions,
    int increaseBy,
    int increaseEvery,
    int decreaseBy,
    int decreaseEvery,
  ) {
    if (instructions.isEmpty ||
        (increaseEvery <= 0 && decreaseEvery <= 0) ||
        (increaseBy <= 0 && decreaseBy <= 0)) {
      return instructions;
    }

    List<List<bool>> result = [];

    // Find the maximum width we'll need (considering increases)
    int maxWidth = instructions[0].length;
    for (int rowIndex = 0; rowIndex < instructions.length; rowIndex++) {
      int widthChange = 0;

      // Calculate width changes from increases
      if (increaseEvery > 0 && increaseBy > 0 && rowIndex > 0) {
        int increaseCount = rowIndex ~/ increaseEvery;
        widthChange +=
            increaseCount * increaseBy * 2; // increases on both sides
      }

      // Calculate width changes from decreases
      if (decreaseEvery > 0 && decreaseBy > 0 && rowIndex > 0) {
        int decreaseCount = rowIndex ~/ decreaseEvery;
        widthChange -=
            decreaseCount * decreaseBy * 2; // decreases on both sides
      }

      maxWidth = (instructions[0].length + widthChange) > maxWidth
          ? (instructions[0].length + widthChange)
          : maxWidth;
    }

    // Process each row with cumulative increases/decreases
    int cumulativeIncrease = 0;
    int cumulativeDecrease = 0;

    for (int rowIndex = 0; rowIndex < instructions.length; rowIndex++) {
      // Check if we need to increase on this row
      if (increaseEvery > 0 &&
          increaseBy > 0 &&
          rowIndex > 0 &&
          rowIndex % increaseEvery == 0) {
        cumulativeIncrease += increaseBy;
      }

      // Check if we need to decrease on this row
      if (decreaseEvery > 0 &&
          decreaseBy > 0 &&
          rowIndex > 0 &&
          rowIndex % decreaseEvery == 0) {
        cumulativeDecrease += decreaseBy;
      }

      // Apply the cumulative changes to this row
      List<bool> processedRow = _applyRowModifications(
        instructions[rowIndex],
        cumulativeIncrease,
        cumulativeDecrease,
        maxWidth,
      );

      result.add(processedRow);
    }

    return result;
  }

  /// Apply increases and decreases to a single row
  static List<bool> _applyRowModifications(
    List<bool> originalRow,
    int increasesToApply,
    int decreasesToApply,
    int targetWidth,
  ) {
    // Find the active stitch ranges
    final List<utils.StitchRange> ranges =
        utils.KnittingUtils.findStitchRanges(originalRow);

    if (ranges.isEmpty) {
      return List<bool>.filled(targetWidth, false);
    }

    // Get the leftmost and rightmost active stitches
    int leftmostStitch = ranges.first.startNeedle;
    int rightmostStitch = ranges.last.endNeedle;

    // Calculate the effective changes (decreases can't exceed available stitches)
    int effectiveIncrease = increasesToApply;
    int effectiveDecrease = decreasesToApply;

    // Count total active stitches
    int activeStitchCount = 0;
    for (final range in ranges) {
      activeStitchCount += range.endNeedle - range.startNeedle + 1;
    }

    // Ensure we don't decrease more than available stitches (leave at least 1)
    if (effectiveDecrease * 2 >= activeStitchCount) {
      effectiveDecrease = (activeStitchCount - 1) ~/ 2;
      if (effectiveDecrease < 0) effectiveDecrease = 0;
    }

    // Create new row with proper width
    List<bool> newRow = List<bool>.filled(targetWidth, false);

    // Calculate the offset to center the pattern
    int netChange = effectiveIncrease - effectiveDecrease;
    int offset = (targetWidth - originalRow.length) ~/ 2;

    // Copy the original stitches with modifications
    int skippedLeft = 0;
    int skippedRight = 0;

    for (int i = 0; i < originalRow.length; i++) {
      if (originalRow[i]) {
        // Skip stitches for decrease from left
        if (skippedLeft < effectiveDecrease &&
            i <= leftmostStitch + effectiveDecrease - 1) {
          skippedLeft++;
          continue;
        }

        // Skip stitches for decrease from right
        if (skippedRight < effectiveDecrease &&
            i >= rightmostStitch - effectiveDecrease + 1) {
          skippedRight++;
          continue;
        }

        // Place the stitch in the new row
        int newIndex = i + offset;
        if (newIndex >= 0 && newIndex < targetWidth) {
          newRow[newIndex] = true;
        }
      }
    }

    // Add increased stitches on the left
    for (int i = 0; i < effectiveIncrease; i++) {
      int newIndex = leftmostStitch + offset - i - 1;
      if (newIndex >= 0 && newIndex < targetWidth) {
        newRow[newIndex] = true;
      }
    }

    // Add increased stitches on the right
    for (int i = 0; i < effectiveIncrease; i++) {
      int newIndex = rightmostStitch + offset + i + 1 - skippedRight;
      if (newIndex >= 0 && newIndex < targetWidth) {
        newRow[newIndex] = true;
      }
    }

    return newRow;
  }

  /// Get the adjusted needle ranges for a zone after configuration has been applied
  /// This method calculates the actual needle positions relative to the full pattern
  static Map<String, dynamic> getAdjustedNeedleRanges(
    KnittingZone zone,
    List<List<bool>> processedInstructions,
    int currentRowIndex,
    int needleCount,
  ) {
    if (processedInstructions.isEmpty) {
      return {
        'left': 0,
        'right': 0,
        'startNeedle': '',
        'endNeedle': '',
        'absoluteLeftmostNeedle': zone.startNeedle,
        'absoluteRightmostNeedle': zone.endNeedle,
      };
    }

    // Ensure row index is valid
    final validRowIndex = currentRowIndex >= processedInstructions.length
        ? processedInstructions.length - 1
        : currentRowIndex;

    if (validRowIndex < 0) {
      return {
        'left': 0,
        'right': 0,
        'startNeedle': '',
        'endNeedle': '',
        'absoluteLeftmostNeedle': zone.startNeedle,
        'absoluteRightmostNeedle': zone.endNeedle,
      };
    }

    final currentRow = processedInstructions[validRowIndex];

    // Find stitch ranges in the processed row
    final stitchRanges = utils.KnittingUtils.findStitchRanges(currentRow);

    if (stitchRanges.isEmpty) {
      return {
        'left': 0,
        'right': 0,
        'startNeedle': '',
        'endNeedle': '',
        'absoluteLeftmostNeedle': zone.startNeedle,
        'absoluteRightmostNeedle': zone.endNeedle,
      };
    }

    // Find the leftmost and rightmost stitches in the processed row
    int leftmostInRow = stitchRanges.first.startNeedle;
    int rightmostInRow = stitchRanges.last.endNeedle;

    // Calculate how much the zone has been shifted due to increases/decreases
    // The zone's instructions might have been padded/centered differently after processing
    int zoneShift = 0;
    if (processedInstructions[0].length != zone.instructions[0].length) {
      // Zone has been resized, calculate the centering offset
      zoneShift =
          (processedInstructions[0].length - zone.instructions[0].length) ~/ 2;
    }

    // Calculate absolute positions by accounting for:
    // 1. The zone's position in the full pattern (zone.startNeedle)
    // 2. The position within the zone
    // 3. Any shifts due to increases/decreases (zoneShift adjustment)
    final adjustedLeftmost = zone.startNeedle + leftmostInRow - zoneShift;
    final adjustedRightmost = zone.startNeedle + rightmostInRow - zoneShift;

    // Calculate center needle for L/R notation
    final centerNeedle = needleCount ~/ 2;

    // Calculate left and right needle counts relative to center
    int leftNeedles = 0;
    int rightNeedles = 0;

    if (adjustedLeftmost < centerNeedle) {
      leftNeedles = centerNeedle - adjustedLeftmost;
    }

    if (adjustedRightmost >= centerNeedle) {
      rightNeedles = adjustedRightmost - centerNeedle + 1;
    }

    // Format needle labels using absolute positions
    final startNeedleLabel = utils.KnittingUtils.formatNeedleNumber(
        adjustedLeftmost, needleCount,
        useLRNotation: true);

    final endNeedleLabel = utils.KnittingUtils.formatNeedleNumber(
        adjustedRightmost, needleCount,
        useLRNotation: true);

    return {
      'left': leftNeedles,
      'right': rightNeedles,
      'startNeedle': startNeedleLabel,
      'endNeedle': endNeedleLabel,
      'absoluteLeftmostNeedle': adjustedLeftmost,
      'absoluteRightmostNeedle': adjustedRightmost,
      'zoneStartNeedle': zone.startNeedle,
      'zoneEndNeedle': zone.endNeedle,
      'zoneShift': zoneShift,
    };
  }

  /// Increases a row by adding stitches to both edges
  static List<bool> _increaseRow(List<bool> row, int increaseCount) {
    // This method is now deprecated in favor of _applyRowModifications
    // Kept for backward compatibility
    return _applyRowModifications(
        row, increaseCount, 0, row.length + increaseCount * 2);
  }

  /// Decreases a row by removing stitches from both edges
  static List<bool> _decreaseRow(List<bool> row, int decreaseCount) {
    // This method is now deprecated in favor of _applyRowModifications
    // Kept for backward compatibility
    return _applyRowModifications(row, 0, decreaseCount, row.length);
  }

  /// Get carriage position string for display
  static String getCarriagePositionText(KnittingZoneConfig config) {
    if (config.autoControlCarriagePosition.value) {
      return 'zoneConfiguration_carriagePosition_automatic'.tr;
    } else {
      return config.carriagePosition.value == "left"
          ? 'zoneConfiguration_carriagePosition_startLeft'.tr
          : 'zoneConfiguration_carriagePosition_startRight'.tr;
    }
  }

  /// Get asymmetrical adjustment string for display
  static String getAsymmetricalAdjustmentText(KnittingZoneConfig config) {
    if (config.autoAdjustAsymmetricalRows.value) {
      return 'zoneConfiguration_asymmetricalAdjustment_automatic'.tr;
    } else {
      return config.asymmetricalDirection.value == "inc"
          ? 'zoneConfiguration_asymmetricalAdjustment_addStitch'.tr
          : 'zoneConfiguration_asymmetricalAdjustment_removeStitch'.tr;
    }
  }

  /// Get increase/decrease control string for display
  static String getIncreaseDecreaseText(KnittingZoneConfig config) {
    if (config.autoControlIncreaseDecrease.value) {
      return 'zoneConfiguration_increaseDecrease_automatic'.tr;
    } else {
      final incText =
          config.increaseBy.value > 0 && config.increaseEvery.value > 0
              ? 'zoneConfiguration_increaseDecrease_incEvery'.trParams({
                  'by': config.increaseBy.value.toString(),
                  'every': config.increaseEvery.value.toString()
                })
              : 'zoneConfiguration_increaseDecrease_noIncreases'.tr;

      final decText =
          config.decreaseBy.value > 0 && config.decreaseEvery.value > 0
              ? 'zoneConfiguration_increaseDecrease_decEvery'.trParams({
                  'by': config.decreaseBy.value.toString(),
                  'every': config.decreaseEvery.value.toString()
                })
              : 'zoneConfiguration_increaseDecrease_noDecreases'.tr;

      return 'zoneConfiguration_increaseDecrease_incDecFormat'
          .trParams({'incText': incText, 'decText': decText});
    }
  }
}
