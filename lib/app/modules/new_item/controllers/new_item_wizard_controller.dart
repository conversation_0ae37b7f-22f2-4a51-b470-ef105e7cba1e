import 'package:get/get.dart';
import 'package:xoxknit/app/data/models/new_item_model.dart';
import 'package:xoxknit/app/data/models/wizard_state_model.dart';
import 'package:xoxknit/app/data/models/shape_test_state_model.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/knitting_zone_models.dart';
import 'package:xoxknit/app/services/wizard_state_service.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/shape_editor_controller.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/knitting_instructions_manager.dart';
import 'package:xoxknit/app/modules/shape_test/models/shape_data.dart';
import 'package:flutter/foundation.dart';
import 'dart:async';

class NewItemWizardController extends GetxController {
  // Feature flag to enable/disable zones editor step
  // To restore the zones editor step, set this to true and the wizard will automatically
  // include step 3 (Zones Editor) between the Shape Editor and Summary steps.
  // When disabled, navigation goes directly from Shape Editor (step 2) to Summary (step 4).
  static const bool enableZonesEditor =
      false; // Set to true to restore zones editor

  final currentStep = 0.obs;
  final newItem = NewItemModel().obs;
  final wizardStateService = WizardStateService.to;
  final currentStateId = RxnString();
  final isConfigInfoOpen = true.obs;

  // Add an observable to store the knitting instructions
  final Rx<List<List<bool>>> knittingInstructions = Rx<List<List<bool>>>([]);

  // Variables for interactive knitting instructions
  final RxInt currentKnittingRow =
      (-1).obs; // Use -1 to indicate uninitialized state
  final RxBool showRowNumbers = true.obs;
  final RxBool startFromBottom = false.obs;

  // Add tracking for current knitting zone
  final RxInt currentKnittingZoneIndex = 0.obs;
  final RxList<bool> completedKnittingZones = <bool>[].obs;

  // Statistics for pattern
  final Rx<Map<String, dynamic>> patternStatistics =
      Rx<Map<String, dynamic>>({});

  // Add flag to track when shapes have been modified since last knitting session
  final RxBool shapesModifiedSinceLastKnitting = false.obs;

  // Total number of steps in the wizard
  // Original steps: 0=Details, 1=Gauge, 2=Shape Editor, 3=Zones Editor, 4=Summary, 5=Knitting Zone Config, 6=Interactive Knitting
  // With zones editor disabled: 0=Details, 1=Gauge, 2=Shape Editor, 3=Summary, 4=Knitting Zone Config, 5=Interactive Knitting
  static int get totalSteps => enableZonesEditor ? 6 : 5;

  static NewItemWizardController get to => Get.find();

  // Loading state indicator
  final RxBool isLoading = false.obs;

  /// Maps logical step numbers to actual step numbers based on feature flags
  int _mapLogicalToActualStep(int logicalStep) {
    if (!enableZonesEditor && logicalStep >= 3) {
      // If zones editor is disabled and we're at step 3 or higher, add 1 to skip the zones editor
      return logicalStep + 1;
    }
    return logicalStep;
  }

  /// Maps actual step numbers to logical step numbers based on feature flags
  int _mapActualToLogicalStep(int actualStep) {
    if (!enableZonesEditor && actualStep >= 4) {
      // If zones editor is disabled and we're at actual step 4 or higher, subtract 1
      return actualStep - 1;
    }
    return actualStep;
  }

  /// Check if current step is the shape editor
  bool get isShapeEditorStep => _mapLogicalToActualStep(currentStep.value) == 2;

  /// Check if current step is the zones editor (only relevant when enabled)
  bool get isZonesEditorStep =>
      enableZonesEditor && _mapLogicalToActualStep(currentStep.value) == 3;

  /// Check if current step is the summary step
  bool get isSummaryStep => _mapLogicalToActualStep(currentStep.value) == 4;

  /// Check if current step is the knitting zone config step
  bool get isKnittingZoneConfigStep =>
      _mapLogicalToActualStep(currentStep.value) == 5;

  /// Check if current step is the interactive knitting step
  bool get isInteractiveKnittingStep =>
      _mapLogicalToActualStep(currentStep.value) == 6;

  /// Get the actual step number for the current logical step (useful for debugging)
  int get currentActualStep => _mapLogicalToActualStep(currentStep.value);

  /// Debug method to verify step mapping logic
  void debugStepMapping() {
    debugPrint("=== Step Mapping Debug ===");
    debugPrint("Zones Editor Enabled: $enableZonesEditor");
    debugPrint("Total Steps: $totalSteps");
    debugPrint("Current Logical Step: ${currentStep.value}");
    debugPrint("Current Actual Step: $currentActualStep");
    debugPrint("Step Mappings:");
    for (int i = 0; i <= totalSteps; i++) {
      final actual = _mapLogicalToActualStep(i);
      final back = _mapActualToLogicalStep(actual);
      debugPrint("  Logical $i -> Actual $actual -> Back to Logical $back");
    }
    debugPrint("========================");
  }

  /// Public method to convert actual step number to logical step number
  /// This is used when resuming items from external controllers like MyItemsController
  int convertActualToLogicalStep(int actualStep) {
    return _mapActualToLogicalStep(actualStep);
  }

  @override
  void onInit() {
    super.onInit();
    _initializeState();
  }

  Future<void> _initializeState() async {
    isLoading.value = true;
    try {
      final uri = Get.rootDelegate.currentConfiguration?.uri ?? Uri();
      final stateId = uri.queryParameters['stateId'];
      WizardStateModel? state;

      if (stateId != null) {
        currentStateId.value = stateId;
        state = await wizardStateService.loadWizardState(stateId);
      }

      if (state != null) {
        _loadState(state); // Sets ID, itemData
        if (state.isCompleted) {
          currentStep.value = 0; // Start from beginning for completed items
        } else {
          // Map the saved actual step to logical step for current navigation
          currentStep.value = _mapActualToLogicalStep(
              state.currentStep); // Resume at the saved step

          // Initialize shape modification flag based on current state
          // If we're resuming and have existing knitting progress, don't reset it unless shapes were modified
          // For now, we'll assume shapes haven't been modified since the last save
          shapesModifiedSinceLastKnitting.value = false;

          // --- Refactored Logic for Steps >= 2 ---
          final actualStep = _mapLogicalToActualStep(currentStep.value);
          if (actualStep >= 2) {
            // Ensure ShapeEditorController is available if needed
            if (!Get.isRegistered<ShapeEditorController>()) {
              debugPrint(
                  "[WizardController] ShapeEditorController not registered, putting instance.");
              Get.put(ShapeEditorController(), permanent: true);
            } else {
              debugPrint(
                  "[WizardController] ShapeEditorController already registered, reusing instance.");
            }

            if (actualStep == 2) {
              // Entering shape editor - reload shapes specifically for this step
              debugPrint(
                  "[WizardController] Initializing at Step 2: Reloading shapes for editor.");
              await reloadShapesForShapeEditor();
            } else {
              // actualStep >= 4 (skipping zones editor step 3 if disabled)
              // Entering Summary or Knitting view - generate instructions, which handles loading if needed
              debugPrint(
                  "[WizardController] Initializing at Step >= 4: Generating knitting instructions.");
              await generateKnittingInstructions();
              // Note: generateKnittingInstructions internally checks and reloads shapes if necessary.
            }
            restoreRowKnittingProgress(state);
          }
          // --- End Refactored Logic ---
        }
      } else {
        // If state not found or no stateId, create new one
        await _createNewState();
      }
    } catch (e) {
      debugPrint("Error during wizard initialization: $e");
      // Handle error state appropriately, maybe reset to step 0
      await _createNewState(); // Example: Create a new state on error
    } finally {
      isLoading.value = false;
    }
  }

  void restoreRowKnittingProgress(WizardStateModel state) {
    // --- Restore Knitting Row Progress ---
    if (state.currentKnittingRowIndex != null &&
        knittingInstructions.value.isNotEmpty) {
      // Ensure saved index is valid for loaded instructions
      if (state.currentKnittingRowIndex! >= 0 &&
          state.currentKnittingRowIndex! < knittingInstructions.value.length) {
        currentKnittingRow.value = state.currentKnittingRowIndex!;
        debugPrint(
            "[WizardController] Restored knitting progress to row index: ${state.currentKnittingRowIndex}");
      } else {
        debugPrint(
            "[WizardController] Invalid saved knitting row index (${state.currentKnittingRowIndex}), leaving uninitialized");
      }
    } else {
      debugPrint(
          "[WizardController] No saved knitting progress, leaving uninitialized");
    }

    // Restore zone progress
    if (state.currentKnittingZoneIndex != null) {
      currentKnittingZoneIndex.value = state.currentKnittingZoneIndex!;
      debugPrint(
          "[WizardController] Restored current knitting zone: ${state.currentKnittingZoneIndex}");
    }

    if (state.completedKnittingZones != null &&
        state.completedKnittingZones!.isNotEmpty) {
      completedKnittingZones.value = state.completedKnittingZones!;
      debugPrint(
          "[WizardController] Restored completed zones: ${state.completedKnittingZones}");
    } else {
      // Initialize empty completed zones list if none was saved
      if (Get.isRegistered<ShapeEditorController>()) {
        final shapeController = Get.find<ShapeEditorController>();
        final zoneCount = shapeController
            .knittingInstructionsManager.knittingZones.value.length;
        if (zoneCount > 0 && completedKnittingZones.isEmpty) {
          completedKnittingZones.value = List.generate(zoneCount, (_) => false);
        }
      }
    }
    // ------------------------------------
  }

  Future<void> _createNewState() async {
    // Create a fresh wizard state with no shapes
    final state = await wizardStateService.createWizardState();
    _loadState(state);
    currentStep.value = 0; // Ensure new state starts at step 0

    // Initialize shape modification flag for new state
    shapesModifiedSinceLastKnitting.value = false;

    // Clear any existing shapes if ShapeEditorController is registered
    if (Get.isRegistered<ShapeEditorController>()) {
      debugPrint("New item created, clearing shape editor state");
      final shapeController = Get.find<ShapeEditorController>();
      shapeController.clearAllShapes();
    }
  }

  void _loadState(WizardStateModel state) {
    currentStateId.value = state.id;
    newItem.value = state.itemData;

    // Load zone progress if available
    if (state.currentKnittingZoneIndex != null) {
      currentKnittingZoneIndex.value = state.currentKnittingZoneIndex!;
    }

    if (state.completedKnittingZones != null &&
        state.completedKnittingZones!.isNotEmpty) {
      completedKnittingZones.value = state.completedKnittingZones!;
    }

    // currentStep is handled in _initializeState
  }

  /// Saves the entire wizard progress: step, item data, and shape state.
  /// Pass [stepBeingSaved] to indicate the step whose *content* is being saved,
  /// which might differ from the current step during navigation.
  Future<void> saveWizardProgress({int? stepBeingSaved}) async {
    if (currentStateId.value == null) {
      debugPrint(
          "[WizardController] Cannot save progress: No current state ID.");
      return;
    }

    // Load the most recent state from the service first
    final state =
        await wizardStateService.loadWizardState(currentStateId.value!);
    if (state == null) {
      debugPrint(
          "[WizardController] Cannot save progress: Failed to load state ${currentStateId.value}.");
      return;
    }

    // Determine which step's content we need to potentially capture (usually the step we are leaving)
    int logicalStepContentToCapture = stepBeingSaved ?? currentStep.value;
    int actualStepContentToCapture =
        _mapLogicalToActualStep(logicalStepContentToCapture);
    debugPrint(
        "[WizardController] saveWizardProgress: Capturing content for logical step $logicalStepContentToCapture (actual step $actualStepContentToCapture), saving current logical step as ${currentStep.value} (actual step ${_mapLogicalToActualStep(currentStep.value)})");

    ShapeTestState? finalShapeState =
        state.shapeTestState; // Start with existing shapes

    // If the step whose content we are capturing is step 2 (Shape Editor)
    if (actualStepContentToCapture == 2 &&
        Get.isRegistered<ShapeEditorController>()) {
      final shapeEditorController = Get.find<ShapeEditorController>();
      // Check if the controller actually has shapes loaded or at least keys in order
      if (shapeEditorController.shapes.isNotEmpty ||
          shapeEditorController.shapeKeysOrder.isNotEmpty) {
        final shapes = <ShapeData>[];
        // Use the reliable method to extract potentially grid-coordinate shapes
        // Access shapeKeysOrder via the public getter
        debugPrint(
            "[WizardController] Capturing shapes from ShapeEditorController. Keys in order: ${shapeEditorController.shapeKeysOrder.length}");
        for (final key in shapeEditorController.shapeKeysOrder) {
          // Use getInternalShapeState to get the data as stored (could be grid or screen)
          final internalShapeData =
              shapeEditorController.getInternalShapeState(key);

          if (internalShapeData != null) {
            // Ensure data is saved as grid coordinates.
            ShapeData dataToSave = internalShapeData.deepCopy(); // Use a copy
            if (!shapeEditorController.hasConvertedToGridCoordinates.value) {
              // If controller thinks it's screen coords, convert to grid before saving.
              debugPrint(
                  "[WizardController] Converting shape $key to grid coordinates before saving.");
              dataToSave = dataToSave.toGridCoordinates(// Convert the copy
                  shapeEditorController.gridCoordinateConverter);
            } else {
              // If controller thinks it's grid coords, the internal data is already grid.
              debugPrint(
                  "[WizardController] Shape $key is already in grid coordinates.");
            }
            shapes.add(dataToSave);
          } else {
            debugPrint(
                "[WizardController] Warning: Shape key $key found in order but not in states during save.");
          }
        }

        // Only update finalShapeState if we actually processed shapes from the controller
        finalShapeState = ShapeTestState(shapes: shapes);
        debugPrint(
            "[WizardController] Saved updated shapes from Step 2 content. Count: ${shapes.length}");

        // Ensure the controller knows coordinates are saved as grid coords now if conversion happened *and* we actually saved shapes
        if (!shapeEditorController.hasConvertedToGridCoordinates.value &&
            shapes.isNotEmpty) {
          shapeEditorController.hasConvertedToGridCoordinates.value = true;
          debugPrint(
              "[WizardController] Marked ShapeEditorController as having converted shapes to grid coordinates during save.");
        }
      } else {
        debugPrint(
            "[WizardController] Step 2 content capture: ShapeEditorController is registered but has no shapes/keys. Preserving existing shape state from loaded data.");
        // Keep finalShapeState as loaded from 'state'
      }
    } else if (actualStepContentToCapture != 2) {
      debugPrint(
          "[WizardController] Step content capture is not Step 2. Preserving existing shape state from loaded data.");
      // Keep finalShapeState as loaded from 'state'
    } else {
      // actualStepContentToCapture == 2 but controller not registered
      debugPrint(
          "[WizardController] Step 2 content capture requested, but ShapeEditorController not registered. Preserving existing shape state from loaded data.");
      // Keep finalShapeState as loaded from 'state'
    }

    // Create the updated state object using the *current* actual step value for navigation state
    final actualCurrentStep = _mapLogicalToActualStep(currentStep.value);
    final updatedState = state.copyWith(
      currentStep:
          actualCurrentStep, // Always save the actual step number for resuming navigation
      itemData: newItem.value,
      shapeTestState:
          finalShapeState, // Use the captured or preserved shape state
      lastModified: DateTime.now(),
      currentKnittingRowIndex: currentKnittingRow.value == -1
          ? null
          : currentKnittingRow.value, // Only save if initialized
      currentKnittingZoneIndex: currentKnittingZoneIndex.value,
      completedKnittingZones: completedKnittingZones.toList(),
    );

    // Save using the service
    try {
      await wizardStateService.saveWizardState(updatedState);
      debugPrint(
          "[WizardController] Wizard progress saved for state ${currentStateId.value}. Saved Actual Step Number: ${updatedState.currentStep}, Captured Content From Logical Step: $logicalStepContentToCapture");
    } catch (e) {
      debugPrint("[WizardController] Error saving wizard progress: $e");
      // Consider re-throwing or handling the error
    }
  }

  /// Generates knitting instructions based on the currently loaded shapes in ShapeEditorController.
  /// Assumes shapes are already loaded correctly. Does NOT save state.
  Future<void> generateKnittingInstructions() async {
    if (!Get.isRegistered<ShapeEditorController>()) {
      debugPrint(
          "Cannot generate instructions: ShapeEditorController not registered.");
      // Attempt to register and load? Or just return? Let's try registering.
      Get.put(ShapeEditorController(), permanent: true);
      // We need shapes loaded to generate instructions.
      await Get.find<ShapeEditorController>().reloadShapesFromWizardState();
      // If reload fails or finds no shapes, instructions will be empty.
    }

    final shapeEditorController = Get.find<ShapeEditorController>();

    // Ensure shapes are loaded if controller exists but has no shapes (e.g., direct jump to step 3/4)
    if (shapeEditorController.shapes.isEmpty && currentStateId.value != null) {
      debugPrint(
          "ShapeEditorController has no shapes, reloading before generating instructions...");
      await shapeEditorController.reloadShapesFromWizardState();
    }

    // Reset knitting progress if shapes were modified
    _resetKnittingProgressDueToModifications();

    // Generate the instructions using the manager (now async)
    debugPrint("Generating knitting instructions...");
    final instructions = await shapeEditorController.knittingInstructionsManager
        .generateInstructions();
    debugPrint("Generated ${instructions.length} rows of instructions.");

    // Store pattern statistics for display
    patternStatistics.value = Map<String, dynamic>.from(shapeEditorController
        .knittingInstructionsManager.patternStatistics.value);

    // Update local instructions observable for the UI
    knittingInstructions.value = instructions;

    // Clear the shape modification flag since we've generated new instructions
    _clearShapeModificationFlag();
  }

  /// Explicitly reload shapes when entering the shape editor step (Step 2).
  Future<void> reloadShapesForShapeEditor() async {
    // No need to check currentStep here, called explicitly when needed.
    if (!Get.isRegistered<ShapeEditorController>()) {
      debugPrint(
          "Cannot reload shapes: ShapeEditorController not registered. Registering...");
      Get.put(ShapeEditorController(), permanent: true);
    }

    final shapeEditorController = Get.find<ShapeEditorController>();
    debugPrint("Reloading shapes for Shape Editor (Step 2)...");

    // Clear existing shapes before loading
    if (currentStateId.value != null &&
        wizardStateService.isNewState(currentStateId.value!)) {
      debugPrint("New item detected, clearing previous shapes...");
      shapeEditorController.clearAllShapes();
    }

    final completer = Completer();
    try {
      await shapeEditorController.reloadShapesFromWizardState(onComplete: () {
        debugPrint("Shape reload completed.");
        completer.complete();
      });
      await completer.future; // Wait for the reload and its callback
    } catch (e) {
      debugPrint('Error reloading shapes for editor: $e');
      if (!completer.isCompleted) completer.completeError(e);
      // Consider how to handle reload failure (e.g., show error, stay on previous step?)
    }
  }

  /// Ensures mirror mode is turned off when leaving the shape editor (Step 2)
  void _disableMirrorModeIfActive() {
    if (Get.isRegistered<ShapeEditorController>()) {
      final shapeEditorController = Get.find<ShapeEditorController>();
      if (shapeEditorController.isMirrorModeActive.value) {
        debugPrint("Disabling mirror mode when leaving Shape Editor");
        shapeEditorController.toggleMirrorMode();
      }
    }
  }

  Future<void> nextStep() async {
    if (currentStep.value < totalSteps) {
      final stepBefore = currentStep.value;
      final actualStepBefore = _mapLogicalToActualStep(stepBefore);
      isLoading.value = true;

      try {
        // If leaving the shape editor step, ensure mirror mode is off
        if (actualStepBefore == 2) {
          _disableMirrorModeIfActive();
        }

        // Save current state BEFORE changing step, indicating the step we are leaving
        await saveWizardProgress(stepBeingSaved: stepBefore);

        // Increment step
        final targetStep = stepBefore + 1;
        final actualTargetStep = _mapLogicalToActualStep(targetStep);
        currentStep.value = targetStep; // Update observable

        // Actions AFTER step change
        if (actualTargetStep == 2) {
          // Entering shape editor: Reload shapes
          await reloadShapesForShapeEditor();
        } else if (actualTargetStep == 3 && enableZonesEditor) {
          // Entering Zones Editor (only when enabled, from step 2)
          // Generate instructions for zones editor
          await generateKnittingInstructions();
        } else if (actualTargetStep == 4) {
          // Entering Summary view
          // Generate instructions if zones editor is disabled (coming directly from step 2)
          // or if coming from zones editor when enabled
          if (!enableZonesEditor || actualStepBefore == 3) {
            await generateKnittingInstructions();
          }
        } else if (actualTargetStep >= 5) {
          // Entering knitting-related steps (Knitting Zone Config or Interactive Knitting)
          // Check if shapes were modified and reset progress if needed
          if (shapesModifiedSinceLastKnitting.value) {
            debugPrint(
                "[WizardController] Entering knitting step ${actualTargetStep} with modified shapes - progress will be reset");
            _resetKnittingProgressDueToModifications();
            _clearShapeModificationFlag();
          } else {
            debugPrint(
                "[WizardController] Entering knitting step ${actualTargetStep} - preserving existing progress");
          }
        } // No specific action needed when entering steps 1

        // Save again to persist the new currentStep value
        await saveWizardProgress();
      } catch (e) {
        debugPrint("Error during nextStep: $e");
        // Rollback step change?
        currentStep.value = stepBefore;
        // Optionally save the rolled-back state?
        await saveWizardProgress(
            stepBeingSaved: stepBefore); // Save the state we rolled back TO
      } finally {
        isLoading.value = false;
      }
    }
  }

  Future<void> previousStep() async {
    if (currentStep.value > 0) {
      final stepBefore = currentStep.value;
      final actualStepBefore = _mapLogicalToActualStep(stepBefore);
      isLoading.value = true;

      try {
        // If leaving the shape editor step, ensure mirror mode is off
        if (actualStepBefore == 2) {
          _disableMirrorModeIfActive();
        }

        // Save current state BEFORE changing step, indicating the step we are leaving
        await saveWizardProgress(stepBeingSaved: stepBefore);

        // Decrement step
        final targetStep = stepBefore - 1;
        final actualTargetStep = _mapLogicalToActualStep(targetStep);
        currentStep.value = targetStep; // Update observable

        // --- Reset Knitting Progress if returning to Shape Editor ---
        if (actualTargetStep == 2 &&
            (actualStepBefore == 3 ||
                actualStepBefore == 4 ||
                actualStepBefore == 5 ||
                actualStepBefore == 6)) {
          // Reset to uninitialized state when returning to Shape Editor
          currentKnittingRow.value = -1;
          debugPrint(
              "[WizardController] Reset knitting progress to uninitialized state due to returning to Shape Editor.");
        }
        // ---------------------------------------------------------

        // Actions AFTER step change
        if (actualTargetStep == 2) {
          // Entering shape editor: Reload shapes
          await reloadShapesForShapeEditor();
        } else if (actualTargetStep == 3 &&
            enableZonesEditor &&
            actualStepBefore > 3) {
          // Returning to Zones Editor from Summary or later (only when zones editor is enabled)
          // No need to regenerate instructions
        }
        // No specific action needed when entering step 0 or 1 from above.
        // No instruction generation needed when going back to steps 3 or 4 from later steps.

        // Save again to persist the new currentStep value
        await saveWizardProgress();
      } catch (e) {
        debugPrint("Error during previousStep: $e");
        // Rollback step change?
        currentStep.value = stepBefore;
        // Optionally save the rolled-back state?
        await saveWizardProgress(
            stepBeingSaved: stepBefore); // Save the state we rolled back TO
      } finally {
        isLoading.value = false;
      }
    }
  }

  Future<void> goToStep(int step) async {
    if (step >= 0 && step <= totalSteps && step != currentStep.value) {
      final stepBefore = currentStep.value;
      final actualStepBefore = _mapLogicalToActualStep(stepBefore);
      final actualTargetStep = _mapLogicalToActualStep(step);
      isLoading.value = true;

      try {
        // If leaving the shape editor step, ensure mirror mode is off
        if (actualStepBefore == 2) {
          _disableMirrorModeIfActive();
        }

        // Save current state BEFORE changing step, indicating the step we are leaving
        await saveWizardProgress(stepBeingSaved: stepBefore);

        // Change step
        currentStep.value = step; // Update observable

        // --- Reset Knitting Progress if navigating to Shape Editor from later steps ---
        if (actualTargetStep == 2 &&
            (actualStepBefore == 3 ||
                actualStepBefore == 4 ||
                actualStepBefore == 5 ||
                actualStepBefore == 6)) {
          // Reset to uninitialized state when navigating to Shape Editor
          currentKnittingRow.value = -1;
          debugPrint(
              "[WizardController] Reset knitting progress to uninitialized state due to navigating to Shape Editor.");
        }
        // -------------------------------------------------------------------------

        // Actions AFTER step change
        if (actualTargetStep == 2) {
          // Entering shape editor: Reload shapes
          await reloadShapesForShapeEditor();
        } else if (actualTargetStep == 3 && enableZonesEditor) {
          // Entering Zones Editor (only when enabled)
          // Generate instructions if coming from earlier steps
          if (actualStepBefore < 3) {
            // Ensure ShapeEditorController is ready if jumping directly
            if (!Get.isRegistered<ShapeEditorController>()) {
              Get.put(ShapeEditorController(), permanent: true);
            }
            // Reload shapes if needed
            await Get.find<ShapeEditorController>()
                .reloadShapesFromWizardState();
            await generateKnittingInstructions();
          }
        } else if (actualTargetStep >= 4) {
          // Entering Summary (step 4) or later views
          // Generate instructions if coming from earlier steps (except step 3 when zones editor is enabled)
          if (actualStepBefore < 3 ||
              (!enableZonesEditor && actualStepBefore < 4) ||
              (actualStepBefore < 4 && actualTargetStep > 4)) {
            // Ensure ShapeEditorController is ready if jumping directly
            if (!Get.isRegistered<ShapeEditorController>()) {
              Get.put(ShapeEditorController(), permanent: true);
            }
            // Reload shapes if needed
            await Get.find<ShapeEditorController>()
                .reloadShapesFromWizardState();
            await generateKnittingInstructions();
          } else if (actualTargetStep >= 5) {
            // Check if shapes were modified when navigating to knitting steps
            if (shapesModifiedSinceLastKnitting.value) {
              debugPrint(
                  "[WizardController] Navigating to knitting step ${actualTargetStep} with modified shapes - progress will be reset");
              _resetKnittingProgressDueToModifications();
              _clearShapeModificationFlag();
            } else {
              debugPrint(
                  "[WizardController] Navigating to knitting step ${actualTargetStep} - preserving existing progress");
            }
          }
        }
        // No specific action needed when entering step 0 or 1.

        // Save again to persist the new currentStep value
        await saveWizardProgress();
      } catch (e) {
        debugPrint("Error during goToStep: $e");
        // Rollback step change?
        currentStep.value = stepBefore;
        // Optionally save the rolled-back state?
        await saveWizardProgress(
            stepBeingSaved: stepBefore); // Save the state we rolled back TO
      } finally {
        isLoading.value = false;
      }
    }
  }

  Future<void> updateItemDetails(NewItemModel item) async {
    // Preserve existing gauge values when updating item details
    final existingGauge = newItem.value;
    item.stitchesPerCm = existingGauge.stitchesPerCm;
    item.rowsPerCm = existingGauge.rowsPerCm;
    item.stitchesPer10Cm = existingGauge.stitchesPer10Cm;
    item.rowsPer10Cm = existingGauge.rowsPer10Cm;
    item.weightPer100CmSquared = existingGauge.weightPer100CmSquared;

    newItem.value = item;
    // No explicit save here, nextStep will handle it
    await nextStep();
  }

  /// Saves draft data without validation or navigation
  /// This is used for auto-saving optional field data as users type
  Future<void> saveDraftData(NewItemModel draftItem) async {
    try {
      // Preserve existing gauge values when saving draft
      final existingGauge = newItem.value;
      draftItem.stitchesPerCm = existingGauge.stitchesPerCm;
      draftItem.rowsPerCm = existingGauge.rowsPerCm;
      draftItem.stitchesPer10Cm = existingGauge.stitchesPer10Cm;
      draftItem.rowsPer10Cm = existingGauge.rowsPer10Cm;
      draftItem.weightPer100CmSquared = existingGauge.weightPer100CmSquared;
      draftItem.swatchInfo = existingGauge.swatchInfo;

      // Update the model
      newItem.value = draftItem;

      // Save to wizard state without changing steps
      await saveWizardProgress();

      debugPrint("[WizardController] Draft data saved successfully");
    } catch (e) {
      debugPrint("[WizardController] Error saving draft data: $e");
      // Don't rethrow to avoid disrupting user experience
    }
  }

  Future<void> updateGauge({
    required double stitchesPerCm,
    required double rowsPerCm,
    required double stitchesPer10Cm,
    required double rowsPer10Cm,
    required double weightPer100CmSquared,
  }) async {
    newItem.update((val) {
      if (val != null) {
        val.stitchesPerCm = stitchesPerCm;
        val.rowsPerCm = rowsPerCm;
        val.stitchesPer10Cm = stitchesPer10Cm;
        val.rowsPer10Cm = rowsPer10Cm;
        val.weightPer100CmSquared = weightPer100CmSquared;

        // Update grid properties in ShapeEditorController if it exists
        if (Get.isRegistered<ShapeEditorController>()) {
          final shapeController = Get.find<ShapeEditorController>();
          shapeController.updateGridProperties(
            newStitchesPerCm: stitchesPerCm,
            newRowsPerCm: rowsPerCm,
          );
        }
      }
    });
    // No explicit save here, nextStep will handle it
    await nextStep();
  }

  // Toggle settings for knitting instructions
  void toggleShowRowNumbers() {
    showRowNumbers.value = !showRowNumbers.value;
  }

  Future<void> completeWizard() async {
    if (currentStateId.value != null) {
      await wizardStateService.completeWizardState(currentStateId.value!);
    }
  }

  /// Saves the configuration of all knitting zones
  /// Note: This method works regardless of the enableZonesEditor flag.
  /// Zones are still generated automatically, even when the zones editor step is skipped.
  Future<void> saveKnittingZoneConfigurations() async {
    if (Get.isRegistered<ShapeEditorController>()) {
      final shapeController = Get.find<ShapeEditorController>();
      final zones =
          shapeController.knittingInstructionsManager.knittingZones.value;

      // Simply using generateKnittingInstructions doesn't save the configs
      // So we need to explicitly update the knittingZones value
      shapeController.knittingInstructionsManager.knittingZones.value = zones;

      // Now save the wizard progress to persist the zone configurations
      await saveWizardProgress();

      debugPrint("Saved configurations for ${zones.length} knitting zones");
    } else {
      debugPrint(
          "Cannot save knitting zone configurations: ShapeEditorController not registered");
    }
  }

  /// Apply the configuration of one zone to all similar zones
  /// Note: This method works regardless of the enableZonesEditor flag.
  /// Zones are still generated automatically, even when the zones editor step is skipped.
  void applyZoneConfigToAll(KnittingZone sourceZone) {
    if (Get.isRegistered<ShapeEditorController>()) {
      final shapeController = Get.find<ShapeEditorController>();
      final zones =
          shapeController.knittingInstructionsManager.knittingZones.value;

      // Determine if this is an empty zone
      final isEmptyZone = sourceZone.config.value.isEmpty.value;

      // Create a copy of the source zone's configuration
      final configToApply = sourceZone.config.value;

      // Apply to all zones of the same type
      for (int i = 0; i < zones.length; i++) {
        final zone = zones[i];

        // Only apply to zones of the same type (empty or regular)
        if (zone.config.value.isEmpty.value == isEmptyZone &&
            zone != sourceZone) {
          zone.config.value = configToApply.copyWith();
        }
      }

      // Update the knittingZones value
      shapeController.knittingInstructionsManager.knittingZones.refresh();

      debugPrint(
          "Applied configuration to all ${isEmptyZone ? 'empty' : 'regular'} zones");
    }
  }

  /// Update zone progress and save
  Future<void> updateKnittingZoneProgress(
      int zoneIndex, List<bool> completedZones) async {
    currentKnittingZoneIndex.value = zoneIndex;
    completedKnittingZones.value = completedZones;
    await saveWizardProgress();
  }

  /// Mark a specific zone as completed
  Future<void> markZoneCompleted(int zoneIndex) async {
    if (zoneIndex < completedKnittingZones.length) {
      final newList = [...completedKnittingZones];
      newList[zoneIndex] = true;
      completedKnittingZones.value = newList;
      await saveWizardProgress();
    }
  }

  /// Move to next zone
  Future<void> moveToNextZone() async {
    // Mark current zone completed
    if (currentKnittingZoneIndex.value < completedKnittingZones.length) {
      final newList = [...completedKnittingZones];
      newList[currentKnittingZoneIndex.value] = true;
      completedKnittingZones.value = newList;
    }

    // Move to next zone
    currentKnittingZoneIndex.value++;

    // Reset row to uninitialized state so it gets properly initialized for the new zone
    currentKnittingRow.value = -1;

    await saveWizardProgress();
  }

  void closeConfigInfo() {
    isConfigInfoOpen.value = false;
  }

  /// Mark shapes as modified - this will reset knitting progress
  /// This method is called from ShapeEditorController.saveShapeState()
  /// but only when actual user modifications occur (not during loading operations)
  void markShapesAsModified() {
    shapesModifiedSinceLastKnitting.value = true;
    debugPrint(
        "[WizardController] Shapes marked as modified - knitting progress will be reset");
  }

  /// Reset knitting progress due to shape modifications
  void _resetKnittingProgressDueToModifications() {
    if (shapesModifiedSinceLastKnitting.value) {
      currentKnittingRow.value = -1;
      currentKnittingZoneIndex.value = 0;

      // Reset zone completion tracking
      if (completedKnittingZones.isNotEmpty) {
        completedKnittingZones.value =
            List.generate(completedKnittingZones.length, (_) => false);
      }

      debugPrint(
          "[WizardController] Knitting progress reset due to shape modifications");
    }
  }

  /// Clear the shape modification flag after generating new instructions
  void _clearShapeModificationFlag() {
    shapesModifiedSinceLastKnitting.value = false;
    debugPrint(
        "[WizardController] Shape modification flag cleared after generating new instructions");
  }
}
