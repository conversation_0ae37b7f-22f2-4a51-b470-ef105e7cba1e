import 'package:flutter/material.dart';
import 'package:xoxknit/app/modules/new_item/utils/knitting_utils.dart';

/// Widget that displays the current stitch pattern
class StitchPatternDisplay extends StatefulWidget {
  final List<bool> row;
  final int rowsToKnit;
  // Add currentProgress parameter to show which row in the repeat we're on
  final int currentProgress;
  // Add machine needle count parameter
  final int needleCount;

  const StitchPatternDisplay({
    super.key,
    required this.row,
    required this.rowsToKnit,
    this.currentProgress = 0,
    this.needleCount = 100,
  });

  @override
  State<StitchPatternDisplay> createState() => _StitchPatternDisplayState();
}

class _StitchPatternDisplayState extends State<StitchPatternDisplay> {
  bool _showNeedleNumbers = false;

  @override
  Widget build(BuildContext context) {
    // Find ranges of stitches for better instruction display
    final stitchRanges = KnittingUtils.findStitchRanges(widget.row);
    final rangeText = KnittingUtils.formatRangesText(
        stitchRanges, widget.needleCount,
        useLRNotation: true);

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 4),
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.purple.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.purple.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with stitch info and repeat indicator in one row - more compact
          Row(
            children: [
              Icon(Icons.construction, size: 14, color: Colors.purple[700]),
              const SizedBox(width: 4),
              const Text(
                'Current Pattern',
                style: TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                  color: Colors.purple,
                ),
              ),
              const Spacer(),
              // Add toggle for needle numbers
              InkWell(
                onTap: () {
                  setState(() {
                    _showNeedleNumbers = !_showNeedleNumbers;
                  });
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: _showNeedleNumbers
                        ? Colors.purple.withOpacity(0.2)
                        : Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.pin_drop,
                        size: 10,
                        color: _showNeedleNumbers
                            ? Colors.purple
                            : Colors.grey[600],
                      ),
                      const SizedBox(width: 2),
                      Text(
                        'Needles',
                        style: TextStyle(
                          fontSize: 10,
                          color: _showNeedleNumbers
                              ? Colors.purple
                              : Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 4),
              if (widget.rowsToKnit > 1)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 4,
                    vertical: 1,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.amber.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.amber.withOpacity(0.5),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.repeat,
                        size: 10,
                        color: Colors.amber,
                      ),
                      const SizedBox(width: 2),
                      Text(
                        'Repeat ${widget.rowsToKnit}×',
                        style: const TextStyle(
                          fontSize: 11,
                          color: Colors.amber,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),

          // Add step indication for repeats - more compact
          if (widget.rowsToKnit > 1) ...[
            const SizedBox(height: 6),
            Row(
              children: [
                Text(
                  'Progress: ${widget.currentProgress + 1}/${widget.rowsToKnit}',
                  style: const TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: LinearProgressIndicator(
                    value: (widget.currentProgress + 1) / widget.rowsToKnit,
                    backgroundColor: Colors.grey[200],
                    valueColor:
                        AlwaysStoppedAnimation<Color>(Colors.purple[300]!),
                    minHeight: 4,
                  ),
                ),
              ],
            ),
          ],

          const SizedBox(height: 4),

          // More compact instructions section
          ExpansionTile(
            title: const Text(
              'Instructions',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 13,
              ),
            ),
            tilePadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
            childrenPadding: const EdgeInsets.only(bottom: 4),
            initiallyExpanded: false, // Start collapsed to save space
            textColor: Colors.blue[700],
            iconColor: Colors.blue[700],
            collapsedTextColor: Colors.blue[700],
            collapsedIconColor: Colors.blue[700],
            children: [
              Text(
                rangeText,
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),

          // The actual stitch pattern in a more compact view
          _buildStitchVisualization(widget.row, widget.needleCount),
        ],
      ),
    );
  }

  Widget _buildStitchVisualization(List<bool> row, int needleCount) {
    // Get stitch ranges to determine the first and last stitch positions
    final ranges = KnittingUtils.findStitchRanges(row);

    // If no stitches, show a placeholder
    if (ranges.isEmpty) {
      return const Center(
        child: Text(
          'No stitches in this row',
          style: TextStyle(
            fontStyle: FontStyle.italic,
            color: Colors.grey,
          ),
        ),
      );
    }

    // Find first and last stitch using the ranges
    final firstStitch = ranges.first.startNeedle;
    final lastStitch = ranges.last.endNeedle;

    // Count total stitches
    int totalStitches = 0;
    for (int i = firstStitch; i <= lastStitch; i++) {
      if (row[i]) totalStitches++;
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Total stitch count
        Container(
          padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 12),
          decoration: BoxDecoration(
            color: Colors.purple.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Text(
            'Total stitches: $totalStitches',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 12),

        // Needle visualization
        SizedBox(
          height: 50,
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
            ),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  // Add legend
                  Container(
                    width: 40,
                    height: 50,
                    color: Colors.grey[100],
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    alignment: Alignment.center,
                    child: const Text(
                      'Needle:',
                      style:
                          TextStyle(fontSize: 10, fontWeight: FontWeight.bold),
                    ),
                  ),

                  // Show needle indicators
                  for (int i = firstStitch; i <= lastStitch; i++)
                    _buildNeedleIndicator(i, row, needleCount),
                ],
              ),
            ),
          ),
        ),

        // Needle range text
        const SizedBox(height: 6),
        Text(
          '(showing needles ${KnittingUtils.formatNeedleNumber(firstStitch, needleCount)} to ${KnittingUtils.formatNeedleNumber(lastStitch, needleCount)})',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );
  }

  /// Build a single needle indicator with number
  Widget _buildNeedleIndicator(
      int needleIndex, List<bool> row, int needleCount) {
    final isStitch = needleIndex < row.length ? row[needleIndex] : false;

    // Format needle number exactly like InstructionCard does
    String needleLabel =
        KnittingUtils.formatNeedleNumber(needleIndex, needleCount);

    return Container(
      width: 28,
      height: 50,
      decoration: BoxDecoration(
        color: isStitch ? Colors.purple[700] : Colors.grey[100],
        border: Border(
          left: BorderSide(color: Colors.grey[300]!),
          right: needleIndex == row.length - 1
              ? BorderSide(color: Colors.grey[300]!)
              : BorderSide.none,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Stitch indicator
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color:
                  isStitch ? Colors.white.withOpacity(0.8) : Colors.transparent,
            ),
            child: isStitch
                ? const Icon(Icons.circle, size: 12, color: Colors.purple)
                : null,
          ),

          // Needle number
          const SizedBox(height: 2),
          Text(
            needleLabel,
            style: TextStyle(
              fontSize: 9,
              fontWeight: FontWeight.bold,
              color: isStitch ? Colors.white : Colors.black54,
            ),
          ),
        ],
      ),
    );
  }
}
