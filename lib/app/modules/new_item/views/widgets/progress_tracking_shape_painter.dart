import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/models/shape_data.dart';
import 'dart:math' as math;

/// Helper class to store shape boundaries for drawing dimensions
class ShapeBounds {
  final Offset topLeft;
  final Offset topRight;
  final Offset bottomLeft;
  final Offset bottomRight;

  ShapeBounds({
    required this.topLeft,
    required this.topRight,
    required this.bottomLeft,
    required this.bottomRight,
  });
}

/// Custom painter that adds progress tracking to shape visualization
class ProgressTrackingShapePainter extends CustomPainter {
  final List<ShapeData> shapes;
  final List<List<bool>> instructions;
  final int currentRow;
  final int totalRows;
  final double stitchesPerCm;
  final double rowsPerCm;
  final double progressPercentage;
  final BuildContext? context;
  final int topStitches;
  final int bottomStitches;
  final String topWidth;
  final String bottomWidth;
  final String height;
  final int rowsCount;
  final int leftNeedles;
  final int rightNeedles;
  final bool showProgress;
  final bool highlightActiveZone;
  final Map<String, dynamic>? zonePositionInfo;

  ProgressTrackingShapePainter({
    this.shapes = const [],
    this.instructions = const [],
    required this.currentRow,
    required this.totalRows,
    required this.progressPercentage,
    required this.topStitches,
    required this.bottomStitches,
    required this.topWidth,
    required this.bottomWidth,
    required this.height,
    required this.rowsCount,
    required this.leftNeedles,
    required this.rightNeedles,
    this.showProgress = true,
    this.context,
    required this.stitchesPerCm,
    required this.rowsPerCm,
    this.highlightActiveZone = false,
    this.zonePositionInfo,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // First draw the shape outline
    _drawShapeOutline(canvas, size);

    // Now add the progress fill if showProgress is true
    if (showProgress) {
      if (instructions.isNotEmpty) {
        _drawProgressFill(canvas, size);
      } else if (shapes.isNotEmpty) {
        _drawDefaultProgressFill(canvas, size);
      } else {
        _drawDefaultProgressFill(canvas, size);
      }
    }

    // If we have position info, show the zone's position in the full pattern
    if (zonePositionInfo != null && highlightActiveZone) {
      _drawZonePositionContext(canvas, size);
    }

    // Draw the dimension indicators
    _drawDimensionIndicators(canvas, size);

    // Only draw progress-related elements if showProgress is true
    if (showProgress) {
      // Draw horizontal indicator line first
      _drawHorizontalRowLine(canvas, size);

      // Draw the current row indicator text (but not the line)
      _drawCurrentRowIndicator(canvas, size);

      // Draw the progress percentage in the top-right corner
      _drawProgressPercentageInTopRight(canvas, size);
    }
  }

  // Get theme-aware colors
  Color getPrimaryColor() {
    return context != null
        ? Theme.of(context!).colorScheme.primary
        : Get.theme.colorScheme.primary;
  }

  Color getProgressColor() {
    return context != null
        ? Theme.of(context!).colorScheme.tertiary.withOpacity(0.3)
        : Colors.green.withOpacity(0.3);
  }

  Color getProgressPillColor() {
    return context != null
        ? Theme.of(context!).colorScheme.tertiary
        : Colors.green;
  }

  Color getLineColor() {
    return context != null
        ? Theme.of(context!).colorScheme.onSurface.withOpacity(0.5)
        : Colors.grey;
  }

  Color getBackgroundColor() {
    return context != null
        ? Theme.of(context!).colorScheme.surface
        : Colors.white;
  }

  Color getTextColor() {
    return context != null
        ? Theme.of(context!).colorScheme.onSurface.withOpacity(0.7)
        : Colors.grey[600]!;
  }

  // Helper method to calculate scaling info based on physical aspect ratio
  ({
    Size scaledSize,
    double scaleX,
    double scaleY,
    double offsetX,
    double offsetY,
    int minStitch,
    int maxStitch,
    int shapeStitchWidth
  }) _calculateScalingInfo(Size availableSize) {
    // Find the actual min/max stitch indices used across all rows
    int minStitch = instructions.isNotEmpty ? instructions.first.length : 0;
    int maxStitch = -1;
    int overallMaxWidth = 0;

    for (final row in instructions) {
      overallMaxWidth = math.max(overallMaxWidth, row.length);
      for (int j = 0; j < row.length; j++) {
        if (row[j]) {
          minStitch = math.min(minStitch, j);
          maxStitch = math.max(maxStitch, j);
        }
      }
    }

    // Handle cases with no stitches or empty instructions
    if (maxStitch < minStitch || instructions.isEmpty) {
      minStitch = 0;
      maxStitch = math.max(0, overallMaxWidth - 1);
    }

    final int shapeStitchWidth = math.max(1, maxStitch - minStitch + 1);
    final int patternHeight = math.max(1, instructions.length);
    final int patternWidth = shapeStitchWidth;

    // Calculate the PHYSICAL aspect ratio
    final double safeStitchesPerCm = math.max(0.01, stitchesPerCm);
    final double safeRowsPerCm = math.max(0.01, rowsPerCm);
    final double physicalWidth = patternWidth / safeStitchesPerCm;
    final double physicalHeight = patternHeight / safeRowsPerCm;
    final double physicalAspectRatio = (physicalHeight > 1e-6)
        ? physicalWidth / physicalHeight
        : 1.0; // Avoid division by zero

    // Calculate a scaled size that fits within the availableSize while maintaining the PHYSICAL aspect ratio
    double scaledWidth;
    double scaledHeight;
    if (availableSize.width / availableSize.height > physicalAspectRatio) {
      scaledHeight = availableSize.height;
      scaledWidth = scaledHeight * physicalAspectRatio;
    } else {
      scaledWidth = availableSize.width;
      scaledHeight = (physicalAspectRatio > 1e-6)
          ? scaledWidth / physicalAspectRatio
          : availableSize.height; // Avoid division by zero
    }

    // Ensure scaledSize doesn't exceed availableSize
    final double finalScaledWidth =
        math.min(scaledWidth, availableSize.width).toDouble();
    final double finalScaledHeight =
        math.min(scaledHeight, availableSize.height).toDouble();
    final Size finalScaledSize = Size(finalScaledWidth, finalScaledHeight);

    // Calculate scale factors
    final double scaleX =
        (patternWidth > 0) ? finalScaledWidth / patternWidth : 0.0;
    final double scaleY =
        (patternHeight > 0) ? finalScaledHeight / patternHeight : 0.0;

    // Center the shape
    const double padding = 40.0;
    final double offsetX =
        padding + (availableSize.width - finalScaledWidth) / 2.0;
    final double offsetY =
        padding + (availableSize.height - finalScaledHeight) / 2.0;

    return (
      scaledSize: finalScaledSize,
      scaleX: scaleX,
      scaleY: scaleY,
      offsetX: offsetX,
      offsetY: offsetY,
      minStitch: minStitch,
      maxStitch: maxStitch,
      shapeStitchWidth: shapeStitchWidth,
    );
  }

  void _drawShapeOutline(Canvas canvas, Size size) {
    const double padding = 40.0; // Padding around the shape

    // Create a border paint
    final borderPaint = Paint()
      ..color = getPrimaryColor()
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    if (instructions.isNotEmpty) {
      // Calculate scaling info using the helper method
      final availableWidth = math.max(0, size.width - 2 * padding);
      final availableHeight = math.max(0, size.height - 2 * padding);
      final scalingInfo = _calculateScalingInfo(
          Size(availableWidth.toDouble(), availableHeight.toDouble()));
      final offsetX = scalingInfo.offsetX;
      final offsetY = scalingInfo.offsetY;
      final scaleX = scalingInfo.scaleX;
      final scaleY = scalingInfo.scaleY;
      final minStitch = scalingInfo.minStitch;

      // Track all edges that need to be drawn to form the outline
      final Set<String> edges = {}; // Using strings to represent edges

      // For each row, identify all boundary edges
      for (int i = 0; i < instructions.length; i++) {
        final row = instructions[i];

        for (int j = 0; j < row.length; j++) {
          if (row[j]) {
            // Check all 4 sides of this stitch to see if they form a boundary

            // Top edge is a boundary if:
            // - This is the first row, or
            // - The stitch above doesn't exist
            bool isTopBoundary = i == 0 ||
                j >= instructions[i - 1].length ||
                !instructions[i - 1][j];

            // Right edge is a boundary if:
            // - This is the last stitch in the row, or
            // - The stitch to the right doesn't exist
            bool isRightBoundary = j == row.length - 1 || !row[j + 1];

            // Bottom edge is a boundary if:
            // - This is the last row, or
            // - The stitch below doesn't exist
            bool isBottomBoundary = i == instructions.length - 1 ||
                j >= instructions[i + 1].length ||
                !instructions[i + 1][j];

            // Left edge is a boundary if:
            // - This is the first stitch in the row, or
            // - The stitch to the left doesn't exist
            bool isLeftBoundary = j == 0 || !row[j - 1];

            // Calculate corner coordinates for this stitch
            final topLeftX = offsetX + (j - minStitch) * scaleX;
            final topLeftY = offsetY + i * scaleY;
            final bottomRightX = topLeftX + scaleX;
            final bottomRightY = topLeftY + scaleY;

            // Add boundary edges to the set
            if (isTopBoundary) {
              edges.add('${topLeftX}_${topLeftY}_${bottomRightX}_${topLeftY}');
            }
            if (isRightBoundary) {
              edges.add(
                  '${bottomRightX}_${topLeftY}_${bottomRightX}_${bottomRightY}');
            }
            if (isBottomBoundary) {
              edges.add(
                  '${topLeftX}_${bottomRightY}_${bottomRightX}_${bottomRightY}');
            }
            if (isLeftBoundary) {
              edges.add('${topLeftX}_${topLeftY}_${topLeftX}_${bottomRightY}');
            }
          }
        }
      }

      // Draw all boundary edges
      for (final edge in edges) {
        final parts = edge.split('_');
        final x1 = double.parse(parts[0]);
        final y1 = double.parse(parts[1]);
        final x2 = double.parse(parts[2]);
        final y2 = double.parse(parts[3]);

        canvas.drawLine(Offset(x1, y1), Offset(x2, y2), borderPaint);
      }
    } else {
      // Fallback: Draw a simple default trapezoid if no instructions
      final bounds = _getDefaultShapeBounds(size);
      final path = Path();
      path.moveTo(bounds.topLeft.dx, bounds.topLeft.dy);
      path.lineTo(bounds.topRight.dx, bounds.topRight.dy);
      path.lineTo(bounds.bottomRight.dx, bounds.bottomRight.dy);
      path.lineTo(bounds.bottomLeft.dx, bounds.bottomLeft.dy);
      path.close();
      canvas.drawPath(path, borderPaint);
    }
  }

  void _drawProgressFill(Canvas canvas, Size size) {
    final path = _getProgressPath(size);
    final fillPaint = Paint()
      ..color = getProgressColor()
      ..style = PaintingStyle.fill;

    canvas.drawPath(path, fillPaint);
  }

  void _drawDefaultProgressFill(Canvas canvas, Size size) {
    // Use the trapezoid shape as in the parent class but fill only the bottom portion
    final path = Path();

    // Use a fixed aspect ratio for the default trapezoid (width:height = 2:1)
    final aspectRatio = 2.0;

    // Calculate a scaled size that fits within the provided size while maintaining aspect ratio
    Size scaledSize;
    if (size.width / size.height > aspectRatio) {
      // Canvas is wider than our target ratio - constrain by height
      scaledSize = Size(size.height * aspectRatio, size.height);
    } else {
      // Canvas is taller than our target ratio - constrain by width
      scaledSize = Size(size.width, size.width / aspectRatio);
    }

    // Center the trapezoid in the available space
    final offsetX = (size.width - scaledSize.width) / 2;
    final offsetY = (size.height - scaledSize.height) / 2;

    // Calculate points for a trapezoid
    final topWidth = scaledSize.width * 0.5; // Top width (narrower)
    final bottomWidth = scaledSize.width * 0.9; // Bottom width (wider)

    // Calculate how much of the height to fill based on progress
    final fillHeight = scaledSize.height * progressPercentage;

    // Only draw if we have some progress
    if (fillHeight > 0) {
      final yPosition = offsetY + scaledSize.height - fillHeight;

      // Calculate the width at the current y position using linear interpolation
      final widthAtY = bottomWidth -
          (bottomWidth - topWidth) * (fillHeight / scaledSize.height);

      // Draw a partial trapezoid from the bottom up to the current progress line
      path.moveTo(offsetX + (scaledSize.width - bottomWidth) / 2,
          offsetY + scaledSize.height);
      path.lineTo(offsetX + (scaledSize.width + bottomWidth) / 2,
          offsetY + scaledSize.height);
      path.lineTo(offsetX + (scaledSize.width + widthAtY) / 2, yPosition);
      path.lineTo(offsetX + (scaledSize.width - widthAtY) / 2, yPosition);
      path.close();

      final fillPaint = Paint()
        ..color = getPrimaryColor().withOpacity(0.3)
        ..style = PaintingStyle.fill;

      canvas.drawPath(path, fillPaint);
    }
  }

  Path _getProgressPath(Size size) {
    final path = Path();
    const double padding = 40.0; // Padding around the shape

    if (instructions.isEmpty || progressPercentage <= 0) {
      return path;
    }

    // Calculate scaling info using the helper method
    final availableWidth = math.max(0, size.width - 2 * padding);
    final availableHeight = math.max(0, size.height - 2 * padding);
    final scalingInfo = _calculateScalingInfo(
        Size(availableWidth.toDouble(), availableHeight.toDouble()));
    final offsetX = scalingInfo.offsetX;
    final offsetY = scalingInfo.offsetY;
    final scaleX = scalingInfo.scaleX;
    final scaleY = scalingInfo.scaleY;
    final minStitch = scalingInfo.minStitch;

    // Correctly calculate rows to include based on progress percentage
    final rowsToInclude = (instructions.length * progressPercentage).round();
    if (rowsToInclude <= 0) {
      return path;
    }

    final startingRow = math.max(0, instructions.length - rowsToInclude);
    final subsetInstructions =
        instructions.sublist(startingRow, instructions.length);

    // Instead of just drawing the outline, draw each individual stitch in the progress area
    // This creates a detailed view of the shape for the progress fill

    // This will hold all the points to be connected
    List<Offset> outlinePoints = [];

    // For each row in the progress portion
    for (int i = 0; i < subsetInstructions.length; i++) {
      final rowIndex = startingRow + i;
      final row = instructions[rowIndex];

      // Track segments in the row
      List<int> segmentStarts = [];
      List<int> segmentEnds = [];
      int? currentSegmentStart;

      // Find all segments in this row
      for (int j = 0; j < row.length; j++) {
        if (row[j]) {
          currentSegmentStart ??= j;
        } else if (currentSegmentStart != null) {
          segmentStarts.add(currentSegmentStart);
          segmentEnds.add(j - 1);
          currentSegmentStart = null;
        }
      }

      // Handle segment that extends to the end of the row
      if (currentSegmentStart != null) {
        segmentStarts.add(currentSegmentStart);
        segmentEnds.add(row.length - 1);
      }

      // If this is the first row of progress, add top points of all segments
      if (i == 0) {
        for (int segIdx = 0; segIdx < segmentStarts.length; segIdx++) {
          final startJ = segmentStarts[segIdx];
          final endJ = segmentEnds[segIdx];

          final startX = offsetX + (startJ - minStitch) * scaleX;
          final endX = offsetX + (endJ - minStitch) * scaleX + scaleX;
          final y = offsetY + rowIndex * scaleY;

          // Add the top-left corner of the segment
          outlinePoints.add(Offset(startX, y));
          // Add the top-right corner of the segment
          outlinePoints.add(Offset(endX, y));
        }
      }

      // For all rows, add the left and right edges of each segment
      for (int segIdx = 0; segIdx < segmentStarts.length; segIdx++) {
        final startJ = segmentStarts[segIdx];
        final endJ = segmentEnds[segIdx];

        final startX = offsetX + (startJ - minStitch) * scaleX;
        final endX = offsetX + (endJ - minStitch) * scaleX + scaleX;
        final topY = offsetY + rowIndex * scaleY;
        final bottomY = topY + scaleY;

        // Add left-bottom corner
        outlinePoints.add(Offset(startX, bottomY));
        // Add right-bottom corner
        outlinePoints.add(Offset(endX, bottomY));
      }
    }

    // Sort outline points to create a coherent filled shape
    // Start with sorting by y-coordinate (rows)
    outlinePoints.sort((a, b) {
      final yDiff = a.dy.compareTo(b.dy);
      return yDiff != 0 ? yDiff : a.dx.compareTo(b.dx);
    });

    // If we have points, create a path
    if (outlinePoints.isNotEmpty) {
      // Move to the first point
      path.moveTo(outlinePoints.first.dx, outlinePoints.first.dy);

      // Get only unique points
      final Set<Offset> uniquePoints = {};
      for (var point in outlinePoints) {
        uniquePoints.add(point);
      }

      // Sort unique points by row (y) and then position (x)
      final sortedUniquePoints = uniquePoints.toList()
        ..sort((a, b) {
          final yDiff = a.dy.compareTo(b.dy);
          return yDiff != 0 ? yDiff : a.dx.compareTo(b.dx);
        });

      // Draw the top row, from left to right
      final topY = sortedUniquePoints.first.dy;
      final topPoints = sortedUniquePoints.where((p) => p.dy == topY).toList()
        ..sort((a, b) => a.dx.compareTo(b.dx));

      if (topPoints.isNotEmpty) {
        path.moveTo(topPoints.first.dx, topPoints.first.dy);
        for (int i = 1; i < topPoints.length; i++) {
          path.lineTo(topPoints[i].dx, topPoints[i].dy);
        }
      }

      // Find the rightmost points at each y-level, sorted by y descending
      final Map<double, Offset> rightmostByRow = {};
      for (var point in sortedUniquePoints) {
        if (!rightmostByRow.containsKey(point.dy) ||
            rightmostByRow[point.dy]!.dx < point.dx) {
          rightmostByRow[point.dy] = point;
        }
      }

      // Draw down the right side
      final rightSidePoints = rightmostByRow.values.toList()
        ..sort((a, b) => a.dy.compareTo(b.dy));

      for (var point in rightSidePoints) {
        path.lineTo(point.dx, point.dy);
      }

      // Draw the bottom row, from right to left
      final bottomY = sortedUniquePoints.last.dy;
      final bottomPoints = sortedUniquePoints
          .where((p) => p.dy == bottomY)
          .toList()
        ..sort((a, b) => b.dx.compareTo(a.dx));

      for (var point in bottomPoints) {
        path.lineTo(point.dx, point.dy);
      }

      // Find the leftmost points at each y-level, sorted by y ascending
      final Map<double, Offset> leftmostByRow = {};
      for (var point in sortedUniquePoints) {
        if (!leftmostByRow.containsKey(point.dy) ||
            leftmostByRow[point.dy]!.dx > point.dx) {
          leftmostByRow[point.dy] = point;
        }
      }

      // Draw up the left side
      final leftSidePoints = leftmostByRow.values.toList()
        ..sort((a, b) => b.dy.compareTo(a.dy));

      for (var point in leftSidePoints) {
        path.lineTo(point.dx, point.dy);
      }

      path.close();
      return path;
    }

    // Fallback to simple outline if no points calculated
    bool firstPoint = true;

    // Go down the left side from the top of the progress to the bottom
    for (int i = 0; i < subsetInstructions.length; i++) {
      final rowIndex = startingRow + i;
      final row = instructions[rowIndex];
      int leftmostStitch = -1;
      for (int j = 0; j < row.length; j++) {
        if (row[j]) {
          leftmostStitch = j;
          break;
        }
      }
      if (leftmostStitch >= 0) {
        final y = offsetY + rowIndex * scaleY;
        final x = offsetX + (leftmostStitch - minStitch) * scaleX;
        if (firstPoint) {
          path.moveTo(x, y);
          firstPoint = false;
        } else {
          path.lineTo(x, y);
        }
      }
    }

    // Draw the bottom row from left to right
    final bottomRowIndex = instructions.length - 1;
    final bottomRow = instructions[bottomRowIndex];
    int leftmostStitchBottom = -1;
    int rightmostStitchBottom = -1;
    for (int j = 0; j < bottomRow.length; j++) {
      if (bottomRow[j]) {
        if (leftmostStitchBottom < 0) leftmostStitchBottom = j;
        rightmostStitchBottom = j;
      }
    }
    if (leftmostStitchBottom >= 0) {
      final y = offsetY + bottomRowIndex * scaleY;
      final xLeft = offsetX + (leftmostStitchBottom - minStitch) * scaleX;
      final xRight = offsetX + (rightmostStitchBottom - minStitch) * scaleX;
      if (!firstPoint) {
        path.lineTo(xLeft, y);
        path.lineTo(xRight, y);
      } else {
        path.moveTo(xLeft, y);
        path.lineTo(xRight, y);
        firstPoint = false;
      }
    }

    // Go back up the right side from bottom to top
    for (int i = subsetInstructions.length - 1; i >= 0; i--) {
      final rowIndex = startingRow + i;
      final row = instructions[rowIndex];
      int rightmostStitch = -1;
      for (int j = row.length - 1; j >= 0; j--) {
        if (row[j]) {
          rightmostStitch = j;
          break;
        }
      }
      if (rightmostStitch >= 0) {
        final y = offsetY + rowIndex * scaleY;
        final x = offsetX + (rightmostStitch - minStitch) * scaleX;
        path.lineTo(x, y);
      }
    }

    path.close();
    return path;
  }

  void _drawDimensionIndicators(Canvas canvas, Size size) {
    // Get the shape bounds - works for both actual patterns and default shapes
    final ShapeBounds bounds = _getShapeBounds(size);

    // Get specific row bounds for more accurate dimension indicators
    final topRowBounds = _getTopRowBounds(size);
    final bottomRowBounds = _getBottomRowBounds(size);

    // Draw top width dimension using the actual top row bounds
    _drawWidthDimension(
        canvas,
        Offset(topRowBounds.topLeft.dx, topRowBounds.topLeft.dy - 40),
        Offset(topRowBounds.topRight.dx, topRowBounds.topRight.dy - 40),
        topWidth,
        'knittingProgress_dimensionIndicator_stitches'
            .trParams({'count': topStitches.toString()}));

    // Draw bottom width dimension using the actual bottom row bounds
    _drawWidthDimension(
        canvas,
        Offset(
            bottomRowBounds.bottomLeft.dx, bottomRowBounds.bottomLeft.dy + 15),
        Offset(bottomRowBounds.bottomRight.dx,
            bottomRowBounds.bottomRight.dy + 15),
        bottomWidth,
        'knittingProgress_dimensionIndicator_stitches'
            .trParams({'count': bottomStitches.toString()}));

    // For height dimension, use the exact pattern bounds from row positions
    final exactHeightBounds = _getExactPatternHeightBounds(size);
    _drawHeightDimension(
        canvas,
        exactHeightBounds,
        height,
        'knittingProgress_dimensionIndicator_rows'
            .trParams({'count': rowsCount.toString()}));
  }

  ShapeBounds _getShapeBounds(Size size) {
    if (instructions.isNotEmpty) {
      // Get bounds from instructions
      final instructionBounds = _getBoundsFromInstructions(size);

      // If we have original shapes, also consider their curved bounds
      if (shapes.isNotEmpty) {
        final shapeBounds = _getBoundsFromShapes(size);
        // Return the union of both bounds to ensure curves are included
        return _combineBounds(instructionBounds, shapeBounds);
      }

      return instructionBounds;
    } else if (shapes.isNotEmpty) {
      return _getBoundsFromShapes(size);
    } else {
      return _getDefaultShapeBounds(size);
    }
  }

  ShapeBounds _getBoundsFromInstructions(Size size) {
    const double padding = 40.0; // Padding around the shape

    if (instructions.isEmpty) {
      return _getDefaultShapeBounds(size);
    }

    // Calculate scaling info using the helper method
    final availableWidth = math.max(0, size.width - 2 * padding);
    final availableHeight = math.max(0, size.height - 2 * padding);
    final scalingInfo = _calculateScalingInfo(
        Size(availableWidth.toDouble(), availableHeight.toDouble()));
    final offsetX = scalingInfo.offsetX;
    final offsetY = scalingInfo.offsetY;
    final scaleX = scalingInfo.scaleX;
    final scaleY = scalingInfo.scaleY;
    final scaledHeight = scalingInfo.scaledSize.height;
    final minStitch = scalingInfo.minStitch;
    final maxStitch = scalingInfo.maxStitch;

    // Find leftmost and rightmost stitches in the *first* row for top bounds
    final topRow = instructions.first;
    int topLeftStitch = -1;
    int topRightStitch = -1;
    for (int j = 0; j < topRow.length; j++) {
      if (topRow[j]) {
        if (topLeftStitch < 0) topLeftStitch = j;
        topRightStitch = j;
      }
    }
    // If top row has no stitches, use the overall min/max for bounds
    if (topLeftStitch < 0) topLeftStitch = minStitch;
    if (topRightStitch < 0) topRightStitch = maxStitch;

    // Find leftmost and rightmost stitches in the *last* row for bottom bounds
    final bottomRow = instructions.last;
    int bottomLeftStitch = -1;
    int bottomRightStitch = -1;
    for (int j = 0; j < bottomRow.length; j++) {
      if (bottomRow[j]) {
        if (bottomLeftStitch < 0) bottomLeftStitch = j;
        bottomRightStitch = j;
      }
    }
    // If bottom row has no stitches, use the overall min/max for bounds
    if (bottomLeftStitch < 0) bottomLeftStitch = minStitch;
    if (bottomRightStitch < 0) bottomRightStitch = maxStitch;

    // Calculate the actual bounds coordinates using normalized positions relative to the padded area
    return ShapeBounds(
      topLeft: Offset(offsetX + (topLeftStitch - minStitch) * scaleX, offsetY),
      topRight:
          Offset(offsetX + (topRightStitch - minStitch) * scaleX, offsetY),
      bottomLeft: Offset(offsetX + (bottomLeftStitch - minStitch) * scaleX,
          offsetY + scaledHeight), // Use scaledHeight for bottom Y
      bottomRight: Offset(offsetX + (bottomRightStitch - minStitch) * scaleX,
          offsetY + scaledHeight), // Use scaledHeight for bottom Y
    );
  }

  ShapeBounds _getDefaultShapeBounds(Size size) {
    // Use a fixed aspect ratio for the default trapezoid (width:height = 2:1)
    final aspectRatio = 2.0;

    // Calculate a scaled size that fits within the provided size while maintaining aspect ratio
    Size scaledSize;
    if (size.width / size.height > aspectRatio) {
      // Canvas is wider than our target ratio - constrain by height
      scaledSize = Size(size.height * aspectRatio, size.height);
    } else {
      // Canvas is taller than our target ratio - constrain by width
      scaledSize = Size(size.width, size.width / aspectRatio);
    }

    // Center the trapezoid in the available space
    final offsetX = (size.width - scaledSize.width) / 2;
    final offsetY = (size.height - scaledSize.height) / 2;

    // Calculate points for a trapezoid
    final topWidth = scaledSize.width * 0.5; // Top width (narrower)
    final bottomWidth = scaledSize.width * 0.9; // Bottom width (wider)

    return ShapeBounds(
        topLeft: Offset(offsetX + (scaledSize.width - topWidth) / 2, offsetY),
        topRight: Offset(offsetX + (scaledSize.width + topWidth) / 2, offsetY),
        bottomLeft: Offset(offsetX + (scaledSize.width - bottomWidth) / 2,
            offsetY + scaledSize.height),
        bottomRight: Offset(offsetX + (scaledSize.width + bottomWidth) / 2,
            offsetY + scaledSize.height));
  }

  ShapeBounds _getBoundsFromShapes(Size size) {
    if (shapes.isEmpty) {
      return _getDefaultShapeBounds(size);
    }

    // Find the combined bounds of all shapes including their curves
    Rect? combinedBounds;

    for (final shape in shapes) {
      final shapePath = _createShapePath(shape);
      final pathBounds = shapePath.getBounds();

      if (combinedBounds == null) {
        combinedBounds = pathBounds;
      } else {
        combinedBounds = combinedBounds.expandToInclude(pathBounds);
      }
    }

    if (combinedBounds == null || combinedBounds.isEmpty) {
      return _getDefaultShapeBounds(size);
    }

    // Transform the bounds to fit within the available canvas size
    const double padding = 40.0;
    final availableWidth = math.max(0, size.width - 2 * padding);
    final availableHeight = math.max(0, size.height - 2 * padding);

    // Calculate the physical aspect ratio based on gauge
    final double safeStitchesPerCm = math.max(0.01, stitchesPerCm);
    final double safeRowsPerCm = math.max(0.01, rowsPerCm);
    final double physicalAspectRatio = safeStitchesPerCm / safeRowsPerCm;

    // Calculate scale to fit the shape bounds within available space
    final shapeWidth = combinedBounds.width;
    final shapeHeight = combinedBounds.height;
    final shapeAspectRatio = shapeWidth / shapeHeight;

    // Apply physical aspect ratio correction
    final correctedWidth = shapeWidth;
    final correctedHeight =
        shapeHeight * (shapeAspectRatio / physicalAspectRatio);

    double scale;
    if (correctedWidth / correctedHeight > availableWidth / availableHeight) {
      // Width is the limiting factor
      scale = availableWidth / correctedWidth;
    } else {
      // Height is the limiting factor
      scale = availableHeight / correctedHeight;
    }

    // Calculate offset to center the shape
    final scaledWidth = correctedWidth * scale;
    final scaledHeight = correctedHeight * scale;
    final offsetX = padding + (availableWidth - scaledWidth) / 2;
    final offsetY = padding + (availableHeight - scaledHeight) / 2;

    // Transform the original bounds coordinates
    final transformedLeft =
        offsetX + (combinedBounds.left - combinedBounds.left) * scale;
    final transformedTop =
        offsetY + (combinedBounds.top - combinedBounds.top) * scale;
    final transformedRight =
        offsetX + (combinedBounds.right - combinedBounds.left) * scale;
    final transformedBottom =
        offsetY + (combinedBounds.bottom - combinedBounds.top) * scale;

    // Find the actual extremes for the bounds - we need to account for shape orientation
    return ShapeBounds(
      topLeft: Offset(transformedLeft, transformedTop),
      topRight: Offset(transformedRight, transformedTop),
      bottomLeft: Offset(transformedLeft, transformedBottom),
      bottomRight: Offset(transformedRight, transformedBottom),
    );
  }

  ShapeBounds _combineBounds(ShapeBounds bounds1, ShapeBounds bounds2) {
    // Find the extremes across both bounds
    final leftmost = math.min(
      math.min(bounds1.topLeft.dx, bounds1.bottomLeft.dx),
      math.min(bounds2.topLeft.dx, bounds2.bottomLeft.dx),
    );
    final rightmost = math.max(
      math.max(bounds1.topRight.dx, bounds1.bottomRight.dx),
      math.max(bounds2.topRight.dx, bounds2.bottomRight.dx),
    );
    final topmost = math.min(
      math.min(bounds1.topLeft.dy, bounds1.topRight.dy),
      math.min(bounds2.topLeft.dy, bounds2.topRight.dy),
    );
    final bottommost = math.max(
      math.max(bounds1.bottomLeft.dy, bounds1.bottomRight.dy),
      math.max(bounds2.bottomLeft.dy, bounds2.bottomRight.dy),
    );

    return ShapeBounds(
      topLeft: Offset(leftmost, topmost),
      topRight: Offset(rightmost, topmost),
      bottomLeft: Offset(leftmost, bottommost),
      bottomRight: Offset(rightmost, bottommost),
    );
  }

  /// Create a path for a single shape, properly handling rotation and curves
  Path _createShapePath(ShapeData shape) {
    // Handle grouped shapes by recursively creating paths for each child shape
    if (shape.runtimeType.toString().contains('GroupShapeData')) {
      try {
        final dynamic groupShape = shape;
        if (groupShape.childShapes != null) {
          final childShapes = groupShape.childShapes as List<ShapeData>;

          if (childShapes.isEmpty) return Path();

          // Create a path for each child shape and combine them
          Path combinedPath = Path();
          for (final childShape in childShapes) {
            final childPath = _createShapePath(childShape);
            combinedPath.addPath(childPath, Offset.zero);
          }
          return combinedPath;
        }
      } catch (e) {
        debugPrint("Error processing group shape in _createShapePath: $e");
        // Fallback to using group's own vertices if child access fails
      }
    }

    final path = Path();

    // Skip shapes with no vertices
    if (shape.vertices.isEmpty) return path;

    // Get all vertices, applying rotation if needed
    final vertices = shape.rotation != 0
        ? shape.vertices
            .map((v) => _rotatePoint(v, shape.center, shape.rotation))
            .toList()
        : shape.vertices;

    if (vertices.isEmpty) return path; // Check again after potential rotation

    // Move to the first vertex
    path.moveTo(vertices[0].dx, vertices[0].dy);

    // Draw each edge of the shape
    for (int i = 0; i < vertices.length; i++) {
      final nextIndex = (i + 1) % vertices.length;
      final currentVertex = vertices[i];
      final nextVertex = vertices[nextIndex];

      // Check for cubic curves first (new system)
      if (shape.hasEdgeCubicCurve(i)) {
        final controls = shape.getEdgeCubicControls(i);
        final edgeMidpoint = Offset(
          (currentVertex.dx + nextVertex.dx) / 2,
          (currentVertex.dy + nextVertex.dy) / 2,
        );

        // Calculate absolute control point positions
        final control1 = edgeMidpoint + controls[0];
        final control2 = edgeMidpoint + controls[1];

        // Apply rotation to control points if needed
        final effectiveControl1 = shape.rotation != 0
            ? _rotatePoint(control1, shape.center, shape.rotation)
            : control1;
        final effectiveControl2 = shape.rotation != 0
            ? _rotatePoint(control2, shape.center, shape.rotation)
            : control2;

        // Add cubic bezier curve to the path
        path.cubicTo(
          effectiveControl1.dx,
          effectiveControl1.dy,
          effectiveControl2.dx,
          effectiveControl2.dy,
          nextVertex.dx,
          nextVertex.dy,
        );
      }
      // Fall back to legacy quadratic curves
      else if (shape.curveControls.containsKey(i)) {
        final controlOffset = shape.curveControls[i]!;
        final currentVertex = vertices[i];
        final nextVertex = vertices[nextIndex];
        final midpoint = Offset((currentVertex.dx + nextVertex.dx) / 2,
            (currentVertex.dy + nextVertex.dy) / 2);

        final controlPoint =
            midpoint.translate(controlOffset.dx, controlOffset.dy);

        // Apply rotation to control point if needed
        final effectiveControlPoint = shape.rotation != 0
            ? _rotatePoint(controlPoint, shape.center, shape.rotation)
            : controlPoint;

        // Add a quadratic bezier curve to the path
        path.quadraticBezierTo(effectiveControlPoint.dx,
            effectiveControlPoint.dy, nextVertex.dx, nextVertex.dy);
      } else {
        // Add a straight line to the next vertex
        path.lineTo(nextVertex.dx, nextVertex.dy);
      }
    }

    // Close the path
    path.close();
    return path;
  }

  /// Helper function to rotate a point around a center
  Offset _rotatePoint(Offset point, Offset center, double angle) {
    // Calculate position relative to center
    final dx = point.dx - center.dx;
    final dy = point.dy - center.dy;

    // Apply rotation
    final cosTheta = math.cos(angle);
    final sinTheta = math.sin(angle);

    final rotatedDx = dx * cosTheta - dy * sinTheta;
    final rotatedDy = dx * sinTheta + dy * cosTheta;

    // Return point in original coordinate system
    return Offset(center.dx + rotatedDx, center.dy + rotatedDy);
  }

  /// Get bounds for just the top-most row of the pattern
  ShapeBounds _getTopRowBounds(Size size) {
    if (instructions.isEmpty) {
      // Fall back to overall bounds if no instructions
      return _getShapeBounds(size);
    }

    const double padding = 40.0;
    final availableWidth = math.max(0, size.width - 2 * padding);
    final availableHeight = math.max(0, size.height - 2 * padding);
    final scalingInfo = _calculateScalingInfo(
        Size(availableWidth.toDouble(), availableHeight.toDouble()));
    final offsetX = scalingInfo.offsetX;
    final offsetY = scalingInfo.offsetY;
    final scaleX = scalingInfo.scaleX;
    final scaleY = scalingInfo.scaleY;
    final minStitch = scalingInfo.minStitch;

    // Get the first row (top-most)
    final topRow = instructions.first;
    int topLeftStitch = -1;
    int topRightStitch = -1;

    // Find leftmost and rightmost stitches in the top row only
    for (int j = 0; j < topRow.length; j++) {
      if (topRow[j]) {
        if (topLeftStitch < 0) topLeftStitch = j;
        topRightStitch = j;
      }
    }

    // If no stitches in top row, use overall min/max as fallback
    if (topLeftStitch < 0) {
      topLeftStitch = minStitch;
      topRightStitch = scalingInfo.maxStitch;
    }

    final topY = offsetY;
    final leftX = offsetX + (topLeftStitch - minStitch) * scaleX;
    final rightX = offsetX + (topRightStitch - minStitch) * scaleX + scaleX;

    return ShapeBounds(
      topLeft: Offset(leftX, topY),
      topRight: Offset(rightX, topY),
      bottomLeft: Offset(leftX, topY + scaleY),
      bottomRight: Offset(rightX, topY + scaleY),
    );
  }

  /// Get bounds for just the bottom-most row of the pattern
  ShapeBounds _getBottomRowBounds(Size size) {
    if (instructions.isEmpty) {
      // Fall back to overall bounds if no instructions
      return _getShapeBounds(size);
    }

    const double padding = 40.0;
    final availableWidth = math.max(0, size.width - 2 * padding);
    final availableHeight = math.max(0, size.height - 2 * padding);
    final scalingInfo = _calculateScalingInfo(
        Size(availableWidth.toDouble(), availableHeight.toDouble()));
    final offsetX = scalingInfo.offsetX;
    final offsetY = scalingInfo.offsetY;
    final scaleX = scalingInfo.scaleX;
    final scaleY = scalingInfo.scaleY;
    final minStitch = scalingInfo.minStitch;

    // Get the last row (bottom-most)
    final bottomRow = instructions.last;
    int bottomLeftStitch = -1;
    int bottomRightStitch = -1;

    // Find leftmost and rightmost stitches in the bottom row only
    for (int j = 0; j < bottomRow.length; j++) {
      if (bottomRow[j]) {
        if (bottomLeftStitch < 0) bottomLeftStitch = j;
        bottomRightStitch = j;
      }
    }

    // If no stitches in bottom row, use overall min/max as fallback
    if (bottomLeftStitch < 0) {
      bottomLeftStitch = minStitch;
      bottomRightStitch = scalingInfo.maxStitch;
    }

    final bottomY = offsetY + (instructions.length - 1) * scaleY;
    final leftX = offsetX + (bottomLeftStitch - minStitch) * scaleX;
    final rightX = offsetX + (bottomRightStitch - minStitch) * scaleX + scaleX;

    return ShapeBounds(
      topLeft: Offset(leftX, bottomY),
      topRight: Offset(rightX, bottomY),
      bottomLeft: Offset(leftX, bottomY + scaleY),
      bottomRight: Offset(rightX, bottomY + scaleY),
    );
  }

  /// Get exact height bounds based on the top of the first row and bottom of the last row
  ShapeBounds _getExactPatternHeightBounds(Size size) {
    if (instructions.isEmpty) {
      // Fall back to overall bounds if no instructions
      return _getShapeBounds(size);
    }

    const double padding = 40.0;
    final availableWidth = math.max(0, size.width - 2 * padding);
    final availableHeight = math.max(0, size.height - 2 * padding);
    final scalingInfo = _calculateScalingInfo(
        Size(availableWidth.toDouble(), availableHeight.toDouble()));
    final offsetX = scalingInfo.offsetX;
    final offsetY = scalingInfo.offsetY;
    final scaleX = scalingInfo.scaleX;
    final scaleY = scalingInfo.scaleY;
    final scaledHeight = scalingInfo.scaledSize.height;
    final minStitch = scalingInfo.minStitch;

    // Find the leftmost stitch across all rows for consistent vertical line placement
    int overallLeftmostStitch = instructions.first.length;
    for (final row in instructions) {
      for (int j = 0; j < row.length; j++) {
        if (row[j]) {
          overallLeftmostStitch = math.min(overallLeftmostStitch, j);
          break;
        }
      }
    }

    // If no stitches found, use minStitch
    if (overallLeftmostStitch == instructions.first.length) {
      overallLeftmostStitch = minStitch;
    }

    // The vertical line X position (consistent for top and bottom)
    final leftX = offsetX + (overallLeftmostStitch - minStitch) * scaleX;

    // Top Y is at the top of the first row
    final topY = offsetY;

    // Bottom Y is at the bottom of the last row
    final bottomY = offsetY + scaledHeight;

    return ShapeBounds(
      topLeft: Offset(leftX, topY),
      topRight: Offset(leftX, topY), // Same X for vertical line
      bottomLeft: Offset(leftX, bottomY),
      bottomRight: Offset(leftX, bottomY), // Same X for vertical line
    );
  }

  void _drawWidthDimension(Canvas canvas, Offset start, Offset end,
      String dimension, String detail) {
    final linePaint = Paint()
      ..color = getLineColor()
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    // Calculate a slight vertical offset so the dimension lines are outside the shape
    final verticalOffset = start.dy < end.dy ? -15.0 : 15.0;

    // Draw the horizontal dimension line with small vertical end markers
    final lineStart = Offset(start.dx, start.dy + verticalOffset);
    final lineEnd = Offset(end.dx, end.dy + verticalOffset);

    // Draw the main horizontal line
    canvas.drawLine(lineStart, lineEnd, linePaint);

    // Draw the small vertical end markers
    canvas.drawLine(Offset(lineStart.dx, lineStart.dy - 5),
        Offset(lineStart.dx, lineStart.dy + 5), linePaint);
    canvas.drawLine(Offset(lineEnd.dx, lineEnd.dy - 5),
        Offset(lineEnd.dx, lineEnd.dy + 5), linePaint);

    // Draw the dimension text
    _drawTextCentered(
        canvas,
        dimension,
        Offset(
            lineStart.dx + (lineEnd.dx - lineStart.dx) / 2, lineStart.dy - 5),
        backgroundColor: getBackgroundColor());

    // Draw the detail text
    _drawTextCentered(
        canvas,
        detail,
        Offset(
            lineStart.dx + (lineEnd.dx - lineStart.dx) / 2, lineStart.dy + 15),
        backgroundColor: getBackgroundColor());
  }

  void _drawHeightDimension(
      Canvas canvas, ShapeBounds bounds, String dimension, String detail) {
    final linePaint = Paint()
      ..color = getLineColor()
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    // Increase the horizontal offset to position the dimension line further away from the shape
    final horizontalOffset = -30.0;

    // Use the exact Y positions from the bounds for precise height measurement
    final verticalLineX = bounds.topLeft.dx + horizontalOffset;
    final topY = bounds.topLeft.dy;
    final bottomY = bounds.bottomLeft.dy;

    // Draw the vertical dimension line
    final lineStart = Offset(verticalLineX, topY);
    final lineEnd = Offset(verticalLineX, bottomY);

    // Draw the main vertical line
    canvas.drawLine(lineStart, lineEnd, linePaint);

    // Draw the small horizontal end markers
    canvas.drawLine(Offset(lineStart.dx - 5, lineStart.dy),
        Offset(lineStart.dx + 5, lineStart.dy), linePaint);
    canvas.drawLine(Offset(lineEnd.dx - 5, lineEnd.dy),
        Offset(lineEnd.dx + 5, lineEnd.dy), linePaint);

    // Draw the dimension text vertically
    canvas.save();

    // Translate to the center of the vertical line, offset to the left
    canvas.translate(
        lineStart.dx - 15, lineStart.dy + (lineEnd.dy - lineStart.dy) / 2);

    // Rotate -90 degrees to make text vertical (reading from bottom to top)
    canvas.rotate(-math.pi / 2);

    // Draw the detail text (number of rows) first, positioned at the top
    _drawTextCentered(canvas, detail, Offset(0, 15),
        backgroundColor: getBackgroundColor());

    // Draw the dimension text (height in cm) below the rows text
    _drawTextCentered(canvas, dimension, Offset(25, 0),
        backgroundColor: getBackgroundColor());

    canvas.restore();
  }

  void _drawTextCentered(Canvas canvas, String text, Offset position,
      {Color? backgroundColor}) {
    final textStyle = TextStyle(
      color: getTextColor(),
      fontSize: 12,
      fontWeight: FontWeight.w500,
    );

    final textSpan = TextSpan(
      text: text,
      style: textStyle,
    );

    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();

    if (backgroundColor != null && backgroundColor != Colors.transparent) {
      final backgroundRect = Rect.fromCenter(
        center: position,
        width: textPainter.width + 10,
        height: textPainter.height + 4,
      );

      canvas.drawRect(
        backgroundRect,
        Paint()..color = backgroundColor,
      );
    }

    textPainter.paint(
        canvas,
        Offset(
          position.dx - textPainter.width / 2,
          position.dy - textPainter.height / 2,
        ));
  }

  void _drawProgressPercentageInTopRight(Canvas canvas, Size size) {
    final percentValue = (progressPercentage * 100).round();
    // Ensure translation key exists or provide a fallback
    final String text = Get.locale?.languageCode == 'en'
        ? '$percentValue% Complete'
        : 'knittingProgress_percentComplete'.trParams({
            'percent': percentValue.toString(),
          });

    final textStyle = TextStyle(
      color: getPrimaryColor(), // Use primary color for visibility
      fontSize: 14,
      fontWeight: FontWeight.bold,
    );

    final textSpan = TextSpan(
      text: text,
      style: textStyle,
    );

    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();

    // Position in the top-right corner, with some padding from the edge
    final position = Offset(
      size.width - textPainter.width - 10, // 10px padding from right
      10, // 10px padding from top
    );

    // Draw a faint background for better readability if needed
    final backgroundPaint = Paint()
      ..color =
          getBackgroundColor().withOpacity(0.7) // Semi-transparent background
      ..style = PaintingStyle.fill;

    final bgRect = RRect.fromLTRBR(
        position.dx - 4,
        position.dy - 2,
        position.dx + textPainter.width + 4,
        position.dy + textPainter.height + 2,
        const Radius.circular(4));
    // canvas.drawRRect(bgRect, backgroundPaint); // Optional: uncomment to draw background

    textPainter.paint(canvas, position);
  }

  void _drawCurrentRowIndicator(Canvas canvas, Size size) {
    final bounds = _getShapeBounds(size);

    // Calculate vertical position of current row based on progress
    final topY = bounds.topLeft.dy;
    final bottomY = bounds.bottomLeft.dy;

    // Calculate the position at the top of the current progress fill
    final progressY = bottomY - (bottomY - topY) * progressPercentage;

    // Position on the right side of the shape
    final rightEdge = math.max(bounds.topRight.dx, bounds.bottomRight.dx) + 90;

    // Draw the row text - use the user-facing row number (1-based from bottom)
    // This means rowNumber = totalRows - currentRow since currentRow is the array index (0-based from top)
    final userFacingRowNum = totalRows - currentRow;

    // Replace hardcoded "row X" with localized version
    final rowText = 'knittingProgress_row'
        .trParams({'number': userFacingRowNum.toString()});

    _drawRightAlignedText(canvas, rowText, Offset(rightEdge, progressY));

    // Draw the needle range - combining left and right
    if (leftNeedles > 0 || rightNeedles > 0) {
      String needleRangeText;

      // Determine the needle range based on which needles are active
      if (leftNeedles > 0 && rightNeedles > 0) {
        // Needles on both sides - show full range L to R
        needleRangeText = 'knittingProgress_needleRangeBoth'.trParams(
            {'left': leftNeedles.toString(), 'right': rightNeedles.toString()});
      } else if (leftNeedles > 0) {
        // Only L needles
        if (leftNeedles > 1) {
          needleRangeText = 'knittingProgress_needleRangeLeft'
              .trParams({'number': leftNeedles.toString()});
        } else {
          needleRangeText = 'knittingProgress_needleRangeSingleLeft'.tr;
        }
      } else {
        // Only R needles
        if (rightNeedles > 1) {
          needleRangeText = 'knittingProgress_needleRangeRight'
              .trParams({'number': rightNeedles.toString()});
        } else {
          needleRangeText = 'knittingProgress_needleRangeSingleRight'.tr;
        }
      }

      _drawRightAlignedText(
          canvas, needleRangeText, Offset(rightEdge, progressY + 15));
    } else {
      // No active needles
      _drawRightAlignedText(canvas, 'knittingProgress_noActiveNeedles'.tr,
          Offset(rightEdge, progressY + 15));
    }
  }

  void _drawHorizontalRowLine(Canvas canvas, Size size) {
    final bounds = _getShapeBounds(size);
    final topY = bounds.topLeft.dy;
    final bottomY = bounds.bottomLeft.dy;
    final progressY = bottomY - (bottomY - topY) * progressPercentage;

    _drawHorizontalRowIndicator(canvas, bounds, progressY);
  }

  void _drawHorizontalRowIndicator(
      Canvas canvas, ShapeBounds bounds, double rowY) {
    // Create a dashed line paint
    final dashPaint = Paint()
      ..color = getPrimaryColor()
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    // Get the leftmost and rightmost points of the shape (irrespective of top/bottom)
    double leftmostX = math.min(bounds.topLeft.dx, bounds.bottomLeft.dx);
    double rightmostX = math.max(bounds.topRight.dx, bounds.bottomRight.dx);

    // Extend the line beyond the shape edges for better visibility
    leftmostX -= 15;
    rightmostX += 35;

    // Draw dashed line
    final dashWidth = 5.0;
    final dashSpace = 3.0;
    double distance = rightmostX - leftmostX;
    double drawn = 0;

    while (drawn < distance) {
      final dashLength = math.min(dashWidth, distance - drawn);
      canvas.drawLine(
        Offset(leftmostX + drawn, rowY),
        Offset(leftmostX + drawn + dashLength, rowY),
        dashPaint,
      );
      drawn += dashLength + dashSpace;
    }

    // Draw small circle indicators at the ends
    final circlePaint = Paint()
      ..color = getPrimaryColor()
      ..style = PaintingStyle.fill;

    canvas.drawCircle(Offset(leftmostX, rowY), 3, circlePaint);
    canvas.drawCircle(Offset(rightmostX, rowY), 3, circlePaint);
  }

  void _drawRightAlignedText(Canvas canvas, String text, Offset position) {
    final textStyle = TextStyle(
      color: getTextColor(),
      fontSize: 12,
    );

    final textSpan = TextSpan(
      text: text,
      style: textStyle,
    );

    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    textPainter.paint(
        canvas,
        Offset(
          position.dx - textPainter.width,
          position.dy - textPainter.height / 2,
        ));
  }

  /// Draw a rectangle showing this zone's position in the full pattern
  void _drawZonePositionContext(Canvas canvas, Size size) {
    if (zonePositionInfo == null) return;

    // Get zone position information
    final int startNeedle = zonePositionInfo!['startNeedle'] ?? 0;
    final int endNeedle = zonePositionInfo!['endNeedle'] ?? 0;
    final int startRow = zonePositionInfo!['startRow'] ?? 0;
    final int endRow = zonePositionInfo!['endRow'] ?? 0;
    final int fullPatternWidth = zonePositionInfo!['fullPatternWidth'] ?? 100;
    final int fullPatternHeight = zonePositionInfo!['fullPatternHeight'] ?? 100;

    if (fullPatternWidth <= 0 || fullPatternHeight <= 0) return;

    // Calculate the padding and available size
    const double padding = 40.0;
    final availableWidth = size.width - 2 * padding;
    final availableHeight = size.height - 2 * padding;

    // Calculate scale to fit a small context indicator at the top right
    const double contextWidth = 80.0;
    const double contextHeight = 60.0;

    // Calculate scale factors for the mini-map
    final double scaleX = contextWidth / fullPatternWidth;
    final double scaleY = contextHeight / fullPatternHeight;

    // Position the context view at the top right
    final double contextX = size.width - contextWidth - 10;
    final double contextY = 10;

    // Draw background for the context
    final backgroundPaint = Paint()
      ..color = getBackgroundColor().withOpacity(0.8)
      ..style = PaintingStyle.fill;

    canvas.drawRect(
        Rect.fromLTWH(contextX, contextY, contextWidth, contextHeight),
        backgroundPaint);

    // Draw border for the context
    final borderPaint = Paint()
      ..color = getPrimaryColor().withOpacity(0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    canvas.drawRect(
        Rect.fromLTWH(contextX, contextY, contextWidth, contextHeight),
        borderPaint);

    // Draw a rectangle representing the full pattern (as a light outline)
    final fullPatternPaint = Paint()
      ..color = Colors.grey.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    canvas.drawRect(
        Rect.fromLTWH(
            contextX + 5, contextY + 5, contextWidth - 10, contextHeight - 10),
        fullPatternPaint);

    // Draw a rectangle representing this zone's position within the full pattern
    final zonePaint = Paint()
      ..color = getPrimaryColor()
      ..style = PaintingStyle.fill
      ..strokeWidth = 0.0;

    // Calculate zone's position within the context
    final zoneX = contextX + 5 + startNeedle * scaleX;
    final zoneY = contextY + 5 + startRow * scaleY;
    final zoneWidth = (endNeedle - startNeedle + 1) * scaleX;
    final zoneHeight = (endRow - startRow + 1) * scaleY;

    canvas.drawRect(
        Rect.fromLTWH(zoneX, zoneY, zoneWidth, zoneHeight), zonePaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is ProgressTrackingShapePainter) {
      return oldDelegate.currentRow != currentRow ||
          oldDelegate.progressPercentage != progressPercentage ||
          oldDelegate.instructions != instructions ||
          oldDelegate.shapes != shapes ||
          oldDelegate.stitchesPerCm != stitchesPerCm ||
          oldDelegate.rowsPerCm != rowsPerCm ||
          oldDelegate.highlightActiveZone != highlightActiveZone ||
          oldDelegate.zonePositionInfo != zonePositionInfo;
    }
    return true;
  }
}
