import 'package:flutter/material.dart';

/// Widget that provides navigation controls for knitting
class KnittingNavigation extends StatelessWidget {
  final VoidCallback? prevAction;
  final VoidCallback? nextAction;

  const KnittingNavigation({
    super.key,
    required this.prevAction,
    required this.nextAction,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Previous button
        ElevatedButton.icon(
          icon: const Icon(Icons.arrow_back),
          label: const Text('Previous'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey[200],
            foregroundColor: Colors.black87,
          ),
          onPressed: prevAction,
        ),
        // Next button
        ElevatedButton.icon(
          icon: const Icon(Icons.check),
          label: const Text('Done & Next'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.purple,
            foregroundColor: Colors.white,
          ),
          onPressed: nextAction,
        ),
      ],
    );
  }
}
