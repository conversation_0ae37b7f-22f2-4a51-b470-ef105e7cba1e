import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Standardized dialog widget with consistent styling across the app
///
/// This widget provides a uniform appearance for all dialogs in the application,
/// ensuring consistent spacing, colors, and button styles using the app's theme.
///
/// ## Basic Usage:
///
/// ```dart
/// // Simple confirmation dialog
/// StandardDialog.show<bool>(
///   context: context,
///   title: 'Confirm Action',
///   content: 'Are you sure you want to proceed?',
///   actions: [
///     DialogAction.outlined(
///       text: 'Cancel',
///       onPressed: () => Navigator.of(context).pop(false),
///     ),
///     DialogAction.elevated(
///       text: 'Confirm',
///       onPressed: () => Navigator.of(context).pop(true),
///     ),
///   ],
/// );
/// ```
///
/// ## With Icon and Custom Content:
///
/// ```dart
/// StandardDialog.show<void>(
///   context: context,
///   title: 'Success!',
///   titleIcon: Icon(Icons.check_circle, color: Colors.green),
///   customContent: Column(
///     mainAxisSize: MainAxisSize.min,
///     children: [
///       Text('Operation completed successfully.'),
///       SizedBox(height: 8),
///       Text('What would you like to do next?'),
///     ],
///   ),
///   actions: [
///     DialogAction.text(
///       text: 'Close',
///       onPressed: () => Navigator.of(context).pop(),
///     ),
///     DialogAction.elevated(
///       text: 'Continue',
///       backgroundColor: Colors.green,
///       onPressed: () {
///         Navigator.of(context).pop();
///         // Continue with next action
///       },
///     ),
///   ],
/// );
/// ```
///
/// ## Action Types:
/// - `DialogAction.text()` - Simple text button using theme primary color
/// - `DialogAction.outlined()` - Outlined button using theme primary color
/// - `DialogAction.elevated()` - Elevated button with customizable colors (defaults to theme primary)
class StandardDialog extends StatelessWidget {
  final String title;
  final String content;
  final List<DialogAction> actions;
  final Widget? titleIcon;
  final bool barrierDismissible;
  final Widget? customContent;

  const StandardDialog({
    super.key,
    required this.title,
    this.content = '',
    required this.actions,
    this.titleIcon,
    this.barrierDismissible = true,
    this.customContent,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: titleIcon != null
          ? Row(
              children: [
                titleIcon!,
                const SizedBox(width: 8),
                Expanded(child: Text(title)),
              ],
            )
          : Text(title),
      content: customContent ?? (content.isNotEmpty ? Text(content) : null),
      actions: actions.map((action) => _buildActionButton(action)).toList(),
    );
  }

  Widget _buildActionButton(DialogAction action) {
    switch (action.type) {
      case DialogActionType.outlined:
        return OutlinedButton(
          style: OutlinedButton.styleFrom(
            foregroundColor: Get.theme.colorScheme.primary,
            side: BorderSide(color: Get.theme.colorScheme.primary),
          ),
          onPressed: action.onPressed,
          child: Text(action.text),
        );
      case DialogActionType.elevated:
        return ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor:
                action.backgroundColor ?? Get.theme.colorScheme.primary,
            foregroundColor:
                action.foregroundColor ?? Get.theme.colorScheme.onPrimary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
          ),
          onPressed: action.onPressed,
          child: Text(action.text),
        );
      case DialogActionType.text:
        return TextButton(
          style: TextButton.styleFrom(
            foregroundColor: Get.theme.colorScheme.primary,
          ),
          onPressed: action.onPressed,
          child: Text(action.text),
        );
    }
  }

  /// Convenience method to show a standard dialog
  static Future<T?> show<T>({
    required BuildContext context,
    required String title,
    String content = '',
    required List<DialogAction> actions,
    Widget? titleIcon,
    bool barrierDismissible = true,
    Widget? customContent,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => StandardDialog(
        title: title,
        content: content,
        actions: actions,
        titleIcon: titleIcon,
        barrierDismissible: barrierDismissible,
        customContent: customContent,
      ),
    );
  }
}

/// Represents an action button in the dialog
class DialogAction {
  final String text;
  final VoidCallback onPressed;
  final DialogActionType type;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const DialogAction({
    required this.text,
    required this.onPressed,
    required this.type,
    this.backgroundColor,
    this.foregroundColor,
  });

  /// Creates an outlined button action
  factory DialogAction.outlined({
    required String text,
    required VoidCallback onPressed,
  }) {
    return DialogAction(
      text: text,
      onPressed: onPressed,
      type: DialogActionType.outlined,
    );
  }

  /// Creates an elevated button action
  factory DialogAction.elevated({
    required String text,
    required VoidCallback onPressed,
    Color? backgroundColor,
    Color? foregroundColor,
  }) {
    return DialogAction(
      text: text,
      onPressed: onPressed,
      type: DialogActionType.elevated,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
    );
  }

  /// Creates a text button action
  factory DialogAction.text({
    required String text,
    required VoidCallback onPressed,
  }) {
    return DialogAction(
      text: text,
      onPressed: onPressed,
      type: DialogActionType.text,
    );
  }
}

/// Types of dialog actions
enum DialogActionType {
  outlined,
  elevated,
  text,
}
