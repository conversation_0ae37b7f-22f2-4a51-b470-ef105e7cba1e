import 'package:flutter/material.dart';
import '../../interactive_knitting_view.dart';
import '../../../components/row_counter.dart';

/// Tab that displays detailed row information
class RowDetailsTab extends StatelessWidget {
  final KnittingRowInfo rowInfo;

  const RowDetailsTab({
    super.key,
    required this.rowInfo,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Row counter with visual indicators
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
                color: Colors.grey.shade50,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.format_list_numbered,
                          color: Colors.purple),
                      const SizedBox(width: 8),
                      const Text(
                        'Row Information',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Direction indicator
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.purple.withOpacity(0.05),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          rowInfo.isBottomUp
                              ? Icons.arrow_upward
                              : Icons.arrow_downward,
                          size: 16,
                          color: Colors.purple,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          rowInfo.isBottomUp
                              ? 'Working bottom-up (${rowInfo.displayRowNum} of ${rowInfo.totalRows})'
                              : 'Working top-down (${rowInfo.displayRowNum} of ${rowInfo.totalRows})',
                          style: const TextStyle(color: Colors.purple),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 12),

                  // Row counters
                  Row(
                    children: [
                      Expanded(
                        child: RowCounter(
                          label: 'Current row',
                          value: rowInfo.startRow.toString(),
                        ),
                      ),
                      if (rowInfo.rowsToKnit > 1) ...[
                        // Arrow indicator for multi-row instructions
                        Container(
                          height: 2,
                          width: 30,
                          color: Colors.purple,
                          margin: const EdgeInsets.symmetric(horizontal: 8),
                        ),
                        Expanded(
                          child: RowCounter(
                            label: 'Final row',
                            value: rowInfo.endRow.toString(),
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Detailed instruction
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.purple.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.purple.withOpacity(0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    rowInfo.rowsToKnit > 1
                        ? 'Knit the following pattern for ${rowInfo.rowsToKnit} rows'
                        : 'Knit the following pattern',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                  if (rowInfo.rowsToKnit > 1) ...[
                    const SizedBox(height: 8),
                    RichText(
                      text: TextSpan(
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.black87,
                        ),
                        children: [
                          const TextSpan(
                            text: 'Repeat this pattern from row ',
                            style: TextStyle(fontStyle: FontStyle.italic),
                          ),
                          TextSpan(
                            text: '${rowInfo.startRow}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.purple,
                            ),
                          ),
                          const TextSpan(
                            text: ' to row ',
                            style: TextStyle(fontStyle: FontStyle.italic),
                          ),
                          TextSpan(
                            text: '${rowInfo.endRow}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.purple,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
