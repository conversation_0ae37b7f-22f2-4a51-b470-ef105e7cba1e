import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../interactive_knitting_view.dart';

/// Tab that displays row completion tracking or instructions
class ProgressTab extends StatelessWidget {
  final KnittingRowInfo rowInfo;
  final RxList<bool> completedRows;
  final Function(int) onToggleCompletion;

  const ProgressTab({
    super.key,
    required this.rowInfo,
    required this.completedRows,
    required this.onToggleCompletion,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: rowInfo.rowsToKnit > 1
            ? _buildMultiRowProgress()
            : _buildSingleRowInstructions(),
      ),
    );
  }

  Widget _buildMultiRowProgress() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTipCard(
          'To stay on track, mark off each row as you complete it. All these rows have the same pattern!',
          Colors.blue,
        ),

        // Row completion tracking
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.green.withOpacity(0.05),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.green.withOpacity(0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.check_circle_outline, color: Colors.green),
                  const SizedBox(width: 8),
                  const Text(
                    'Row Completion Tracker',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Row completion tracker using the class methods for state management
              Obx(() => Wrap(
                    spacing: 8.0,
                    runSpacing: 8.0,
                    children: List.generate(
                      rowInfo.rowsToKnit,
                      (index) => InkWell(
                        onTap: () => onToggleCompletion(index),
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.green),
                            borderRadius: BorderRadius.circular(4),
                            color: completedRows.length > index &&
                                    completedRows[index]
                                ? Colors.green.withOpacity(0.2)
                                : Colors.white,
                          ),
                          alignment: Alignment.center,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                rowInfo.isBottomUp
                                    ? '${rowInfo.startRow - index}'
                                    : '${rowInfo.startRow + index}',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: completedRows.length > index &&
                                          completedRows[index]
                                      ? Colors.green
                                      : Colors.black87,
                                ),
                              ),
                              Icon(
                                (completedRows.length > index &&
                                        completedRows[index])
                                    ? Icons.check_circle
                                    : Icons.circle_outlined,
                                size: 16,
                                color: completedRows.length > index &&
                                        completedRows[index]
                                    ? Colors.green
                                    : Colors.grey,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  )),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSingleRowInstructions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Detailed instruction for a single row
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.blue.withOpacity(0.05),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.blue.withOpacity(0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.blue[700]),
                  const SizedBox(width: 8),
                  Text(
                    'Instructions for Row ${rowInfo.displayRowNum}',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[800],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              const Text(
                'Follow the stitch pattern shown at the top of the screen.',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 8),
              RichText(
                text: TextSpan(
                  style: const TextStyle(color: Colors.black, fontSize: 14),
                  children: [
                    const TextSpan(text: '• '),
                    TextSpan(
                      text: 'Purple cells',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.purple[800],
                      ),
                    ),
                    const TextSpan(text: ' indicate knit stitches.'),
                  ],
                ),
              ),
              const SizedBox(height: 4),
              RichText(
                text: TextSpan(
                  style: const TextStyle(color: Colors.black, fontSize: 14),
                  children: [
                    const TextSpan(text: '• '),
                    TextSpan(
                      text: 'Light cells',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[600],
                      ),
                    ),
                    const TextSpan(text: ' indicate purl or empty stitches.'),
                  ],
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        _buildTipCard(
          'When you complete this row, click "Done & Next" to move to the next row in your pattern.',
          Colors.amber,
        ),
      ],
    );
  }

  Widget _buildTipCard(String tipText, Color color) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.lightbulb_outline, color: color),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              tipText,
              style: TextStyle(color: color),
            ),
          ),
        ],
      ),
    );
  }
}
