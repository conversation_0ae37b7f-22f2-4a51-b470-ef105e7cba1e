import 'package:flutter/material.dart';
import 'package:xoxknit/app/modules/new_item/controllers/new_item_wizard_controller.dart';
import 'package:xoxknit/app/modules/new_item/utils/knitting_utils.dart';

class InstructionCard extends StatelessWidget {
  final NewItemWizardController controller;
  final int currentRowIndex;
  final int rowsToKnit;
  final bool isBottomUp;

  const InstructionCard({
    super.key,
    required this.controller,
    required this.currentRowIndex,
    required this.rowsToKnit,
    this.isBottomUp = false,
  });

  @override
  Widget build(BuildContext context) {
    final instructions = controller.knittingInstructions.value;
    if (instructions.isEmpty || currentRowIndex >= instructions.length) {
      return _buildEmptyCard(context);
    }

    // Get the stitch pattern for the current row
    final currentRow = instructions[currentRowIndex];

    // Analyze row for stitch ranges
    final ranges = KnittingUtils.findStitchRanges(currentRow);
    final hasDiscontinuous = ranges.length > 1;

    // Access to knitting machine configuration
    final machine = controller.newItem.value.knittingMachine;
    final needleCount = machine?.needlesCount ?? 100;

    // Get row information
    final rowHeaderText = _getRowHeaderText();

    // Get needle range information (first to last needle)
    String firstNeedleLabel = "N/A";
    String lastNeedleLabel = "N/A";

    if (ranges.isNotEmpty) {
      final firstNeedle = ranges.first.startNeedle;
      final lastNeedle = ranges.last.endNeedle;

      firstNeedleLabel = KnittingUtils.formatNeedleNumber(
          firstNeedle, needleCount,
          useLRNotation: true);
      lastNeedleLabel = KnittingUtils.formatNeedleNumber(
          lastNeedle, needleCount,
          useLRNotation: true);
    }

    // Define theme colors for a more beautiful UI
    final primaryColor = hasDiscontinuous ? Colors.orange : Colors.purple;
    final secondaryColor = Colors.blue;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 12),
      elevation: 3,
      shadowColor: primaryColor.withOpacity(0.3),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: primaryColor.withOpacity(0.4),
          width: 1.5,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              primaryColor.withOpacity(0.05),
            ],
            stops: const [0.7, 1.0],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Row indicator header with icon - This already shows the row information
              Container(
                padding: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: Colors.grey.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Icon(
                        hasDiscontinuous
                            ? Icons.warning_amber_rounded
                            : (rowsToKnit > 1
                                ? Icons.repeat
                                : Icons.straighten),
                        color: primaryColor,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        rowHeaderText,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize:
                              22, // Increased size since this is now the only row indicator
                          color: primaryColor.shade800,
                        ),
                      ),
                    ),
                    if (rowsToKnit > 1)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 6),
                        decoration: BoxDecoration(
                          color: primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: primaryColor.withOpacity(0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.repeat,
                              size: 14,
                              color: primaryColor,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${rowsToKnit}×',
                              style: TextStyle(
                                fontSize: 14,
                                color: primaryColor.shade800,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // Simplified instruction information in a clean container
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      secondaryColor.withOpacity(0.05),
                      secondaryColor.withOpacity(0.1),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.03),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Section title with decorative element
                    Row(
                      children: [
                        Container(
                          height: 18,
                          width: 3,
                          decoration: BoxDecoration(
                            color: secondaryColor,
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Instructions',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: secondaryColor.shade700,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),

                    // Needle range information - Now the primary focus of the instructions section
                    Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 16, horizontal: 18),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                        border: Border.all(
                          color: Colors.grey.withOpacity(0.2),
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade100,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.straighten,
                              size: 24,
                              color: Colors.grey.shade700,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Knit Needles",
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                              const SizedBox(height: 4),
                              ranges.isEmpty
                                  ? const Text(
                                      "None",
                                      style: TextStyle(
                                        fontSize: 28,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.red,
                                      ),
                                    )
                                  : _buildPromientNumberRange(
                                      firstNeedleLabel, lastNeedleLabel),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // Only show a warning for discontinuous stitches
                    if (hasDiscontinuous) ...[
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Colors.orange.withOpacity(0.1),
                              Colors.orange.withOpacity(0.2),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(10),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.orange.withOpacity(0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.8),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Icon(
                                Icons.warning_amber,
                                size: 20,
                                color: Colors.orange,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                "This row has gaps! Check detailed pattern view.",
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.orange.shade800,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper widget to build prominent number display
  Widget _buildPromientNumber(dynamic number) {
    return Text(
      number.toString(),
      style: const TextStyle(
        fontSize: 28, // Increased size to make it more prominent
        fontWeight: FontWeight.bold,
        color: Colors.black87,
        letterSpacing: -0.5,
      ),
    );
  }

  // Helper widget to build prominent range display
  Widget _buildPromientNumberRange(dynamic start, dynamic end) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          start.toString(),
          style: const TextStyle(
            fontSize: 28, // Increased size to make it more prominent
            fontWeight: FontWeight.bold,
            color: Colors.black87,
            letterSpacing: -0.5,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 6),
          child: Icon(Icons.arrow_right_alt, size: 28, color: Colors.grey[700]),
        ),
        Text(
          end.toString(),
          style: const TextStyle(
            fontSize: 28, // Increased size to make it more prominent
            fontWeight: FontWeight.bold,
            color: Colors.black87,
            letterSpacing: -0.5,
          ),
        ),
      ],
    );
  }

  String _getRowHeaderText() {
    // Display different text based on whether knitting multiple rows or not
    if (rowsToKnit > 1) {
      return 'Rows ${_getRowStartNumber()}-${_getRowEndNumber()}';
    } else {
      return 'Row ${_getRowStartNumber()}';
    }
  }

  int _getRowStartNumber() {
    return isBottomUp
        ? (controller.knittingInstructions.value.length - currentRowIndex)
        : (currentRowIndex + 1);
  }

  int _getRowEndNumber() {
    final startRow = _getRowStartNumber();
    return startRow + rowsToKnit - 1;
  }

  Widget _buildEmptyCard(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 12),
      elevation: 2,
      shadowColor: Colors.grey.withOpacity(0.3),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              Colors.grey.withOpacity(0.1),
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.info_outline,
                size: 32,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 12),
              Text(
                'No instruction available',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
