import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Custom painter for drawing the knitting grid with row and column numbers
class KnittingGridPainter extends CustomPainter {
  final List<List<bool>> instructions;
  final double gridSize;
  final bool showNumbers;
  final int rowLabelInterval;
  final int columnLabelInterval;

  KnittingGridPainter({
    required this.instructions,
    required this.gridSize,
    this.showNumbers = true,
    this.rowLabelInterval = 5,
    this.columnLabelInterval = 5,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.withOpacity(0.3)
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    // Draw horizontal grid lines (rows)
    for (int i = 0; i <= instructions.length; i++) {
      final y = i * gridSize;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );

      // Draw row numbers on the left
      if (showNumbers && i < instructions.length && i % rowLabelInterval == 0) {
        textPainter.text = TextSpan(
          text: (i + 1).toString(),
          style: TextStyle(
            color: Colors.grey[700],
            fontSize: math.min(14, gridSize * 0.7),
            fontWeight: i % 10 == 0 ? FontWeight.bold : FontWeight.normal,
          ),
        );
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(-textPainter.width - 5, y - textPainter.height / 2),
        );
      }
    }

    // Draw vertical grid lines (columns)
    for (int i = 0; i <= instructions[0].length; i++) {
      final x = i * gridSize;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );

      // Draw column numbers at the top
      if (showNumbers &&
          i < instructions[0].length &&
          i % columnLabelInterval == 0) {
        // Calculate the center point for L/R needle notations
        final centerPoint = instructions[0].length ~/ 2;

        // Format as L/R notation for knitting needles
        final position = i - centerPoint;
        final needleLabel = position == 0
            ? 'C'
            : position < 0
                ? 'L${-position}'
                : 'R$position';

        final isCenter = position == 0;

        textPainter.text = TextSpan(
          text: needleLabel,
          style: TextStyle(
            color: isCenter
                ? Colors.purple.withOpacity(0.8)
                : position < 0
                    ? Colors.blue.withOpacity(0.7)
                    : Colors.red.withOpacity(0.7),
            fontSize: math.min(14, gridSize * 0.7),
            fontWeight:
                isCenter || i % 10 == 0 ? FontWeight.bold : FontWeight.normal,
          ),
        );
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(x - textPainter.width / 2, -textPainter.height - 5),
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant KnittingGridPainter oldDelegate) =>
      instructions != oldDelegate.instructions ||
      gridSize != oldDelegate.gridSize ||
      showNumbers != oldDelegate.showNumbers ||
      rowLabelInterval != oldDelegate.rowLabelInterval ||
      columnLabelInterval != oldDelegate.columnLabelInterval;
}
