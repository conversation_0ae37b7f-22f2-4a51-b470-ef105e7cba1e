import 'package:flutter/material.dart';

/// A widget that displays an icon and text, used for statistics
class StatItem extends StatelessWidget {
  final IconData icon;
  final String text;
  final Color? textColor;
  final Color? iconColor;
  final double? iconSize;

  const StatItem({
    super.key,
    required this.icon,
    required this.text,
    this.textColor = Colors.white,
    this.iconColor = Colors.white,
    this.iconSize = 16,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: iconColor, size: iconSize),
        const SizedBox(width: 4),
        Text(
          text,
          style: TextStyle(color: textColor),
        ),
      ],
    );
  }
}
