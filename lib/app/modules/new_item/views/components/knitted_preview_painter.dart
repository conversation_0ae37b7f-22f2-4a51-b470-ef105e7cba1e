import 'package:flutter/material.dart';
import 'package:xoxknit/app/core/theme/app_colors.dart';
import 'dart:math' as math;

/// Custom painter for rendering a preview of the knitted piece
class KnittedPreviewPainter extends CustomPainter {
  final List<List<bool>> instructions;
  final double gridSize;
  final Color stitchColor;
  final bool showYarnConnections;

  KnittedPreviewPainter({
    required this.instructions,
    required this.gridSize,
    this.stitchColor = const Color(0xFFD81B60),
    this.showYarnConnections = true,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Paint for filling stitches
    final fillPaint = Paint()
      ..color = stitchColor
      ..style = PaintingStyle.fill;

    // Paint for stitch outlines
    final outlinePaint = Paint()
      ..color = Colors.black.withOpacity(0.4)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    // Paint for yarn connections
    final yarnPaint = Paint()
      ..color = stitchColor.withOpacity(0.4)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    // Paint for gaps in yarn
    final gapPaint = Paint()
      ..color = Colors.grey.withOpacity(0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0
      ..strokeCap = StrokeCap.round;

    // Draw each stitch and connection
    for (int row = 0; row < instructions.length; row++) {
      List<int> stitchPositions = [];

      // Record stitch positions in this row
      for (int col = 0; col < instructions[row].length; col++) {
        if (instructions[row][col]) {
          stitchPositions.add(col);
        }
      }

      // Draw the stitches
      for (int col = 0; col < instructions[row].length; col++) {
        if (instructions[row][col]) {
          // Calculate stitch size based on grid (slightly smaller than grid cell)
          final stitchSize = gridSize * 0.75;
          final xCenter = col * gridSize + gridSize / 2;
          final yCenter = row * gridSize + gridSize / 2;

          // Draw stitch as a circle
          final stitchRect = Rect.fromCenter(
            center: Offset(xCenter, yCenter),
            width: stitchSize,
            height: stitchSize,
          );

          canvas.drawOval(stitchRect, fillPaint);
          canvas.drawOval(stitchRect, outlinePaint);
        }
      }

      // Draw yarn connections between stitches
      if (showYarnConnections &&
          stitchPositions.isNotEmpty &&
          row < instructions.length) {
        for (int i = 0; i < stitchPositions.length - 1; i++) {
          final startCol = stitchPositions[i];
          final endCol = stitchPositions[i + 1];

          // Calculate stitch centers
          final startX = startCol * gridSize + gridSize / 2;
          final endX = endCol * gridSize + gridSize / 2;
          final y = row * gridSize + gridSize / 2;

          // Draw the yarn connections
          canvas.drawLine(
            Offset(startX, y),
            Offset(endX, y),
            yarnPaint,
          );

          // If gap is large, indicate missing stitches
          if (endCol - startCol > 1) {
            final dashCount = endCol - startCol - 1;
            final dashWidth = (endX - startX) / (dashCount * 2);

            for (int d = 0; d < dashCount; d++) {
              final dashX = startX + (d + 1) * dashWidth * 2 - dashWidth;
              canvas.drawLine(
                Offset(dashX, y),
                Offset(dashX + dashWidth, y),
                gapPaint,
              );
            }
          }
        }
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
