import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// A widget that displays a toggle to keep the screen on
class ScreenOnToggle extends StatelessWidget {
  final RxBool keepScreenOn;

  const ScreenOnToggle({
    super.key,
    required this.keepScreenOn,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Obx(() => Switch(
              value: keepScreenOn.value,
              onChanged: (value) => keepScreenOn.value = value,
              activeColor: Colors.teal,
            )),
        const SizedBox(width: 8),
        const Text(
          'Keep screen on',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}
