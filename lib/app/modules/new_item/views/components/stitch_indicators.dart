import 'package:flutter/material.dart';
import '../../utils/knitting_utils.dart';

/// A widget that displays stitch indicators showing left/right stitch counts
/// with needle numbers for easier following of knitting patterns
class StitchIndicators extends StatelessWidget {
  final List<bool> row;
  // Add flag to show needle numbers
  final bool showNeedleNumbers;
  // Add needleCount to format needle numbers properly
  final int needleCount;
  // Add option to use L/R notation
  final bool useLRNotation;

  const StitchIndicators({
    super.key,
    required this.row,
    this.showNeedleNumbers = true,
    this.needleCount = 100,
    this.useLRNotation = true,
  });

  @override
  Widget build(BuildContext context) {
    // Use KnittingUtils to find stitch ranges, just like InstructionCard does
    final ranges = KnittingUtils.findStitchRanges(row);

    // If no ranges, show placeholder
    if (ranges.isEmpty) {
      return const Center(
        child: Text(
          'No stitches in this row',
          style: TextStyle(
            fontStyle: FontStyle.italic,
            color: Colors.grey,
          ),
        ),
      );
    }

    // Find first and last stitch using the ranges
    final firstStitch = ranges.first.startNeedle;
    final lastStitch = ranges.last.endNeedle;

    // Count total number of stitches in this row
    int totalStitches = 0;
    for (int i = firstStitch; i <= lastStitch; i++) {
      if (row[i]) totalStitches++;
    }

    // Count number of knit stitches on each side
    int leftKnitCount = 0;
    int rightKnitCount = 0;

    // Calculate midpoint based on actual stitches, not just array indices
    // This ensures a more balanced left/right distribution
    int stitchCounter = 0;
    int midpointIndex = firstStitch;

    // Find the midpoint stitch index
    for (int i = firstStitch; i <= lastStitch; i++) {
      if (row[i]) {
        stitchCounter++;
        if (stitchCounter > totalStitches / 2) {
          midpointIndex = i;
          break;
        }
      }
    }

    // Count left side knit stitches
    for (int i = firstStitch; i < midpointIndex; i++) {
      if (row[i]) leftKnitCount++;
    }

    // Count right side knit stitches
    for (int i = midpointIndex; i <= lastStitch; i++) {
      if (row[i]) rightKnitCount++;
    }

    // Build the stitch indicators
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Total stitch count
        Container(
          padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 12),
          decoration: BoxDecoration(
            color: Colors.purple.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Text(
            'Total stitches: $totalStitches',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 12),

        // Left/Right indicators
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Left stitch indicator
            _buildStitchCounter(
              'L$leftKnitCount',
              'Stitches ${KnittingUtils.formatNeedleNumber(firstStitch, needleCount)}-${KnittingUtils.formatNeedleNumber(midpointIndex, needleCount)}',
            ),

            const SizedBox(width: 20),

            // Right stitch indicator
            _buildStitchCounter(
              'R$rightKnitCount',
              'Stitches ${KnittingUtils.formatNeedleNumber(midpointIndex, needleCount)}-${KnittingUtils.formatNeedleNumber(lastStitch, needleCount)}',
            ),
          ],
        ),

        // Add detailed stitch visualization with needle numbers
        if (showNeedleNumbers) ...[
          const SizedBox(height: 12),
          const Divider(height: 1),
          const SizedBox(height: 6),
          Row(
            children: [
              const Text(
                'Needle Guide',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Container(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    '(showing needles ${KnittingUtils.formatNeedleNumber(firstStitch, needleCount)} to ${KnittingUtils.formatNeedleNumber(lastStitch, needleCount)})',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          _buildDetailedStitchVisualization(firstStitch, lastStitch),
        ],
      ],
    );
  }

  Widget _buildStitchCounter(String count, String description) {
    return Container(
      width: 100,
      height: 50,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            count,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            description,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds detailed visualization of stitches with needle numbers
  Widget _buildDetailedStitchVisualization(int firstStitch, int lastStitch) {
    // Limit the number of needles shown to prevent overflow
    int maxNeedlesToShow = 15;
    int needlesToShow = lastStitch - firstStitch + 1;

    // If too many needles, take a representative sample
    List<int> needlesToRender = [];
    if (needlesToShow > maxNeedlesToShow) {
      // Always include first and last
      needlesToRender.add(firstStitch);

      // Add evenly distributed needles in between
      int step = (lastStitch - firstStitch) ~/ (maxNeedlesToShow - 2);
      for (int i = firstStitch + step; i < lastStitch; i += step) {
        needlesToRender.add(i);
      }

      // Add the last needle
      needlesToRender.add(lastStitch);
    } else {
      // Show all needles if not too many
      for (int i = firstStitch; i <= lastStitch; i++) {
        needlesToRender.add(i);
      }
    }

    return SizedBox(
      height: 50,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              // Add scale/legend for reference
              Container(
                width: 40,
                height: 50,
                color: Colors.grey[100],
                padding: const EdgeInsets.symmetric(horizontal: 4),
                alignment: Alignment.center,
                child: const Text(
                  'Needle:',
                  style: TextStyle(fontSize: 10, fontWeight: FontWeight.bold),
                ),
              ),

              // Create stitch indicators with needle numbers
              if (needlesToShow > maxNeedlesToShow) ...[
                // Show representative needles with "..." for skipped ones
                for (int i = 0; i < needlesToRender.length; i++) ...[
                  _buildNeedleIndicator(needlesToRender[i]),
                  if (i < needlesToRender.length - 1 &&
                      needlesToRender[i + 1] - needlesToRender[i] > 1)
                    Container(
                      width: 28,
                      height: 50,
                      alignment: Alignment.center,
                      color: Colors.grey[50],
                      child: const Text('...', style: TextStyle(fontSize: 10)),
                    ),
                ]
              ] else ...[
                // Show all needles in the range
                for (int i = firstStitch; i <= lastStitch; i++)
                  _buildNeedleIndicator(i),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Build a single needle indicator with number
  Widget _buildNeedleIndicator(int needleIndex) {
    final isStitch = needleIndex < row.length ? row[needleIndex] : false;
    final showNumber = true; // Always show number in compact view

    // Format needle number exactly like InstructionCard does
    String needleLabel =
        KnittingUtils.formatNeedleNumber(needleIndex, needleCount);

    return Container(
      width: 28,
      height: 50,
      decoration: BoxDecoration(
        color: isStitch ? Colors.purple[700] : Colors.grey[100],
        border: Border(
          left: BorderSide(color: Colors.grey[300]!),
          right: needleIndex == row.length - 1
              ? BorderSide(color: Colors.grey[300]!)
              : BorderSide.none,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Stitch indicator
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color:
                  isStitch ? Colors.white.withOpacity(0.8) : Colors.transparent,
            ),
            child: isStitch
                ? const Icon(Icons.circle, size: 12, color: Colors.purple)
                : null,
          ),

          // Needle number
          if (showNumber) ...[
            const SizedBox(height: 2),
            Text(
              needleLabel,
              style: TextStyle(
                fontSize: 9,
                fontWeight: FontWeight.bold,
                color: isStitch ? Colors.white : Colors.black54,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Format a needle index into a user-friendly number using KnittingUtils
  String _formatNeedleNumber(int needleIndex) {
    // Exactly match InstructionCard's call pattern
    return KnittingUtils.formatNeedleNumber(needleIndex, needleCount);
  }
}
