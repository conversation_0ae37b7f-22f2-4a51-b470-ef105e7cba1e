import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:math' as math;
import '../../utils/knitting_utils.dart';

/// An enhanced visualization component for knitting patterns
/// that provides interactive features and clear instructions
class EnhancedPatternVisualizer extends StatefulWidget {
  /// The 2D array of knitting instructions
  /// Each cell represents the presence (true) or absence (false) of a stitch
  final List<List<bool>> pattern;

  /// The total number of needles on the machine
  final int needleCount;

  /// Whether to use L/R notation for needle numbers
  final bool useLRNotation;

  /// Optional callback when a specific row is selected
  final Function(int rowIndex)? onRowSelected;

  /// Optional callback when a specific stitch is tapped
  final Function(int rowIndex, int needleIndex)? onStitchTap;

  const EnhancedPatternVisualizer({
    super.key,
    required this.pattern,
    required this.needleCount,
    this.useLRNotation = true,
    this.onRowSelected,
    this.onStitchTap,
  });

  @override
  State<EnhancedPatternVisualizer> createState() =>
      _EnhancedPatternVisualizerState();
}

class _EnhancedPatternVisualizerState extends State<EnhancedPatternVisualizer> {
  /// Currently selected row for highlighting
  int _selectedRowIndex = -1;

  /// Visualization mode: 'grid' or 'text'
  String _viewMode = 'grid';

  /// Zoom level for the pattern grid
  double _zoomLevel = 1.0;

  /// Processed instructions for display
  late List<KnittingInstruction> _instructions;

  /// Scroll controller for the grid view
  final ScrollController _horizontalScrollController = ScrollController();

  /// Scroll controller for the row list
  final ScrollController _verticalScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _processInstructions();
  }

  @override
  void didUpdateWidget(EnhancedPatternVisualizer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.pattern != widget.pattern) {
      _processInstructions();
    }
  }

  @override
  void dispose() {
    _horizontalScrollController.dispose();
    _verticalScrollController.dispose();
    super.dispose();
  }

  /// Process the raw pattern into user-friendly instructions
  void _processInstructions() {
    _instructions = [];

    if (widget.pattern.isEmpty) return;

    int currentRow = 0;

    while (currentRow < widget.pattern.length) {
      // Check for repeating rows
      final repeatCount =
          KnittingUtils.findRepeatingRows(widget.pattern, currentRow);

      if (repeatCount > 1) {
        // Found repeating rows
        final row = widget.pattern[currentRow];
        final ranges = KnittingUtils.findStitchRanges(row);

        _instructions.add(KnittingInstruction(
          type: InstructionType.repeat,
          rowIndex: currentRow,
          repeatCount: repeatCount,
          stitchRanges: ranges,
          displayText: _generateInstructionText(ranges, repeatCount),
        ));

        currentRow += repeatCount;
      } else {
        // Process a single row
        final row = widget.pattern[currentRow];
        final ranges = KnittingUtils.findStitchRanges(row);
        final hasDiscontinuous = ranges.length > 1;

        _instructions.add(KnittingInstruction(
          type: hasDiscontinuous
              ? InstructionType.discontinuous
              : InstructionType.single,
          rowIndex: currentRow,
          repeatCount: 1,
          stitchRanges: ranges,
          displayText: _generateInstructionText(ranges, 1),
          warnings: _generateWarnings(row, ranges),
        ));

        currentRow++;
      }
    }
  }

  /// Generate instruction text for a set of stitch ranges
  String _generateInstructionText(List<StitchRange> ranges, int repeatCount) {
    final rangesText = KnittingUtils.formatRangesText(
        ranges, widget.needleCount,
        useLRNotation: widget.useLRNotation);

    if (repeatCount > 1) {
      return 'knittingInstructions_interactive_repeatProgress'.trParams({
        'current': '1',
        'total': repeatCount.toString(),
      });
    }

    return rangesText;
  }

  /// Generate warnings for a row (wide gaps, etc.)
  List<String> _generateWarnings(List<bool> row, List<StitchRange> ranges) {
    final List<String> warnings = [];

    // Check for unusually wide gaps between stitches
    if (ranges.length > 1) {
      for (int i = 0; i < ranges.length - 1; i++) {
        final gapSize = ranges[i + 1].startNeedle - ranges[i].endNeedle - 1;
        if (gapSize > 10) {
          // Threshold for "wide" gap
          final startNeedle = KnittingUtils.formatNeedleNumber(
              ranges[i].endNeedle, widget.needleCount,
              useLRNotation: widget.useLRNotation);
          final endNeedle = KnittingUtils.formatNeedleNumber(
              ranges[i + 1].startNeedle, widget.needleCount,
              useLRNotation: widget.useLRNotation);
          warnings
              .add('knittingInstructions_interactive_patternHelp_wideGaps'.tr);
        }
      }
    }

    return warnings;
  }

  /// Check if a row has an increase/decrease compared to previous row
  bool _hasIncreaseOrDecrease(int rowIndex) {
    if (rowIndex <= 0 || rowIndex >= widget.pattern.length) return false;

    final currentRow = widget.pattern[rowIndex];
    final previousRow = widget.pattern[rowIndex - 1];

    // Count stitches in both rows
    int currentStitches = 0;
    int previousStitches = 0;

    for (int i = 0; i < currentRow.length && i < previousRow.length; i++) {
      if (currentRow[i]) currentStitches++;
      if (previousRow[i]) previousStitches++;
    }

    return currentStitches != previousStitches;
  }

  /// Check if a pattern row has symmetry
  bool _hasSymmetry(List<bool> row) {
    // Find first and last stitch
    int first = 0;
    while (first < row.length && !row[first]) first++;

    int last = row.length - 1;
    while (last >= 0 && !row[last]) last--;

    if (first >= last) return true; // Empty or single stitch is symmetric

    // Check if pattern is symmetric around the middle
    final middle = (first + last) ~/ 2;
    for (int i = first; i <= middle; i++) {
      final mirrorPos = last - (i - first);
      if (row[i] != row[mirrorPos]) return false;
    }

    return true;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.pattern.isEmpty) {
      return Center(
        child:
            Text('knittingInstructions_interactive_noInstructionsAvailable'.tr),
      );
    }

    return Column(
      children: [
        // Control bar - zoom and view mode toggle
        _buildControlBar(),

        // Pattern visualization
        Expanded(
          child: _viewMode == 'grid' ? _buildGridView() : _buildTextView(),
        ),
      ],
    );
  }

  /// Build the control bar with zoom and view mode options
  Widget _buildControlBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        children: [
          // View mode toggle
          ToggleButtons(
            isSelected: [_viewMode == 'grid', _viewMode == 'text'],
            onPressed: (index) {
              setState(() {
                _viewMode = index == 0 ? 'grid' : 'text';
              });
            },
            borderRadius: BorderRadius.circular(8),
            children: const [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: Icon(Icons.grid_on),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: Icon(Icons.text_fields),
              ),
            ],
          ),

          const Spacer(),

          // Zoom controls (only for grid view)
          if (_viewMode == 'grid') ...[
            IconButton(
              icon: const Icon(Icons.zoom_out),
              onPressed: _zoomLevel > 0.5
                  ? () => setState(() => _zoomLevel -= 0.25)
                  : null,
            ),
            Text('${(_zoomLevel * 100).round()}%',
                style: const TextStyle(fontWeight: FontWeight.bold)),
            IconButton(
              icon: const Icon(Icons.zoom_in),
              onPressed: _zoomLevel < 3.0
                  ? () => setState(() => _zoomLevel += 0.25)
                  : null,
            ),
          ],
        ],
      ),
    );
  }

  /// Build the grid visualization of the pattern
  Widget _buildGridView() {
    // Calculate cell size based on zoom level
    final cellSize = 20.0 * _zoomLevel;

    // Find the maximum width needed for all rows
    int maxNeedles = 0;
    for (final row in widget.pattern) {
      maxNeedles = math.max(maxNeedles, row.length);
    }

    // Calculate content width for proper sizing
    final contentWidth = 40 + (maxNeedles * cellSize);

    return Column(
      children: [
        // Main grid content with a single horizontal scroll view
        Expanded(
          child: Scrollbar(
            controller: _verticalScrollController,
            thumbVisibility: true,
            thickness: 6,
            child: SingleChildScrollView(
              controller: _horizontalScrollController,
              scrollDirection: Axis.horizontal,
              physics: const ClampingScrollPhysics(),
              child: SizedBox(
                width: contentWidth,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Needle numbers header
                    SizedBox(
                      height: 20,
                      child: Row(
                        children: [
                          // Empty space for row number column
                          Container(width: 40),

                          // Needle numbers
                          for (int i = 0; i < maxNeedles; i++)
                            if (i % 10 == 0)
                              Container(
                                width: cellSize,
                                height: 20,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade200,
                                  border: Border.all(
                                      color: Colors.grey.shade300, width: 0.5),
                                ),
                                child: Text(
                                  KnittingUtils.formatNeedleNumber(
                                    i,
                                    widget.needleCount,
                                    useLRNotation: widget.useLRNotation,
                                  ),
                                  style: TextStyle(
                                    fontSize: 10 * _zoomLevel,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              )
                            else
                              Container(
                                width: cellSize,
                                height: 20,
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade200,
                                  border: Border.all(
                                      color: Colors.grey.shade300, width: 0.5),
                                ),
                              ),
                        ],
                      ),
                    ),

                    // Pattern rows
                    Expanded(
                      child: ListView.builder(
                        key: const PageStorageKey('vertical_pattern_scroll'),
                        controller: _verticalScrollController,
                        physics: const ClampingScrollPhysics(),
                        itemCount: widget.pattern.length,
                        itemBuilder: (context, rowIndex) {
                          final rowNumber =
                              rowIndex + 1; // 1-based row numbering
                          final row = widget.pattern[rowIndex];

                          // Find instruction for this row
                          KnittingInstruction? instruction =
                              _findInstructionForRow(rowIndex);

                          // Check for increases/decreases
                          final hasChange = _hasIncreaseOrDecrease(rowIndex);

                          // Check for symmetry
                          final isSymmetric = _hasSymmetry(row);

                          return GestureDetector(
                            onTap: () {
                              setState(() {
                                _selectedRowIndex = rowIndex;
                              });
                              widget.onRowSelected?.call(rowIndex);
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                color: _selectedRowIndex == rowIndex
                                    ? Colors.purple.withOpacity(0.1)
                                    : Colors.transparent,
                                border: Border(
                                  bottom:
                                      BorderSide(color: Colors.grey.shade300),
                                ),
                              ),
                              padding: const EdgeInsets.symmetric(vertical: 4),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Row header with row number and indicators
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8),
                                    child: Row(
                                      children: [
                                        // Row number indicator
                                        Container(
                                          width: 40,
                                          height: 24,
                                          alignment: Alignment.center,
                                          decoration: BoxDecoration(
                                            color: Colors.purple,
                                            borderRadius:
                                                BorderRadius.circular(12),
                                          ),
                                          child: Text(
                                            '$rowNumber',
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),

                                        const SizedBox(width: 8),

                                        // Repeat indicator if applicable
                                        if (instruction?.type ==
                                            InstructionType.repeat)
                                          Container(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 8, vertical: 2),
                                            decoration: BoxDecoration(
                                              color:
                                                  Colors.amber.withOpacity(0.2),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                              border: Border.all(
                                                  color: Colors.amber),
                                            ),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                const Icon(Icons.repeat,
                                                    size: 14,
                                                    color: Colors.amber),
                                                const SizedBox(width: 4),
                                                Text(
                                                  '${instruction?.repeatCount}x',
                                                  style: const TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.amber,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),

                                        // Discontinuous indicator if applicable
                                        if (instruction?.type ==
                                            InstructionType.discontinuous)
                                          Container(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 8, vertical: 2),
                                            decoration: BoxDecoration(
                                              color: Colors.orange
                                                  .withOpacity(0.2),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                              border: Border.all(
                                                  color: Colors.orange),
                                            ),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                const Icon(Icons.warning,
                                                    size: 14,
                                                    color: Colors.orange),
                                                const SizedBox(width: 4),
                                                Text(
                                                  'knittingInstructions_interactive_patternHelp_discontinuousRows'
                                                      .tr,
                                                  style: const TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.orange,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),

                                        // Increase/decrease indicator
                                        if (hasChange)
                                          Container(
                                            margin:
                                                const EdgeInsets.only(left: 4),
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 8, vertical: 2),
                                            decoration: BoxDecoration(
                                              color:
                                                  Colors.blue.withOpacity(0.2),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                              border: Border.all(
                                                  color: Colors.blue),
                                            ),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                const Icon(Icons.trending_up,
                                                    size: 14,
                                                    color: Colors.blue),
                                                const SizedBox(width: 4),
                                                Text(
                                                  'knittingInstructions_interactive_patternHelp_changeRows'
                                                      .tr,
                                                  style: const TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.blue,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),

                                        // Symmetry indicator
                                        if (isSymmetric)
                                          Container(
                                            margin:
                                                const EdgeInsets.only(left: 4),
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 8, vertical: 2),
                                            decoration: BoxDecoration(
                                              color:
                                                  Colors.green.withOpacity(0.2),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                              border: Border.all(
                                                  color: Colors.green),
                                            ),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                const Icon(Icons.compare_arrows,
                                                    size: 14,
                                                    color: Colors.green),
                                                const SizedBox(width: 4),
                                                Text(
                                                  'knittingInstructions_interactive_patternHelp_symmetricRows'
                                                      .tr,
                                                  style: const TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.green,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),

                                  const SizedBox(height: 4),

                                  // Grid of stitches
                                  SizedBox(
                                    height: cellSize,
                                    child: Row(
                                      children: [
                                        // Row number column
                                        Container(
                                          width: 40,
                                          height: cellSize,
                                          alignment: Alignment.center,
                                          color: Colors.grey.shade200,
                                          child: Text(
                                            '$rowNumber',
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),

                                        // Stitch columns
                                        for (int needleIndex = 0;
                                            needleIndex < row.length;
                                            needleIndex++)
                                          GestureDetector(
                                            onTap: () {
                                              widget.onStitchTap
                                                  ?.call(rowIndex, needleIndex);
                                            },
                                            child: Container(
                                              width: cellSize,
                                              height: cellSize,
                                              decoration: BoxDecoration(
                                                color: row[needleIndex]
                                                    ? Colors.purple.shade700
                                                    : Colors.grey.shade100,
                                                border: Border.all(
                                                  color: Colors.grey.shade300,
                                                  width: 0.5,
                                                ),
                                              ),
                                              alignment: Alignment.center,
                                              child: needleIndex % 10 == 0
                                                  ? Text(
                                                      KnittingUtils
                                                          .formatNeedleNumber(
                                                        needleIndex,
                                                        widget.needleCount,
                                                        useLRNotation: widget
                                                            .useLRNotation,
                                                      ),
                                                      style: TextStyle(
                                                        fontSize:
                                                            10 * _zoomLevel,
                                                        color: row[needleIndex]
                                                            ? Colors.white
                                                            : Colors.black,
                                                      ),
                                                    )
                                                  : null,
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),

                                  // Show warnings if selected and applicable
                                  if (_selectedRowIndex == rowIndex &&
                                      instruction != null &&
                                      instruction.warnings.isNotEmpty)
                                    Padding(
                                      padding: const EdgeInsets.all(8),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          for (final warning
                                              in instruction.warnings)
                                            Row(
                                              children: [
                                                const Icon(Icons.warning_amber,
                                                    size: 16,
                                                    color: Colors.orange),
                                                const SizedBox(width: 4),
                                                Expanded(
                                                  child: Text(
                                                    warning,
                                                    style: const TextStyle(
                                                      fontSize: 12,
                                                      color: Colors.orange,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                        ],
                                      ),
                                    ),

                                  // Show detailed instructions if selected
                                  if (_selectedRowIndex == rowIndex &&
                                      instruction != null)
                                    Padding(
                                      padding: const EdgeInsets.all(8),
                                      child: Text(
                                        instruction.displayText,
                                        style: const TextStyle(
                                            fontWeight: FontWeight.bold),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Build the text-based view of instructions
  Widget _buildTextView() {
    return ListView.builder(
      key: const PageStorageKey('text_pattern_view'),
      controller: _verticalScrollController,
      physics: const ClampingScrollPhysics(),
      itemCount: _instructions.length,
      itemBuilder: (context, index) {
        final instruction = _instructions[index];

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          elevation:
              _findInstructionIndex(instruction.rowIndex) == index ? 4 : 1,
          child: ListTile(
            selected: _findInstructionIndex(instruction.rowIndex) == index,
            selectedTileColor: Colors.purple.withOpacity(0.1),
            onTap: () {
              setState(() {
                _selectedRowIndex = instruction.rowIndex;
              });
              widget.onRowSelected?.call(instruction.rowIndex);
            },
            title: Text(
              _getInstructionTitle(instruction),
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 8),
                Text(instruction.displayText),

                // Show warnings if any
                if (instruction.warnings.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  const Divider(),
                  for (final warning in instruction.warnings)
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Row(
                        children: [
                          const Icon(Icons.warning_amber,
                              size: 16, color: Colors.orange),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              warning,
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.orange,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ],
            ),
            isThreeLine: instruction.warnings.isNotEmpty,
            leading: CircleAvatar(
              backgroundColor: _getInstructionColor(instruction.type),
              child: _getInstructionIcon(instruction.type),
            ),
            trailing: instruction.type == InstructionType.repeat
                ? Chip(
                    label: Text('${instruction.repeatCount}x'),
                    backgroundColor: Colors.amber.withOpacity(0.2),
                  )
                : null,
          ),
        );
      },
    );
  }

  /// Find the instruction object for a specific row
  KnittingInstruction? _findInstructionForRow(int rowIndex) {
    for (final instruction in _instructions) {
      if (instruction.type == InstructionType.repeat) {
        // Check if rowIndex falls within repeat range
        if (rowIndex >= instruction.rowIndex &&
            rowIndex < instruction.rowIndex + instruction.repeatCount) {
          return instruction;
        }
      } else if (instruction.rowIndex == rowIndex) {
        return instruction;
      }
    }
    return null;
  }

  /// Find the index of an instruction based on row index
  int _findInstructionIndex(int rowIndex) {
    for (int i = 0; i < _instructions.length; i++) {
      final instruction = _instructions[i];
      if (instruction.type == InstructionType.repeat) {
        if (rowIndex >= instruction.rowIndex &&
            rowIndex < instruction.rowIndex + instruction.repeatCount) {
          return i;
        }
      } else if (instruction.rowIndex == rowIndex) {
        return i;
      }
    }
    return -1;
  }

  /// Get a title for an instruction
  String _getInstructionTitle(KnittingInstruction instruction) {
    if (instruction.type == InstructionType.repeat) {
      final startRow = instruction.rowIndex + 1; // 1-indexed
      final endRow = startRow + instruction.repeatCount - 1;
      return 'knittingInstructions_interactive_rowsInstructions'.trParams({
        'start': startRow.toString(),
        'end': endRow.toString(),
      });
    } else {
      return 'knittingInstructions_interactive_rowInstructions'.trParams({
        'row': (instruction.rowIndex + 1).toString(),
      });
    }
  }

  /// Get an appropriate color for the instruction type
  Color _getInstructionColor(InstructionType type) {
    switch (type) {
      case InstructionType.single:
        return Colors.blue;
      case InstructionType.repeat:
        return Colors.amber;
      case InstructionType.discontinuous:
        return Colors.orange;
    }
  }

  /// Get an appropriate icon for the instruction type
  Widget _getInstructionIcon(InstructionType type) {
    switch (type) {
      case InstructionType.single:
        return const Icon(Icons.straighten, color: Colors.white);
      case InstructionType.repeat:
        return const Icon(Icons.repeat, color: Colors.white);
      case InstructionType.discontinuous:
        return const Icon(Icons.warning, color: Colors.white);
    }
  }
}

/// Enum representing types of knitting instructions
enum InstructionType {
  single,
  repeat,
  discontinuous,
}

/// Class representing a processed knitting instruction
class KnittingInstruction {
  final InstructionType type;
  final int rowIndex;
  final int repeatCount;
  final List<StitchRange> stitchRanges;
  final String displayText;
  final List<String> warnings;

  KnittingInstruction({
    required this.type,
    required this.rowIndex,
    required this.repeatCount,
    required this.stitchRanges,
    required this.displayText,
    this.warnings = const [],
  });
}
