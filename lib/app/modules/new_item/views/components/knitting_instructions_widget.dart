import 'package:flutter/material.dart';

/// A widget that displays interactive knitting instructions
/// based on a 2D boolean array representation of stitches
class InteractiveKnittingView extends StatefulWidget {
  /// The 2D array of knitting instructions
  /// Each cell represents the presence (true) or absence (false) of a stitch
  final List<List<bool>> stitchPattern;

  /// The total number of needles on the machine
  final int needleCount;

  /// Whether to display needle numbers with L/R notation
  /// L for left of center, R for right of center
  final bool useLRNotation;

  /// Optional callback when user taps on a row or stitch
  final Function(int row, int needle)? onStitchTap;

  const InteractiveKnittingView({
    super.key,
    required this.stitchPattern,
    required this.needleCount,
    this.useLRNotation = true,
    this.onStitchTap,
  });

  @override
  State<InteractiveKnittingView> createState() =>
      _InteractiveKnittingViewState();
}

class _InteractiveKnittingViewState extends State<InteractiveKnittingView> {
  /// Store the processed instructions for display
  late List<KnittingInstruction> _instructions;

  /// Currently selected row (for highlighting)
  int _selectedRow = -1;

  @override
  void initState() {
    super.initState();
    _processInstructions();
  }

  @override
  void didUpdateWidget(InteractiveKnittingView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.stitchPattern != widget.stitchPattern) {
      _processInstructions();
    }
  }

  /// Process the raw stitch pattern into user-friendly instructions
  void _processInstructions() {
    _instructions = KnittingInstructionsProcessor.processPattern(
      widget.stitchPattern,
      widget.needleCount,
      widget.useLRNotation,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Instructions header
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            'Knitting Instructions',
            style: Theme.of(context).textTheme.titleLarge,
          ),
        ),

        // The actual instruction list
        Expanded(
          child: _instructions.isEmpty
              ? const Center(child: Text('No instructions available'))
              : ListView.builder(
                  itemCount: _instructions.length,
                  itemBuilder: (context, index) {
                    final instruction = _instructions[index];
                    return _buildInstructionRow(context, instruction, index);
                  },
                ),
        ),
      ],
    );
  }

  /// Build a single instruction row with appropriate styling and interaction
  Widget _buildInstructionRow(
      BuildContext context, KnittingInstruction instruction, int index) {
    final isSelected = index == _selectedRow;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedRow = isSelected ? -1 : index;
        });
        // If this is a repeat instruction, expand to show details
        if (instruction.type == InstructionType.repeat) {
          _showRepeatDetails(context, instruction);
        }
      },
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
        elevation: isSelected ? 4.0 : 1.0,
        color:
            isSelected ? Theme.of(context).primaryColor.withOpacity(0.1) : null,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Row number indicator
              Text(
                _getRowLabel(instruction),
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),

              // The actual instruction text
              Text(instruction.displayText),

              // Visual stitch pattern representation
              if (isSelected || instruction.type != InstructionType.repeat)
                _buildStitchVisualization(instruction),
            ],
          ),
        ),
      ),
    );
  }

  /// Generate a human-readable row label
  String _getRowLabel(KnittingInstruction instruction) {
    switch (instruction.type) {
      case InstructionType.single:
        return 'Row ${instruction.startRow + 1}';
      case InstructionType.repeat:
        final startRow = instruction.startRow + 1;
        final endRow = instruction.endRow + 1;
        return 'Rows $startRow to $endRow (${endRow - startRow + 1} rows)';
      case InstructionType.discontinuous:
        return 'Row ${instruction.startRow + 1}';
    }
  }

  /// Build a visual representation of the stitches in this instruction
  Widget _buildStitchVisualization(KnittingInstruction instruction) {
    // For repeat instructions, just show the first row pattern when selected
    final pattern = instruction.type == InstructionType.repeat
        ? widget.stitchPattern[instruction.startRow]
        : widget.stitchPattern[instruction.startRow];

    return Container(
      margin: const EdgeInsets.only(top: 16),
      height: 30,
      child: Row(
        children: [
          // Show a row label
          const SizedBox(width: 60, child: Text('Pattern:')),

          // Visualize the stitch pattern
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: List.generate(pattern.length, (needleIndex) {
                  return GestureDetector(
                    onTap: () {
                      widget.onStitchTap
                          ?.call(instruction.startRow, needleIndex);
                    },
                    child: Container(
                      width: 15,
                      height: 30,
                      margin: const EdgeInsets.symmetric(horizontal: 1),
                      decoration: BoxDecoration(
                        color: pattern[needleIndex]
                            ? Colors.blue.shade700
                            : Colors.grey.shade200,
                        border: Border.all(
                          color: Colors.grey.shade400,
                          width: 0.5,
                        ),
                      ),
                      child: needleIndex % 10 == 0
                          ? Center(
                              child: Text(
                                _formatNeedleNumber(needleIndex),
                                style: TextStyle(
                                  fontSize: 8,
                                  color: pattern[needleIndex]
                                      ? Colors.white
                                      : Colors.black,
                                ),
                              ),
                            )
                          : null,
                    ),
                  );
                }),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Convert a needle index to a user-friendly needle number
  String _formatNeedleNumber(int needleIndex) {
    if (!widget.useLRNotation) return needleIndex.toString();

    // Calculate center needle for L/R notation
    final centerNeedle = widget.needleCount ~/ 2;
    if (needleIndex == centerNeedle) return 'C';

    if (needleIndex < centerNeedle) {
      return 'L${centerNeedle - needleIndex}';
    } else {
      return 'R${needleIndex - centerNeedle}';
    }
  }

  /// Show detailed view for a repeat instruction
  void _showRepeatDetails(
      BuildContext context, KnittingInstruction instruction) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
            'Repeat for Rows ${instruction.startRow + 1}-${instruction.endRow + 1}'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView(
            shrinkWrap: true,
            children: [
              Text(
                  'Knit the following pattern for ${instruction.endRow - instruction.startRow + 1} rows:'),
              const SizedBox(height: 16),
              Text(instruction.displayText),
              const SizedBox(height: 16),
              // Show a single row of the repeat pattern
              _buildStitchVisualization(instruction),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

/// Types of knitting instructions
enum InstructionType {
  /// A single row with its own pattern
  single,

  /// Multiple identical rows that can be repeated
  repeat,

  /// A row with discontinuous stitches
  discontinuous,
}

/// Represents a processed knitting instruction
class KnittingInstruction {
  /// The type of instruction
  final InstructionType type;

  /// The starting row in the pattern (0-based)
  final int startRow;

  /// The ending row in the pattern (for repeats, 0-based)
  final int endRow;

  /// Human-readable instruction text
  final String displayText;

  /// For discontinuous instructions, contains the ranges of stitches
  final List<StitchRange>? stitchRanges;

  KnittingInstruction({
    required this.type,
    required this.startRow,
    required this.displayText,
    this.endRow = 0,
    this.stitchRanges,
  });
}

/// Represents a range of continuous stitches
class StitchRange {
  final int startNeedle;
  final int endNeedle;

  StitchRange(this.startNeedle, this.endNeedle);
}

/// Static utility class to process knitting patterns into instructions
class KnittingInstructionsProcessor {
  /// Process a 2D boolean array of stitches into user-friendly instructions
  static List<KnittingInstruction> processPattern(
    List<List<bool>> pattern,
    int needleCount,
    bool useLRNotation,
  ) {
    final List<KnittingInstruction> instructions = [];
    if (pattern.isEmpty) return instructions;

    int currentRow = 0;

    while (currentRow < pattern.length) {
      // Check for repeating rows
      final repeatLength = _findRepeatingRows(pattern, currentRow);

      if (repeatLength > 1) {
        // Found repeating rows
        instructions.add(_createRepeatInstruction(pattern, currentRow,
            currentRow + repeatLength - 1, needleCount, useLRNotation));
        currentRow += repeatLength;
      } else {
        // Check if row has discontinuous stitches
        final ranges = _findStitchRanges(pattern[currentRow]);

        if (ranges.length > 1) {
          // Discontinuous stitches on this row
          instructions.add(_createDiscontinuousInstruction(
            pattern,
            currentRow,
            ranges,
            needleCount,
            useLRNotation,
          ));
        } else {
          // Simple single row
          instructions.add(_createSingleRowInstruction(
            pattern,
            currentRow,
            needleCount,
            useLRNotation,
          ));
        }
        currentRow++;
      }
    }

    return instructions;
  }

  /// Find the number of consecutive rows that are identical
  static int _findRepeatingRows(List<List<bool>> pattern, int startRow) {
    int repeatCount = 1;
    final baseRow = pattern[startRow];

    for (int i = startRow + 1; i < pattern.length; i++) {
      if (_rowsAreEqual(baseRow, pattern[i])) {
        repeatCount++;
      } else {
        break;
      }
    }

    // Only return as a repeat if there are at least 2 identical rows
    return repeatCount;
  }

  /// Check if two rows have identical stitch patterns
  static bool _rowsAreEqual(List<bool> row1, List<bool> row2) {
    if (row1.length != row2.length) return false;

    for (int i = 0; i < row1.length; i++) {
      if (row1[i] != row2[i]) return false;
    }

    return true;
  }

  /// Find ranges of continuous stitches in a row
  static List<StitchRange> _findStitchRanges(List<bool> row) {
    final List<StitchRange> ranges = [];

    int? startNeedle;

    for (int i = 0; i < row.length; i++) {
      if (row[i]) {
        // Stitch exists at this position
        if (startNeedle == null) {
          // Start of a new range
          startNeedle = i;
        }
      } else {
        // No stitch here
        if (startNeedle != null) {
          // End of a range
          ranges.add(StitchRange(startNeedle, i - 1));
          startNeedle = null;
        }
      }
    }

    // Handle case where range extends to the end of the row
    if (startNeedle != null) {
      ranges.add(StitchRange(startNeedle, row.length - 1));
    }

    return ranges;
  }

  /// Create an instruction for repeating rows
  static KnittingInstruction _createRepeatInstruction(
    List<List<bool>> pattern,
    int startRow,
    int endRow,
    int needleCount,
    bool useLRNotation,
  ) {
    final ranges = _findStitchRanges(pattern[startRow]);
    final displayText = _formatRangesText(ranges, needleCount, useLRNotation);

    return KnittingInstruction(
      type: InstructionType.repeat,
      startRow: startRow,
      endRow: endRow,
      displayText: 'Repeat for ${endRow - startRow + 1} rows: $displayText',
    );
  }

  /// Create an instruction for a row with discontinuous stitches
  static KnittingInstruction _createDiscontinuousInstruction(
    List<List<bool>> pattern,
    int row,
    List<StitchRange> ranges,
    int needleCount,
    bool useLRNotation,
  ) {
    final displayText = _formatRangesText(ranges, needleCount, useLRNotation);

    return KnittingInstruction(
      type: InstructionType.discontinuous,
      startRow: row,
      displayText: displayText,
      stitchRanges: ranges,
    );
  }

  /// Create an instruction for a simple single row
  static KnittingInstruction _createSingleRowInstruction(
    List<List<bool>> pattern,
    int row,
    int needleCount,
    bool useLRNotation,
  ) {
    final ranges = _findStitchRanges(pattern[row]);
    final displayText = _formatRangesText(ranges, needleCount, useLRNotation);

    return KnittingInstruction(
      type: InstructionType.single,
      startRow: row,
      displayText: displayText,
    );
  }

  /// Format a list of stitch ranges into human-readable text
  static String _formatRangesText(
    List<StitchRange> ranges,
    int needleCount,
    bool useLRNotation,
  ) {
    if (ranges.isEmpty) return "No stitches on this row";

    final List<String> rangeTexts = [];

    for (final range in ranges) {
      final startNeedle =
          _formatNeedleNumber(range.startNeedle, needleCount, useLRNotation);
      final endNeedle =
          _formatNeedleNumber(range.endNeedle, needleCount, useLRNotation);

      if (range.startNeedle == range.endNeedle) {
        rangeTexts.add("Knit needle $startNeedle");
      } else {
        rangeTexts.add("Knit needles $startNeedle to $endNeedle");
      }
    }

    return rangeTexts.join(", then ");
  }

  /// Format a needle index into a user-friendly number
  static String _formatNeedleNumber(
      int needleIndex, int needleCount, bool useLRNotation) {
    if (!useLRNotation) return needleIndex.toString();

    // Calculate center needle for L/R notation
    final centerNeedle = needleCount ~/ 2;
    if (needleIndex == centerNeedle) return 'C';

    if (needleIndex < centerNeedle) {
      return 'L${centerNeedle - needleIndex}';
    } else {
      return 'R${needleIndex - centerNeedle}';
    }
  }
}
