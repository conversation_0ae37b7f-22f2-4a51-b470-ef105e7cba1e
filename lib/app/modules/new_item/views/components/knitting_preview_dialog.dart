import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:math' as math;
import 'package:xoxknit/app/core/theme/app_colors.dart';
import 'knitting_grid_painter.dart';
import 'knitted_preview_painter.dart';
import 'stat_item.dart';
import '../../controllers/new_item_wizard_controller.dart';

/// A dialog that shows a preview of the knitted piece
class KnittingPreviewDialog extends StatefulWidget {
  final NewItemWizardController controller;
  final List<List<bool>> instructions;

  const KnittingPreviewDialog({
    super.key,
    required this.controller,
    required this.instructions,
  });

  @override
  State<KnittingPreviewDialog> createState() => _KnittingPreviewDialogState();
}

class _KnittingPreviewDialogState extends State<KnittingPreviewDialog>
    with SingleTickerProviderStateMixin {
  late TransformationController transformationController;
  late AnimationController _animationController;
  Animation<Matrix4>? _animation;
  late double initialScale;
  bool showGrid = true;
  bool showYarnConnections = true;
  Color stitchColor = const Color(0xFFD81B60); // Default magenta
  int rowLabelInterval = 5;
  int columnLabelInterval = 5;

  // Pattern dimension calculations
  late int maxWidth;
  late int usedNeedles;
  late int minNeedle;
  late int maxNeedle;
  late int totalRows;
  late double gridSize;

  @override
  void initState() {
    super.initState();
    transformationController = TransformationController();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // Calculate pattern dimensions and statistics
    _calculatePatternStats();
  }

  void _calculatePatternStats() {
    maxWidth = 0;
    usedNeedles = 0;
    minNeedle = widget.instructions[0].length;
    maxNeedle = 0;

    for (final row in widget.instructions) {
      int rowWidth = 0;
      for (int i = 0; i < row.length; i++) {
        if (row[i]) {
          rowWidth++;
          minNeedle = math.min(minNeedle, i);
          maxNeedle = math.max(maxNeedle, i);
        }
      }
      maxWidth = math.max(maxWidth, rowWidth);
    }

    usedNeedles = maxNeedle - minNeedle + 1;
    totalRows = widget.instructions.length;
  }

  void _calculateInitialScale() {
    // Calculate the optimal initial transformation for the view
    final screenWidth = MediaQuery.of(context).size.width * 0.85;
    final screenHeight = MediaQuery.of(context).size.height * 0.65;

    // Calculate a grid size that fits the pattern on screen
    gridSize = math.min(
        screenWidth / (maxNeedle + 4), // +4 for some padding
        screenHeight / (totalRows + 4));

    // Safety limits
    gridSize = math.max(6.0, math.min(gridSize, 20.0));

    // Calculate content dimensions
    final contentWidth =
        widget.instructions[0].length * gridSize + 92; // Add padding
    final contentHeight = widget.instructions.length * gridSize + 92;

    // Calculate optimal scale factors
    final scaleX = screenWidth / contentWidth;
    final scaleY = screenHeight / contentHeight;
    initialScale = math.min(scaleX, scaleY) * 0.95; // 95% to add padding

    // Center the pattern
    final dx = (screenWidth - (contentWidth * initialScale)) / 2;
    final dy = (screenHeight - (contentHeight * initialScale)) / 2;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Apply the initial transformation
      transformationController.value = Matrix4.identity()
        ..translate(dx, dy)
        ..scale(initialScale);
    });
  }

  // Method to smoothly zoom to a specified scale
  void _animateScale(double targetScale) {
    _animationController.reset();
    final Matrix4 endMatrix = Matrix4.identity()..scale(targetScale);
    _animation = Matrix4Tween(
      begin: transformationController.value,
      end: endMatrix,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.addListener(() {
      transformationController.value = _animation!.value;
    });

    _animationController.forward();
  }

  // Reset to the initial view
  void _resetView() {
    _calculateInitialScale();
  }

  @override
  void dispose() {
    transformationController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _calculateInitialScale();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog.fullscreen(
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Knitting Pattern Preview'),
          actions: [
            // Zoom controls with better UI
            IconButton(
              icon: const Icon(Icons.zoom_in),
              onPressed: () => _animateScale(
                  transformationController.value.getMaxScaleOnAxis() * 1.5),
              tooltip: 'Zoom In',
            ),
            IconButton(
              icon: const Icon(Icons.zoom_out),
              onPressed: () => _animateScale(
                  transformationController.value.getMaxScaleOnAxis() / 1.5),
              tooltip: 'Zoom Out',
            ),
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _resetView,
              tooltip: 'Reset View',
            ),
            PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert),
              tooltip: 'More Options',
              onSelected: (value) {
                switch (value) {
                  case 'toggle_grid':
                    setState(() {
                      showGrid = !showGrid;
                    });
                    break;
                  case 'toggle_yarn':
                    setState(() {
                      showYarnConnections = !showYarnConnections;
                    });
                    break;
                  case 'color_magenta':
                    setState(() {
                      stitchColor = const Color(0xFFD81B60);
                    });
                    break;
                  case 'color_blue':
                    setState(() {
                      stitchColor = const Color(0xFF1976D2);
                    });
                    break;
                  case 'color_green':
                    setState(() {
                      stitchColor = const Color(0xFF388E3C);
                    });
                    break;
                  case 'labels_dense':
                    setState(() {
                      rowLabelInterval = 1;
                      columnLabelInterval = 1;
                    });
                    break;
                  case 'labels_normal':
                    setState(() {
                      rowLabelInterval = 5;
                      columnLabelInterval = 5;
                    });
                    break;
                  case 'labels_sparse':
                    setState(() {
                      rowLabelInterval = 10;
                      columnLabelInterval = 10;
                    });
                    break;
                  case 'save':
                    Get.snackbar(
                      'Save Pattern',
                      'Pattern saving will be implemented in a future update',
                      snackPosition: SnackPosition.BOTTOM,
                    );
                    break;
                  case 'print':
                    Get.snackbar(
                      'Print Instructions',
                      'Pattern printing will be implemented in a future update',
                      snackPosition: SnackPosition.BOTTOM,
                    );
                    break;
                }
              },
              itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
                CheckedPopupMenuItem<String>(
                  value: 'toggle_grid',
                  checked: showGrid,
                  child: const Text('Show Grid'),
                ),
                CheckedPopupMenuItem<String>(
                  value: 'toggle_yarn',
                  checked: showYarnConnections,
                  child: const Text('Show Yarn Connections'),
                ),
                const PopupMenuDivider(),
                const PopupMenuItem<String>(
                  value: 'color_magenta',
                  child: Row(
                    children: [
                      Icon(Icons.circle, color: Color(0xFFD81B60), size: 16),
                      SizedBox(width: 10),
                      Text('Magenta Stitches'),
                    ],
                  ),
                ),
                const PopupMenuItem<String>(
                  value: 'color_blue',
                  child: Row(
                    children: [
                      Icon(Icons.circle, color: Color(0xFF1976D2), size: 16),
                      SizedBox(width: 10),
                      Text('Blue Stitches'),
                    ],
                  ),
                ),
                const PopupMenuItem<String>(
                  value: 'color_green',
                  child: Row(
                    children: [
                      Icon(Icons.circle, color: Color(0xFF388E3C), size: 16),
                      SizedBox(width: 10),
                      Text('Green Stitches'),
                    ],
                  ),
                ),
                const PopupMenuDivider(),
                PopupMenuItem<String>(
                  value: 'labels_normal',
                  child: Row(
                    children: [
                      Icon(
                        Icons.grid_on,
                        color:
                            rowLabelInterval == 5 ? Colors.blue : Colors.grey,
                        size: 16,
                      ),
                      const SizedBox(width: 10),
                      const Text('Normal Labels (every 5)'),
                    ],
                  ),
                ),
                PopupMenuItem<String>(
                  value: 'labels_sparse',
                  child: Row(
                    children: [
                      Icon(
                        Icons.grid_4x4,
                        color:
                            rowLabelInterval == 10 ? Colors.blue : Colors.grey,
                        size: 16,
                      ),
                      const SizedBox(width: 10),
                      const Text('Sparse Labels (every 10)'),
                    ],
                  ),
                ),
                PopupMenuItem<String>(
                  value: 'labels_dense',
                  child: Row(
                    children: [
                      Icon(
                        Icons.grid_3x3,
                        color:
                            rowLabelInterval == 1 ? Colors.blue : Colors.grey,
                        size: 16,
                      ),
                      const SizedBox(width: 10),
                      const Text('All Labels (every row)'),
                    ],
                  ),
                ),
                const PopupMenuDivider(),
                const PopupMenuItem<String>(
                  value: 'save',
                  child: Row(
                    children: [
                      Icon(Icons.save_alt, size: 16),
                      SizedBox(width: 10),
                      Text('Save Pattern'),
                    ],
                  ),
                ),
                const PopupMenuItem<String>(
                  value: 'print',
                  child: Row(
                    children: [
                      Icon(Icons.print, size: 16),
                      SizedBox(width: 10),
                      Text('Print Pattern'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        body: SafeArea(
          child: Column(
            children: [
              // Info bar with pattern statistics
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.dark,
                  border: Border(
                    bottom: BorderSide(color: AppColors.white.withOpacity(0.1)),
                  ),
                ),
                height: 58,
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  children: [
                    // Machine Info
                    Row(
                      children: [
                        const Icon(Icons.precision_manufacturing,
                            color: Colors.white),
                        const SizedBox(width: 8),
                        Text(
                          widget.controller.newItem.value.knittingMachine
                                  ?.customName ??
                              "Not selected",
                          style: const TextStyle(color: Colors.white),
                        ),
                      ],
                    ),
                    const SizedBox(width: 16),

                    // Gauge Info
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.grid_4x4, color: Colors.white),
                        const SizedBox(width: 8),
                        Text(
                          '${widget.controller.newItem.value.stitchesPerCm} st/cm × ${widget.controller.newItem.value.rowsPerCm} rows/cm',
                          style: const TextStyle(color: Colors.white),
                        ),
                      ],
                    ),
                    const SizedBox(width: 16),

                    // Pattern Stats
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        StatItem(
                          icon: Icons.vertical_align_center,
                          text: '$usedNeedles needles',
                        ),
                        const SizedBox(width: 16),
                        StatItem(
                          icon: Icons.height,
                          text: '$totalRows rows',
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Main preview area
              Expanded(
                child: Container(
                  color: AppColors.darkGray,
                  child: InteractiveViewer(
                    constrained: false,
                    boundaryMargin: const EdgeInsets.all(
                        50), // Increased boundary for better panning
                    transformationController: transformationController,
                    minScale: 1,
                    maxScale: 10.0,
                    child: Padding(
                      padding: const EdgeInsets.all(46.0),
                      child: Stack(
                        children: [
                          // Grid layer
                          if (showGrid)
                            CustomPaint(
                              painter: KnittingGridPainter(
                                instructions: widget.instructions,
                                gridSize: gridSize,
                                showNumbers: true,
                                rowLabelInterval: rowLabelInterval,
                                columnLabelInterval: columnLabelInterval,
                              ),
                              child: SizedBox(
                                width: widget.instructions[0].length * gridSize,
                                height: widget.instructions.length * gridSize,
                              ),
                            ),

                          // Knitting preview layer
                          CustomPaint(
                            painter: KnittedPreviewPainter(
                              instructions: widget.instructions,
                              gridSize: gridSize,
                              stitchColor: stitchColor,
                              showYarnConnections: showYarnConnections,
                            ),
                            child: SizedBox(
                              width: widget.instructions[0].length * gridSize,
                              height: widget.instructions.length * gridSize,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

              // Bottom controls bar
              Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Zoom controls
                    Row(
                      children: [
                        IconButton(
                          icon: const Icon(Icons.zoom_out),
                          onPressed: () => _animateScale(
                              transformationController.value
                                      .getMaxScaleOnAxis() /
                                  1.5),
                          tooltip: 'Zoom Out',
                        ),
                        IconButton(
                          icon: const Icon(Icons.zoom_in),
                          onPressed: () => _animateScale(
                              transformationController.value
                                      .getMaxScaleOnAxis() *
                                  1.5),
                          tooltip: 'Zoom In',
                        ),
                        IconButton(
                          icon: const Icon(Icons.fit_screen),
                          onPressed: _resetView,
                          tooltip: 'Fit to Screen',
                        ),
                      ],
                    ),

                    // Visualization button
                    ElevatedButton.icon(
                      icon: const Icon(Icons.grid_on),
                      label: const Text('Interactive Visualization'),
                      onPressed: () {
                        // Capture the tab controller before closing the dialog
                        final tabController =
                            DefaultTabController.maybeOf(context);

                        // Close the dialog
                        Navigator.of(context).pop();

                        // Wait a moment for the dialog to close
                        Future.delayed(const Duration(milliseconds: 100), () {
                          // Switch to the visualization tab
                          if (tabController != null) {
                            tabController.animateTo(
                                2); // Properly target the visualization tab
                          }
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.purple,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 12),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
