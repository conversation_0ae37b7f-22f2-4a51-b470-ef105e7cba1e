import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/knitting_instructions_manager.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/knitting_zone_models.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/shape_editor_controller.dart';
import 'package:xoxknit/app/modules/new_item/views/widgets/knitting_zone_control_widget.dart';
import 'package:xoxknit/app/modules/new_item/controllers/new_item_wizard_controller.dart';
import 'package:xoxknit/app/modules/new_item/utils/zone_configuration_processor.dart';

class KnittingZoneConfigView extends StatefulWidget {
  const KnittingZoneConfigView({super.key});

  @override
  State<KnittingZoneConfigView> createState() => _KnittingZoneConfigViewState();
}

class _KnittingZoneConfigViewState extends State<KnittingZoneConfigView> {
  final PageController _pageController = PageController();
  int _currentPageIndex = 0;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final shapeController = Get.find<ShapeEditorController>();

    return Obx(() {
      final zones =
          shapeController.knittingInstructionsManager.knittingZones.value;
      if (zones.isEmpty) {
        return Center(
          child: Text('knittingZone_noZonesFound'.tr),
        );
      }

      return Column(
        children: [
          // Page indicator
          if (zones.length > 1)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 12.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  for (int i = 0; i < zones.length; i++)
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 4.0),
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: i == _currentPageIndex
                            ? Theme.of(context).primaryColor
                            : Colors.grey.shade300,
                      ),
                    ),
                ],
              ),
            ),
          // PageView with zones
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentPageIndex = index;
                });
              },
              itemCount: zones.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: SingleChildScrollView(
                    // padding: const EdgeInsets.symmetric(vertical: 16.0),
                    child: Column(
                      children: [
                        // Large single zone visualization
                        Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade200,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: _buildSingleZoneVisualization(
                              zones[index], index),
                        ),
                        const SizedBox(height: 24),
                        // Single zone control panel
                        Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.2),
                                spreadRadius: 1,
                                blurRadius: 5,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          padding: const EdgeInsets.all(16.0),
                          child: KnittingZoneControlWidget(
                            zone: zones[index],
                            index: index,
                            onPreviousZone: () {
                              if (index > 0) {
                                _pageController.previousPage(
                                  duration: const Duration(milliseconds: 300),
                                  curve: Curves.easeInOut,
                                );
                              }
                            },
                            onNextZone: () {
                              if (index < zones.length - 1) {
                                _pageController.nextPage(
                                  duration: const Duration(milliseconds: 300),
                                  curve: Curves.easeInOut,
                                );
                              }
                            },
                            isSelected: true,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      );
    });
  }

  Widget _buildSingleZoneVisualization(KnittingZone zone, [int? zoneIndex]) {
    final wizardController = Get.find<NewItemWizardController>();

    // Calculate the aspect ratio based on the knitting gauge
    double aspectRatio = 0.75; // Default value

    // Get the gauge values from the controller
    final stitchesPerCm = wizardController.newItem.value.stitchesPerCm;
    final rowsPerCm = wizardController.newItem.value.rowsPerCm;

    // Calculate the aspect ratio if gauge values are valid
    if (rowsPerCm != null &&
        rowsPerCm > 0 &&
        stitchesPerCm != null &&
        stitchesPerCm > 0) {
      aspectRatio = stitchesPerCm / rowsPerCm;
    }

    final isEmptyZone = zone.config.value.isEmpty.value;

    // Apply zone configurations to get processed instructions
    final processedInstructions =
        ZoneConfigurationProcessor.applyZoneConfiguration(zone);

    // Get trimmed data for accurate stitch counts from processed instructions
    final trimmedData = _getTrimmedInstructions(processedInstructions);
    final trimmedInstructions = trimmedData['instructions'] as List<List<bool>>;

    // Calculate actual dimensions after trimming
    final actualRowCount =
        trimmedInstructions.isEmpty ? 0 : trimmedInstructions.length;
    final actualStitchWidth =
        trimmedInstructions.isEmpty ? 0 : trimmedInstructions[0].length;

    return Container(
      decoration: BoxDecoration(
        color: _getZoneColor(zoneIndex ?? 0),
        borderRadius: BorderRadius.circular(12),
      ),
      padding: const EdgeInsets.all(6.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: double.infinity,
            constraints: const BoxConstraints(
              maxWidth: 450,
              maxHeight: 300,
            ),
            padding: const EdgeInsets.all(7.0),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: isEmptyZone
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.horizontal_rule,
                          size: 48,
                          color: Colors.grey.shade400,
                        ),
                        const SizedBox(height: 12),
                        Text(
                          'knittingZone_emptyEdgeZone'.tr,
                          style: TextStyle(
                            fontStyle: FontStyle.italic,
                            color: Colors.grey,
                            fontSize: 16,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  )
                : Obx(() {
                    // Wrap in Obx to rebuild when config changes
                    // Force a rebuild by accessing the config values
                    zone.config.value.autoAdjustAsymmetricalRows.value;
                    zone.config.value.asymmetricalDirection.value;
                    zone.config.value.autoControlIncreaseDecrease.value;
                    zone.config.value.increaseBy.value;
                    zone.config.value.increaseEvery.value;
                    zone.config.value.decreaseBy.value;
                    zone.config.value.decreaseEvery.value;

                    return AspectRatio(
                      aspectRatio: aspectRatio,
                      child: CustomPaint(
                        painter: KnittingZonePainter(
                          zone: zone,
                          aspectRatio: aspectRatio,
                        ),
                      ),
                    );
                  }),
          ),
          const SizedBox(height: 12),
          Obx(() {
            // Also wrap the text in Obx to update counts when config changes
            zone.config.value.autoAdjustAsymmetricalRows.value;
            zone.config.value.asymmetricalDirection.value;
            zone.config.value.autoControlIncreaseDecrease.value;
            zone.config.value.increaseBy.value;
            zone.config.value.increaseEvery.value;
            zone.config.value.decreaseBy.value;
            zone.config.value.decreaseEvery.value;

            // Recalculate dimensions with current config
            final currentProcessedInstructions =
                ZoneConfigurationProcessor.applyZoneConfiguration(zone);
            final currentTrimmedData =
                _getTrimmedInstructions(currentProcessedInstructions);
            final currentTrimmedInstructions =
                currentTrimmedData['instructions'] as List<List<bool>>;
            final currentRowCount = currentTrimmedInstructions.isEmpty
                ? 0
                : currentTrimmedInstructions.length;
            final currentStitchWidth = currentTrimmedInstructions.isEmpty
                ? 0
                : currentTrimmedInstructions[0].length;

            return Column(
              children: [
                Text(
                  'knittingZone_rowsCount'
                      .trParams({'count': currentRowCount.toString()}),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (!isEmptyZone && currentStitchWidth > 0)
                  Text(
                    'knittingZone_stitchesWide'
                        .trParams({'count': currentStitchWidth.toString()}),
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 12,
                    ),
                  ),
              ],
            );
          }),
        ],
      ),
    );
  }

  /// Helper method to get trimmed instructions (removing padding false values)
  Map<String, dynamic> _getTrimmedInstructions(List<List<bool>> instructions) {
    if (instructions.isEmpty) {
      return {
        'instructions': <List<bool>>[],
        'leftTrim': 0,
        'rightTrim': 0,
        'topTrim': 0,
        'bottomTrim': 0,
      };
    }

    int leftTrim = instructions[0].length;
    int rightTrim = 0;
    int topTrim = 0;
    int bottomTrim = 0;

    // Find the bounding box of all true values
    bool foundContent = false;
    int firstContentRow = -1;
    int lastContentRow = -1;

    for (int row = 0; row < instructions.length; row++) {
      bool rowHasContent = false;
      for (int col = 0; col < instructions[row].length; col++) {
        if (instructions[row][col]) {
          rowHasContent = true;
          foundContent = true;
          leftTrim = leftTrim < col ? leftTrim : col;
          rightTrim = rightTrim > col ? rightTrim : col;
        }
      }
      if (rowHasContent) {
        if (firstContentRow == -1) firstContentRow = row;
        lastContentRow = row;
      }
    }

    if (!foundContent) {
      return {
        'instructions': <List<bool>>[],
        'leftTrim': 0,
        'rightTrim': 0,
        'topTrim': 0,
        'bottomTrim': 0,
      };
    }

    // Calculate trims
    topTrim = firstContentRow;
    bottomTrim = instructions.length - 1 - lastContentRow;
    final rightPadding = instructions[0].length - 1 - rightTrim;

    // Create trimmed instructions
    final List<List<bool>> trimmed = [];
    for (int row = firstContentRow; row <= lastContentRow; row++) {
      final trimmedRow = instructions[row].sublist(leftTrim, rightTrim + 1);
      trimmed.add(trimmedRow);
    }

    return {
      'instructions': trimmed,
      'leftTrim': leftTrim,
      'rightTrim': rightPadding,
      'topTrim': topTrim,
      'bottomTrim': bottomTrim,
    };
  }

  Color _getZoneColor(int index) {
    final colors = [
      Colors.blue.shade700,
      Colors.teal.shade700,
      Colors.orange.shade700,
      Colors.purple.shade700,
      Colors.green.shade700,
      Colors.red.shade700,
    ];

    return colors[index % colors.length];
  }
}
