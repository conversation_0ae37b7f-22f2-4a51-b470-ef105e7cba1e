import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/shape_editor_controller.dart';

class ZonesEditorView extends StatelessWidget {
  final bool hideAppBar;

  const ZonesEditorView({
    super.key,
    this.hideAppBar = false,
  });

  @override
  Widget build(BuildContext context) {
    // We're using the ZonesEditorView from the shape_test module, but hiding its AppBar
    // since we're in the wizard flow with its own navigation
    return WizardWrappedZonesEditorView(hideAppBar: hideAppBar);
  }
}

class WizardWrappedZonesEditorView extends StatelessWidget {
  final bool hideAppBar;

  const WizardWrappedZonesEditorView({
    super.key,
    this.hideAppBar = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Zones editor without AppBar
        Expanded(
          child: Material(
            color: Colors.transparent,
            child: Builder(
              builder: (context) {
                if (!Get.isRegistered<ShapeEditorController>()) {
                  // If ShapeEditorController is not registered, show loading
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                // Get ShapeEditorController to access KnittingInstructionsManager
                final shapeController = Get.find<ShapeEditorController>();

                // Check if instructions are available
                if (shapeController.knittingInstructionsManager
                    .currentInstructions.value.isEmpty) {
                  // Generate instructions if not available
                  shapeController.knittingInstructionsManager
                      .generateInstructions();
                }

                // Get the ZonesEditorView implementation from shape_test module
                return ZonesEditorView(
                  hideAppBar: hideAppBar,
                );
              },
            ),
          ),
        ),

        // Information text at the bottom
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.blue.shade50,
          child: Text(
            'Edit knitting zones by selecting them and adjusting their boundaries. '
            'You can create new zones or delete existing ones. When finished, continue to the pattern summary.',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }
}
