import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // Import for FilteringTextInputFormatter
import 'package:get/get.dart';
import 'package:xoxknit/app/services/knitting/knitting_settings_service.dart';
import '../controllers/new_item_form_controller.dart';

class NewItemForm extends GetResponsiveView<NewItemFormController> {
  NewItemForm({super.key});

  @override
  Widget? builder() {
    return GestureDetector(
      onTap: () => FocusScope.of(Get.context!).unfocus(),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Get.theme.colorScheme.background,
              Get.theme.colorScheme.background.withOpacity(0.9),
            ],
          ),
        ),
        child: Center(
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(
              horizontal: screen.isDesktop
                  ? 40
                  : screen.isTablet
                      ? 32
                      : 16,
              vertical: 24,
            ),
            child: Container(
              constraints: BoxConstraints(
                maxWidth: screen.isDesktop ? 1000 : double.infinity,
              ),
              child: Obx(
                () => Form(
                  key: controller.formKey,
                  autovalidateMode: controller.autovalidateMode.value,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _buildSection(
                        title: 'newItemWizard_form_requiredInformation'.tr,
                        children: [
                          if (screen.isDesktop || screen.isTablet)
                            _buildDesktopRequiredFields()
                          else
                            _buildMobileRequiredFields(),
                        ],
                      ),
                      SizedBox(height: screen.isPhone ? 16 : 24),
                      _buildSection(
                        title: 'newItemWizard_form_optionalInformation'.tr,
                        children: [
                          if (screen.isDesktop || screen.isTablet)
                            _buildDesktopOptionalFields()
                          else
                            _buildMobileOptionalFields(),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMobileRequiredFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        _buildTextField(
          label: '${"newItemWizard_form_itemName".tr} *',
          controller: controller.nameController,
          validator: (value) => controller.validateRequired(
              value, 'newItemWizard_form_itemName'.tr.toLowerCase()),
          prefixIcon: Icons.label_outline,
        ),
        const SizedBox(height: 12),
        _buildTextField(
          label: '${"newItemWizard_form_stitchType".tr} *',
          controller: controller.stitchTypeController,
          validator: (value) => controller.validateRequired(
              value, 'newItemWizard_form_stitchType'.tr.toLowerCase()),
          prefixIcon: Icons.pattern,
        ),
        const SizedBox(height: 12),
        _buildMachineSelector(),
        const SizedBox(height: 12),
        _buildTensionSelector(),
        const SizedBox(height: 12),
        _buildTextField(
          label: '${"newItemWizard_form_yarnTitle".tr} *',
          controller: controller.yarnTitleController,
          prefixIcon: Icons.title_outlined,
          validator: (value) => controller.validateRequired(
              value, 'newItemWizard_form_yarnTitle'.tr.toLowerCase()),
        ),
      ],
    );
  }

  Widget _buildDesktopRequiredFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 2,
              child: _buildTextField(
                label: '${"newItemWizard_form_itemName".tr} *',
                controller: controller.nameController,
                validator: (value) => controller.validateRequired(
                    value, 'newItemWizard_form_itemName'.tr.toLowerCase()),
                prefixIcon: Icons.label_outline,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              flex: 1,
              child: _buildTextField(
                label: '${"newItemWizard_form_yarnTitle".tr} *',
                controller: controller.yarnTitleController,
                prefixIcon: Icons.title_outlined,
                validator: (value) => controller.validateRequired(
                    value, 'newItemWizard_form_yarnTitle'.tr.toLowerCase()),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: _buildTextField(
                label: '${"newItemWizard_form_stitchType".tr} *',
                controller: controller.stitchTypeController,
                validator: (value) => controller.validateRequired(
                    value, 'newItemWizard_form_stitchType'.tr.toLowerCase()),
                prefixIcon: Icons.pattern,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: _buildMachineSelector(),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildTensionSelector(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMobileOptionalFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        _buildTextField(
          label: 'newItemWizard_form_swatchNumber'.tr,
          controller: controller.swatchNumberController,
          prefixIcon: Icons.tag_outlined,
        ),
        const SizedBox(height: 12),
        _buildDateField(
          label: 'newItemWizard_form_startDate'.tr,
          controller: controller.startDateController,
          onTap: () => controller.selectDate(Get.context!, isStartDate: true),
          prefixIcon: Icons.calendar_today_outlined,
        ),
        const SizedBox(height: 12),
        _buildDateField(
          label: 'newItemWizard_form_neededBy'.tr,
          controller: controller.neededByDateController,
          onTap: () => controller.selectDate(Get.context!, isStartDate: false),
          prefixIcon: Icons.event_outlined,
        ),
        const SizedBox(height: 12),
        _buildTextField(
          label: 'newItemWizard_form_supplier'.tr,
          controller: controller.yarnSupplierController,
          prefixIcon: Icons.store_outlined,
        ),
        const SizedBox(height: 12),
        _buildTextField(
          label: 'newItemWizard_form_color'.tr,
          controller: controller.colorController,
          prefixIcon: Icons.color_lens_outlined,
        ),
        const SizedBox(height: 12),
        _buildTextField(
          label: 'newItemWizard_form_composition'.tr,
          controller: controller.yarnCompositionController,
          prefixIcon: Icons.format_color_fill_outlined,
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildTextField(
                label: 'newItemWizard_form_strands'.tr,
                controller: controller.strandsController,
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                prefixIcon: Icons.linear_scale_outlined,
                validator: (value) => controller.validateNumber(
                    value, 'newItemWizard_form_strands'.tr),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildTextField(
                label: 'newItemWizard_form_yarnOnHand'.tr,
                controller: controller.weightController,
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*$'))
                ],
                prefixIcon: Icons.scale_outlined,
                validator: (value) => controller.validateNumber(
                    value, 'newItemWizard_form_yarnOnHand'.tr),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        _buildTextField(
          label: 'newItemWizard_form_additionalNotes'.tr,
          controller: controller.notesController,
          maxLines: 3,
          prefixIcon: Icons.note_outlined,
        ),
      ],
    );
  }

  Widget _buildDesktopOptionalFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: _buildTextField(
                label: 'newItemWizard_form_swatchNumber'.tr,
                controller: controller.swatchNumberController,
                prefixIcon: Icons.tag_outlined,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: _buildDateField(
                label: 'newItemWizard_form_startDate'.tr,
                controller: controller.startDateController,
                onTap: () =>
                    controller.selectDate(Get.context!, isStartDate: true),
                prefixIcon: Icons.calendar_today_outlined,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildDateField(
                label: 'newItemWizard_form_neededBy'.tr,
                controller: controller.neededByDateController,
                onTap: () =>
                    controller.selectDate(Get.context!, isStartDate: false),
                prefixIcon: Icons.event_outlined,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildTextField(
                label: 'newItemWizard_form_supplier'.tr,
                controller: controller.yarnSupplierController,
                prefixIcon: Icons.store_outlined,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildTextField(
                label: 'newItemWizard_form_color'.tr,
                controller: controller.colorController,
                prefixIcon: Icons.color_lens_outlined,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildTextField(
                label: 'newItemWizard_form_composition'.tr,
                controller: controller.yarnCompositionController,
                prefixIcon: Icons.format_color_fill_outlined,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    child: _buildTextField(
                      label: 'newItemWizard_form_strands'.tr,
                      controller: controller.strandsController,
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      prefixIcon: Icons.linear_scale_outlined,
                      validator: (value) => controller.validateNumber(
                          value, 'newItemWizard_form_strands'.tr),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildTextField(
                      label: 'newItemWizard_form_yarnOnHand'.tr,
                      controller: controller.weightController,
                      keyboardType:
                          const TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                            RegExp(r'^\d*\.?\d*$'))
                      ],
                      prefixIcon: Icons.scale_outlined,
                      validator: (value) => controller.validateNumber(
                          value, 'newItemWizard_form_yarnOnHand'.tr),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        _buildTextField(
          label: 'newItemWizard_form_additionalNotes'.tr,
          controller: controller.notesController,
          maxLines: screen.isPhone ? 2 : 3,
          prefixIcon: Icons.note_outlined,
        ),
      ],
    );
  }

  Widget _buildSection(
      {required String title, required List<Widget> children}) {
    return Container(
      padding: EdgeInsets.all(screen.isPhone ? 12 : 16),
      decoration: BoxDecoration(
        color: Get.theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Get.theme.colorScheme.onSurface.withOpacity(0.1),
          width: 0.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Get.theme.colorScheme.shadow.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: screen.isPhone ? 16 : 18,
              fontWeight: FontWeight.bold,
              color: Get.theme.colorScheme.primary,
            ),
          ),
          SizedBox(height: screen.isPhone ? 12 : 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    String? Function(String?)? validator,
    int? maxLines,
    TextInputType? keyboardType,
    IconData? prefixIcon,
    List<TextInputFormatter>? inputFormatters,
  }) {
    return TextFormField(
      controller: controller,
      validator: validator,
      maxLines: maxLines ?? 1,
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
      style: TextStyle(
        fontSize: screen.isPhone ? 13 : 14,
        color: Get.theme.colorScheme.onSurface,
      ),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: TextStyle(
          fontSize: screen.isPhone ? 12 : 13,
          color: Get.theme.colorScheme.onSurface.withOpacity(0.7),
        ),
        floatingLabelStyle: TextStyle(
          fontSize: screen.isPhone ? 14 : 15,
          color: Get.theme.colorScheme.primary,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Get.theme.colorScheme.onSurface.withOpacity(0.2),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Get.theme.colorScheme.primary),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        filled: true,
        fillColor: Get.theme.colorScheme.surfaceContainerHighest,
        prefixIcon: prefixIcon != null
            ? Icon(
                prefixIcon,
                size: screen.isPhone ? 20 : 22,
                color: Get.theme.colorScheme.onSurface.withOpacity(0.7),
              )
            : null,
        isDense: true,
        contentPadding: EdgeInsets.symmetric(
          horizontal: screen.isPhone ? 12 : 14,
          vertical: screen.isPhone ? 16 : 18,
        ),
      ),
    );
  }

  Widget _buildDateField({
    required String label,
    required TextEditingController controller,
    required VoidCallback onTap,
    IconData? prefixIcon,
  }) {
    return TextFormField(
      controller: controller,
      readOnly: true,
      onTap: onTap,
      style: TextStyle(
        fontSize: screen.isPhone ? 13 : 14,
        color: Get.theme.colorScheme.onSurface,
      ),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: TextStyle(
          fontSize: screen.isPhone ? 12 : 13,
          color: Get.theme.colorScheme.onSurface.withOpacity(0.7),
        ),
        floatingLabelStyle: TextStyle(
          fontSize: screen.isPhone ? 14 : 15,
          color: Get.theme.colorScheme.primary,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Get.theme.colorScheme.onSurface.withOpacity(0.2),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Get.theme.colorScheme.primary),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        filled: true,
        fillColor: Get.theme.colorScheme.surfaceContainerHighest,
        prefixIcon: prefixIcon != null
            ? Icon(
                prefixIcon,
                size: screen.isPhone ? 20 : 22,
                color: Get.theme.colorScheme.onSurface.withOpacity(0.7),
              )
            : null,
        suffixIcon: Icon(
          Icons.calendar_today,
          size: screen.isPhone ? 20 : 22,
          color: Get.theme.colorScheme.onSurface.withOpacity(0.7),
        ),
        isDense: true,
        contentPadding: EdgeInsets.symmetric(
          horizontal: screen.isPhone ? 12 : 14,
          vertical: screen.isPhone ? 16 : 18,
        ),
      ),
    );
  }

  Widget _buildMachineSelector() {
    final knittingService = Get.find<KnittingSettingsService>();
    return Obx(() {
      final machines = knittingService.userMachines;

      final currentMachine = controller.selectedMachine.value;
      final validSelection = currentMachine != null &&
          machines.any((m) => m.id == currentMachine.id);
      final displayValue = validSelection ? currentMachine : null;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DropdownButtonFormField(
            value: displayValue,
            validator: (value) => controller.validateRequired(
                value?.id, 'newItemWizard_form_machine'.tr),
            items: [
              ...machines.map((machine) => DropdownMenuItem(
                    value: machine,
                    child: Text(
                      "${machine.customName.isNotEmpty ? machine.customName : machine.fullName} (${'userMachines_machineInfo_needles'.trParams({
                            "count": machine.needlesCount.toString()
                          })})",
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: screen.isPhone ? 13 : 14,
                        color: Get.theme.colorScheme.onSurface,
                      ),
                    ),
                  )),
              DropdownMenuItem(
                value: null,
                child: Row(
                  children: [
                    Icon(
                      Icons.add,
                      size: screen.isPhone ? 16 : 18,
                      color: Get.theme.colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'newItemWizard_form_addNewMachine'.tr,
                      style: TextStyle(
                        fontSize: screen.isPhone ? 13 : 14,
                        color: Get.theme.colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            onChanged: (value) {
              if (value == null) {
                controller.showAddMachineDialog();
              } else {
                controller.selectedMachine.value = value;
              }
            },
            isExpanded: true,
            style: TextStyle(
              fontSize: screen.isPhone ? 13 : 14,
              color: Get.theme.colorScheme.onSurface,
            ),
            decoration: InputDecoration(
              labelText: '${"newItemWizard_form_machine".tr} *',
              labelStyle: TextStyle(
                fontSize: screen.isPhone ? 12 : 13,
                color: Get.theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              floatingLabelStyle: TextStyle(
                fontSize: screen.isPhone ? 14 : 15,
                color: Get.theme.colorScheme.primary,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Get.theme.colorScheme.onSurface.withOpacity(0.2),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Get.theme.colorScheme.primary),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Colors.red),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Colors.red, width: 2),
              ),
              filled: true,
              fillColor: Get.theme.colorScheme.surfaceContainerHighest,
              prefixIcon: Icon(
                Icons.precision_manufacturing_outlined,
                size: screen.isPhone ? 20 : 22,
                color: Get.theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              isDense: true,
              contentPadding: EdgeInsets.symmetric(
                horizontal: screen.isPhone ? 12 : 14,
                vertical: screen.isPhone ? 16 : 18,
              ),
            ),
          ),
          if (controller.selectedMachine.value != null &&
              controller.selectedMachine.value!.customName.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 4.0, left: 12.0),
              child: Text(
                controller.selectedMachine.value!.fullName,
                style: TextStyle(
                  fontSize: screen.isPhone ? 11 : 12,
                  color: Get.theme.colorScheme.onSurface.withOpacity(0.6),
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      );
    });
  }

  Widget _buildTensionSelector() {
    return Obx(() {
      final parts = controller.selectedTension.value.split('.');
      final currentWhole = int.tryParse(parts[0]) ?? 3;
      final currentFrac = parts.length > 1 ? (int.tryParse(parts[1]) ?? 0) : 0;

      return FormField<String>(
        initialValue: controller.selectedTension.value,
        validator: (value) => controller.validateRequired(value, 'tension'),
        builder: (FormFieldState<String> field) {
          return Container(
            padding: EdgeInsets.symmetric(
                horizontal: screen.isPhone ? 12 : 14,
                vertical: screen.isPhone ? 8 : 10),
            decoration: BoxDecoration(
              color: Get.theme.colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: field.hasError
                    ? Colors.red
                    : Get.theme.colorScheme.onSurface.withOpacity(0.2),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Combined row for label and controls
                Row(
                  children: [
                    // Label and icon
                    Icon(
                      Icons.tune_outlined,
                      size: screen.isPhone ? 20 : 22,
                      color: Get.theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${"newItemWizard_form_tension".tr} *',
                      style: TextStyle(
                        fontSize: screen.isPhone ? 12 : 13,
                        color: Get.theme.colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                    const SizedBox(width: 16),

                    // Whole number controls
                    _buildCircleButton(
                      icon: Icons.remove,
                      onTap: currentWhole > 0
                          ? () {
                              final newValue = currentWhole - 1;
                              controller.selectedTension.value =
                                  '$newValue.$currentFrac';
                              field.didChange(controller.selectedTension.value);
                            }
                          : null,
                      small: true,
                    ),
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 8),
                      width: screen.isPhone ? 32 : 36,
                      height: screen.isPhone ? 32 : 36,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: Get.theme.colorScheme.surface,
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(
                          color: Get.theme.colorScheme.primary.withOpacity(0.5),
                        ),
                      ),
                      child: Text(
                        currentWhole.toString(),
                        style: TextStyle(
                          fontSize: screen.isPhone ? 14 : 16,
                          fontWeight: FontWeight.bold,
                          color: Get.theme.colorScheme.primary,
                        ),
                      ),
                    ),
                    _buildCircleButton(
                      icon: Icons.add,
                      onTap: currentWhole < 10
                          ? () {
                              final newValue = currentWhole + 1;
                              controller.selectedTension.value =
                                  '$newValue.$currentFrac';
                              field.didChange(controller.selectedTension.value);
                            }
                          : null,
                      small: true,
                    ),

                    const SizedBox(width: 8),
                    Text(
                      '.',
                      style: TextStyle(
                        fontSize: screen.isPhone ? 18 : 20,
                        fontWeight: FontWeight.bold,
                        color: Get.theme.colorScheme.primary,
                      ),
                    ),
                    const SizedBox(width: 8),

                    // Decimal selector
                    Expanded(
                      child: Container(
                        height: screen.isPhone ? 32 : 36,
                        decoration: BoxDecoration(
                          color: Get.theme.colorScheme.surface,
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(
                            color: Get.theme.colorScheme.onSurface
                                .withOpacity(0.2),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: List.generate(3, (index) {
                            final isSelected = index == currentFrac;
                            return Expanded(
                              child: GestureDetector(
                                onTap: () {
                                  controller.selectedTension.value =
                                      '$currentWhole.$index';
                                  field.didChange(
                                      controller.selectedTension.value);
                                },
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: isSelected
                                        ? Get.theme.colorScheme.primary
                                        : Colors.transparent,
                                    borderRadius: BorderRadius.horizontal(
                                      left: index == 0
                                          ? const Radius.circular(5)
                                          : Radius.zero,
                                      right: index == 2
                                          ? const Radius.circular(5)
                                          : Radius.zero,
                                    ),
                                    border: index > 0
                                        ? Border(
                                            left: BorderSide(
                                              color: Get
                                                  .theme.colorScheme.onSurface
                                                  .withOpacity(0.2),
                                            ),
                                          )
                                        : null,
                                  ),
                                  alignment: Alignment.center,
                                  child: Text(
                                    index.toString(),
                                    style: TextStyle(
                                      fontSize: screen.isPhone ? 13 : 14,
                                      fontWeight: FontWeight.bold,
                                      color: isSelected
                                          ? Get.theme.colorScheme.onPrimary
                                          : Get.theme.colorScheme.onSurface,
                                    ),
                                  ),
                                ),
                              ),
                            );
                          }),
                        ),
                      ),
                    ),
                  ],
                ),

                // Error text if any
                if (field.errorText != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      field.errorText!,
                      style: const TextStyle(
                        color: Colors.red,
                        fontSize: 12,
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      );
    });
  }

  Widget _buildCircleButton({
    required IconData icon,
    VoidCallback? onTap,
    bool small = false,
  }) {
    final isEnabled = onTap != null;
    final size =
        small ? (screen.isPhone ? 24.0 : 28.0) : (screen.isPhone ? 32.0 : 36.0);
    final iconSize =
        small ? (screen.isPhone ? 14.0 : 16.0) : (screen.isPhone ? 16.0 : 18.0);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: isEnabled
              ? Get.theme.colorScheme.primary
              : Get.theme.colorScheme.onSurface.withOpacity(0.2),
        ),
        child: Icon(
          icon,
          size: iconSize,
          color: isEnabled
              ? Get.theme.colorScheme.onPrimary
              : Get.theme.colorScheme.onSurface.withOpacity(0.5),
        ),
      ),
    );
  }
}
