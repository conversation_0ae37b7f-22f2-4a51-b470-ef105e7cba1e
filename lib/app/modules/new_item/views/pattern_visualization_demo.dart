import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/new_item_wizard_controller.dart';
import '../utils/knitting_utils.dart';
import 'components/enhanced_pattern_visualizer.dart';
import 'widgets/standard_dialog.dart';

/// A demo screen for the enhanced pattern visualization
class PatternVisualizationDemo extends GetView<NewItemWizardController> {
  const PatternVisualizationDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('knittingInstructions_interactive_patternVisualization'.tr),
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () => _showHelpDialog(context),
          ),
        ],
      ),
      body: Column(
        children: [
          // Info panel with stats
          _buildInfoPanel(),

          // Pattern visualization (main content)
          Expanded(
            child: Obx(() {
              final pattern = controller.knittingInstructions.value;

              if (pattern.isEmpty) {
                return Center(
                  child: Text(
                      'knittingInstructions_interactive_visualizer_noPatternCreateFirst'
                          .tr),
                );
              }

              // Get machine settings from controller
              final machine = controller.newItem.value.knittingMachine;
              final needleCount =
                  machine?.needlesCount ?? 100; // Default if not set

              return EnhancedPatternVisualizer(
                pattern: pattern,
                needleCount: needleCount,
                useLRNotation: true,
                onRowSelected: (rowIndex) {
                  // Update the current row in the controller
                  controller.currentKnittingRow.value = rowIndex;
                },
                onStitchTap: (rowIndex, needleIndex) {
                  // Show detailed info for the stitch
                  _showStitchDetails(context, rowIndex, needleIndex);
                },
              );
            }),
          ),

          // Bottom controls
          _buildBottomControls(context),
        ],
      ),
    );
  }

  Widget _buildInfoPanel() {
    return Obx(() {
      final pattern = controller.knittingInstructions.value;

      if (pattern.isEmpty) return const SizedBox.shrink();

      // Calculate pattern stats
      final totalRows = pattern.length;
      final maxWidth = KnittingUtils.calculateMaxWidth(pattern);

      // Count stitches in current row
      int currentRowStitches = 0;
      if (controller.currentKnittingRow.value < pattern.length) {
        final currentRow = pattern[controller.currentKnittingRow.value];
        for (final stitch in currentRow) {
          if (stitch) currentRowStitches++;
        }
      }

      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.purple.withOpacity(0.05),
          border: Border(
            bottom: BorderSide(color: Colors.purple.withOpacity(0.2)),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildStatItem('Total Rows', '$totalRows'),
            _buildStatItem('Max Width', '$maxWidth stitches'),
            _buildStatItem('Current Row Stitches', '$currentRowStitches'),
          ],
        ),
      );
    });
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildBottomControls(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          ElevatedButton.icon(
            icon: const Icon(Icons.arrow_back),
            label: Text('common_back'.tr),
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.white,
              backgroundColor: Colors.grey[700],
            ),
            onPressed: () => controller.previousStep(),
          ),
          ElevatedButton.icon(
            icon: const Icon(Icons.print),
            label: Text('common_print'.tr),
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.white,
              backgroundColor: Colors.blue[700],
            ),
            onPressed: () {
              // Placeholder for print functionality
              Get.snackbar(
                'knittingInstructions_print_printSent'.tr,
                'knittingInstructions_print_printingFutureUpdate'.tr,
                snackPosition: SnackPosition.BOTTOM,
              );
            },
          ),
          ElevatedButton.icon(
            icon: const Icon(Icons.construction),
            label: Text('common_startKnitting'.tr),
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.white,
              backgroundColor: Colors.purple[700],
            ),
            onPressed: () {
              // Navigate to the interactive knitting view
              Get.toNamed('/knitting/interactive');
            },
          ),
        ],
      ),
    );
  }

  void _showStitchDetails(BuildContext context, int rowIndex, int needleIndex) {
    final pattern = controller.knittingInstructions.value;
    if (pattern.isEmpty ||
        rowIndex >= pattern.length ||
        needleIndex >= pattern[rowIndex].length) {
      return;
    }

    final isStitch = pattern[rowIndex][needleIndex];
    final machine = controller.newItem.value.knittingMachine;
    final needleCount = machine?.needlesCount ?? 100;

    final needleLabel = KnittingUtils.formatNeedleNumber(
        needleIndex, needleCount,
        useLRNotation: true);

    StandardDialog.show<void>(
      context: context,
      title: 'Stitch Details - Row ${rowIndex + 1}, Needle $needleLabel',
      customContent: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Stitch present: ${isStitch ? 'Yes' : 'No'}'),
          const SizedBox(height: 8),
          Text(
              'Action: ${isStitch ? 'Knit this stitch' : 'Skip this needle (no stitch)'}'),
          const SizedBox(height: 16),
          Text('Row ${rowIndex + 1} of ${pattern.length}'),
          Text('Needle $needleLabel of $needleCount'),
        ],
      ),
      actions: [
        DialogAction.elevated(
          text: 'Close',
          onPressed: () => Navigator.of(context).pop(),
        ),
      ],
    );
  }

  void _showHelpDialog(BuildContext context) {
    StandardDialog.show<void>(
      context: context,
      title: 'Pattern Visualization Help',
      customContent: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: const [
            Text(
              'Understanding the Visualization',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            SizedBox(height: 8),
            Text('• Purple cells represent stitches to knit'),
            Text('• Light gray cells represent needles to skip (no stitch)'),
            SizedBox(height: 16),
            Text(
              'Row Types & Indicators',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            SizedBox(height: 8),
            Text('• Repeat: Multiple identical rows that can be knit together'),
            Text('• Discontinuous: Rows with gaps in the stitch pattern'),
            Text('• Change: Rows with increases or decreases'),
            Text('• Symmetric: Rows with mirror symmetry in the pattern'),
            SizedBox(height: 16),
            Text(
              'Interactive Features',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            SizedBox(height: 8),
            Text('• Tap on a row to see detailed instructions'),
            Text('• Tap on individual stitches for details'),
            Text('• Toggle between grid and text view'),
            Text('• Use zoom controls to resize the grid'),
            SizedBox(height: 16),
            Text(
              'Warnings',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            SizedBox(height: 8),
            Text('• Wide gaps: Unusually large spaces between stitches'),
            Text('• Additional warnings may appear for specific row types'),
          ],
        ),
      ),
      actions: [
        DialogAction.elevated(
          text: 'Got it',
          onPressed: () => Navigator.of(context).pop(),
        ),
      ],
    );
  }
}
