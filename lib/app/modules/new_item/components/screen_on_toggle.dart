import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Widget that controls whether the screen stays on
class ScreenOnToggle extends StatelessWidget {
  final RxBool keepScreenOn;

  const ScreenOnToggle({
    super.key,
    required this.keepScreenOn,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Obx(() => Switch(
              value: keepScreenOn.value,
              onChanged: (value) => keepScreenOn.value = value,
              activeColor: Colors.teal,
            )),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Obx(() => Text(
                    keepScreenOn.value
                        ? 'Keep screen on (Active)'
                        : 'Keep screen on',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color:
                          keepScreenOn.value ? Colors.teal : Colors.grey[700],
                    ),
                  )),
              Text(
                'Prevent the screen from turning off while knitting',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
