import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/components/multiselect_dropdown.dart';
import 'package:xoxknit/app/core/theme/app_colors.dart';
import 'package:xoxknit/app/data/models/knitting_machine_model.dart';
import 'package:xoxknit/app/data/models/user_knitting_machine_model.dart';
import 'package:xoxknit/app/middleware/machine_check_middleware.dart';

class MachineDialog extends StatefulWidget {
  final List<KnittingMachineModel> availableMachines;
  final UserKnittingMachineModel? machine;
  final Function(KnittingMachineModel baseMachine, String name, String notes)
      onSave;

  const MachineDialog({
    super.key,
    required this.availableMachines,
    required this.onSave,
    this.machine,
  });

  @override
  State<MachineDialog> createState() => _MachineDialogState();
}

class _MachineDialogState extends State<MachineDialog>
    with SingleTickerProviderStateMixin {
  late TextEditingController _nameController;
  late TextEditingController _notesController;
  late TextEditingController _searchController;
  KnittingMachineModel? _selectedMachine;
  final _formKey = GlobalKey<FormState>();
  bool canSave = false;
  String _searchQuery = '';
  String? _selectedBrand;
  late TabController _tabController;
  List<String> _brands = [];

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.machine?.customName);
    _notesController =
        TextEditingController(text: widget.machine?.notes.toString());
    _searchController = TextEditingController();
    _searchController.addListener(_onSearchChanged);

    // Extract unique brands from available machines
    _brands = widget.availableMachines.map((m) => m.mainBrand).toSet().toList()
      ..sort();

    _tabController = TabController(length: _brands.length + 1, vsync: this);

    if (widget.machine != null) {
      _selectedMachine = widget.availableMachines
          .firstWhere((m) => m.id == widget.machine!.baseModelId);
      _selectedBrand = _selectedMachine?.mainBrand;

      // Find the tab index for the selected brand
      if (_selectedBrand != null) {
        final brandIndex = _brands.indexOf(_selectedBrand!);
        if (brandIndex != -1) {
          _tabController.animateTo(brandIndex + 1); // +1 for "All" tab
        }
      }
    }
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.toLowerCase();
    });
  }

  List<KnittingMachineModel> _getFilteredMachines() {
    return widget.availableMachines.where((machine) {
      final matchesSearch = _searchQuery.isEmpty ||
          machine.fullName.toLowerCase().contains(_searchQuery) ||
          machine.type.toLowerCase().contains(_searchQuery) ||
          machine.machineClass.toLowerCase().contains(_searchQuery);

      final matchesBrand =
          _selectedBrand == null || machine.mainBrand == _selectedBrand;

      return matchesSearch && matchesBrand;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final filteredMachines = _getFilteredMachines();
    final screenHeight = MediaQuery.of(context).size.height;
    final isSmallScreen = screenHeight < 600;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: Get.width * 0.9,
        constraints: BoxConstraints(
          maxWidth: 600,
          maxHeight: Get.height * (isSmallScreen ? 0.7 : 0.8),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Container(
              padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
              decoration: BoxDecoration(
                color: Get.theme.colorScheme.primary,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.machine == null
                        ? 'userMachines_addMachine'.tr
                        : 'userMachines_editMachine'.tr,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                  ),
                  if (!isSmallScreen) const SizedBox(height: 8),
                  if (!isSmallScreen)
                    Text(
                      'userMachines_machineDialog_selectYourMachine'.tr,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.white.withOpacity(0.8),
                          ),
                    ),
                ],
              ),
            ),

            if (widget.machine == null) ...[
              // Search field
              Padding(
                padding: EdgeInsets.fromLTRB(16, isSmallScreen ? 8 : 16, 16, 0),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'userMachines_machineDialog_searchMachines'.tr,
                    prefixIcon: Icon(Icons.search, color: Colors.grey.shade700),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    contentPadding: EdgeInsets.symmetric(
                        vertical: isSmallScreen ? 8 : 12, horizontal: 16),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                  ),
                ),
              ),

              // Brand tabs
              TabBar(
                controller: _tabController,
                isScrollable: true,
                labelColor: Get.theme.colorScheme.primary,
                unselectedLabelColor: Colors.grey.shade700,
                indicatorColor: Get.theme.colorScheme.primary,
                labelPadding: EdgeInsets.symmetric(
                  horizontal: isSmallScreen ? 12 : 16,
                  vertical: isSmallScreen ? 6 : 8,
                ),
                tabs: [
                  Tab(text: 'userMachines_machineDialog_all'.tr),
                  ..._brands.map((brand) => Tab(text: brand)),
                ],
                onTap: (index) {
                  setState(() {
                    _selectedBrand = index == 0 ? null : _brands[index - 1];
                  });
                },
              ),

              // Machine list
              Expanded(
                child: Padding(
                  padding: EdgeInsets.all(isSmallScreen ? 8 : 16),
                  child: filteredMachines.isEmpty
                      ? Center(
                          child: Text(
                            'userMachines_machineDialog_noMachinesFound'.tr,
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 16,
                            ),
                          ),
                        )
                      : ListView.builder(
                          itemCount: filteredMachines.length,
                          itemBuilder: (context, index) {
                            final machine = filteredMachines[index];
                            final isSelected =
                                _selectedMachine?.id == machine.id;

                            return Card(
                              elevation: isSelected ? 2 : 0,
                              margin: EdgeInsets.only(
                                  bottom: isSmallScreen ? 4 : 8),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                                side: BorderSide(
                                  color: isSelected
                                      ? Get.theme.colorScheme.primary
                                      : Colors.grey.shade300,
                                  width: isSelected ? 2 : 1,
                                ),
                              ),
                              child: InkWell(
                                onTap: () {
                                  setState(() {
                                    _selectedMachine = machine;
                                    _nameController.text =
                                        "My-${machine.fullName}";
                                    canSave = true;
                                  });
                                },
                                borderRadius: BorderRadius.circular(12),
                                child: Padding(
                                  padding:
                                      EdgeInsets.all(isSmallScreen ? 8 : 12),
                                  child: Row(
                                    children: [
                                      Container(
                                        width: isSmallScreen ? 32 : 40,
                                        height: isSmallScreen ? 32 : 40,
                                        decoration: BoxDecoration(
                                          color: Get.theme.colorScheme
                                              .primaryContainer,
                                          shape: BoxShape.circle,
                                        ),
                                        child: Icon(
                                          Icons.precision_manufacturing,
                                          color: Get.theme.colorScheme.primary,
                                          size: isSmallScreen ? 16 : 20,
                                        ),
                                      ),
                                      SizedBox(width: isSmallScreen ? 8 : 12),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              machine.fullName,
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontSize:
                                                    isSmallScreen ? 14 : 16,
                                                color: Get.theme.colorScheme
                                                    .onSurface,
                                              ),
                                            ),
                                            SizedBox(
                                                height: isSmallScreen ? 2 : 4),
                                            Text(
                                              'userMachines_machineInfo_needles'
                                                  .trParams({
                                                "count": machine.needlesCount
                                                    .toString()
                                              }),
                                              style: TextStyle(
                                                fontSize:
                                                    isSmallScreen ? 12 : 14,
                                                color: Colors.grey.shade700,
                                              ),
                                            ),
                                            Text(
                                              '${machine.type} - ${machine.machineClass}',
                                              style: TextStyle(
                                                fontSize:
                                                    isSmallScreen ? 10 : 12,
                                                color: Colors.grey.shade600,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      if (isSelected)
                                        Icon(
                                          Icons.check_circle,
                                          color: Get.theme.colorScheme.primary,
                                          size: isSmallScreen ? 16 : 20,
                                        ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                ),
              ),
            ],

            // Form fields section
            Container(
              padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (widget.machine != null || _selectedMachine != null) ...[
                      // Display selected machine info if editing
                      if (widget.machine != null)
                        Container(
                          padding: EdgeInsets.all(isSmallScreen ? 8 : 12),
                          decoration: BoxDecoration(
                            color: Get.theme.colorScheme.surfaceVariant,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.precision_manufacturing,
                                color: Get.theme.colorScheme.primary,
                                size: isSmallScreen ? 16 : 20,
                              ),
                              SizedBox(width: isSmallScreen ? 8 : 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      _selectedMachine?.fullName ?? '',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: isSmallScreen ? 14 : 16,
                                      ),
                                    ),
                                    Text(
                                      'userMachines_machineInfo_needles'
                                          .trParams({
                                        "count": _selectedMachine?.needlesCount
                                                .toString() ??
                                            '0'
                                      }),
                                      style: TextStyle(
                                        fontSize: isSmallScreen ? 10 : 12,
                                        color: Colors.grey.shade700,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                      SizedBox(height: isSmallScreen ? 12 : 16),
                    ],

                    // Custom Name Field
                    Text(
                      'userMachines_machineDetails_name'.tr,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontSize: isSmallScreen ? 12 : 14,
                          ),
                    ),
                    SizedBox(height: isSmallScreen ? 4 : 8),
                    TextFormField(
                      controller: _nameController,
                      onChanged: (val) {
                        if (!val.isBlank! && canSave == false) {
                          setState(() {
                            canSave = true;
                          });
                        } else if (val.isBlank! && canSave == true) {
                          setState(() {
                            canSave = false;
                          });
                        }
                      },
                      decoration: InputDecoration(
                        hintText: 'userMachines_machineDetails_nameHint'.tr,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                        fillColor: Colors.grey.shade50,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: isSmallScreen ? 8 : 12,
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'userMachines_machineDetails_nameHint'.tr;
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: isSmallScreen ? 12 : 16),

                    // Notes Field
                    Text(
                      'userMachines_machineDetails_needleBed'.tr,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontSize: isSmallScreen ? 12 : 14,
                          ),
                    ),
                    SizedBox(height: isSmallScreen ? 4 : 8),
                    TextFormField(
                      controller: _notesController,
                      maxLines: isSmallScreen ? 2 : 3,
                      decoration: InputDecoration(
                        hintText:
                            'userMachines_machineDetails_needleBedHint'.tr,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                        fillColor: Colors.grey.shade50,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: isSmallScreen ? 8 : 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Action Buttons
            Padding(
              padding: EdgeInsets.fromLTRB(
                  16, isSmallScreen ? 4 : 8, 16, isSmallScreen ? 12 : 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Get.back(),
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.symmetric(
                        horizontal: isSmallScreen ? 12 : 16,
                        vertical: isSmallScreen ? 8 : 12,
                      ),
                    ),
                    child: Text(
                      'common_cancel'.tr,
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: isSmallScreen ? 12 : 14,
                      ),
                    ),
                  ),
                  SizedBox(width: isSmallScreen ? 8 : 12),
                  ElevatedButton(
                    onPressed: (canSave || widget.machine != null)
                        ? _validateAndSave
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Get.theme.colorScheme.primary,
                      foregroundColor: Get.theme.colorScheme.onPrimary,
                      padding: EdgeInsets.symmetric(
                        horizontal: isSmallScreen ? 16 : 24,
                        vertical: isSmallScreen ? 8 : 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'common_save'.tr,
                      style: TextStyle(
                        fontSize: isSmallScreen ? 12 : 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _validateAndSave() {
    if (_formKey.currentState!.validate() &&
        (_selectedMachine != null || widget.machine != null)) {
      widget.onSave(
        _selectedMachine ??
            widget.availableMachines
                .firstWhere((m) => m.id == widget.machine!.baseModelId),
        _nameController.text,
        _notesController.text.trim(),
      );
      Get.back();
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _notesController.dispose();
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }
}
