import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/data/models/knitting_machine_model.dart';
import '../controllers/user_machines_controller.dart';
import 'components/machine_dialog.dart';

class UserMachinesView extends GetView<UserMachinesController> {
  const UserMachinesView({super.key});

  @override
  Widget build(BuildContext context) {
    // Get arguments from route parameters instead of Get.arguments
    final message = Get.parameters['message'] ??
        (Get.rootDelegate.parameters['message'] ??
            (Get.arguments?['message'] as String?));

    return Scaffold(
      appBar: AppBar(
        title: Text('userMachines_title'.tr),
        centerTitle: true,
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (controller.userMachines.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.precision_manufacturing_outlined,
                  size: 64,
                  color: Colors.grey,
                ),
                const SizedBox(height: 16),
                Text(
                  'userMachines_noMachines'.tr,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                if (message != null) ...[
                  Text(
                    message,
                    style: TextStyle(
                      color: Get.theme.colorScheme.error,
                      fontWeight: FontWeight.w700,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                ],
                Text(
                  'userMachines_addYourFirstMachine'.tr,
                  style: const TextStyle(
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (context) => MachineDialog(
                        availableMachines: controller.availableMachines,
                        onSave: (baseMachine, name, needles) {
                          controller.addMachine(baseMachine, name, needles);
                        },
                      ),
                    );
                  },
                  icon: const Icon(Icons.add),
                  label: Text('userMachines_addMachine'.tr),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          itemCount: controller.userMachines.length,
          padding: const EdgeInsets.all(16),
          itemBuilder: (context, index) {
            final machine = controller.userMachines[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(color: Colors.grey.shade200),
              ),
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                machine.customName,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                machine.machineClass,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ),
                        ),
                        PopupMenuButton(
                          icon: const Icon(Icons.more_vert),
                          color: Get.theme.colorScheme.surface,
                          itemBuilder: (context) => [
                            PopupMenuItem(
                              child: Row(
                                children: [
                                  const Icon(Icons.edit_outlined),
                                  const SizedBox(width: 8),
                                  Text('userMachines_editMachine'.tr),
                                ],
                              ),
                              onTap: () => Future.delayed(
                                const Duration(seconds: 0),
                                () => showDialog(
                                  // ignore: use_build_context_synchronously
                                  context: context,
                                  builder: (context) => MachineDialog(
                                    availableMachines:
                                        controller.availableMachines,
                                    machine: machine,
                                    onSave: (baseMachine, name, needles) {
                                      controller.updateMachine(
                                        machine,
                                        name,
                                        needles,
                                      );
                                    },
                                  ),
                                ),
                              ),
                            ),
                            PopupMenuItem(
                              child: Row(
                                children: [
                                  const Icon(
                                    Icons.delete_outline,
                                    color: Colors.red,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'common_delete'.tr,
                                    style: const TextStyle(color: Colors.red),
                                  ),
                                ],
                              ),
                              onTap: () => Future.delayed(
                                Duration.zero,
                                () => Get.dialog(
                                  AlertDialog(
                                    title: Text(
                                        'userMachines_deleteConfirm_title'.tr),
                                    content: Text(
                                      'userMachines_deleteConfirm_message'.tr,
                                    ),
                                    actions: [
                                      TextButton(
                                        onPressed: () => Get.back(),
                                        child: Text(
                                          'common_cancel'.tr,
                                          style: TextStyle(
                                            color: Colors.grey.shade700,
                                          ),
                                        ),
                                      ),
                                      ElevatedButton(
                                        onPressed: () {
                                          controller.deleteMachine(machine.id);
                                          Get.back();
                                        },
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.red,
                                          foregroundColor: Colors.white,
                                        ),
                                        child: Text('common_delete'.tr),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    const Divider(height: 24),
                    Row(
                      children: [
                        _buildInfoChip(
                          icon: Icons.straighten,
                          label: 'userMachines_machineInfo_needles'.trParams({
                            'count': machine.needlesCount.toString(),
                          }),
                        ),
                        const SizedBox(width: 8),
                        _buildInfoChip(
                          icon: Icons.space_bar,
                          label: 'userMachines_machineInfo_pitch'.trParams({
                            'pitch': machine.needlePitch.toString(),
                          }),
                        ),
                        if (machine.patternControlType != null) ...[
                          const SizedBox(width: 8),
                          _buildInfoChip(
                            icon: Icons.pattern,
                            label: 'userMachines_machineInfo_patternControl'.tr,
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      }),
      floatingActionButton: Obx(() {
        if (controller.isLoading.value || controller.userMachines.isEmpty) {
          return const SizedBox.shrink();
        }
        return FloatingActionButton(
          onPressed: () {
            //show new machine dialog
            showDialog(
              context: context,
              builder: (context) => MachineDialog(
                availableMachines: controller.availableMachines,
                onSave: (KnittingMachineModel baseMachine, String name,
                    String notes) {
                  controller.addMachine(baseMachine, name, notes);
                },
              ),
            );
          },
          child: const Icon(Icons.add),
        );
      }),
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String label,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: Colors.grey.shade700),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }
}
