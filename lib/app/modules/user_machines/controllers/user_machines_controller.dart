import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';
import 'package:xoxknit/app/data/models/knitting_machine_model.dart';
import 'package:xoxknit/app/data/models/user_knitting_machine_model.dart';
import 'package:xoxknit/app/data/models/user_model.dart';
import 'package:xoxknit/app/modules/user_machines/views/components/machine_dialog.dart';
import 'package:xoxknit/app/services/auth_service.dart';
import 'package:xoxknit/app/services/knitting/knitting_settings_service.dart';

class UserMachinesController extends GetxController {
  final _settingsService = KnittingSettingsService.to;
  final _authService = AuthService.to;
  final isLoading = true.obs;

  @override
  void onInit() {
    super.onInit();
    loadMachines().then((_) {
      if (!getParams().isBlank!) {
        showAddMachineDialog();
      }
    });
  }

  String? getParams() {
    final uri = Uri.tryParse(
        Get.rootDelegate.currentConfiguration?.locationString ?? '');
    return uri?.query;
  }

  Future<void> loadMachines() async {
    isLoading.value = true;
    try {
      await _settingsService.loadUserMachinePrefs();
    } finally {
      isLoading.value = false;
    }
  }

  List<KnittingMachineModel> get availableMachines =>
      _settingsService.knittingMachines;

  List<UserKnittingMachineModel> get userMachines =>
      _settingsService.userMachines;

  void showAddMachineDialog() {
    showDialog(
      context: Get.context!,
      builder: (context) => MachineDialog(
        availableMachines: availableMachines,
        onSave: (baseMachine, name, notes) {
          addMachine(baseMachine, name, notes);
        },
      ),
    );
  }

  Future<void> addMachine(
    KnittingMachineModel baseMachine,
    String customName,
    String? notes,
  ) async {
    isLoading.value = true;
    final newMachine = UserKnittingMachineModel(
      id: const Uuid().v4(),
      customName: customName,
      userId: _authService.currentUser.value!.shopifyIdNumber,
      baseModelId: baseMachine.id,
      notes: notes ?? "",
      machineClass: baseMachine.machineClass,
      mainBrand: baseMachine.mainBrand,
      model: baseMachine.model,
      type: baseMachine.type,
      needlePitch: baseMachine.needlePitch,
      needlesCount: baseMachine.needlesCount,
      patternControlType: baseMachine.patternControlType,
      patternRepeatLength: baseMachine.patternRepeatLength,
      altBrands: baseMachine.altBrands,
    );

    await _settingsService.addUserMachine(newMachine);
    isLoading.value = false;

    if (getParams() != null) {
      Get.rootDelegate.popRoute(popMode: PopMode.History);
    }
  }

  Future<void> updateMachine(
    UserKnittingMachineModel machine,
    String customName,
    String? notes,
  ) async {
    isLoading.value = true;

    final updatedMachine = machine.copyWith(
      customName: customName,
      notes: notes,
    );

    final index = userMachines.indexWhere((m) => m.id == machine.id);
    if (index != -1) {
      _settingsService.userMachines[index] = updatedMachine;
      await _settingsService.updateUserMachinePrefs(userMachines);
    }
    isLoading.value = false;
  }

  Future<void> deleteMachine(String machineId) async {
    try {
      // Don't set loading state for delete operation
      await _settingsService.removeUserKnittingMachine(machineId);
    } catch (e) {
      // Handle error if needed
      rethrow;
    }
  }
}
