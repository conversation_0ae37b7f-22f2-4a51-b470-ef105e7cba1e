import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:xoxknit/app/routes/app_pages.dart';
import '../../../data/models/wizard_state_model.dart';
import '../controllers/my_items_controller.dart';

class MyItemsView extends GetResponsiveView<MyItemsController> {
  MyItemsView({super.key});

  @override
  Widget? builder() {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Get.theme.colorScheme.surfaceContainerHighest,
        iconTheme: IconThemeData(color: Get.theme.colorScheme.primary),
        title: Obx(() => Text(
              controller.showArchived.value
                  ? 'myItems_archivedItems'.tr
                  : 'myItems_title'.tr,
              style: TextStyle(
                color: Get.theme.colorScheme.primary,
                fontWeight: FontWeight.bold,
                fontSize: screen.isPhone ? 20 : 22,
              ),
            )),
        actions: [
          Obx(() => IconButton(
                icon: Icon(
                  controller.showArchived.value
                      ? Icons.inventory_2_outlined
                      : FontAwesomeIcons.boxArchive,
                  color: Get.theme.colorScheme.primary,
                ),
                onPressed: controller.toggleArchivedView,
                tooltip: controller.showArchived.value
                    ? 'myItems_showActive'.tr
                    : 'myItems_showArchived'.tr,
              ))
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Get.theme.colorScheme.surfaceContainerHighest,
              Get.theme.colorScheme.surfaceContainerHighest.withOpacity(0.9),
            ],
          ),
        ),
        child: RefreshIndicator.adaptive(
          onRefresh: () => controller.loadItems(),
          child: Obx(
            () => controller.isLoading.value
                ? Center(
                    child: SpinKitPulse(
                      color: Get.theme.colorScheme.primary,
                      size: 50.0,
                    ),
                  )
                : controller.items.isEmpty
                    ? _buildEmptyState()
                    : _buildItemsList(),
          ),
        ),
      ),
      floatingActionButton: Obx(() => controller.showArchived.value
          ? const SizedBox.shrink()
          : FloatingActionButton(
              onPressed: () => Get.rootDelegate
                  .toNamed(Routes.MY_ITEMS + Routes.NEW_ITEM_WIZARD),
              backgroundColor: Get.theme.colorScheme.primary,
              child: const Icon(Icons.add),
            )),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Container(
        constraints: BoxConstraints(
          maxWidth: screen.isDesktop
              ? 600
              : screen.isTablet
                  ? 500
                  : double.infinity,
        ),
        padding: EdgeInsets.all(screen.isPhone ? 24 : 32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Obx(() => Icon(
                  controller.showArchived.value
                      ? Icons.archive_outlined
                      : Icons.inventory_2_outlined,
                  size: screen.isPhone ? 80 : 100,
                  color: Get.theme.colorScheme.primary,
                )),
            SizedBox(height: screen.isPhone ? 24 : 32),
            Obx(() => Text(
                  controller.showArchived.value
                      ? 'myItems_emptyStateArchived_title'.tr
                      : 'myItems_emptyStateActive_title'.tr,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: screen.isPhone ? 24 : 28,
                    fontWeight: FontWeight.bold,
                    color: Get.theme.colorScheme.onSurface,
                  ),
                )),
            SizedBox(height: screen.isPhone ? 16 : 20),
            Obx(() => Text(
                  controller.showArchived.value
                      ? 'myItems_emptyStateArchived_message'.tr
                      : 'myItems_emptyStateActive_message'.tr,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: screen.isPhone ? 16 : 18,
                    color: Get.theme.colorScheme.onSurface.withOpacity(0.6),
                  ),
                )),
            SizedBox(height: screen.isPhone ? 32 : 40),
            Obx(() => controller.showArchived.value
                ? ElevatedButton.icon(
                    onPressed: controller.toggleArchivedView,
                    icon: const Icon(Icons.inventory_2_outlined),
                    label:
                        Text('myItems_emptyStateArchived_viewActiveButton'.tr),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Get.theme.colorScheme.primary,
                      foregroundColor: Get.theme.colorScheme.onPrimary,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 32, vertical: 16),
                    ),
                  )
                : ElevatedButton.icon(
                    onPressed: () =>
                        Get.rootDelegate.toNamed(Routes.NEW_ITEM_WIZARD),
                    icon: const Icon(Icons.add),
                    label: Text('myItems_emptyStateActive_createButton'.tr),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Get.theme.colorScheme.primary,
                      foregroundColor: Get.theme.colorScheme.onPrimary,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 32, vertical: 16),
                    ),
                  )),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsList() {
    // Separate completed and in-progress items
    final completedItems =
        controller.items.where((item) => item.isCompleted).toList();
    final inProgressItems =
        controller.items.where((item) => !item.isCompleted).toList();

    // Determine layout based on screen size
    if (screen.isPhone) {
      return _buildSingleColumnList(inProgressItems, completedItems);
    } else {
      return _buildResponsiveList(inProgressItems, completedItems);
    }
  }

  Widget _buildSingleColumnList(List<WizardStateModel> inProgressItems,
      List<WizardStateModel> completedItems) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // In-progress items section
        if (inProgressItems.isNotEmpty) ...[
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Text(
              'myItems_inProgress'.tr,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Get.theme.colorScheme.onSurface.withOpacity(0.8),
              ),
            ),
          ),
          ...inProgressItems
              .map((item) => _buildDismissibleItemCard(Get.context!, item)),
        ],

        // Add divider if both sections have items
        if (inProgressItems.isNotEmpty && completedItems.isNotEmpty) ...[
          const SizedBox(height: 16),
          Divider(
              height: 1,
              color: Get.theme.colorScheme.onSurface.withOpacity(0.2)),
          const SizedBox(height: 16),
        ],

        // Completed items section
        if (completedItems.isNotEmpty) ...[
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Text(
              'myItems_completed'.tr,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Get.theme.colorScheme.onSurface.withOpacity(0.8),
              ),
            ),
          ),
          ...completedItems
              .map((item) => _buildDismissibleItemCard(Get.context!, item)),
        ],
      ],
    );
  }

  Widget _buildResponsiveList(List<WizardStateModel> inProgressItems,
      List<WizardStateModel> completedItems) {
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(
          horizontal: screen.isDesktop ? 40 : 24, vertical: 24),
      child: Center(
        child: Container(
          constraints: BoxConstraints(
            maxWidth: screen.isDesktop ? 1200 : 900,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (inProgressItems.isNotEmpty) ...[
                Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: Text(
                    'In Progress',
                    style: TextStyle(
                      fontSize: screen.isDesktop ? 20 : 18,
                      fontWeight: FontWeight.bold,
                      color: Get.theme.colorScheme.onSurface.withOpacity(0.8),
                    ),
                  ),
                ),
                _buildGridLayout(inProgressItems),
                SizedBox(height: 32),
              ],
              if (completedItems.isNotEmpty) ...[
                Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: Text(
                    'Completed',
                    style: TextStyle(
                      fontSize: screen.isDesktop ? 20 : 18,
                      fontWeight: FontWeight.bold,
                      color: Get.theme.colorScheme.onSurface.withOpacity(0.8),
                    ),
                  ),
                ),
                _buildGridLayout(completedItems),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGridLayout(List<WizardStateModel> items) {
    int crossAxisCount = screen.isDesktop ? 2 : 1;

    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        mainAxisSpacing: 16,
        crossAxisSpacing: 24,
        childAspectRatio: screen.isDesktop ? 3.2 : 2.8,
        mainAxisExtent: 140,
      ),
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      itemCount: items.length,
      itemBuilder: (context, index) {
        return _buildItemCard(context, items[index], isGrid: true);
      },
    );
  }

  Widget _buildDismissibleItemCard(
      BuildContext context, WizardStateModel item) {
    return Dismissible(
      key: Key(item.id),
      direction: DismissDirection.horizontal,
      background: Container(
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.only(left: 20.0),
        color: Colors.red,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(width: 20),
            const Icon(
              Icons.delete_outline,
              color: Colors.white,
              size: 24,
            ),
            const SizedBox(width: 8),
            Text(
              'common_delete'.tr,
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
      secondaryBackground: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20.0),
        color: controller.showArchived.value ? Colors.green : Colors.orange,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              controller.showArchived.value ? Icons.unarchive : Icons.archive,
              color: Colors.white,
              size: 24,
            ),
            const SizedBox(width: 8),
            Text(
              controller.showArchived.value
                  ? 'myItems_itemCard_unarchive'.tr
                  : 'myItems_itemCard_archive'.tr,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 20),
          ],
        ),
      ),
      confirmDismiss: (direction) async {
        if (direction == DismissDirection.endToStart) {
          if (controller.showArchived.value) {
            await controller.unarchiveItem(item);
          } else {
            await controller.archiveItem(item);
          }
          return true;
        } else if (direction == DismissDirection.startToEnd) {
          return await _showDeleteConfirmation(context, item);
        }
        return false;
      },
      child: _buildItemCard(context, item),
    );
  }

  Widget _buildItemCard(BuildContext context, WizardStateModel item,
      {bool isGrid = false}) {
    final progress = (item.currentStep + 1) / 3;
    final hasShapeData = item.shapeTestState != null;

    // Define card colors based on item state
    final cardColor = item.isCompleted
        ? Get.theme.colorScheme.surfaceContainerHighest
        : Get.theme.colorScheme.surface;

    final cardBorderColor = Get.isDarkMode
        ? Get.theme.colorScheme.onSurface.withOpacity(0.1)
        : Get.theme.colorScheme.onSurface.withOpacity(0.05);

    return Hero(
      tag: 'item_${item.id}',
      child: Card(
        margin: EdgeInsets.only(bottom: isGrid ? 0 : 12),
        color: cardColor,
        elevation: 3,
        shadowColor: Get.theme.colorScheme.shadow.withOpacity(0.3),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: cardBorderColor,
            width: 0.5,
          ),
        ),
        child: InkWell(
          onTap: () => controller.continueItem(item),
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            children: [
              Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: LinearProgressIndicator(
                  value: item.isCompleted ? 1.0 : progress,
                  backgroundColor:
                      Get.theme.colorScheme.surfaceContainerHighest,
                  valueColor: AlwaysStoppedAnimation<Color>(item.isCompleted
                      ? Colors.green
                      : Get.theme.colorScheme.primary),
                  minHeight: 3,
                ),
              ),
              if (item.isCompleted)
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: const BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(12),
                        bottomLeft: Radius.circular(8),
                      ),
                    ),
                    child: Text(
                      'myItems_itemCard_completed'.tr,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              Padding(
                padding: EdgeInsets.all(isGrid ? 16 : 12),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: isGrid ? 56 : 48,
                      height: isGrid ? 56 : 48,
                      decoration: BoxDecoration(
                        color: (item.isCompleted
                                ? Colors.green
                                : Get.theme.colorScheme.primary)
                            .withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: (item.isCompleted
                                  ? Colors.green
                                  : Get.theme.colorScheme.primary)
                              .withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: Center(
                        child: Icon(
                          controller.showArchived.value
                              ? Icons.archive_outlined
                              : item.isCompleted
                                  ? Icons.check_circle
                                  : hasShapeData
                                      ? Icons.shape_line
                                      : Icons.inventory_2_outlined,
                          color: item.isCompleted
                              ? Colors.green
                              : Get.theme.colorScheme.primary,
                          size: isGrid ? 28 : 24,
                        ),
                      ),
                    ),
                    SizedBox(width: isGrid ? 16 : 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            item.itemData.name ??
                                'myItems_itemCard_untitledItem'.tr,
                            style: TextStyle(
                              fontSize: isGrid ? 18 : 16,
                              fontWeight: FontWeight.bold,
                              color: item.isCompleted
                                  ? Get.theme.colorScheme.onSurface
                                      .withOpacity(0.8)
                                  : Get.theme.colorScheme.onSurface,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(height: isGrid ? 8 : 6),
                          Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: [
                              _buildInfoChip(
                                Icons.access_time,
                                _formatDate(item.lastModified),
                                color: item.isCompleted
                                    ? Colors.green
                                    : Get.theme.colorScheme.primary,
                                isGrid: isGrid,
                              ),
                              _buildInfoChip(
                                item.isCompleted
                                    ? Icons.check
                                    : Icons.flag_outlined,
                                item.isCompleted
                                    ? 'myItems_stepDescription_completed'.tr
                                    : controller
                                        .getStepDescription(item.currentStep),
                                color: item.isCompleted
                                    ? Colors.green
                                    : Get.theme.colorScheme.primary,
                                isGrid: isGrid,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    PopupMenuButton(
                      icon: Icon(Icons.more_vert,
                          size: 20,
                          color:
                              Get.theme.colorScheme.onSurface.withOpacity(0.8)),
                      color: Get.theme.colorScheme.surface,
                      elevation: 4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      itemBuilder: (context) => [
                        if (!controller.showArchived.value)
                          PopupMenuItem(
                            child: ListTile(
                              leading: Icon(Icons.edit,
                                  color: Get.theme.colorScheme.onSurface),
                              title: Text('common_continue'.tr),
                              contentPadding: EdgeInsets.zero,
                              dense: true,
                            ),
                            onTap: () => controller.continueItem(item),
                          ),
                        if (!controller.showArchived.value)
                          PopupMenuItem(
                            child: ListTile(
                              leading: Icon(
                                item.isCompleted
                                    ? Icons.unpublished_outlined
                                    : Icons.check_circle_outline,
                                color: item.isCompleted
                                    ? Colors.orange
                                    : Colors.green,
                              ),
                              title: Text(
                                item.isCompleted
                                    ? 'myItems_itemCard_knitAgain'.tr
                                    : 'myItems_itemCard_markComplete'.tr,
                                style: TextStyle(
                                  color: item.isCompleted
                                      ? Colors.orange
                                      : Colors.green,
                                ),
                              ),
                              contentPadding: EdgeInsets.zero,
                              dense: true,
                            ),
                            onTap: () => item.isCompleted
                                ? controller.markAsIncomplete(item)
                                : controller.markAsCompleted(item),
                          ),
                        PopupMenuItem(
                          child: ListTile(
                            leading: Icon(
                              controller.showArchived.value
                                  ? Icons.unarchive_outlined
                                  : Icons.archive_outlined,
                              color: Colors.orange,
                            ),
                            title: Text(
                              controller.showArchived.value
                                  ? 'myItems_itemCard_unarchive'.tr
                                  : 'myItems_itemCard_archive'.tr,
                              style: const TextStyle(color: Colors.orange),
                            ),
                            contentPadding: EdgeInsets.zero,
                            dense: true,
                          ),
                          onTap: () => controller.showArchived.value
                              ? controller.unarchiveItem(item)
                              : controller.archiveItem(item),
                        ),
                        PopupMenuItem(
                          child: ListTile(
                            leading: const Icon(Icons.delete_outline,
                                color: Colors.red),
                            title: Text('common_delete'.tr,
                                style: const TextStyle(color: Colors.red)),
                            contentPadding: EdgeInsets.zero,
                            dense: true,
                          ),
                          onTap: () => _showDeleteConfirmation(context, item),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip(IconData icon, String label,
      {Color? color, bool isGrid = false}) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: isGrid ? 4 : 2),
      decoration: BoxDecoration(
        color: (color ?? Get.theme.colorScheme.primary).withOpacity(0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: (color ?? Get.theme.colorScheme.primary).withOpacity(0.2),
          width: 0.5,
        ),
      ),
      child: Wrap(
        crossAxisAlignment: WrapCrossAlignment.center,
        children: [
          Icon(
            icon,
            size: isGrid ? 14 : 12,
            color: color ?? Get.theme.colorScheme.primary,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: isGrid ? 12 : 11,
              color: color ?? Get.theme.colorScheme.primary,
              fontWeight: FontWeight.w500,
              overflow: TextOverflow.ellipsis,
            ),
            maxLines: 1,
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return '${'common_today'.tr} ${DateFormat('HH:mm', Get.locale?.languageCode).format(date)}';
    } else if (difference.inDays == 1) {
      return '${'common_yesterday'.tr} ${DateFormat('HH:mm', Get.locale?.languageCode).format(date)}';
    } else if (difference.inDays < 7) {
      return 'common_daysAgo'.trParams({'days': difference.inDays.toString()});
    } else {
      return DateFormat('MMM d', Get.locale?.languageCode).format(date);
    }
  }

  Future<bool> _showDeleteConfirmation(BuildContext context, item) async {
    bool? result = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('myItems_deleteConfirmation_title'.tr),
          content: Text('myItems_deleteConfirmation_message'.tr),
          backgroundColor: Get.theme.colorScheme.surface,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          actions: <Widget>[
            TextButton(
              child: Text('common_cancel'.tr),
              onPressed: () => Navigator.of(context).pop(false),
            ),
            TextButton(
              child: Text(
                'common_delete'.tr,
                style: const TextStyle(color: Colors.red),
              ),
              onPressed: () {
                Navigator.of(context).pop(true);
              },
            ),
          ],
        );
      },
    );

    if (result == true) {
      await controller.deleteItem(item);
      return true;
    }

    return false;
  }
}
