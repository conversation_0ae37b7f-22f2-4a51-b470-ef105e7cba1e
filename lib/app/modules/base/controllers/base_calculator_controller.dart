import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/components/patterns/stitch_patterns.dart';
import 'package:xoxknit/app/components/patterns/v_stitch_pattern.dart';
import 'package:xoxknit/app/components/patterns/wave_stitch_pattern.dart';
import 'package:xoxknit/app/components/patterns/zigzag_stitch_pattern.dart';
import 'package:xoxknit/app/data/models/user_knitting_machine_model.dart';
import 'package:xoxknit/app/routes/app_pages.dart';
import 'package:xoxknit/app/services/knitting/knitting_settings_service.dart';
import 'package:xoxknit/app/utils/debouncer.dart';
import '../../../modules/shared/controllers/result_view_controller.dart';
import '../../../modules/shared/models/result_item.dart';

abstract class BaseCalculatorController extends GetxController {
  @protected
  late KnittingSettingsService knittingSettingsService;

  @protected
  final debouncer = Debouncer(delay: const Duration(milliseconds: 500));

  final isWizardMode = true.obs;
  final currentStep = 0.obs;
  final hasCalculated = false.obs;
  final isLoading = true.obs;
  final canCalculate = false.obs;
  final machineWarning = RxnString();

  // Common units
  final finalWidthUnit = 'cm'.obs;
  final finalLengthUnit = 'cm'.obs;

  final Rxn<UserKnittingMachineModel> selectedMachine =
      Rxn<UserKnittingMachineModel>();
  final RxInt needleCount = 0.obs;

  // Common controllers
  final stitchesController = TextEditingController();
  final rowsController = TextEditingController();
  final finalWidthController = TextEditingController();
  final finalLengthController = TextEditingController();

  // Results
  final calculatedStitches = 0.obs;
  final calculatedRows = 0.obs;

  @protected
  final Map<String, StitchPattern> stitchPatterns = {
    "v": const VStitchPattern(),
    'zigzag': const ZigZagStitchPattern(),
    'wave': const WaveStitchPattern()
  };

  // Animation related
  final visualizationProgress = 0.0.obs;
  AnimationController? _animationController;

  @protected
  int get maxSteps => 5;

  @override
  void onInit() {
    super.onInit();
    if (!Get.isRegistered<KnittingSettingsService>()) {
      Get.put(KnittingSettingsService());
    }
    knittingSettingsService = KnittingSettingsService.to;

    setupControllerListeners();
    loadSavedCalculatorValues();
  }

  void setupControllerListeners() {
    stitchesController.addListener(onFieldChange);
    rowsController.addListener(onFieldChange);
    finalWidthController.addListener(onFieldChange);
    finalLengthController.addListener(onFieldChange);
  }

  void toggleMode() => isWizardMode.value = !isWizardMode.value;

  void setMachine(UserKnittingMachineModel? machine) {
    selectedMachine.value = machine;
    if (machine != null) {
      needleCount.value = machine.needlesCount;
      _updateCanCalculate();
    }
  }

  bool validateMachineConstraints(int calculatedStitches) {
    if (selectedMachine.value == null) return false;

    final machine = selectedMachine.value!;
    if (calculatedStitches > machine.needlesCount) {
      _setMachineWarning(calculatedStitches, machine.needlesCount);
      return false;
    }
    machineWarning.value = null;
    return true;
  }

  void _setMachineWarning(int calculatedStitches, int machineNeedles) {
    final double maxPossibleWidth = (machineNeedles * getSwatchWidth()) /
        double.parse(stitchesController.text);
    final String widthUnit = finalWidthUnit.value;
    final String recommendedWidth = widthUnit == 'in'
        ? '${(maxPossibleWidth / 2.54).toStringAsFixed(2)} inches'
        : '${maxPossibleWidth.toStringAsFixed(2)} cm';

    machineWarning.value =
        'The calculated stitches ($calculatedStitches) exceed your machine\'s needle count ($machineNeedles). '
        'Try reducing the final width to $recommendedWidth or less to fit your machine.';
  }

  // Abstract methods to be implemented by subclasses
  double getSwatchWidth();
  double getSwatchLength();
  bool validateAllFields();

  Future<void> loadSavedCalculatorValues();
  void saveCalculatorValues();

  // Common utilities
  double convertToCm(double value, String unit) =>
      unit == 'in' ? value * 2.54 : value;

  // Common actions
  Future<void> calculate(
      {bool showResult = true, VoidCallback? onComplete}) async {
    debouncer(() {
      if (validateInputs()) {
        double finalWidth = double.parse(finalWidthController.text);
        double finalLength = double.parse(finalLengthController.text);
        int stitches = int.parse(stitchesController.text);
        int rows = int.parse(rowsController.text);

        finalWidth = convertToCm(finalWidth, finalWidthUnit.value);
        finalLength = convertToCm(finalLength, finalLengthUnit.value);

        final newCalculatedStitches =
            ((stitches * finalWidth) / getSwatchWidth()).round();
        final newCalculatedRows =
            ((rows * finalLength) / getSwatchLength()).round();

        calculatedStitches.value = newCalculatedStitches;
        calculatedRows.value = newCalculatedRows;
        hasCalculated.value = true;

        if (!validateMachineConstraints(newCalculatedStitches)) return;

        saveCalculatorValues();

        if (showResult) onShowResult();
      } else if (showResult) {
        previousStep();
      }

      if (onComplete != null) onComplete();
    });
  }

  // To be implemented by subclasses if needed
  void onShowResult() {}

  bool validateInputs() {
    if (selectedMachine.value == null) {
      Get.snackbar(
        'Error',
        'Please select a knitting machine',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    if (!validateAllFields()) {
      // Fluttertoast.showToast(msg: "Please enter valid numbers in all fields");
      return false;
    }

    return true;
  }

  // Common step navigation
  void nextStep() {
    print("currentStep.value: ${currentStep.value}  maxSteps: $maxSteps");
    if (currentStep.value < maxSteps) {
      currentStep.value++;
      if (currentStep.value == maxSteps && canCalculate.value) {
        calculate(showResult: false);
      }
    }
  }

  void previousStep() {
    if (currentStep.value > 0) {
      currentStep.value--;
    }
  }

  void _updateCanCalculate() => canCalculate.value = validateAllFields();

  @protected
  void onFieldChange() {
    _updateCanCalculate();
    if (canCalculate.value) calculate(showResult: false);
  }

  void skipToStep(int step) {
    if (step == maxSteps) {
      if (!validateAllFields()) {
        Get.snackbar(
          'Error',
          'Please fill in all fields to calculate',
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }
      calculate(showResult: false);
    }
    currentStep.value = step;
  }

  void clearCalculator() {
    selectedMachine.value = null;
    stitchesController.clear();
    rowsController.clear();
    finalWidthController.clear();
    finalLengthController.clear();
    calculatedStitches.value = 0;
    calculatedRows.value = 0;
    hasCalculated.value = false;
    currentStep.value = 0;
    machineWarning.value = null;

    // Clear values from local storage
    knittingSettingsService.clearLastValues();
  }

  void saveResults() async {
    // TODO: Save results to user project (firestore)
    Get.snackbar(
      'Success',
      'Results saved',
      snackPosition: SnackPosition.BOTTOM,
    );

    // Exit calculator page
    Get.rootDelegate.popRoute(popMode: PopMode.History).then((_) {
      return Get.rootDelegate.popRoute(popMode: PopMode.History);
    }).then((_) {
      if (Get.rootDelegate.currentConfiguration?.locationString ==
          Routes.CALCULATORS) {
        Get.rootDelegate.popRoute(popMode: PopMode.History);
      }
    });
  }

  void toggleUnit(String field) {
    final unitMap = {
      'finalWidth': finalWidthUnit,
      'finalLength': finalLengthUnit,
    };

    if (unitMap.containsKey(field)) {
      final unit = unitMap[field]!;
      unit.value = unit.value == 'cm' ? 'in' : 'cm';
      knittingSettingsService.saveLastValues(
        pieceWidthUnit: finalWidthUnit.value,
        pieceLengthUnit: finalLengthUnit.value,
      );
    }
  }

  @override
  void onClose() {
    debouncer.dispose();
    stitchesController.dispose();
    rowsController.dispose();
    finalWidthController.dispose();
    finalLengthController.dispose();
    disposeAnimation();
    super.onClose();
  }

  // Animation related methods
  void initializeAnimation(TickerProvider vsync) {
    // ... existing animation code ...
  }

  void disposeAnimation() {
    _animationController?.stop();
    _animationController?.dispose();
    _animationController = null;
  }

  void showResults({
    required String title,
    required List<ResultItem> results,
    Widget? visualization,
    bool showPatternSelector = false,
    Map<String, dynamic>? patterns,
  }) {
    ResultViewController.navigateToResult(
      title: title,
      results: results,
      visualization: visualization,
      showPatternSelector: showPatternSelector,
      patterns: patterns,
    );
  }
}
