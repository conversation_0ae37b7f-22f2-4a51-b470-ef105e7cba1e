import 'package:get/get.dart';
import 'package:xoxknit/app/routes/app_pages.dart';
import 'package:xoxknit/app/services/app_settings_service.dart';
import 'package:xoxknit/app/services/auth_service.dart';
import 'package:xoxknit/app/services/knitting/knitting_settings_service.dart';
import 'package:xoxknit/main.dart';

class SplashScreenController extends GetxController {
  final isLoading = true.obs;

  final List<RxBool> visibleLetters = List.generate(7, (_) => false.obs);
  final showLoadingIndicator = false.obs;
  final showLogo = false.obs;
  static const animationDuration = 2500;

  @override
  void onInit() {
    super.onInit();
    _animateLetters();
    initializeApp();
  }

  Future<void> _animateLetters() async {
    showLogo.value = true;
    await Future.delayed(const Duration(milliseconds: 500));

    for (int i = 0; i < visibleLetters.length; i++) {
      visibleLetters[i].value = true;
      await Future.delayed(const Duration(milliseconds: 200));
    }

    await Future.delayed(const Duration(milliseconds: 300));
    showLoadingIndicator.value = true;
  }

  Future<void> initializeApp() async {
    try {
      // Initialize all services
      await initializeServices();

      // Ensure critical services are ready
      final authService = AuthService.to;
      final appSettings = AppSettingsService.to;
      final knittingSettings = KnittingSettingsService.to;

      // Wait for all critical services to be ready
      await Future.wait([
        Future.value(authService.isInitialized),
        Future.value(appSettings.isReady),
        Future.value(knittingSettings.isInitialized),
      ]);

      // Wait for both the animation duration and services initialization
      await Future.delayed(const Duration(milliseconds: animationDuration));

      // Determine where to navigate
      final isFirstLoad = appSettings.isFirstLogin.value;

      if (isFirstLoad) {
        await Get.rootDelegate.offNamed(Routes.SIGNUP);
      } else if (authService.isLoggedIn) {
        await Get.rootDelegate.offNamed(Routes.HOME);
      } else {
        await Get.rootDelegate.offNamed(Routes.LOGIN);
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'An error occurred while initializing the app. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }
}
