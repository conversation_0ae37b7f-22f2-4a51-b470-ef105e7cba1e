import 'package:flutter/material.dart';
import 'dart:math' as math;

class KnittingPainter extends CustomPainter {
  final double animationValue;
  final Color needleColor;
  final Color yarnColor;
  final List<Offset> stitchPoints;

  KnittingPainter({
    required this.animationValue,
    required this.needleColor,
    required this.yarnColor,
    required this.stitchPoints,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..strokeCap = StrokeCap.round;

    // Draw knitting needles
    _drawNeedles(canvas, size, paint);

    // Draw yarn and stitches
    _drawYarnAndStitches(canvas, size, paint);
  }

  void _drawNeedles(Canvas canvas, Size size, Paint paint) {
    final needleLength = size.width * 0.8;
    final needleWidth = size.height * 0.03;

    // First needle (stationary)
    paint.color = needleColor;
    paint.strokeWidth = needleWidth;

    canvas.save();
    canvas.translate(size.width * 0.1, size.height * 0.4);
    canvas.drawLine(
      Offset.zero,
      Offset(needleLength, 0),
      paint,
    );

    // Needle tip (pointed end)
    final tipPath = Path()
      ..moveTo(needleLength, -needleWidth / 2)
      ..lineTo(needleLength + needleWidth * 2, 0)
      ..lineTo(needleLength, needleWidth / 2)
      ..close();

    paint.style = PaintingStyle.fill;
    canvas.drawPath(tipPath, paint);
    paint.style = PaintingStyle.stroke;
    canvas.restore();

    // Second needle (animated)
    canvas.save();
    canvas.translate(size.width * 0.1, size.height * 0.6);

    // Calculate needle movement
    final angle = math.sin(animationValue * math.pi * 2) * 0.2;
    canvas.rotate(angle);

    canvas.drawLine(
      Offset.zero,
      Offset(needleLength, 0),
      paint,
    );

    // Animated needle tip
    final animatedTipPath = Path()
      ..moveTo(needleLength, -needleWidth / 2)
      ..lineTo(needleLength + needleWidth * 2, 0)
      ..lineTo(needleLength, needleWidth / 2)
      ..close();

    paint.style = PaintingStyle.fill;
    canvas.drawPath(animatedTipPath, paint);
    paint.style = PaintingStyle.stroke;
    canvas.restore();
  }

  void _drawYarnAndStitches(Canvas canvas, Size size, Paint paint) {
    paint.color = yarnColor;
    paint.strokeWidth = 1.5;

    // Draw existing stitches
    for (int i = 0; i < stitchPoints.length - 1; i++) {
      final point = stitchPoints[i];
      final nextPoint = stitchPoints[i + 1];

      // Draw stitch loop
      final controlPoint1 = Offset(
        point.dx + (nextPoint.dx - point.dx) / 4,
        point.dy - size.height * 0.02,
      );
      final controlPoint2 = Offset(
        point.dx + (nextPoint.dx - point.dx) * 3 / 4,
        point.dy - size.height * 0.02,
      );

      final path = Path()
        ..moveTo(point.dx, point.dy)
        ..cubicTo(
          controlPoint1.dx,
          controlPoint1.dy,
          controlPoint2.dx,
          controlPoint2.dy,
          nextPoint.dx,
          nextPoint.dy,
        );

      canvas.drawPath(path, paint);
    }

    // Draw active yarn movement
    if (stitchPoints.isNotEmpty) {
      final lastPoint = stitchPoints.last;
      final yarnEndX = lastPoint.dx + size.width * 0.1;
      final yarnEndY = lastPoint.dy +
          math.sin(animationValue * math.pi * 2) * size.height * 0.03;

      paint.strokeWidth = 2.0;
      final activePath = Path()
        ..moveTo(lastPoint.dx, lastPoint.dy)
        ..quadraticBezierTo(
          lastPoint.dx + size.width * 0.05,
          lastPoint.dy + size.height * 0.02,
          yarnEndX,
          yarnEndY,
        );

      canvas.drawPath(activePath, paint);
    }
  }

  @override
  bool shouldRepaint(KnittingPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }
}
