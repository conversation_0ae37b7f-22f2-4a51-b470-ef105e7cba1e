import 'package:flutter/material.dart';
import 'knitting_painter.dart';

class AnimatedKnitting extends StatefulWidget {
  final Color needleColor;
  final Color yarnColor;

  const AnimatedKnitting({
    super.key,
    required this.needleColor,
    required this.yarnColor,
  });

  @override
  State<AnimatedKnitting> createState() => _AnimatedKnittingState();
}

class _AnimatedKnittingState extends State<AnimatedKnitting>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  final List<Offset> stitchPoints = [];

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat();

    // Initialize stitch points
    _initializeStitchPoints();
  }

  void _initializeStitchPoints() {
    // Create initial stitch pattern
    const numberOfStitches = 10;
    for (int i = 0; i < numberOfStitches; i++) {
      stitchPoints.add(Offset(
        40.0 + (i * 20.0),
        100.0,
      ));
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 300,
      height: 200,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return CustomPaint(
            painter: KnittingPainter(
              animationValue: _controller.value,
              needleColor: widget.needleColor,
              yarnColor: widget.yarnColor,
              stitchPoints: stitchPoints,
            ),
          );
        },
      ),
    );
  }
}
