import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/components/app_loader.dart';
import 'package:xoxknit/app/core/theme/app_typography.dart';
import 'package:xoxknit/app/modules/splash_screen/views/animated_knitting.dart';
import '../controllers/splash_screen_controller.dart';
import '../../../core/theme/app_colors.dart';

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SplashScreenController>(
      init: SplashScreenController(),
      builder: (controller) {
        return Scaffold(
            backgroundColor: Theme.of(context).scaffoldBackgroundColor,
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Obx(() => AnimatedOpacity(
                        opacity: controller.showLogo.value ? 1.0 : 0.0,
                        duration: Duration(milliseconds: 300),
                        child: Image.asset(
                          "assets/images/logo_white.png",
                          width: 300,
                        ),
                      )),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildAnimatedLetter('x', 0, controller),
                      _buildAnimatedLetter('o', 1, controller),
                      _buildAnimatedLetter('x', 2, controller),
                      _buildAnimatedLetter('k', 3, controller),
                      _buildAnimatedLetter('n', 4, controller),
                      _buildAnimatedLetter('.', 4, controller),
                      _buildAnimatedLetter('i', 5, controller),
                      _buildAnimatedLetter('t', 6, controller),
                    ],
                  ),
                  const SizedBox(height: 10),
                  Obx(() => AnimatedOpacity(
                        duration: const Duration(milliseconds: 500),
                        opacity:
                            controller.showLoadingIndicator.value ? 1.0 : 0.0,
                        child: _buildKnittingNeedle(context),
                      )),

                  // const SizedBox(height: 20),

                  AppLoader()
                ],
              ),
            ));
      },
    );
  }

  Widget _buildAnimatedLetter(
      String letter, int index, SplashScreenController controller) {
    return Obx(() {
      final isVisible = controller.visibleLetters[index].value;
      return AnimatedSlide(
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeOutBack,
        offset: isVisible ? Offset.zero : const Offset(0, 1),
        child: AnimatedOpacity(
          duration: const Duration(milliseconds: 500),
          opacity: isVisible ? 1.0 : 0.0,
          child: Text(
            letter,
            style: TextStyle(
                fontSize: 48,
                fontFamily: AppTypography.brandFont,
                fontWeight: FontWeight.bold,
                color: Get.theme.colorScheme.primary),
          ),
        ),
      );
    });
  }

  Widget _buildKnittingNeedle(BuildContext context) {
    return AnimatedKnitting(
      needleColor: Theme.of(context).brightness == Brightness.light
          ? AppColors.darkGray
          : AppColors.lightGray,
      yarnColor: Theme.of(context).primaryColor,
    );
  }
}
