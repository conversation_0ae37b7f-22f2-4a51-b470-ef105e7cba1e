import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/core/theme/app_colors.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import '../controllers/result_view_controller.dart';

class ResultView extends GetView<ResultViewController> {
  const ResultView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Safe<PERSON><PERSON>(
        child: Stack(
          children: [
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Align(
                        alignment: Alignment.topLeft, child: BackButton()),
                    const SizedBox(height: 40),
                    _buildResultHeader(),
                    const SizedBox(height: 24),
                    _buildResultNumbers(),
                    const SizedBox(height: 24),
                    Obx(() => controller.showPatternSelector.value
                        ? Column(
                            children: [
                              _buildPatternSelectDropDown(),
                              const SizedBox(height: 10),
                            ],
                          )
                        : const SizedBox.shrink()),
                    Obx(() => controller.resultVisualization.value != null
                        ? Expanded(child: controller.resultVisualization.value!)
                        : const SizedBox.shrink()),
                    const SizedBox(height: 20),
                    ElevatedButton(
                        onPressed: controller.saveResults,
                        child: Text('common_saveAndExit'.tr)),
                  ],
                ),
              ),
            ),
            Obx(() => controller.isLoading.value
                ? Container(
                    color: Colors.black54,
                    child: Center(
                      child: SpinKitDoubleBounce(
                        color: Get.theme.colorScheme.primary,
                        size: 50.0,
                      ),
                    ),
                  )
                : const SizedBox.shrink()),
          ],
        ),
      ),
    );
  }

  Widget _buildResultHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Get.theme.colorScheme.primary,
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.auto_awesome,
            color: AppColors.white,
          ),
        ),
        const SizedBox(width: 16),
        Obx(() => Text(
              controller.resultTitle.value,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            )),
      ],
    );
  }

  Widget _buildResultNumbers() {
    return Obx(() => Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: controller.results
              .map((result) => _buildResultCard(
                    result.label,
                    result.value,
                    result.icon,
                  ))
              .toList(),
        ));
  }

  Widget _buildResultCard(String label, String value, IconData icon) {
    return Card(
      elevation: 2,
      child: Container(
        padding: const EdgeInsets.all(16),
        width: 140,
        child: Column(
          children: [
            Icon(
              icon,
              color: Get.theme.colorScheme.primary,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Get.theme.colorScheme.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPatternSelectDropDown() {
    return Obx(() => Container(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            color: Get.theme.colorScheme.primary,
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: controller.selectedPattern.value,
              dropdownColor: Get.theme.colorScheme.primary,
              items: controller.patterns.keys
                  .map((pattern) => DropdownMenuItem<String>(
                        value: pattern,
                        child: Text(
                          "${pattern.capitalizeFirst!} Stitch",
                          style: const TextStyle(color: AppColors.white),
                        ),
                      ))
                  .toList(),
              onChanged: controller.selectPattern,
              icon: const Icon(
                Icons.arrow_drop_down,
                color: AppColors.white,
              ),
              style: const TextStyle(color: AppColors.white),
            ),
          ),
        ));
  }
}
