import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../routes/app_pages.dart';
import '../models/result_item.dart';

class ResultViewController extends GetxController
    with GetSingleTickerProviderStateMixin {
  static ResultViewController get to => Get.find();

  late AnimationController animationController;
  final isLoading = false.obs;
  final visualizationProgress = 0.0.obs;

  final List<ResultItem> results = <ResultItem>[].obs;
  final selectedPattern = ''.obs;
  final Map<String, dynamic> patterns = <String, dynamic>{}.obs;
  final resultTitle = ''.obs;
  final showPatternSelector = false.obs;
  final resultVisualization = Rxn<Widget>();

  @override
  void onInit() {
    super.onInit();
    initializeAnimation();
  }

  void initializeAnimation() {
    animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..addListener(() {
        visualizationProgress.value = animationController.value;
      });

    animationController.forward();
  }

  @override
  void onClose() {
    disposeAnimation();
    super.onClose();
  }

  void disposeAnimation() {
    animationController.dispose();
  }

  void selectPattern(String? pattern) {
    if (pattern != null) {
      selectedPattern.value = pattern;
    }
  }

  void setupResult({
    required String title,
    required List<ResultItem> results,
    Widget? visualization,
    bool showPatternSelector = false,
    Map<String, dynamic>? patterns,
  }) {
    resultTitle.value = title;
    this.results.clear();
    this.results.addAll(results);
    this.showPatternSelector.value = showPatternSelector;

    if (patterns != null) {
      this.patterns.clear();
      this.patterns.addAll(patterns);
      if (patterns.isNotEmpty) {
        selectedPattern.value = patterns.keys.first;
      }
    }

    if (visualization != null) {
      resultVisualization.value = visualization;
    }
  }

  Future<void> saveResults() async {
    isLoading.value = true;
    //TODO:  Implement save logic here
    await Future.delayed(const Duration(seconds: 1)); // Mock save
    isLoading.value = false;
    Get.back();
  }

  static void navigateToResult({
    required String title,
    required List<ResultItem> results,
    Widget? visualization,
    bool showPatternSelector = false,
    Map<String, dynamic>? patterns,
  }) {
    final controller = Get.find<ResultViewController>();
    controller.setupResult(
      title: title,
      results: results,
      visualization: visualization,
      showPatternSelector: showPatternSelector,
      patterns: patterns,
    );
    Get.rootDelegate.toNamed(Routes.CALCULATORS + Routes.CALCULATOR_RESULT);
  }
}
