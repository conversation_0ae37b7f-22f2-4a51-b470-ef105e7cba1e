import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/swatch_calculator/views/components/Input_field.dart';
import '../controllers/swatch_calculator_controller.dart';

class SwatchInfoScreen extends StatelessWidget {
  const SwatchInfoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 450;
    final horizontalPadding = isSmallScreen ? 8.0 : 16.0;

    return GetBuilder<SwatchController>(
      init: SwatchController(),
      builder: (controller) => Obx(
        () => controller.isLoading.value
            ? const Center(child: CircularProgressIndicator())
            : LayoutBuilder(
                builder: (context, constraints) {
                  return SingleChildScrollView(
                    child: GestureDetector(
                      onTap: () => Get.focusScope?.unfocus(),
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: horizontalPadding, vertical: 8.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Card(
                              child: Padding(
                                padding:
                                    EdgeInsets.all(isSmallScreen ? 8.0 : 12.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'swatchCalculator_measurements'.tr,
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleMedium
                                          ?.copyWith(
                                            fontWeight: FontWeight.bold,
                                            color: Theme.of(context)
                                                .colorScheme
                                                .primary,
                                          ),
                                    ),
                                    const SizedBox(height: 8),
                                    InputField(
                                      label:
                                          'swatchCalculator_swatchWidth_label'
                                              .tr,
                                      hint: 'swatchCalculator_swatchWidth_hint'
                                          .tr,
                                      field: controller.swatchWidth,
                                      controller: controller,
                                      textController:
                                          controller.swatchWidthController,
                                      description:
                                          'swatchCalculator_swatchWidth_description'
                                              .tr,
                                    ),
                                    InputField(
                                      label:
                                          'swatchCalculator_swatchLength_label'
                                              .tr,
                                      hint: 'swatchCalculator_swatchLength_hint'
                                          .tr,
                                      field: controller.swatchLength,
                                      controller: controller,
                                      textController:
                                          controller.swatchLengthController,
                                      description:
                                          'swatchCalculator_swatchLength_description'
                                              .tr,
                                    ),
                                    InputField(
                                      label:
                                          'swatchCalculator_swatchWeight_label'
                                              .tr,
                                      hint: 'swatchCalculator_swatchWeight_hint'
                                          .tr,
                                      field: controller.swatchWeight,
                                      controller: controller,
                                      textController:
                                          controller.swatchWeightController,
                                      description:
                                          'swatchCalculator_swatchWeight_description'
                                              .tr,
                                    ),
                                    InputField(
                                      label:
                                          'swatchCalculator_stitchesInSwatch_label'
                                              .tr,
                                      hint:
                                          'swatchCalculator_stitchesInSwatch_hint'
                                              .tr,
                                      field: controller.stitchesInSwatch,
                                      controller: controller,
                                      textController:
                                          controller.stitchesController,
                                      description:
                                          'swatchCalculator_stitchesInSwatch_description'
                                              .tr,
                                    ),
                                    Obx(() => controller
                                            .validationMessage.value.isNotEmpty
                                        ? Padding(
                                            padding: const EdgeInsets.only(
                                                top: 8.0, bottom: 4.0),
                                            child: Text(
                                              controller
                                                  .validationMessage.value,
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.copyWith(
                                                    color: Theme.of(context)
                                                        .colorScheme
                                                        .error,
                                                  ),
                                            ),
                                          )
                                        : const SizedBox.shrink()),
                                    InputField(
                                      label:
                                          'swatchCalculator_rowsInSwatch_label'
                                              .tr,
                                      hint: 'swatchCalculator_rowsInSwatch_hint'
                                          .tr,
                                      field: controller.rowsInSwatch,
                                      controller: controller,
                                      textController: controller.rowsController,
                                      description:
                                          'swatchCalculator_rowsInSwatch_description'
                                              .tr,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(height: 8),

                            // results card
                            Obx(
                              () => controller.isLoading.value
                                  ? const Center(
                                      child: CircularProgressIndicator())
                                  : Card(
                                      child: Padding(
                                        padding: EdgeInsets.all(
                                            isSmallScreen ? 12.0 : 16.0),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'swatchCalculator_results'.tr,
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .titleMedium
                                                  ?.copyWith(
                                                    fontWeight: FontWeight.bold,
                                                    color: Theme.of(context)
                                                        .colorScheme
                                                        .primary,
                                                  ),
                                            ),
                                            const SizedBox(height: 16),
                                            Card(
                                              elevation: 0,
                                              color: Theme.of(context)
                                                  .colorScheme
                                                  .surfaceVariant
                                                  .withOpacity(0.5),
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                                side: BorderSide(
                                                  color: Theme.of(context)
                                                      .colorScheme
                                                      .outline
                                                      .withOpacity(0.2),
                                                ),
                                              ),
                                              child: Padding(
                                                padding: EdgeInsets.all(
                                                    isSmallScreen
                                                        ? 12.0
                                                        : 16.0),
                                                child: Column(
                                                  children: [
                                                    _buildResultRow(
                                                      context: context,
                                                      label:
                                                          'swatchCalculator_resultLabels_stitchesPer10Cm'
                                                              .tr,
                                                      value: controller
                                                          .stitchesPer10Cm.value
                                                          .round()
                                                          .toString(),
                                                      icon: Icons.grid_4x4,
                                                      isBold: true,
                                                      isSmallScreen:
                                                          isSmallScreen,
                                                    ),
                                                    const Padding(
                                                      padding:
                                                          EdgeInsets.symmetric(
                                                              vertical: 8.0),
                                                      child: Divider(),
                                                    ),
                                                    _buildResultRow(
                                                      context: context,
                                                      label:
                                                          'swatchCalculator_resultLabels_rowsPer10Cm'
                                                              .tr,
                                                      value: controller
                                                          .rowsPer10Cm.value
                                                          .round()
                                                          .toString(),
                                                      icon: Icons
                                                          .view_week_outlined,
                                                      isBold: true,
                                                      isSmallScreen:
                                                          isSmallScreen,
                                                    ),
                                                    if (controller
                                                            .weightPer100CmSquared
                                                            .value >
                                                        0) ...[
                                                      const Padding(
                                                        padding: EdgeInsets
                                                            .symmetric(
                                                                vertical: 8.0),
                                                        child: Divider(),
                                                      ),
                                                      _buildResultRow(
                                                        context: context,
                                                        label:
                                                            'swatchCalculator_resultLabels_weightPer100CmSquared'
                                                                .tr,
                                                        value:
                                                            '${controller.weightPer100CmSquared.value.round()}g',
                                                        icon: Icons
                                                            .scale_outlined,
                                                        isBold: true,
                                                        isSmallScreen:
                                                            isSmallScreen,
                                                      ),
                                                    ],
                                                  ],
                                                ),
                                              ),
                                            ),
                                            const SizedBox(height: 16),
                                          ],
                                        ),
                                      ),
                                    ),
                            )
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
      ),
    );
  }

  Widget _buildResultRow({
    required BuildContext context,
    required String label,
    required String value,
    required IconData icon,
    bool isBold = false,
    bool isSmallScreen = false,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: isSmallScreen ? 4.0 : 6.0),
      child: LayoutBuilder(
        builder: (context, constraints) {
          final isNarrow = constraints.maxWidth < 300;

          if (isNarrow) {
            // Stack layout for very narrow screens
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: Theme.of(context)
                            .colorScheme
                            .primary
                            .withOpacity(0.1),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Icon(
                        icon,
                        color: Theme.of(context).colorScheme.primary,
                        size: 16,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        label,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight:
                                  isBold ? FontWeight.bold : FontWeight.normal,
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurface
                                  .withOpacity(0.8),
                            ),
                      ),
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, top: 4.0),
                  child: Text(
                    value,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontWeight:
                              isBold ? FontWeight.w900 : FontWeight.normal,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                  ),
                ),
              ],
            );
          }

          // Default row layout
          return Row(
            children: [
              Container(
                padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: Theme.of(context).colorScheme.primary,
                  size: isSmallScreen ? 18 : 20,
                ),
              ),
              SizedBox(width: isSmallScreen ? 8 : 12),
              Expanded(
                child: Text(
                  label,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontWeight:
                            isBold ? FontWeight.w900 : FontWeight.normal,
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withOpacity(0.8),
                        fontSize: isSmallScreen ? 14 : null,
                      ),
                ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: isBold ? FontWeight.w900 : FontWeight.normal,
                      color: Theme.of(context).colorScheme.primary,
                      fontSize: isSmallScreen ? 14 : null,
                    ),
              ),
            ],
          );
        },
      ),
    );
  }
}
