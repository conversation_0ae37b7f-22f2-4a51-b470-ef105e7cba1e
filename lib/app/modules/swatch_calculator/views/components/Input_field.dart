import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/swatch_calculator/controllers/swatch_calculator_controller.dart';

import '../../models/measurement_field.dart';

class InputField extends StatelessWidget {
  final String label;
  final String hint;
  final MeasurementField field;
  final SwatchController controller;
  final String description; // Add this field
  final TextEditingController textController;

  const InputField({
    super.key,
    required this.label,
    required this.hint,
    required this.field,
    required this.controller,
    required this.textController,
    this.description = '',
  });

  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(label),
          content: Text(description),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('swatchCalculator_gotIt'.tr),
            ),
          ],
        );
      },
    );
  }

  String _getHintText(String hint) {
    switch (hint) {
      case 'Width':
        return 'swatchCalculator_enterSwatchWidth'.tr;
      case 'Length':
        return 'swatchCalculator_enterSwatchLength'.tr;
      case 'Weight':
        return 'swatchCalculator_enterSwatchWeight'.tr;
      case 'Stitches':
        return 'swatchCalculator_enterSwatchStitches'.tr;
      case 'Rows':
        return 'swatchCalculator_enterSwatchRows'.tr;
      default:
        return 'swatchCalculator_enterMeasurement'.tr;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check screen width for responsive layout
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 450;

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(
            horizontal: isSmallScreen ? 8.0 : 12.0,
            vertical: isSmallScreen ? 10.0 : 8.0),
        child: LayoutBuilder(
          builder: (context, constraints) {
            final isNarrowLayout = MediaQuery.of(context).size.width < 400;

            if (isNarrowLayout) {
              return Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    flex: 5,
                    child: Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                label,
                                style: Theme.of(context)
                                    .textTheme
                                    .titleSmall
                                    ?.copyWith(fontWeight: FontWeight.bold),
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 2),
                              Text(
                                _getHintText(hint),
                                style: Theme.of(context).textTheme.bodySmall,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                        IconButton(
                          icon: Icon(
                            Icons.help_outline,
                            size: 20,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          onPressed: () => _showHelpDialog(context),
                          tooltip: 'swatchCalculator_learnMore'
                              .trParams({'label': label}),
                          constraints:
                              const BoxConstraints(minWidth: 30, minHeight: 30),
                          padding: EdgeInsets.zero,
                          visualDensity: VisualDensity.compact,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    flex: 4,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Expanded(
                          child: SizedBox(
                            height: 40,
                            child: TextFormField(
                              controller: textController,
                              textAlign: TextAlign.center,
                              keyboardType:
                                  const TextInputType.numberWithOptions(
                                      decimal: true),
                              decoration: InputDecoration(
                                hintText: hint,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8.0),
                                ),
                                contentPadding:
                                    const EdgeInsets.symmetric(horizontal: 8.0),
                                isDense: true,
                              ),
                              onChanged: (value) {
                                field.value.value = value;
                                controller.calculateResults();
                              },
                            ),
                          ),
                        ),
                        const SizedBox(width: 6),

                        // Unit button
                        SizedBox(
                          height: 40,
                          width: 45,
                          child: field.type == MeasurementType.count
                              ? const SizedBox.shrink()
                              : Obx(() {
                                  final unitLabel = field.unitLabel;

                                  if (unitLabel == '') {
                                    return const SizedBox.shrink();
                                  }

                                  return ElevatedButton(
                                    onPressed: () {
                                      controller.toggleFieldUnit(
                                          field, textController);
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor:
                                          Theme.of(context).colorScheme.primary,
                                      foregroundColor: Theme.of(context)
                                          .colorScheme
                                          .onPrimary,
                                      padding: EdgeInsets.zero,
                                      shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                      ),
                                      textStyle: Theme.of(context)
                                          .textTheme
                                          .bodyMedium
                                          ?.copyWith(
                                              fontWeight: FontWeight.bold),
                                    ),
                                    child: Text(
                                      unitLabel,
                                      softWrap: false,
                                    ),
                                  );
                                }),
                        ),
                      ],
                    ),
                  ),
                ],
              );
            }

            // Horizontal layout for wider screens
            return Row(
              children: [
                Expanded(
                  flex: 3,
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              label,
                              style: Theme.of(context).textTheme.titleSmall,
                            ),
                            const SizedBox(height: 2),
                            Text(
                              _getHintText(hint),
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.help_outline),
                        onPressed: () => _showHelpDialog(context),
                        tooltip: 'swatchCalculator_learnMore'
                            .trParams({'label': label}),
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  flex: 2,
                  child: Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: textController,
                          keyboardType: const TextInputType.numberWithOptions(
                              decimal: true),
                          decoration: InputDecoration(
                            hintText: hint,
                            border: const OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                vertical: isSmallScreen ? 6 : 8,
                                horizontal: isSmallScreen ? 8 : 12),
                          ),
                          onChanged: (value) {
                            field.value.value = value;
                            controller.calculateResults();
                          },
                        ),
                      ),
                      if (field.type != MeasurementType.count) ...[
                        const SizedBox(width: 8),
                        SizedBox(
                          width: field.type == MeasurementType.weight ? 40 : 40,
                          child: field.type == MeasurementType.count
                              ? const SizedBox.shrink()
                              : Obx(() {
                                  final label = field.unitLabel;
                                  return ElevatedButton(
                                    onPressed:
                                        field.type == MeasurementType.count
                                            ? null
                                            : () {
                                                controller.toggleFieldUnit(
                                                    field, textController);
                                              },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Theme.of(context)
                                          .colorScheme
                                          .primaryContainer,
                                      padding: EdgeInsets.zero,
                                      textStyle: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onPrimaryContainer,
                                      ),
                                    ),
                                    child: Text(label),
                                  );
                                }),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
