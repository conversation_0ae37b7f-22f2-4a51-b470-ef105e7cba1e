import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ResultPair extends StatelessWidget {
  final String label1;
  final RxDouble value1;
  final String label2;
  final RxDouble value2;
  final bool secondIsLength;
  final bool firstNeedsConversion;

  const ResultPair({
    super.key,
    required this.label1,
    required this.value1,
    required this.label2,
    required this.value2,
    required this.secondIsLength,
    this.firstNeedsConversion = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label1,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  Obx(() => Text(
                        firstNeedsConversion
                            ? '${(value1.value * 10).round().toStringAsFixed(1)} /10cm (${(value1.value * 2.54).toStringAsFixed(1)} /in)'
                            : value1.value.toStringAsFixed(1),
                        style: Theme.of(context).textTheme.bodyMedium,
                      )),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label2,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  Obx(() => Text(
                        secondIsLength
                            ? '${value2.value.toStringAsFixed(2)} cm (${(value2.value * 0.393701).toStringAsFixed(2)} in)'
                            : value2.value.toStringAsFixed(2),
                        style: Theme.of(context).textTheme.bodyMedium,
                      )),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
