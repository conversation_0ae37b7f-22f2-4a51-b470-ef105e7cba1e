import 'package:get/get.dart';

enum MeasurementUnit { cm, inches }

enum WeightUnit { g, oz }

enum MeasurementType { length, weight, count }

class MeasurementField {
  final RxString value = RxString('');
  final MeasurementType type;
  final Rx<MeasurementUnit> unit = Rx<MeasurementUnit>(MeasurementUnit.cm);
  final Rx<WeightUnit> weightUnit = Rx<WeightUnit>(WeightUnit.g);

  // static const double _cmToInches = 0.393701;
  static const double _inchesToCm = 2.54;
  static const double _ozToG = 28.3495;
  // static const double _gToOz = 0.035274;

  String get defaultValue => type == MeasurementType.length ? '10' : '';

  MeasurementField([
    String initialValue = '',
    bool isWeight = false,
    this.type = MeasurementType.length,
  ]) {
    value.value = initialValue.isEmpty ? defaultValue : initialValue;
  }

  void toggleUnit() {
    if (type == MeasurementType.count) return; // No unit toggle for count type

    if (type == MeasurementType.weight) {
      weightUnit.value =
          weightUnit.value == WeightUnit.g ? WeightUnit.oz : WeightUnit.g;
    } else if (type == MeasurementType.length) {
      unit.value = unit.value == MeasurementUnit.cm
          ? MeasurementUnit.inches
          : MeasurementUnit.cm;
    }
  }

  double get valueInCm {
    if (type != MeasurementType.length) return 0;
    if (value.value.isEmpty) return 0;

    double? number = double.tryParse(value.value);
    if (number == null) return 0;

    return unit.value == MeasurementUnit.inches ? number * _inchesToCm : number;
  }

  double get valueInGrams {
    if (type != MeasurementType.weight) return 0;
    if (value.value.isEmpty) return 0;

    double? number = double.tryParse(value.value);
    if (number == null) return 0;

    return weightUnit.value == WeightUnit.oz ? number * _ozToG : number;
  }

  void reset() {
    value.value = defaultValue;
    unit.value = MeasurementUnit.cm;
    weightUnit.value = WeightUnit.g;
  }

  String get unitLabel {
    switch (type) {
      case MeasurementType.length:
        return unit.value == MeasurementUnit.cm ? 'cm' : 'in';
      case MeasurementType.weight:
        return weightUnit.value == WeightUnit.g ? 'g' : 'oz';
      case MeasurementType.count:
        return '';
    }
  }
}
