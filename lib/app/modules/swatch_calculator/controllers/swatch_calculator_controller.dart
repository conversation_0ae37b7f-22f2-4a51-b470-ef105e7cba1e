import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/src/rx_workers/utils/debouncer.dart';
import 'package:xoxknit/app/services/knitting/knitting_settings_service.dart';
import 'package:xoxknit/app/modules/new_item/controllers/new_item_wizard_controller.dart';
import '../models/measurement_field.dart';

class SwatchController extends GetxController {
  final wizardController = Get.find<NewItemWizardController>();

  // Input fields with specific measurement types
  final swatchWidth = MeasurementField('10', false, MeasurementType.length);
  final swatchLength = MeasurementField('10', false, MeasurementType.length);
  final swatchWeight = MeasurementField('', true, MeasurementType.weight);
  final stitchesInSwatch = MeasurementField('', false, MeasurementType.count);
  final rowsInSwatch = MeasurementField('', false, MeasurementType.count);

  final knittingService = Get.find<KnittingSettingsService>();

  final _debouncer = Debouncer(delay: const Duration(milliseconds: 500));

  // Result observables
  final stitchesPerCm = 0.0.obs;
  final cmPerStitch = 0.0.obs;
  final rowsPerCm = 0.0.obs;
  final cmPerRow = 0.0.obs;

  // New observables for 10cm measurements
  final stitchesPer10Cm = 0.0.obs;
  final rowsPer10Cm = 0.0.obs;
  final weightPer100CmSquared = 0.0.obs;

  // Gauge-specific results
  final gaugeMessage = ''.obs;
  final isGaugeCalculated = false.obs;
  final validationMessage = ''.obs;

  RxBool isLoading = false.obs;

  // Conversion factors
  static const double _cmToInches = 0.393701;
  static const double _inchesToCm = 2.54;
  static const double _gramsToOunces = 0.035274;
  static const double _ouncesToGrams = 28.3495;

  // Add TextEditingControllers
  late TextEditingController swatchWidthController;
  late TextEditingController swatchLengthController;
  late TextEditingController swatchWeightController;
  late TextEditingController stitchesController;
  late TextEditingController rowsController;

  // Flag to track if initial load happened
  bool _initialLoadComplete = false;

  bool validateStitchesCount(String value) {
    if (value.isEmpty) return true;

    final stitches = int.tryParse(value);
    if (stitches == null) return false;

    final machine = wizardController.newItem.value.knittingMachine;
    if (machine == null) {
      validationMessage.value = 'newItemWizard_form_validation_required'
          .trParams({'field': 'machine'.tr});
      return false;
    }

    if (stitches > machine.needlesCount) {
      validationMessage.value = 'newItemWizard_form_validation_number'.tr;
      return false;
    }

    validationMessage.value = '';
    return true;
  }

  @override
  void onInit() {
    super.onInit();
    // Initialize controllers
    swatchWidthController = TextEditingController();
    swatchLengthController = TextEditingController();
    swatchWeightController = TextEditingController();
    stitchesController = TextEditingController();
    rowsController = TextEditingController();

    // Prioritize loading from newItem
    bool loadedFromNewItem = _loadFromNewItem();

    // If not loaded from newItem, try loading last values
    if (!loadedFromNewItem) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        loadLastValues();
      });
    } else {
      _initialLoadComplete = true;
      calculateResults();
    }

    // Set up listeners for text controllers
    swatchWidthController.addListener(() {
      swatchWidth.value.value = swatchWidthController.text;
      if (_initialLoadComplete) calculateResults();
    });

    swatchLengthController.addListener(() {
      swatchLength.value.value = swatchLengthController.text;
      if (_initialLoadComplete) calculateResults();
    });

    swatchWeightController.addListener(() {
      swatchWeight.value.value = swatchWeightController.text;
      if (_initialLoadComplete) calculateResults();
    });

    stitchesController.addListener(() {
      if (validateStitchesCount(stitchesController.text)) {
        stitchesInSwatch.value.value = stitchesController.text;
        if (_initialLoadComplete) calculateResults();
      }
    });

    rowsController.addListener(() {
      rowsInSwatch.value.value = rowsController.text;
      if (_initialLoadComplete) calculateResults();
    });
  }

  @override
  void onClose() {
    swatchWidthController.dispose();
    swatchLengthController.dispose();
    swatchWeightController.dispose();
    stitchesController.dispose();
    rowsController.dispose();
    super.onClose();
  }

  // Load data from NewItemModel
  bool _loadFromNewItem() {
    final newItem = wizardController.newItem.value;
    final swatchInfo = newItem.swatchInfo;

    if (swatchInfo != null && swatchInfo.isNotEmpty) {
      swatchWidthController.text = swatchInfo['width'] ?? '10';
      swatchLengthController.text = swatchInfo['length'] ?? '10';
      swatchWeightController.text = swatchInfo['weight'] ?? '';
      stitchesController.text = swatchInfo['stitches'] ?? '';
      rowsController.text = swatchInfo['rows'] ?? '';

      // Update MeasurementField values
      swatchWidth.value.value = swatchWidthController.text;
      swatchLength.value.value = swatchLengthController.text;
      swatchWeight.value.value = swatchWeightController.text;
      stitchesInSwatch.value.value = stitchesController.text;
      rowsInSwatch.value.value = rowsController.text;

      // *** Load field-specific units if available in swatchInfo ***
      // Example: assumes swatchInfo stores 'widthUnit' and 'lengthUnit'
      swatchWidth.unit.value = (swatchInfo['widthUnit'] == 'inches')
          ? MeasurementUnit.inches
          : MeasurementUnit.cm;
      swatchLength.unit.value = (swatchInfo['lengthUnit'] == 'inches')
          ? MeasurementUnit.inches
          : MeasurementUnit.cm;

      print("Loaded swatch data from NewItemModel");
      return true;
    }

    if (newItem.weightOnHand != null && swatchWeightController.text.isEmpty) {
      swatchWeightController.text = newItem.weightOnHand.toString();
      swatchWeight.value.value = swatchWeightController.text;
    }

    return false;
  }

  Future<void> loadLastValues() async {
    if (_initialLoadComplete) return;

    isLoading.value = true;
    await knittingService.loadLastValues();

    if (knittingService.lastSwatchWidth.value != null) {
      // *** Set field-specific units first ***
      swatchWidth.unit.value =
          knittingService.lastSwatchWidthUnit.value == 'inches'
              ? MeasurementUnit.inches
              : MeasurementUnit.cm;
      swatchLength.unit.value =
          knittingService.lastSwatchLengthUnit.value == 'inches'
              ? MeasurementUnit.inches
              : MeasurementUnit.cm;

      swatchWidthController.text =
          knittingService.lastSwatchWidth.value?.toString() ?? '10';
      swatchLengthController.text =
          knittingService.lastSwatchLength.value?.toString() ?? '10';
      stitchesController.text =
          knittingService.lastStitches.value?.toString() ?? '';
      rowsController.text = knittingService.lastRows.value?.toString() ?? '';
      swatchWeightController.text =
          knittingService.lastSwatchWeight.value?.toString() ?? '';

      // Update the MeasurementField values (obs part)
      swatchWidth.value.value = swatchWidthController.text;
      swatchLength.value.value = swatchLengthController.text;
      stitchesInSwatch.value.value = stitchesController.text;
      rowsInSwatch.value.value = rowsController.text;
      swatchWeight.value.value = swatchWeightController.text;

      print("Loaded swatch data from KnittingSettingsService");
      calculateResults();
    } else {
      // Set defaults if nothing loaded
      swatchWidthController.text = '10';
      swatchLengthController.text = '10';
      swatchWidth.value.value = '10';
      swatchLength.value.value = '10';
      swatchWidth.unit.value = MeasurementUnit.cm; // Default unit
      swatchLength.unit.value = MeasurementUnit.cm; // Default unit
      calculateResults();
    }

    isLoading.value = false;
    _initialLoadComplete = true;
  }

  // Toggle unit for a specific field and update its text field ***
  void toggleFieldUnit(
      MeasurementField field, TextEditingController textController) {
    // Allow toggling only for length and weight types
    if (field.type != MeasurementType.length &&
        field.type != MeasurementType.weight) {
      return;
    }

    double? currentValue = double.tryParse(textController.text
        .replaceAll(',', '.')); // Handle both dots and commas
    if (currentValue == null || currentValue == 0) {
      field.toggleUnit(); // Toggle unit label even if value is invalid/zero
      calculateResults();
      return;
    }

    // Store the unit *before* toggling
    final oldLengthUnit = field.unit.value;
    final oldWeightUnit = field.weightUnit.value;

    field
        .toggleUnit(); // Toggles the appropriate unit within the MeasurementField

    // Get the unit *after* toggling
    final newLengthUnit = field.unit.value;
    final newWeightUnit = field.weightUnit.value;

    double convertedValue;

    // Handle Length Conversion
    if (field.type == MeasurementType.length) {
      if (oldLengthUnit == MeasurementUnit.cm &&
          newLengthUnit == MeasurementUnit.inches) {
        convertedValue = currentValue * _cmToInches;
      } else if (oldLengthUnit == MeasurementUnit.inches &&
          newLengthUnit == MeasurementUnit.cm) {
        convertedValue = currentValue * _inchesToCm;
      } else {
        convertedValue = currentValue;
      }
    }
    // Handle Weight Conversion
    else if (field.type == MeasurementType.weight) {
      if (oldWeightUnit == WeightUnit.g && newWeightUnit == WeightUnit.oz) {
        convertedValue = currentValue * _gramsToOunces;
      } else if (oldWeightUnit == WeightUnit.oz &&
          newWeightUnit == WeightUnit.g) {
        convertedValue = currentValue * _ouncesToGrams;
      } else {
        convertedValue = currentValue;
      }
    } else {
      // Should not happen due to the initial check
      convertedValue = currentValue;
    }

    // Format the converted value for display
    String formattedValue;
    if (newLengthUnit == MeasurementUnit.inches ||
        newWeightUnit == WeightUnit.oz) {
      // Use more precision for imperial units if needed, e.g., 2 or 3 decimals
      formattedValue = convertedValue.toStringAsFixed(2);
    } else {
      // cm or grams
      // Use fewer decimals for metric
      formattedValue = convertedValue.toStringAsFixed(1);
    }

    // Update the text controller
    textController.text = formattedValue;
    field.value.value = formattedValue;

    // Recalculate results based on the new unit and value
    calculateResults();
  }

  // Simplified format function - primarily for results display now
  String formatMeasurement(double valueCm, MeasurementType type) {
    if (type == MeasurementType.count) {
      return valueCm.round().toString();
    }
    if (type == MeasurementType.weight) {
      return valueCm.toStringAsFixed(1); // Format weight
    }
    // For length/width results (already in cm), format consistently
    return valueCm.toStringAsFixed(1);
  }

  void calculateResults() {
    _debouncer.cancel();
    _performCalculation();
    _debouncer(_performCalculation);
  }

  // Extracted calculation logic
  void _performCalculation() {
    if (!_initialLoadComplete) {
      print("Calculation skipped: Initial load not complete.");
      return;
    }
    if (validationMessage.value.isNotEmpty) {
      print("Calculation skipped: Validation error.");
      return;
    }

    // *** Use MeasurementField's valueInCm which uses its internal unit ***
    double width = swatchWidth.valueInCm;
    double length = swatchLength.valueInCm;
    double weight = swatchWeight.valueInGrams;
    double stitches = double.tryParse(stitchesInSwatch.value.value) ?? 0;
    double rows = double.tryParse(rowsInSwatch.value.value) ?? 0;

    print(
        "Calculating with: W=$width cm, L=$length cm, Wt=$weight g, S=$stitches, R=$rows"); // Removed unit from log

    // Reset results before recalculating
    stitchesPerCm.value = 0.0;
    cmPerStitch.value = 0.0;
    stitchesPer10Cm.value = 0.0;
    rowsPerCm.value = 0.0;
    cmPerRow.value = 0.0;
    rowsPer10Cm.value = 0.0;
    weightPer100CmSquared.value = 0.0;

    if (width > 0 && stitches > 0) {
      stitchesPerCm.value = stitches / width;
      cmPerStitch.value = width / stitches;
      stitchesPer10Cm.value = (stitchesPerCm.value * 10);
    }

    if (length > 0 && rows > 0) {
      rowsPerCm.value = rows / length;
      cmPerRow.value = length / rows;
      rowsPer10Cm.value = rowsPerCm.value * 10;
    }

    if (width > 0 && length > 0 && weight > 0) {
      double area = width * length;
      weightPer100CmSquared.value = (weight / area) * 100;
    } else {
      weightPer100CmSquared.value = 0.0;
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Update the NewItemModel
      wizardController.newItem.update((val) {
        if (val != null) {
          val.stitchesPerCm = stitchesPerCm.value;
          val.rowsPerCm = rowsPerCm.value;
          val.stitchesPer10Cm = stitchesPer10Cm.value;
          val.rowsPer10Cm = rowsPer10Cm.value;
          val.weightPer100CmSquared = weightPer100CmSquared.value;
          // Store raw input values & their units
          val.swatchInfo = {
            'stitches': stitchesController.text.trim(),
            'rows': rowsController.text.trim(),
            'weight': swatchWeightController.text.trim(),
            'width': swatchWidthController.text.trim(),
            'length': swatchLengthController.text.trim(),
            // *** Store field-specific units ***
            'widthUnit': swatchWidth.unit.value == MeasurementUnit.inches
                ? 'inches'
                : 'cm',
            'lengthUnit': swatchLength.unit.value == MeasurementUnit.inches
                ? 'inches'
                : 'cm',
          };
          print("Updated NewItemModel: ${val.swatchInfo}");
        }
      });
    });

    calculateGauge();
  }

  void calculateGauge() {
    if (!_areAllFieldsFilled()) {
      gaugeMessage.value = 'newItemWizard_form_validation_required'
          .trParams({'field': 'all fields'.tr});
      isGaugeCalculated.value = false;
      return;
    }

    // Use the calculated cm values for gauge logic
    double widthCm = swatchWidth.valueInCm;
    double lengthCm = swatchLength.valueInCm;
    double stitches = double.tryParse(stitchesInSwatch.value.value) ?? 0;
    double rows = double.tryParse(rowsInSwatch.value.value) ?? 0;

    if (widthCm <= 0 || lengthCm <= 0 || stitches <= 0 || rows <= 0) {
      gaugeMessage.value = 'newItemWizard_form_validation_number'.tr;
      isGaugeCalculated.value = false;
      return;
    }

    // Use the already calculated per-cm results
    String horizontalGauge = _formatGauge(
      stitchesPerCm.value,
      cmPerStitch.value,
      'stitches'.tr,
    );
    String verticalGauge = _formatGauge(
      rowsPerCm.value,
      cmPerRow.value,
      'rows'.tr,
    );

    gaugeMessage.value = 'Gauge:\n$horizontalGauge\n$verticalGauge';
    isGaugeCalculated.value = true;

    if (isGaugeCalculated.value) {
      knittingService.saveLastValues(
        // *** FIX: Save value directly from controller text, not assumed getter ***
        swatchWidth: double.tryParse(swatchWidthController.text),
        swatchLength: double.tryParse(swatchLengthController.text),
        stitches: int.tryParse(stitchesInSwatch.value.value),
        rows: int.tryParse(rowsInSwatch.value.value),
        swatchWeight: swatchWeight.valueInGrams,
        // *** Save field-specific units ***
        swatchWidthUnit:
            swatchWidth.unit.value == MeasurementUnit.inches ? 'inches' : 'cm',
        swatchLengthUnit:
            swatchLength.unit.value == MeasurementUnit.inches ? 'inches' : 'cm',
        stitchesPerCm: stitchesPerCm.value,
        rowsPerCm: rowsPerCm.value,
      );
      print("Saved values to KnittingSettingsService");
    }
  }

  String _formatGauge(double unitsPerCm, double cmPerUnit, String unit) {
    bool isCloseToWhole = (unitsPerCm * 10).round() % 10 == 0;
    if (isCloseToWhole) {
      return '${unitsPerCm.round()} $unit per cm (${cmPerUnit.toStringAsFixed(2)} cm per $unit)';
    } else {
      return '${unitsPerCm.toStringAsFixed(1)} $unit per cm (${cmPerUnit.toStringAsFixed(2)} cm per $unit)';
    }
  }

  bool _areAllFieldsFilled() {
    // Weight is optional, others are not (based on MeasurementField definition)
    return swatchWidth.value.value.isNotEmpty &&
        swatchLength.value.value.isNotEmpty &&
        stitchesInSwatch.value.value.isNotEmpty &&
        rowsInSwatch.value.value.isNotEmpty;
  }

  void resetCalculator() {
    swatchWidthController.text = '10';
    swatchLengthController.text = '10';
    swatchWeightController.text = '';
    stitchesController.text = '';
    rowsController.text = '';
    // Reset MeasurementField obs values and units
    swatchWidth.reset(); // Assumes reset() handles value and unit
    swatchLength.reset();
    swatchWeight.reset();
    stitchesInSwatch.reset();
    rowsInSwatch.reset();
    // Reset results
    stitchesPerCm.value = 0.0;
    cmPerStitch.value = 0.0;
    rowsPerCm.value = 0.0;
    cmPerRow.value = 0.0;
    stitchesPer10Cm.value = 0.0;
    rowsPer10Cm.value = 0.0;
    weightPer100CmSquared.value = 0.0;
    gaugeMessage.value = '';
    isGaugeCalculated.value = false;
    validationMessage.value = '';
    _initialLoadComplete = false; // Allow reloading
    WidgetsBinding.instance.addPostFrameCallback((_) {
      bool loaded = _loadFromNewItem();
      if (!loaded) {
        loadLastValues();
      }
    });
  }

  String? validateInput(String value) {
    if (value.isEmpty) return null;
    double? number = double.tryParse(value);
    if (number == null) {
      return 'newItemWizard_form_validation_number'.tr;
    }
    if (number <= 0) {
      return 'newItemWizard_form_validation_number'.tr;
    }
    return null;
  }

  void openShapeEditor() {
    if (isGaugeCalculated.value) {
      wizardController.updateGauge(
        stitchesPerCm: stitchesPerCm.value,
        rowsPerCm: rowsPerCm.value,
        stitchesPer10Cm: stitchesPer10Cm.value,
        rowsPer10Cm: rowsPer10Cm.value,
        weightPer100CmSquared: weightPer100CmSquared.value,
      );
      // Maybe navigate here? Get.toNamed(...);
    }
  }
}
