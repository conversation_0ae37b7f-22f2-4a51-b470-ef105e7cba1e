import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LoadingIndicator extends StatelessWidget {
  const LoadingIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: GetPlatform.isIOS
          ? CupertinoActivityIndicator(
              color: Get.theme.colorScheme.surface,
              radius: 15,
            )
          : CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(
                Get.theme.colorScheme.surface,
              ),
            ),
    );
  }
}
