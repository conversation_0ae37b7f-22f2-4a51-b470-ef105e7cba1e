import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'calculator_widgets.dart';

abstract class CalculatorFormBase extends StatelessWidget {
  const CalculatorFormBase({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Get.focusScope?.unfocus(),
      child: SingleChildScrollView(
        child: Form(
          key: getForm<PERSON>ey(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: buildFormFields(context),
          ),
        ),
      ),
    );
  }

  GlobalKey<FormState> getFormKey();
  List<Widget> buildFormFields(BuildContext context);
}
