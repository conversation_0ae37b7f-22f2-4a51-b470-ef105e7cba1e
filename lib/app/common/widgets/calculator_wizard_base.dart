import 'package:flutter/material.dart';
import 'package:get/get.dart';

abstract class CalculatorWizardBase extends StatelessWidget {
  const CalculatorWizardBase({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Stepper(
          type: StepperType.vertical,
          currentStep: getCurrentStep(),
          onStepContinue: () {
            if (getCurrentStep() < getMaxSteps()) {
              onNextStep();
            }
          },
          onStepCancel: onPreviousStep,
          onStepTapped: onStepTapped,
          controlsBuilder: _buildControls,
          steps: buildSteps(),
        ));
  }

  Widget _buildControls(BuildContext context, ControlsDetails details) {
    return Padding(
      padding: const EdgeInsets.only(top: 16.0),
      child: Row(
        children: [
          if (getCurrentStep() < getMaxSteps())
            ElevatedButton(
              onPressed: details.onStepContinue,
              child: Text('common_continue'.tr),
            ),
          const SizedBox(width: 8),
          if (getCurrentStep() == getMaxSteps()) const SizedBox.shrink(),
          const SizedBox(width: 8),
          if (getCurrentStep() > 0)
            OutlinedButton(
              onPressed: details.onStepCancel,
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: Get.theme.colorScheme.primary),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text('common_back'.tr),
            ),
        ],
      ),
    );
  }

  int getCurrentStep();
  int getMaxSteps();
  void onNextStep();
  void onPreviousStep();
  void onStepTapped(int step);
  List<Step> buildSteps();
}
