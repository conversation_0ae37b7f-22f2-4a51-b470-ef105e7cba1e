import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CalculatorSectionTitle extends StatelessWidget {
  final String title;

  const CalculatorSectionTitle(this.title, {super.key});

  @override
  Widget build(BuildContext context) {
    return Text(
      title.tr,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
      ),
    );
  }
}

class CalculatorMeasurementField extends StatelessWidget {
  final String label;
  final TextEditingController controller;
  final RxString unitObservable;
  final VoidCallback onUnitToggle;

  const CalculatorMeasurementField({
    super.key,
    required this.label,
    required this.controller,
    required this.unitObservable,
    required this.onUnitToggle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label.tr),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: controller,
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                decoration: const InputDecoration(border: OutlineInputBorder()),
              ),
            ),
            const SizedBox(width: 8),
            Obx(() => ElevatedButton(
                  onPressed: onUnitToggle,
                  child: Text(unitObservable.value),
                )),
          ],
        ),
      ],
    );
  }
}

class CalculatorNumberField extends StatelessWidget {
  final String label;
  final TextEditingController controller;

  const CalculatorNumberField({
    super.key,
    required this.label,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label.tr),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(border: OutlineInputBorder()),
        ),
      ],
    );
  }
}
