import 'package:flutter/material.dart';
import 'package:get/get.dart';

mixin CalculatorBaseMixin {
  final formKey = GlobalKey<FormState>();
  final currentStep = 0.obs;
  final isWizardMode = true.obs;
  final isLoading = false.obs;

  void toggleMode() {
    isWizardMode.value = !isWizardMode.value;
  }

  void nextStep() {
    currentStep.value++;
  }

  void previousStep() {
    if (currentStep.value > 0) {
      currentStep.value--;
    }
  }

  void skipToStep(int step) {
    currentStep.value = step;
  }
}
