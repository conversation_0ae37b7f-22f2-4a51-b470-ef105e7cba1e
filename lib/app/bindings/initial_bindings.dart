import 'package:get/get.dart';
import 'package:xoxknit/app/core/theme/theme_binding.dart';
import 'package:xoxknit/app/services/app_settings_service.dart';
import 'package:xoxknit/app/services/auth_service.dart';
import 'package:xoxknit/app/services/knitting/knitting_settings_service.dart';
import 'package:xoxknit/app/services/locale_service.dart';
import 'package:xoxknit/app/services/product_service.dart';
import 'package:xoxknit/app/services/route_service.dart';
import 'package:xoxknit/app/services/wizard_state_service.dart';

class InitialBinding implements Bindings {
  @override
  void dependencies() async {
    ThemeBinding().dependencies();
    Get.put(LocaleService(), permanent: true);
    Get.put(RouteService());
    Get.put(AppSettingsService());
    Get.put(ProductService());
  }
}
