import 'package:flutter/foundation.dart';

import 'package:get/get.dart';

import '../middleware/auth_middleware.dart';
import '../middleware/machine_check_middleware.dart';
import '../modules/home/<USER>/home_binding.dart';
import '../modules/home/<USER>/home_view.dart';
import '../modules/login/bindings/login_binding.dart';
import '../modules/login/views/login_view.dart';
import '../modules/projects/bindings/projects_binding.dart';
import '../modules/projects/views/projects_view.dart';
import '../modules/reset_password/bindings/reset_password_binding.dart';
import '../modules/reset_password/views/reset_password_view.dart';
import '../modules/settings/bindings/settings_binding.dart';
import '../modules/settings/views/settings_view.dart';
import '../modules/shape_test/bindings/shape_test_binding.dart';
import '../modules/shape_test/views/shape_editor_view.dart';
import '../modules/shape_test/views/zones_editor_view.dart';
import '../modules/signup/bindings/signup_binding.dart';
import '../modules/signup/views/signup_view.dart';
import '../modules/splash_screen/bindings/splash_screen_binding.dart';
import '../modules/splash_screen/views/splash_screen_view.dart';
import '../modules/swatch_calculator/bindings/swatch_calculator_binding.dart';
import '../modules/swatch_calculator/views/swatch_calculator_view.dart';
import '../modules/user_machines/bindings/user_machines_binding.dart';
import '../modules/user_machines/views/user_machines_view.dart';
import '../modules/new_item/bindings/new_item_wizard_binding.dart';
import '../modules/new_item/views/new_item_wizard_view.dart';
import '../modules/my_items/views/my_items_view.dart';
import '../modules/my_items/controllers/my_items_controller.dart';

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static const INITIAL = kIsWeb ? Routes.HOME : Routes.SPLASH_SCREEN;

  static final routes = [
    GetPage(
      name: _Paths.HOME,
      page: () => HomeView(),
      binding: HomeBinding(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: _Paths.SPLASH_SCREEN,
      page: () => const SplashScreen(),
      binding: SplashScreenBinding(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: _Paths.LOGIN,
      page: () => LoginView(),
      binding: LoginBinding(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: _Paths.SIGNUP,
      page: () => SignupView(),
      binding: SignupBinding(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: _Paths.PROJECTS,
      page: () => const ProjectsView(),
      binding: ProjectsBinding(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: _Paths.SETTINGS,
      page: () => const SettingsView(),
      binding: SettingsBinding(),
      middlewares: [AuthMiddleware()],
      children: [
        GetPage(
          name: _Paths.USER_MACHINES,
          page: () => const UserMachinesView(),
          binding: UserMachinesBinding(),
        ),
      ],
    ),
    GetPage(
      name: _Paths.RESET_PASSWORD,
      page: () => const ResetPasswordView(),
      binding: ResetPasswordBinding(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: _Paths.SHAPE_TEST,
      page: () => const ShapeEditorView(),
      binding: ShapeTestBinding(),
      children: [
        GetPage(
          name: _Paths.ZONES_EDITOR,
          page: () => const ZonesEditorView(),
        ),
      ],
    ),
    GetPage(
      name: _Paths.ZONES_EDITOR,
      page: () => const ZonesEditorView(),
      binding: ShapeTestBinding(),
    ),
    GetPage(
      name: _Paths.SWATCH_CALCULATOR,
      page: () => SwatchInfoScreen(),
      binding: SwatchCalculatorBinding(),
      middlewares: [
        MachineCheckMiddleware(),
        AuthMiddleware(),
      ],
    ),
    GetPage(
        name: Routes.MY_ITEMS,
        page: () => MyItemsView(),
        binding: BindingsBuilder(() {
          Get.lazyPut<MyItemsController>(() => MyItemsController());
        }),
        middlewares: [
          AuthMiddleware()
        ],
        children: [
          GetPage(
            name: _Paths.NEW_ITEM_WIZARD,
            page: () => NewItemWizardView(),
            binding: NewItemWizardBinding(),
            middlewares: [AuthMiddleware()],
            participatesInRootNavigator: true,
            preventDuplicates: true,
            fullscreenDialog: true,
            transition: Transition.rightToLeft,
            popGesture: true,
            children: [
              GetPage(
                name: '/swatch-calculator',
                page: () => const SwatchInfoScreen(),
                binding: SwatchCalculatorBinding(),
              ),
            ],
          ),
        ]),
  ];
}
