part of 'app_pages.dart';
// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

abstract class Routes {
  Routes._();
  static const HOME = _Paths.HOME;
  static const SPLASH_SCREEN = _Paths.SPLASH_SCREEN;
  static const LOGIN = _Paths.LOGIN;
  static const SIGNUP = _Paths.SIGNUP;
  static const PROJECTS = _Paths.PROJECTS;
  static const SETTINGS = _Paths.SETTINGS;
  static const CALCULATORS = _Paths.CALCULATORS;
  static const GAUGE_CALCULATOR = _Paths.GAUGE_CALCULATOR;
  static const RESET_PASSWORD = _Paths.RESET_PASSWORD;
  static const USER_MACHINES = _Paths.USER_MACHINES;
  static const RECTANGLE_CALCULATOR = _Paths.RECTANGLE_CALCULATOR;
  static const CALCULATOR_RESULT = _Paths.CALCULATOR_RESULT;
  static const TRIANGLE_CALCULATOR = _Paths.TRIANGLE_CALCULATOR;
  // static const SHAPE_EDITOR = _Paths.SHAPE_EDITOR;
  static const SWATCH_CALCULATOR = _Paths.SWATCH_CALCULATOR;
  static const NEW_SHAPE_EDITOR = _Paths.NEW_SHAPE_EDITOR;
  static const NEW_ITEM_WIZARD = _Paths.NEW_ITEM_WIZARD;
  static const MY_ITEMS = _Paths.MY_ITEMS;
  static const SHAPE_TEST = _Paths.SHAPE_TEST;
  static const ZONES_EDITOR = _Paths.ZONES_EDITOR;
}

abstract class _Paths {
  _Paths._();
  static const HOME = '/home';
  static const SPLASH_SCREEN = '/splash-screen';
  static const LOGIN = '/login';
  static const SIGNUP = '/signup';
  static const PROJECTS = '/projects';
  static const SETTINGS = '/settings';
  static const CALCULATORS = '/calculators';

  static const GAUGE_CALCULATOR = '/gauge';
  static const RECTANGLE_CALCULATOR = '/rectangle-calculator';
  static const TRIANGLE_CALCULATOR = '/triangle-calculator';
  static const RESET_PASSWORD = '/reset-password';
  static const USER_MACHINES = '/user-machines';
  static const CALCULATOR_RESULT = '/result';

  static const SWATCH_CALCULATOR = '/swatch-calculator';
  static const NEW_SHAPE_EDITOR = '/new-shape-editor';
  static const NEW_ITEM_WIZARD = '/new-item-wizard';
  static const MY_ITEMS = '/my-items';
  static const SHAPE_TEST = '/shape-test';
  static const ZONES_EDITOR = '/zones-editor';
}
