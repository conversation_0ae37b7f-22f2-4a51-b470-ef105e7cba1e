import 'dart:math' as math;
import 'package:xoxknit/app/modules/shape_test/utils/decimal_input_helper.dart';

/// Utility class for consistent dimension calculations and conversions.
class DimensionUtils {
  // Fixed decimal places for consistent display
  static const int CM_DISPLAY_PRECISION = 1; // 0.0 format for cm
  static const int PIXEL_PRECISION = 1; // 0.0 format for internal calculations

  // --- Display Formatting ---

  /// Formats a centimeter value for consistent display, with standardized rounding.
  /// Uses locale-aware decimal separator (comma or period).
  static String formatCmForDisplay(double cm, {bool? useCommaAsSeparator}) {
    // Round to fixed number of decimal places for all cm display
    return DecimalInputHelper.formatDecimal(
      cm,
      decimalPlaces: CM_DISPLAY_PRECISION,
      useCommaAsSeparator: useCommaAsSeparator,
    );
  }

  /// Formats a grid unit (stitches/rows) value for display - whole numbers only.
  static String formatGridUnitsForDisplay(int units) {
    return units.toString();
  }

  /// Parse a user input string that may contain comma or period as decimal separator
  /// Returns null if invalid or not positive
  static double? parseUserDecimalInput(String input,
      {bool requirePositive = true}) {
    if (requirePositive) {
      return DecimalInputHelper.parsePositiveDecimal(input);
    } else {
      return DecimalInputHelper.parseDecimal(input);
    }
  }

  /// Validate user decimal input and get detailed feedback
  static ValidationResult validateUserDecimalInput(String input,
      {bool requirePositive = true}) {
    return DecimalInputHelper.validateDecimalInput(input,
        requirePositive: requirePositive);
  }

  /// Standardized rounding for cm values to ensure consistency
  static double roundCmValue(double value) {
    // Use a consistent rounding method for all cm values
    final factor = math.pow(10, CM_DISPLAY_PRECISION);
    return (value * factor).round() / factor;
  }

  /// Standardized rounding for pixel values in internal calculations
  static double roundPixelValue(double pixels) {
    final factor = math.pow(10, PIXEL_PRECISION);
    return (pixels * factor).round() / factor;
  }

  // --- Pixels to Grid Units ---

  /// Converts pixel width to the nearest whole number of stitches.
  static int pixelsToStitches(double pixels, double cellWidth) {
    if (cellWidth <= 0) return 0;
    // Use rounded pixel value for consistency
    pixels = roundPixelValue(pixels);
    return (pixels / cellWidth).round();
  }

  /// Converts pixel height to the nearest whole number of rows.
  static int pixelsToRows(double pixels, double cellWidth, double aspectRatio) {
    if (cellWidth <= 0 || aspectRatio <= 0) return 0;
    // Use rounded pixel value for consistency
    pixels = roundPixelValue(pixels);
    return (pixels / (cellWidth * aspectRatio)).round();
  }

  // --- Grid Units to Pixels ---

  /// Converts stitches to pixel width.
  static double stitchesToPixels(int stitches, double cellWidth) {
    double pixels = stitches * cellWidth;
    // Round for consistency in internal calculations
    return roundPixelValue(pixels);
  }

  /// Converts rows to pixel height.
  static double rowsToPixels(int rows, double cellWidth, double aspectRatio) {
    double pixels = rows * cellWidth * aspectRatio;
    // Round for consistency in internal calculations
    return roundPixelValue(pixels);
  }

  // --- Pixels to Metric Units (cm) ---

  /// Converts pixel width to centimeters.
  static double pixelsToCmWidth(
      double pixels, double cellWidth, double stitchesPerCm) {
    if (cellWidth <= 0 || stitchesPerCm <= 0) return 0.0;
    // Pixels -> Grid Units -> CM
    pixels = roundPixelValue(pixels);
    double cm = (pixels / cellWidth) / stitchesPerCm;
    return roundCmValue(cm);
  }

  /// Converts pixel height to centimeters.
  static double pixelsToCmHeight(
      double pixels, double cellWidth, double aspectRatio, double rowsPerCm) {
    if (cellWidth <= 0 || aspectRatio <= 0 || rowsPerCm <= 0) return 0.0;
    // Pixels -> Grid Units -> CM
    pixels = roundPixelValue(pixels);
    double cm = (pixels / (cellWidth * aspectRatio)) / rowsPerCm;
    return roundCmValue(cm);
  }

  // --- Metric Units (cm) to Pixels ---

  /// Converts centimeters width to pixels.
  static double cmToPixelsWidth(
      double cm, double cellWidth, double stitchesPerCm) {
    // CM -> Grid Units -> Pixels
    cm = roundCmValue(cm);
    double pixels = cm * stitchesPerCm * cellWidth;
    return roundPixelValue(pixels);
  }

  /// Converts centimeters height to pixels.
  static double cmToPixelsHeight(
      double cm, double cellWidth, double aspectRatio, double rowsPerCm) {
    // CM -> Grid Units -> Pixels
    cm = roundCmValue(cm);
    double pixels = cm * rowsPerCm * cellWidth * aspectRatio;
    return roundPixelValue(pixels);
  }

  // --- Grid Units to Metric Units (cm) ---

  /// Converts stitches to centimeters.
  static double stitchesToCm(int stitches, double stitchesPerCm) {
    if (stitchesPerCm <= 0) return 0.0;
    double cm = stitches / stitchesPerCm;
    return roundCmValue(cm);
  }

  /// Converts rows to centimeters.
  static double rowsToCm(int rows, double rowsPerCm) {
    if (rowsPerCm <= 0) return 0.0;
    double cm = rows / rowsPerCm;
    return roundCmValue(cm);
  }

  // --- Metric Units (cm) to Grid Units ---

  /// Converts centimeters width to the nearest whole number of stitches.
  static int cmToStitches(double cm, double stitchesPerCm) {
    if (stitchesPerCm <= 0) return 0;
    cm = roundCmValue(cm);
    return (cm * stitchesPerCm).round();
  }

  /// Converts centimeters height to the nearest whole number of rows.
  static int cmToRows(double cm, double rowsPerCm) {
    if (rowsPerCm <= 0) return 0;
    cm = roundCmValue(cm);
    return (cm * rowsPerCm).round();
  }

  // --- Calculations from Instructions ---

  /// Calculates the number of active stitches in the first row (top width).
  static int getTopStitches(List<List<bool>> instructions) {
    if (instructions.isEmpty) return 0;
    return instructions.first.where((stitch) => stitch).length;
  }

  /// Calculates the number of active stitches in the last row (bottom width).
  static int getBottomStitches(List<List<bool>> instructions) {
    if (instructions.isEmpty) return 0;
    return instructions.last.where((stitch) => stitch).length;
  }

  /// Calculates the total number of rows.
  static int getTotalRows(List<List<bool>> instructions) {
    return instructions.length;
  }
}
