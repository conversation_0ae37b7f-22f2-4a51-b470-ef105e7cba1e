import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:xoxknit/app/data/models/new_item_model.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/knitting_zone_models.dart';
import 'package:xoxknit/app/modules/shape_test/models/shape_data.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:xoxknit/app/services/auth_service.dart';
import 'package:xoxknit/app/utils/dimension_utils.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/shape_editor_controller.dart';
import 'package:xoxknit/app/modules/new_item/controllers/new_item_wizard_controller.dart';
import 'package:xoxknit/app/modules/new_item/utils/zone_configuration_processor.dart';

/// Helper class for representing zone outline edges
class ZoneEdge {
  final double x1;
  final double y1;
  final double x2;
  final double y2;

  ZoneEdge(this.x1, this.y1, this.x2, this.y2);
}

/// Enum representing types of knitting instructions (same as interactive_knitting_view.dart)
enum InstructionType {
  single,
  repeat,
  discontinuous,
}

/// Class representing a processed knitting instruction (same as interactive_knitting_view.dart)
class KnittingInstruction {
  final InstructionType type;
  final int rowIndex;
  final int repeatCount;
  final List<StitchRange> stitchRanges;
  final String displayText;
  final List<String> warnings;

  KnittingInstruction({
    required this.type,
    required this.rowIndex,
    required this.repeatCount,
    required this.stitchRanges,
    required this.displayText,
    this.warnings = const [],
  });
}

/// Class representing a stitch range (similar to KnittingUtils.StitchRange)
class StitchRange {
  final int startNeedle;
  final int endNeedle;

  StitchRange(this.startNeedle, this.endNeedle);
}

class PatternPrinterService {
  static final PatternPrinterService _instance =
      PatternPrinterService._internal();

  factory PatternPrinterService() {
    return _instance;
  }

  PatternPrinterService._internal();

  /// Print the knitting pattern
  Future<bool> printPattern({
    required NewItemModel itemData,
    required List<List<bool>> instructions,
    required List<ShapeData> shapes,
    required Map<String, dynamic> patternStatistics,
    required BuildContext context,
  }) async {
    // Generate the PDF document
    final pdf = await _generatePDF(
      itemData: itemData,
      instructions: instructions,
      shapes: shapes,
      patternStatistics: patternStatistics,
    );

    // Print the document
    final printed = await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf,
      name:
          '${itemData.name ?? 'patternPrinter_defaultPatternName'.tr}_${DateFormat('yyyyMMdd').format(DateTime.now())}',
      format: PdfPageFormat.a4,
    );

    return printed;
  }

  /// Generate the PDF document
  Future<Uint8List> _generatePDF({
    required NewItemModel itemData,
    required List<List<bool>> instructions,
    required List<ShapeData> shapes,
    required Map<String, dynamic> patternStatistics,
  }) async {
    // Load the logo image
    final ByteData logoData =
        await rootBundle.load('assets/images/logo_white.png');
    final Uint8List logoBytes = logoData.buffer.asUint8List();
    final pw.MemoryImage logoImage = pw.MemoryImage(logoBytes);

    // Create a PDF document
    final pdf = pw.Document();

    // Calculate dimensions using DimensionUtils for consistency
    final topStitches = DimensionUtils.getTopStitches(instructions);
    final bottomStitches = DimensionUtils.getBottomStitches(instructions);
    final totalRows = DimensionUtils.getTotalRows(instructions);

    // Calculate real dimensions based on gauge using DimensionUtils
    final stitchesPerCm = itemData.stitchesPerCm ?? 2.0;
    final rowsPerCm = itemData.rowsPerCm ?? 2.5;

    // Use DimensionUtils to calculate cm values with standardized rounding
    final topWidthCm = DimensionUtils.stitchesToCm(topStitches, stitchesPerCm);
    final bottomWidthCm =
        DimensionUtils.stitchesToCm(bottomStitches, stitchesPerCm);
    final heightCm = DimensionUtils.rowsToCm(totalRows, rowsPerCm);

    // Use standardized formatting for consistent display
    final topWidthFormatted = DimensionUtils.formatCmForDisplay(topWidthCm);
    final bottomWidthFormatted =
        DimensionUtils.formatCmForDisplay(bottomWidthCm);
    final heightFormatted = DimensionUtils.formatCmForDisplay(heightCm);

    // Generate shape preview image
    final shapePreviewImage = await _captureShapePreview(
      topStitches: topStitches,
      bottomStitches: bottomStitches,
      rowsCount: totalRows,
      topWidth: topWidthFormatted,
      bottomWidth: bottomWidthFormatted,
      height: heightFormatted,
      instructions: instructions,
      shapes: shapes,
    );

    // Convert UI image to PDF image
    final pw.MemoryImage shapeImagePdf = pw.MemoryImage(shapePreviewImage);

    // Add the cover page
    pdf.addPage(_buildCoverPage(
      itemData: itemData,
      logoImage: logoImage,
      patternStatistics: patternStatistics,
      instructions: instructions,
      shapes: shapes,
      shapeImage: shapeImagePdf,
    ));

    // Add instruction pages
    _addInstructionPages(pdf, instructions, itemData, logoImage);

    // Return the PDF as bytes
    return pdf.save();
  }

  /// Capture shape preview as an image
  Future<Uint8List> _captureShapePreview({
    required int topStitches,
    required int bottomStitches,
    required int rowsCount,
    required String topWidth,
    required String bottomWidth,
    required String height,
    required List<List<bool>> instructions,
    required List<ShapeData> shapes,
  }) async {
    return _createBasicShapeImage(topStitches, bottomStitches, rowsCount,
        topWidth, bottomWidth, height, instructions, shapes);
  }

  /// Create a basic shape image using Flutter's drawing capabilities
  Future<Uint8List> _createBasicShapeImage(
    int topStitches,
    int bottomStitches,
    int rowsCount,
    String topWidth,
    String bottomWidth,
    String height,
    List<List<bool>> instructions,
    List<ShapeData> shapes,
  ) async {
    // Create a picture recorder
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final size = const Size(280, 280);

    // Draw a white background
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Paint()..color = Colors.white,
    );

    // Define shape dimensions with padding
    const double padding = 40.0;
    final availableWidth = size.width - (padding * 2);
    final availableHeight = size.height - (padding * 2);

    // Draw the knitting pattern outline based on instructions
    _drawKnittingPatternOutline(
        canvas, instructions, availableWidth, availableHeight, padding);

    // Draw dimension lines
    _drawDimensionLines(canvas, size, padding, availableWidth, availableHeight);

    // Add dimension text
    _drawDimensionTexts(
        canvas,
        size,
        topWidth,
        bottomWidth,
        height,
        '$topStitches ${'patternPrinter_units_stitches'.tr}',
        '$bottomStitches ${'patternPrinter_units_stitches'.tr}',
        '$rowsCount ${'patternPrinter_units_rows'.tr}');

    // Convert to image
    final picture = recorder.endRecording();
    final img = await picture.toImage(size.width.toInt(), size.height.toInt());
    final pngBytes = await img.toByteData(format: ui.ImageByteFormat.png);

    return pngBytes!.buffer.asUint8List();
  }

  /// Draw knitting pattern outline based on instructions (similar to ProgressTrackingShapePainter)
  void _drawKnittingPatternOutline(
    Canvas canvas,
    List<List<bool>> instructions,
    double availableWidth,
    double availableHeight,
    double padding,
  ) {
    if (instructions.isEmpty) {
      // Fallback: draw a simple outline
      _drawFallbackOutline(canvas, availableWidth, availableHeight, padding);
      return;
    }

    final outlinePaint = Paint()
      ..color = const Color(0xFFE6007E) // Theme color
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    // Calculate scaling info using the same approach as ProgressTrackingShapePainter
    final scalingInfo = _calculateScalingInfo(
        instructions, Size(availableWidth, availableHeight), padding);

    final offsetX = scalingInfo.offsetX;
    final offsetY = scalingInfo.offsetY;
    final scaleX = scalingInfo.scaleX;
    final scaleY = scalingInfo.scaleY;
    final minStitch = scalingInfo.minStitch;

    // Track all edges that need to be drawn to form the outline (same as ProgressTrackingShapePainter)
    final Set<String> edges = {}; // Using strings to represent edges

    // For each row, identify all boundary edges
    for (int i = 0; i < instructions.length; i++) {
      final row = instructions[i];

      for (int j = 0; j < row.length; j++) {
        if (row[j]) {
          // Check all 4 sides of this stitch to see if they form a boundary

          // Top edge is a boundary if:
          // - This is the first row, or
          // - The stitch above doesn't exist
          bool isTopBoundary = i == 0 ||
              j >= instructions[i - 1].length ||
              !instructions[i - 1][j];

          // Right edge is a boundary if:
          // - This is the last stitch in the row, or
          // - The stitch to the right doesn't exist
          bool isRightBoundary = j == row.length - 1 || !row[j + 1];

          // Bottom edge is a boundary if:
          // - This is the last row, or
          // - The stitch below doesn't exist
          bool isBottomBoundary = i == instructions.length - 1 ||
              j >= instructions[i + 1].length ||
              !instructions[i + 1][j];

          // Left edge is a boundary if:
          // - This is the first stitch in the row, or
          // - The stitch to the left doesn't exist
          bool isLeftBoundary = j == 0 || !row[j - 1];

          // Calculate corner coordinates for this stitch
          final topLeftX = offsetX + (j - minStitch) * scaleX;
          final topLeftY = offsetY + i * scaleY;
          final bottomRightX = topLeftX + scaleX;
          final bottomRightY = topLeftY + scaleY;

          // Add boundary edges to the set
          if (isTopBoundary) {
            edges.add('${topLeftX}_${topLeftY}_${bottomRightX}_${topLeftY}');
          }
          if (isRightBoundary) {
            edges.add(
                '${bottomRightX}_${topLeftY}_${bottomRightX}_${bottomRightY}');
          }
          if (isBottomBoundary) {
            edges.add(
                '${topLeftX}_${bottomRightY}_${bottomRightX}_${bottomRightY}');
          }
          if (isLeftBoundary) {
            edges.add('${topLeftX}_${topLeftY}_${topLeftX}_${bottomRightY}');
          }
        }
      }
    }

    // Draw all boundary edges
    for (final edge in edges) {
      final parts = edge.split('_');
      final x1 = double.parse(parts[0]);
      final y1 = double.parse(parts[1]);
      final x2 = double.parse(parts[2]);
      final y2 = double.parse(parts[3]);

      canvas.drawLine(Offset(x1, y1), Offset(x2, y2), outlinePaint);
    }
  }

  /// Calculate scaling info based on physical aspect ratio (same as ProgressTrackingShapePainter)
  ({
    double scaleX,
    double scaleY,
    double offsetX,
    double offsetY,
    int minStitch,
    int maxStitch,
    int shapeStitchWidth
  }) _calculateScalingInfo(
    List<List<bool>> instructions,
    Size availableSize,
    double padding,
  ) {
    // Find the actual min/max stitch indices used across all rows
    int minStitch = instructions.isNotEmpty ? instructions.first.length : 0;
    int maxStitch = -1;
    int overallMaxWidth = 0;

    for (final row in instructions) {
      overallMaxWidth = math.max(overallMaxWidth, row.length);
      for (int j = 0; j < row.length; j++) {
        if (row[j]) {
          minStitch = math.min(minStitch, j);
          maxStitch = math.max(maxStitch, j);
        }
      }
    }

    // Handle cases with no stitches or empty instructions
    if (maxStitch < minStitch || instructions.isEmpty) {
      minStitch = 0;
      maxStitch = math.max(0, overallMaxWidth - 1);
    }

    final int shapeStitchWidth = math.max(1, maxStitch - minStitch + 1);
    final int patternHeight = math.max(1, instructions.length);
    final int patternWidth = shapeStitchWidth;

    // Get gauge values - use defaults if not available
    double stitchesPerCm = 2.0; // Default
    double rowsPerCm = 2.5; // Default

    if (Get.isRegistered<NewItemWizardController>()) {
      final wizardController = Get.find<NewItemWizardController>();
      final item = wizardController.newItem.value;
      stitchesPerCm = item.stitchesPerCm ?? 2.0;
      rowsPerCm = item.rowsPerCm ?? 2.5;
    }

    // Calculate the PHYSICAL aspect ratio
    final double safeStitchesPerCm = math.max(0.01, stitchesPerCm);
    final double safeRowsPerCm = math.max(0.01, rowsPerCm);
    final double physicalWidth = patternWidth / safeStitchesPerCm;
    final double physicalHeight = patternHeight / safeRowsPerCm;
    final double physicalAspectRatio = (physicalHeight > 1e-6)
        ? physicalWidth / physicalHeight
        : 1.0; // Avoid division by zero

    // Calculate a scaled size that fits within the availableSize while maintaining the PHYSICAL aspect ratio
    double scaledWidth;
    double scaledHeight;
    if (availableSize.width / availableSize.height > physicalAspectRatio) {
      scaledHeight = availableSize.height;
      scaledWidth = scaledHeight * physicalAspectRatio;
    } else {
      scaledWidth = availableSize.width;
      scaledHeight = (physicalAspectRatio > 1e-6)
          ? scaledWidth / physicalAspectRatio
          : availableSize.height; // Avoid division by zero
    }

    // Ensure scaledSize doesn't exceed availableSize
    final double finalScaledWidth =
        math.min(scaledWidth, availableSize.width).toDouble();
    final double finalScaledHeight =
        math.min(scaledHeight, availableSize.height).toDouble();

    // Calculate scale factors
    final double scaleX =
        (patternWidth > 0) ? finalScaledWidth / patternWidth : 0.0;
    final double scaleY =
        (patternHeight > 0) ? finalScaledHeight / patternHeight : 0.0;

    // Center the shape
    final double offsetX =
        padding + (availableSize.width - finalScaledWidth) / 2.0;
    final double offsetY =
        padding + (availableSize.height - finalScaledHeight) / 2.0;

    return (
      scaleX: scaleX,
      scaleY: scaleY,
      offsetX: offsetX,
      offsetY: offsetY,
      minStitch: minStitch,
      maxStitch: maxStitch,
      shapeStitchWidth: shapeStitchWidth,
    );
  }

  /// Draw fallback outline when instructions are not available
  void _drawFallbackOutline(
    Canvas canvas,
    double availableWidth,
    double availableHeight,
    double padding,
  ) {
    final outlinePaint = Paint()
      ..color = const Color(0xFFE6007E)
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final path = Path();

    // Create a simple trapezoid shape
    final topWidth = availableWidth * 0.6;
    final bottomWidth = availableWidth * 0.9;

    path.moveTo(padding + (availableWidth - topWidth) / 2, padding);
    path.lineTo(padding + (availableWidth + topWidth) / 2, padding);
    path.lineTo(padding + (availableWidth + bottomWidth) / 2,
        padding + availableHeight);
    path.lineTo(padding + (availableWidth - bottomWidth) / 2,
        padding + availableHeight);
    path.close();

    canvas.drawPath(path, outlinePaint);
  }

  /// Draw dimension lines on canvas
  void _drawDimensionLines(
      Canvas canvas, Size size, double padding, double width, double height) {
    final linePaint = Paint()
      ..color = const Color(0xFFE6007E) // Theme color
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    // Top width dimension line
    canvas.drawLine(Offset(padding, padding - 10),
        Offset(padding + width, padding - 10), linePaint);

    // Top width ticks
    canvas.drawLine(
        Offset(padding, padding - 15), Offset(padding, padding - 5), linePaint);
    canvas.drawLine(Offset(padding + width, padding - 15),
        Offset(padding + width, padding - 5), linePaint);

    // Bottom width dimension line
    canvas.drawLine(Offset(padding, padding + height + 10),
        Offset(padding + width, padding + height + 10), linePaint);

    // Bottom width ticks
    canvas.drawLine(Offset(padding, padding + height + 5),
        Offset(padding, padding + height + 15), linePaint);
    canvas.drawLine(Offset(padding + width, padding + height + 5),
        Offset(padding + width, padding + height + 15), linePaint);

    // Height dimension line
    canvas.drawLine(Offset(padding - 10, padding),
        Offset(padding - 10, padding + height), linePaint);

    // Height ticks
    canvas.drawLine(
        Offset(padding - 15, padding), Offset(padding - 5, padding), linePaint);
    canvas.drawLine(Offset(padding - 15, padding + height),
        Offset(padding - 5, padding + height), linePaint);
  }

  /// Draw dimension texts on canvas
  void _drawDimensionTexts(
      Canvas canvas,
      Size size,
      String topWidth,
      String bottomWidth,
      String height,
      String topStitches,
      String bottomStitches,
      String rows) {
    const padding = 40.0;

    // Top dimension
    _drawCenteredText(
      canvas,
      '$topWidth cm',
      Offset(size.width / 2, padding - 20),
      fontSize: 12,
      isBold: true,
    );
    _drawCenteredText(
      canvas,
      topStitches,
      Offset(size.width / 2, padding - 35),
      fontSize: 10,
    );

    // Bottom dimension
    _drawCenteredText(
      canvas,
      '$bottomWidth cm',
      Offset(size.width / 2, size.height - padding + 20),
      fontSize: 12,
      isBold: true,
    );
    _drawCenteredText(
      canvas,
      bottomStitches,
      Offset(size.width / 2, size.height - padding + 35),
      fontSize: 10,
    );

    // Height dimension
    _drawCenteredText(
      canvas,
      '$height cm',
      Offset(padding - 25, size.height / 2),
      fontSize: 12,
      isBold: true,
      rotate: true,
    );
    _drawCenteredText(
      canvas,
      rows,
      Offset(padding - 25, size.height / 2 + 15),
      fontSize: 10,
    );
  }

  /// Draw centered text on canvas
  void _drawCenteredText(Canvas canvas, String text, Offset position,
      {double fontSize = 12, bool isBold = false, bool rotate = false}) {
    final textStyle = TextStyle(
      color: const Color(0xFFE6007E), // Theme color
      fontSize: fontSize,
      fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
    );

    final textSpan = TextSpan(
      text: text,
      style: textStyle,
    );

    final textPainter = TextPainter(
      text: textSpan,
      textDirection: ui.TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    textPainter.layout();

    if (rotate) {
      canvas.save();
      canvas.translate(position.dx, position.dy);
      canvas.rotate(-math.pi / 2);
      textPainter.paint(
          canvas, Offset(-textPainter.width / 2, -textPainter.height / 2));
      canvas.restore();
    } else {
      textPainter.paint(
          canvas,
          Offset(position.dx - textPainter.width / 2,
              position.dy - textPainter.height / 2));
    }
  }

  /// Build the cover page with pattern summary
  pw.Page _buildCoverPage({
    required NewItemModel itemData,
    required pw.MemoryImage logoImage,
    required Map<String, dynamic> patternStatistics,
    required List<List<bool>> instructions,
    required List<ShapeData> shapes,
    required pw.MemoryImage shapeImage,
  }) {
    // Calculate dimensions for pattern summary using DimensionUtils for consistency
    final totalRows = DimensionUtils.getTotalRows(instructions);
    final topStitches = DimensionUtils.getTopStitches(instructions);
    final bottomStitches = DimensionUtils.getBottomStitches(instructions);

    // Format current date and time for the footer
    final now = DateTime.now();
    final formattedDate =
        DateFormat('yyyy-MM-dd HH:mm', Get.locale!.languageCode).format(now);

    // Get zones list for display
    List<KnittingZone> zones = [];
    if (Get.isRegistered<ShapeEditorController>()) {
      final shapeController = Get.find<ShapeEditorController>();
      zones = shapeController.knittingInstructionsManager.knittingZones.value;
    }

    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      margin: const pw.EdgeInsets.all(20),
      build: (pw.Context context) {
        // Calculate available size
        final availableWidth = PdfPageFormat.a4.width - 40; // Minus margins
        final columnWidth = availableWidth / 2 -
            7.5; // Half width minus spacing between columns

        // Fixed heights for each row to ensure sections have the same height
        final row1Height = 100.0; // General info and machine info
        final row2Height = 120.0; // Yarn requirements
        final row3Height =
            250.0; // Shape design and gauge calculation (restored original height)
        final row4Height =
            zones.isNotEmpty ? 80.0 : 0.0; // Zone list section (conditional)

        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // Header with title and logo
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      itemData.name ?? 'patternPrinter_defaultItemName'.tr,
                      style: pw.TextStyle(
                        fontSize: 24,
                        color: PdfColor.fromHex('E6007E'),
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.Text(
                      'patternPrinter_knittingInstructions'.tr,
                      style: pw.TextStyle(
                        fontSize: 18,
                        color: PdfColor.fromHex('E6007E'),
                      ),
                    ),
                    pw.SizedBox(height: 5),
                    pw.Text(
                      AuthService.to.currentUser.value?.fullName ??
                          'patternPrinter_defaultUserName'.tr,
                      style: pw.TextStyle(
                        fontSize: 14,
                        color: PdfColor.fromHex('E6007E'),
                      ),
                    ),
                  ],
                ),
                pw.Image(logoImage, width: 80, height: 80),
              ],
            ),

            pw.SizedBox(height: 20),

            // Main content with information boxes - First row
            pw.Row(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Left column - General info
                pw.Container(
                  width: columnWidth,
                  height: row1Height,
                  child: _buildInfoBox(
                    title: 'patternPrinter_generalInfo'.tr,
                    content: [
                      pw.SizedBox(
                        width: columnWidth - 20,
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            _buildInfoRow(
                                'patternPrinter_generalInfo'.tr, '$totalRows'),
                            _buildInfoRow('patternPrinter_machineType'.tr,
                                '${itemData.knittingMachine?.needlesCount ?? 148}'),
                            _buildInfoRow('patternPrinter_totalYarnRequired'.tr,
                                '${_calculateYarnRequirement(patternStatistics, itemData)}g'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(width: 15),

                // Right column - Machine info
                pw.Container(
                  width: columnWidth,
                  height: row1Height,
                  child: _buildInfoBox(
                    title:
                        "${itemData.knittingMachine?.mainBrand ?? '-'} ${itemData.knittingMachine?.model ?? '-'}",
                    content: [
                      pw.SizedBox(
                        width: columnWidth - 20,
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            _buildInfoRow(
                                'patternPrinter_machineType'.tr,
                                itemData.knittingMachine?.model ??
                                    'patternPrinter_defaultMachine'.tr),
                            _buildInfoRow('patternPrinter_stitchType'.tr,
                                itemData.stitchType ?? '-'),
                            _buildInfoRow('patternPrinter_tensionSetting'.tr,
                                itemData.tension ?? '4.2'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            pw.SizedBox(height: 15),

            // Main content with information boxes - Second row
            pw.Row(
              crossAxisAlignment: pw.CrossAxisAlignment.center,
              children: [
                // Yarn requirements - full width
                pw.Container(
                  width: availableWidth,
                  height: row2Height,
                  child: _buildInfoBox(
                    title: 'patternPrinter_yarnRequirements'.tr,
                    content: [
                      pw.SizedBox(
                        width: availableWidth,
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.center,
                          children: [
                            _buildInfoRow(
                                'patternPrinter_yarnTitleAndStrands'.tr,
                                '${itemData.yarnTitle ?? '3/23'} - ${itemData.strands ?? 3} strands'),
                            _buildInfoRow(
                                'patternPrinter_supplier'.tr,
                                itemData.yarnSupplier ??
                                    'patternPrinter_defaultSupplier'.tr),
                            _buildInfoRow('patternPrinter_totalYarnRequired'.tr,
                                '${_calculateYarnRequirement(patternStatistics, itemData)}g'),
                          ],
                        ),
                      ),
                    ],
                    expandContent: true,
                  ),
                ),
              ],
            ),

            pw.SizedBox(height: 15),

            // Shape design and Gauge calculation - Third row (restored original layout)
            pw.Row(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Shape design (left)
                pw.Container(
                  width: columnWidth,
                  height: row3Height,
                  child: _buildInfoBox(
                    title: 'patternPrinter_shapeDesign'.tr,
                    content: [
                      pw.SizedBox(
                        width: columnWidth - 20,
                        child: pw.Center(
                          child: pw.SizedBox(
                            width: 200,
                            height: 200,
                            child: pw.Image(shapeImage),
                          ),
                        ),
                      ),
                    ],
                    expandContent: true,
                  ),
                ),

                pw.SizedBox(width: 15),

                // Gauge calculation (right)
                pw.Container(
                  width: columnWidth,
                  height: row3Height,
                  child: _buildInfoBox(
                    title: 'patternPrinter_gaugeCalculation'.tr,
                    content: [
                      pw.SizedBox(
                        width: columnWidth - 20,
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            // RESULTS section - header
                            pw.Text(
                              'patternPrinter_results'.tr,
                              style: pw.TextStyle(
                                fontSize: 12,
                                color: PdfColor.fromHex('E6007E'),
                                fontWeight: pw.FontWeight.bold,
                              ),
                            ),
                            pw.SizedBox(height: 5),

                            // Results rows
                            pw.Row(
                              mainAxisAlignment:
                                  pw.MainAxisAlignment.spaceBetween,
                              children: [
                                pw.Text('patternPrinter_stitchesPer10Cm'.tr,
                                    style: pw.TextStyle(fontSize: 11)),
                                pw.Text(
                                    '${(itemData.stitchesPer10Cm ?? 20).toInt()}',
                                    style: pw.TextStyle(
                                        fontSize: 11,
                                        fontWeight: pw.FontWeight.bold)),
                              ],
                            ),
                            pw.SizedBox(height: 4),
                            pw.Row(
                              mainAxisAlignment:
                                  pw.MainAxisAlignment.spaceBetween,
                              children: [
                                pw.Text('patternPrinter_rowsPer10Cm'.tr,
                                    style: pw.TextStyle(fontSize: 11)),
                                pw.Text(
                                    '${(itemData.rowsPer10Cm ?? 45).toInt()}',
                                    style: pw.TextStyle(
                                        fontSize: 11,
                                        fontWeight: pw.FontWeight.bold)),
                              ],
                            ),

                            pw.SizedBox(height: 15),

                            // MEASUREMENTS section - header
                            pw.Text(
                              'patternPrinter_measurements'.tr,
                              style: pw.TextStyle(
                                fontSize: 12,
                                color: PdfColor.fromHex('E6007E'),
                                fontWeight: pw.FontWeight.bold,
                              ),
                            ),
                            pw.SizedBox(height: 5),

                            // Measurements rows
                            _buildSimpleMeasurementRow(
                                'patternPrinter_swatchWidth'.tr,
                                '${itemData.swatchInfo?['width'] ?? "N/A"} cm'),
                            _buildSimpleMeasurementRow(
                                'patternPrinter_swatchLength'.tr,
                                '${itemData.swatchInfo?['length'] ?? "N/A"} cm'),
                            _buildSimpleMeasurementRow(
                                'patternPrinter_swatchWeight'.tr,
                                '${itemData.swatchInfo?['weight'] ?? "N/A"} g '),
                            _buildSimpleMeasurementRow(
                                'patternPrinter_stitchesInSwatch'.tr,
                                '${itemData.swatchInfo?['stitches'] ?? "N/A"}'),
                            _buildSimpleMeasurementRow(
                                'patternPrinter_rowsInSwatch'.tr,
                                '${itemData.swatchInfo?['rows'] ?? "N/A"}'),
                          ],
                        ),
                      ),
                    ],
                    expandContent: true,
                  ),
                ),
              ],
            ),

            // Zone List section - Fourth row (only if zones exist)
            if (zones.isNotEmpty) ...[
              pw.SizedBox(height: 15),
              pw.Container(
                width: availableWidth,
                height: row4Height,
                child: _buildInfoBox(
                  title: 'patternPrinter_zonesOverview'.tr,
                  content: [
                    pw.SizedBox(
                      width: availableWidth - 20,
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                            'patternPrinter_patternContainsZones'
                                .tr
                                .replaceAll('@count', '${zones.length}'),
                            style: pw.TextStyle(
                              fontSize: 12,
                              color: PdfColors.grey800,
                            ),
                          ),
                          pw.SizedBox(height: 8),
                          pw.Wrap(
                            spacing: 8,
                            runSpacing: 6,
                            children: zones.asMap().entries.map((entry) {
                              final index = entry.key;
                              final zone = entry.value;
                              final zoneName = _getZoneName(zone, index);

                              return pw.Container(
                                padding: const pw.EdgeInsets.symmetric(
                                    horizontal: 6, vertical: 3),
                                decoration: pw.BoxDecoration(
                                  color: PdfColor.fromHex('F8F9FA'),
                                  border: pw.Border.all(
                                    color: PdfColor.fromHex('E6007E'),
                                    width: 1,
                                  ),
                                  borderRadius: const pw.BorderRadius.all(
                                      pw.Radius.circular(4)),
                                ),
                                child: pw.Text(
                                  'Zone ${index + 1}: $zoneName',
                                  style: pw.TextStyle(
                                    fontSize: 9,
                                    color: PdfColor.fromHex('E6007E'),
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                        ],
                      ),
                    ),
                  ],
                  expandContent: true,
                ),
              ),
            ],

            pw.Spacer(),

            // Footer
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.center,
              children: [
                pw.Text(
                  'patternPrinter_page'
                      .tr
                      .replaceAll('@current', '1')
                      .replaceAll('@total',
                          '${1 + _calculateInstructionPages(instructions)}'),
                  style: pw.TextStyle(color: PdfColor.fromHex('E6007E')),
                ),
              ],
            ),

            pw.SizedBox(height: 5),

            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.center,
              children: [
                pw.Text(
                  formattedDate,
                  style: pw.TextStyle(color: PdfColor.fromHex('E6007E')),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  /// Add instruction pages to the PDF
  void _addInstructionPages(pw.Document pdf, List<List<bool>> instructions,
      NewItemModel itemData, pw.MemoryImage logoImage) {
    if (!Get.isRegistered<ShapeEditorController>()) {
      debugPrint(
          "ShapeEditorController not registered, cannot generate zone-based instructions");
      _addOriginalInstructionPages(pdf, instructions, itemData, logoImage);
      return;
    }

    final shapeController = Get.find<ShapeEditorController>();
    final zones =
        shapeController.knittingInstructionsManager.knittingZones.value;

    // If no zones available, fall back to the original method
    if (zones.isEmpty) {
      debugPrint(
          "No zones available, falling back to original instruction format");
      _addOriginalInstructionPages(pdf, instructions, itemData, logoImage);
      return;
    }

    // Format current date and time for footer
    final now = DateTime.now();
    final formattedDate = DateFormat('yyyy-MM-dd HH:mm').format(now);

    // Calculate total pages for all zones
    final int totalZonePages = _calculateZonePages(zones);

    int currentPageNumber = 2; // Start after cover page

    // Add zone sections
    for (int zoneIndex = 0; zoneIndex < zones.length; zoneIndex++) {
      final zone = zones[zoneIndex];

      if (zone == null) {
        debugPrint("Warning: Zone at index $zoneIndex is null, skipping");
        continue;
      }

      // Create zone pages
      final zonePagesCount = _addZoneSection(
          pdf,
          zone,
          zoneIndex,
          itemData,
          logoImage,
          formattedDate,
          currentPageNumber,
          totalZonePages + 1, // +1 for cover page
          instructions);

      currentPageNumber += zonePagesCount;
    }
  }

  /// Add the original instruction pages (fallback method)
  void _addOriginalInstructionPages(
      pw.Document pdf,
      List<List<bool>> instructions,
      NewItemModel itemData,
      pw.MemoryImage logoImage) {
    // Process instructions into row groups
    final rowGroups = _processInstructionsFromPattern(
        instructions, itemData.knittingMachine?.needlesCount ?? 100);

    // Create instruction tables with a maximum of 20 rows per page
    const int rowsPerPage = 20;

    // Format current date and time for footer
    final now = DateTime.now();
    final formattedDate = DateFormat('yyyy-MM-dd HH:mm').format(now);

    // Calculate total pages for instructions
    final int totalInstructionPages = (rowGroups.length / rowsPerPage).ceil();

    for (int i = 0; i < rowGroups.length; i += rowsPerPage) {
      final currentPageRows = rowGroups.skip(i).take(rowsPerPage).toList();
      final isLastPage = (i ~/ rowsPerPage) == totalInstructionPages - 1;

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(20),
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Header with title, machine info, and logo
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    // Left side - pattern title
                    pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text(
                          itemData.name ?? 'patternPrinter_defaultItemName'.tr,
                          style: pw.TextStyle(
                            fontSize: 24,
                            color: PdfColor.fromHex('E6007E'),
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                        pw.Text(
                          'patternPrinter_knittingInstructions'.tr,
                          style: pw.TextStyle(
                            fontSize: 18,
                            color: PdfColor.fromHex('E6007E'),
                          ),
                        ),
                        pw.SizedBox(height: 5),
                        pw.Text(
                          AuthService.to.currentUser.value?.fullName ??
                              'patternPrinter_defaultUserName'.tr,
                          style: pw.TextStyle(
                            fontSize: 14,
                            color: PdfColor.fromHex('E6007E'),
                          ),
                        ),
                      ],
                    ),

                    // Center - machine info box
                    pw.Container(
                      width: 200,
                      child: _buildInfoBox(
                        title:
                            "${itemData.knittingMachine?.mainBrand ?? 'Doni\'s Brother'} ${itemData.knittingMachine?.model ?? 'chunky'}",
                        content: [
                          pw.SizedBox(
                            width: 180,
                            child: pw.Column(
                              crossAxisAlignment: pw.CrossAxisAlignment.start,
                              children: [
                                _buildInfoRow(
                                    'patternPrinter_machineType'.tr,
                                    itemData.knittingMachine?.model ??
                                        'patternPrinter_defaultMachine'.tr,
                                    fontSize: 10),
                                _buildInfoRow('patternPrinter_stitchType'.tr,
                                    'patternPrinter_jersey'.tr,
                                    fontSize: 10),
                                _buildInfoRow(
                                    'patternPrinter_tensionSetting'.tr,
                                    itemData.tension ?? '4.2',
                                    fontSize: 10),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Right side - logo
                    pw.Image(logoImage, width: 80, height: 80),
                  ],
                ),

                pw.SizedBox(height: 30),

                // Instructions Table
                _buildInstructionsTable(currentPageRows),

                // Add "Finished!" text if this is the last page
                if (isLastPage) ...[
                  pw.SizedBox(height: 20),
                  pw.Center(
                    child: pw.Text(
                      'patternPrinter_finished'.tr,
                      style: pw.TextStyle(
                        fontSize: 24,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColor.fromHex('E6007E'),
                      ),
                    ),
                  ),
                ],

                pw.Spacer(),

                // Footer
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.center,
                  children: [
                    pw.Text(
                      'patternPrinter_page'
                          .tr
                          .replaceAll('@current', '${2 + i ~/ rowsPerPage}')
                          .replaceAll('@total',
                              '${1 + _calculateInstructionPages(instructions)}'),
                      style: pw.TextStyle(color: PdfColor.fromHex('E6007E')),
                    ),
                  ],
                ),

                pw.SizedBox(height: 5),

                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.center,
                  children: [
                    pw.Text(
                      formattedDate,
                      style: pw.TextStyle(color: PdfColor.fromHex('E6007E')),
                    ),
                  ],
                ),
              ],
            );
          },
        ),
      );
    }
  }

  /// Add a zone section to the PDF
  int _addZoneSection(
    pw.Document pdf,
    KnittingZone zone, // Updated type
    int zoneIndex,
    NewItemModel itemData,
    pw.MemoryImage logoImage,
    String formattedDate,
    int currentPageNumber,
    int totalPages,
    List<List<bool>> instructions,
  ) {
    int pagesAdded = 0;

    // Get zone-specific data
    final zoneConfig = zone.config.value;
    final zoneName = _getZoneName(zone, zoneIndex);
    final zoneInstructions = _getZoneInstructions(zone, instructions);

    // Early exit if no instructions to display
    if (zoneInstructions.isEmpty) {
      debugPrint("Warning: No zone instructions for zone $zoneIndex, skipping");
      return 0;
    }

    // Create zone visual
    final zoneVisual = _createZoneVisual(zone, itemData);

    // Calculate available space more conservatively
    // Standard A4 page height minus margins, header, and footer space
    final double pageHeight =
        PdfPageFormat.a4.height - 40; // Minus top/bottom margins
    final double headerHeight = 140; // More conservative header height estimate
    final double footerHeight = 80; // More conservative footer space
    final double zoneHeaderHeight =
        60; // More conservative zone mini header height
    final double visualSectionHeight = zoneVisual != null
        ? 220
        : 0; // More conservative visual + title + spacing
    final double availableForInstructions = pageHeight -
        headerHeight -
        footerHeight -
        zoneHeaderHeight -
        visualSectionHeight;

    // Calculate instruction row height more conservatively
    final double instructionRowHeight =
        30; // More conservative row height estimate (increased from 25)
    final int maxInstructionRowsPerPage =
        (availableForInstructions / instructionRowHeight).floor();

    // Be more conservative with first page rows and ensure minimum viable content
    final int firstPageMaxRows = math.max(
        3, math.min(8, maxInstructionRowsPerPage)); // Minimum 3, maximum 8 rows
    final int subsequentPageMaxRows = 20; // Reduced from 25 for better spacing

    final int totalZoneRows = zoneInstructions.length;

    // Calculate pages needed more accurately with better validation
    int zonePagesNeeded = 1; // At least one page
    int remainingRows = totalZoneRows;

    if (remainingRows > firstPageMaxRows) {
      remainingRows -= firstPageMaxRows;
      zonePagesNeeded += (remainingRows / subsequentPageMaxRows).ceil();
    }

    for (int pageInZone = 0; pageInZone < zonePagesNeeded; pageInZone++) {
      final isFirstZonePage = pageInZone == 0;
      final isLastZonePage = pageInZone == zonePagesNeeded - 1;
      final isLastZone = zoneIndex ==
          Get.find<ShapeEditorController>()
                  .knittingInstructionsManager
                  .knittingZones
                  .value
                  .length -
              1;

      // Calculate row range for this page
      int startRowIndex;
      int endRowIndex;

      if (isFirstZonePage) {
        startRowIndex = 0;
        endRowIndex = math.min(firstPageMaxRows, totalZoneRows);
      } else {
        startRowIndex =
            firstPageMaxRows + (pageInZone - 1) * subsequentPageMaxRows;
        endRowIndex =
            math.min(startRowIndex + subsequentPageMaxRows, totalZoneRows);
      }

      // Enhanced validation to prevent blank pages
      if (startRowIndex >= totalZoneRows ||
          startRowIndex < 0 ||
          startRowIndex >= endRowIndex) {
        debugPrint(
            "Warning: Invalid row range [$startRowIndex, $endRowIndex) for zone $zoneIndex, page $pageInZone. Total rows: $totalZoneRows");
        continue; // Skip this page instead of creating a blank one
      }

      final pageRows = zoneInstructions.sublist(startRowIndex, endRowIndex);

      // Additional validation: skip page if no rows to display
      if (pageRows.isEmpty) {
        debugPrint(
            "Warning: No rows to display for zone $zoneIndex, page $pageInZone. Skipping page.");
        continue;
      }

      // For first page, ensure we have minimal content if visual is present
      if (isFirstZonePage && zoneVisual != null && pageRows.length < 2) {
        debugPrint(
            "Warning: Insufficient content for first page of zone $zoneIndex with visual. Only ${pageRows.length} rows available.");
        // Still create the page but with reduced visual section if needed
      }

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(20),
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Header with title, machine info, and logo
                _buildPageHeader(itemData, logoImage),

                pw.SizedBox(
                    height: 12), // Reduced spacing for better space management

                // Zone Mini Header (only on first page of zone)
                if (isFirstZonePage) ...[
                  _buildZoneMiniHeader(zoneName, zoneIndex),
                  pw.SizedBox(height: 8), // Reduced spacing
                ],

                // Combined Zone Visual and Instructions Section
                if (isFirstZonePage && zoneVisual != null) ...[
                  _buildCombinedZoneSection(zoneVisual, pageRows, zoneName),
                ] else ...[
                  // Instructions only for subsequent pages
                  _buildZoneInstructionsSection(pageRows, null),
                ],

                // Add "Zone Complete!" text if this is the last page of the zone
                if (isLastZonePage) ...[
                  pw.SizedBox(height: 12),
                  pw.Center(
                    child: pw.Text(
                      'patternPrinter_zoneComplete'
                          .tr
                          .replaceAll('@number', '${zoneIndex + 1}'),
                      style: pw.TextStyle(
                        fontSize: 18,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColor.fromHex('E6007E'),
                      ),
                    ),
                  ),
                ],

                // Add "Pattern Finished!" text if this is the last zone
                if (isLastZonePage && isLastZone) ...[
                  pw.SizedBox(height: 8),
                  pw.Center(
                    child: pw.Text(
                      'patternPrinter_finished'.tr,
                      style: pw.TextStyle(
                        fontSize: 24,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColor.fromHex('E6007E'),
                      ),
                    ),
                  ),
                ],

                pw.Spacer(),

                // Footer
                _buildPageFooter(
                    currentPageNumber + pageInZone, totalPages, formattedDate),
              ],
            );
          },
        ),
      );

      pagesAdded++;
    }

    return pagesAdded;
  }

  /// Build combined zone visual and instructions section for optimal space usage
  pw.Widget _buildCombinedZoneSection(
      pw.Widget zoneVisual, List<RowGroup> zoneRows, String zoneName) {
    // Handle empty content gracefully
    if (zoneRows.isEmpty) {
      return pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // Zone visual section only - more compact
          pw.Container(
            width: double.infinity,
            child: pw.Row(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Visual on the left
                pw.Container(
                  width: 180, // Slightly reduced width
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.center,
                    children: [
                      pw.Text(
                        'patternPrinter_zoneWorkArea'.tr,
                        style: pw.TextStyle(
                          fontSize: 11, // Slightly smaller font
                          fontWeight: pw.FontWeight.bold,
                          color: PdfColor.fromHex('E6007E'),
                        ),
                      ),
                      pw.SizedBox(height: 6), // Reduced spacing
                      pw.Container(
                        height: 100, // Further reduced height
                        child: pw.Center(child: zoneVisual),
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(width: 15), // Reduced spacing

                // Instructions note on the right
                pw.Expanded(
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'patternPrinter_instructionsFor'
                            .tr
                            .replaceAll('@zoneName', zoneName),
                        style: pw.TextStyle(
                          fontSize: 14, // Slightly smaller
                          fontWeight: pw.FontWeight.bold,
                          color: PdfColor.fromHex('E6007E'),
                        ),
                      ),
                      pw.SizedBox(height: 6),
                      pw.Text(
                        'patternPrinter_noInstructionsForZone'.tr,
                        style: pw.TextStyle(
                          fontSize: 10,
                          color: PdfColors.grey700,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Zone visual section - more compact
        pw.Container(
          width: double.infinity,
          child: pw.Row(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Visual on the left
              pw.Container(
                width: 180, // Slightly reduced width
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                  children: [
                    pw.Text(
                      'patternPrinter_zoneWorkArea'.tr,
                      style: pw.TextStyle(
                        fontSize: 11, // Slightly smaller font
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColor.fromHex('E6007E'),
                      ),
                    ),
                    pw.SizedBox(height: 6), // Reduced spacing
                    pw.Container(
                      height: 100, // Further reduced height
                      child: pw.Center(child: zoneVisual),
                    ),
                  ],
                ),
              ),

              pw.SizedBox(width: 15), // Reduced spacing

              // Instructions title on the right
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'patternPrinter_instructionsFor'
                          .tr
                          .replaceAll('@zoneName', zoneName),
                      style: pw.TextStyle(
                        fontSize: 14, // Slightly smaller
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColor.fromHex('E6007E'),
                      ),
                    ),
                    pw.SizedBox(height: 6), // Reduced spacing
                    pw.Text(
                      'patternPrinter_followInstructionsBelow'.tr,
                      style: pw.TextStyle(
                        fontSize: 9, // Smaller font
                        color: PdfColors.grey700,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        pw.SizedBox(height: 12), // Reduced spacing

        // Instructions table directly below - only if we have content
        if (zoneRows.isNotEmpty) _buildInstructionsTable(zoneRows),
      ],
    );
  }

  /// Build page header with title, machine info, and logo
  pw.Widget _buildPageHeader(NewItemModel itemData, pw.MemoryImage logoImage) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        // Left side - pattern title
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              itemData.name ?? 'patternPrinter_defaultItemName'.tr,
              style: pw.TextStyle(
                fontSize: 24,
                color: PdfColor.fromHex('E6007E'),
                fontWeight: pw.FontWeight.bold,
              ),
            ),
            pw.Text(
              'patternPrinter_knittingInstructions'.tr,
              style: pw.TextStyle(
                fontSize: 18,
                color: PdfColor.fromHex('E6007E'),
              ),
            ),
            pw.SizedBox(height: 5),
            pw.Text(
              AuthService.to.currentUser.value?.fullName ??
                  'patternPrinter_defaultUserName'.tr,
              style: pw.TextStyle(
                fontSize: 14,
                color: PdfColor.fromHex('E6007E'),
              ),
            ),
          ],
        ),

        // Center - machine info box
        pw.Container(
          width: 200,
          child: _buildInfoBox(
            title:
                "${itemData.knittingMachine?.mainBrand ?? 'Doni\'s Brother'} ${itemData.knittingMachine?.model ?? 'chunky'}",
            content: [
              pw.SizedBox(
                width: 180,
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    _buildInfoRow(
                        'patternPrinter_machineType'.tr,
                        itemData.knittingMachine?.model ??
                            'patternPrinter_defaultMachine'.tr,
                        fontSize: 10),
                    _buildInfoRow('patternPrinter_stitchType'.tr,
                        itemData.stitchType ?? '-'),
                    _buildInfoRow('patternPrinter_tensionSetting'.tr,
                        itemData.tension ?? '4.2',
                        fontSize: 10),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Right side - logo
        pw.Image(logoImage, width: 80, height: 80),
      ],
    );
  }

  /// Build zone mini header
  pw.Widget _buildZoneMiniHeader(String zoneName, int zoneIndex) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColor.fromHex('E6007E'),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(10)),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            'Zone ${zoneIndex + 1}',
            style: pw.TextStyle(
              fontSize: 20,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.white,
            ),
          ),
          pw.Text(
            zoneName,
            style: pw.TextStyle(
              fontSize: 16,
              color: PdfColors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// Build zone instructions section
  pw.Widget _buildZoneInstructionsSection(
      List<RowGroup> zoneRows, String? zoneName) {
    // Handle empty content gracefully
    if (zoneRows.isEmpty) {
      return pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          if (zoneName != null) ...[
            pw.Text(
              'patternPrinter_instructionsFor'
                  .tr
                  .replaceAll('@zoneName', zoneName),
              style: pw.TextStyle(
                fontSize: 16,
                fontWeight: pw.FontWeight.bold,
                color: PdfColor.fromHex('E6007E'),
              ),
            ),
            pw.SizedBox(height: 10),
          ],
          pw.Center(
            child: pw.Container(
              padding: const pw.EdgeInsets.all(20),
              decoration: pw.BoxDecoration(
                color: PdfColor.fromHex('F8F9FA'),
                border: pw.Border.all(
                  color: PdfColor.fromHex('E6007E'),
                  width: 1,
                ),
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
              ),
              child: pw.Text(
                'patternPrinter_noInstructionsAvailable'.tr,
                style: pw.TextStyle(
                  fontSize: 12,
                  color: PdfColor.fromHex('666666'),
                  fontStyle: pw.FontStyle.italic,
                ),
                textAlign: pw.TextAlign.center,
              ),
            ),
          ),
        ],
      );
    }

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        if (zoneName != null) ...[
          pw.Text(
            'patternPrinter_instructionsFor'
                .tr
                .replaceAll('@zoneName', zoneName),
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColor.fromHex('E6007E'),
            ),
          ),
          pw.SizedBox(height: 10),
        ],
        _buildInstructionsTable(zoneRows),
      ],
    );
  }

  /// Build page footer
  pw.Widget _buildPageFooter(
      int currentPage, int totalPages, String formattedDate) {
    return pw.Column(
      children: [
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.center,
          children: [
            pw.Text(
              'patternPrinter_page'
                  .tr
                  .replaceAll('@current', '$currentPage')
                  .replaceAll('@total', '$totalPages'),
              style: pw.TextStyle(color: PdfColor.fromHex('E6007E')),
            ),
          ],
        ),
        pw.SizedBox(height: 5),
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.center,
          children: [
            pw.Text(
              formattedDate,
              style: pw.TextStyle(color: PdfColor.fromHex('E6007E')),
            ),
          ],
        ),
      ],
    );
  }

  /// Get zone name
  String _getZoneName(KnittingZone zone, int zoneIndex) {
    // Try to get a meaningful name from the zone configuration
    final config = zone.config.value;
    if (zone.name.isNotEmpty) {
      return zone.name;
    }

    // Fallback to descriptive names based on zone properties
    if (config.isEmpty?.value == true) {
      return 'patternPrinter_emptyZoneNumbered'
          .tr
          .replaceAll('@number', '${zoneIndex + 1}');
    }

    return 'patternPrinter_workZone'
        .tr
        .replaceAll('@number', '${zoneIndex + 1}');
  }

  /// Get zone-specific instructions using the same logic as interactive knitting view
  List<RowGroup> _getZoneInstructions(
      KnittingZone zone, List<List<bool>> globalInstructions) {
    // Use the same logic as interactive knitting view
    // Get the zone's own instructions directly (same as interactive_knitting_view.dart)
    final zoneInstructions = zone.instructions;

    if (zoneInstructions.isEmpty) {
      debugPrint("Warning: No zone instructions available");
      return [];
    }

    // Apply zone configurations (same as interactive_knitting_view.dart)
    final processedZoneInstructions =
        ZoneConfigurationProcessor.applyZoneConfiguration(zone);

    if (processedZoneInstructions.isEmpty) {
      debugPrint("Warning: No processed zone instructions available");
      return [];
    }

    // Process instructions using the same logic as interactive knitting view
    final processedInstructions =
        _processZoneInstructionsForPrint(processedZoneInstructions, zone);

    // Convert knitting instructions to row groups for printing
    return _convertKnittingInstructionsToRowGroups(processedInstructions, zone);
  }

  /// Process instructions for a specific zone (same logic as interactive_knitting_view.dart)
  List<KnittingInstruction> _processZoneInstructionsForPrint(
      List<List<bool>> zoneInstructions, KnittingZone zone) {
    final processed = <KnittingInstruction>[];

    if (zoneInstructions.isEmpty) return processed;

    int currentRow = 0;
    final needleCount = Get.isRegistered<NewItemWizardController>()
        ? Get.find<NewItemWizardController>()
                .newItem
                .value
                .knittingMachine
                ?.needlesCount ??
            100
        : 100;

    while (currentRow < zoneInstructions.length) {
      // Check for repeating rows (same as interactive_knitting_view.dart)
      final repeatCount =
          _findRepeatingRowsInZone(currentRow, zoneInstructions);

      if (repeatCount > 1) {
        // Found repeating rows
        final row = zoneInstructions[currentRow];
        final ranges = _findStitchRangesInRow(row);

        // Adjust ranges to absolute needle positions (same as interactive_knitting_view.dart)
        final adjustedRanges = _adjustStitchRangesToAbsolutePositions(
            ranges, zone, zoneInstructions, row);

        processed.add(KnittingInstruction(
          type: InstructionType.repeat,
          rowIndex: currentRow,
          repeatCount: repeatCount,
          stitchRanges: adjustedRanges,
          displayText: _generateInstructionTextForZone(
              adjustedRanges, repeatCount,
              needleCount: needleCount),
        ));

        currentRow += repeatCount;
      } else {
        // Process a single row
        final row = zoneInstructions[currentRow];
        final ranges = _findStitchRangesInRow(row);
        final hasDiscontinuous = ranges.length > 1;

        // Adjust ranges to absolute needle positions (same as interactive_knitting_view.dart)
        final adjustedRanges = _adjustStitchRangesToAbsolutePositions(
            ranges, zone, zoneInstructions, row);

        processed.add(KnittingInstruction(
          type: hasDiscontinuous
              ? InstructionType.discontinuous
              : InstructionType.single,
          rowIndex: currentRow,
          repeatCount: 1,
          stitchRanges: adjustedRanges,
          displayText: _generateInstructionTextForZone(adjustedRanges, 1,
              needleCount: needleCount),
          warnings: hasDiscontinuous ? ['Discontinuous pattern'] : [],
        ));

        currentRow++;
      }
    }

    return processed;
  }

  /// Adjust stitch ranges to absolute needle positions (same as interactive_knitting_view.dart)
  List<StitchRange> _adjustStitchRangesToAbsolutePositions(
      List<StitchRange> ranges,
      KnittingZone zone,
      List<List<bool>> processedInstructions,
      List<bool> currentRow) {
    if (ranges.isEmpty) return ranges;

    // Calculate zone shift due to increases/decreases (same as interactive_knitting_view.dart)
    int zoneShift = 0;
    if (processedInstructions.isNotEmpty &&
        zone.instructions.isNotEmpty &&
        processedInstructions[0].length != zone.instructions[0].length) {
      zoneShift =
          (processedInstructions[0].length - zone.instructions[0].length) ~/ 2;
    }

    // Adjust each range to absolute positions (same as interactive_knitting_view.dart)
    return ranges.map((range) {
      final adjustedStart = zone.startNeedle + range.startNeedle - zoneShift;
      final adjustedEnd = zone.startNeedle + range.endNeedle - zoneShift;

      return StitchRange(adjustedStart, adjustedEnd);
    }).toList();
  }

  /// Find how many consecutive repeating rows there are (same as interactive_knitting_view.dart)
  int _findRepeatingRowsInZone(
      int startRow, List<List<bool>> patternInstructions) {
    if (startRow >= patternInstructions.length) return 0;

    int count = 1;
    final baseRow = patternInstructions[startRow];

    for (int i = startRow + 1; i < patternInstructions.length; i++) {
      if (_rowsAreSimilar(baseRow, patternInstructions[i])) {
        count++;
      } else {
        break;
      }
    }

    return count;
  }

  /// Generate instruction text for stitch ranges (same as interactive_knitting_view.dart)
  String _generateInstructionTextForZone(
      List<StitchRange> ranges, int repeatCount,
      {required int needleCount}) {
    final rangesText = _formatRangesText(
      ranges,
      needleCount,
      useLRNotation: true,
    );

    if (repeatCount > 1) {
      // For repeating rows, add repeat information
      return 'patternPrinter_repeatForRows'
              .tr
              .replaceAll('@count', '$repeatCount') +
          ' $rangesText';
    }

    return rangesText;
  }

  /// Convert KnittingInstructions to RowGroups for printing
  List<RowGroup> _convertKnittingInstructionsToRowGroups(
      List<KnittingInstruction> instructions, KnittingZone zone) {
    final List<RowGroup> rowGroups = [];
    final needleCount = Get.isRegistered<NewItemWizardController>()
        ? Get.find<NewItemWizardController>()
                .newItem
                .value
                .knittingMachine
                ?.needlesCount ??
            100
        : 100;

    for (final instruction in instructions) {
      if (instruction.stitchRanges.isEmpty) continue;

      // Get the leftmost and rightmost needle positions
      final leftmostNeedle = instruction.stitchRanges.first.startNeedle;
      final rightmostNeedle = instruction.stitchRanges.last.endNeedle;

      // Convert to needle designations
      final leftNeedleDesignation =
          _getNeedleDesignation(leftmostNeedle, needleCount);
      final rightNeedleDesignation =
          _getNeedleDesignation(rightmostNeedle, needleCount);

      // Calculate row numbers (zone instructions are 0-based internally, but we display 1-based)
      final startRow = instruction.rowIndex + 1;

      rowGroups.add(RowGroup(
        startRow: startRow,
        rowCount: instruction.repeatCount,
        leftNeedleDesignation: leftNeedleDesignation['designation']!,
        leftNeedleNumber: leftNeedleDesignation['number']!,
        rightNeedleDesignation: rightNeedleDesignation['designation']!,
        rightNeedleNumber: rightNeedleDesignation['number']!,
      ));
    }

    return rowGroups;
  }

  /// Get needle designation (L/R) and number
  Map<String, dynamic> _getNeedleDesignation(
      int needlePosition, int needleCount) {
    final centerNeedle = needleCount ~/ 2;

    if (needlePosition < centerNeedle) {
      return {
        'designation': 'L',
        'number': centerNeedle - needlePosition,
      };
    } else {
      return {
        'designation': 'R',
        'number': needlePosition - centerNeedle + 1,
      };
    }
  }

  /// Helper methods that match the interactive knitting view logic

  /// Find stitch ranges in a row (similar to KnittingUtils.findStitchRanges)
  List<StitchRange> _findStitchRangesInRow(List<bool> row) {
    final List<StitchRange> ranges = [];
    int? rangeStart;

    for (int i = 0; i < row.length; i++) {
      if (row[i]) {
        // Found a stitch
        if (rangeStart == null) {
          rangeStart = i; // Start new range
        }
      } else {
        // Found empty space
        if (rangeStart != null) {
          // End current range
          ranges.add(StitchRange(rangeStart, i - 1));
          rangeStart = null;
        }
      }
    }

    // Handle case where range extends to end of row
    if (rangeStart != null) {
      ranges.add(StitchRange(rangeStart, row.length - 1));
    }

    return ranges;
  }

  /// Check if two rows are similar (similar to KnittingUtils.rowsAreSimilar)
  bool _rowsAreSimilar(List<bool> row1, List<bool> row2) {
    if (row1.length != row2.length) return false;

    for (int i = 0; i < row1.length; i++) {
      if (row1[i] != row2[i]) return false;
    }

    return true;
  }

  /// Format ranges text (similar to KnittingUtils.formatRangesText)
  String _formatRangesText(List<StitchRange> ranges, int needleCount,
      {bool useLRNotation = true}) {
    if (ranges.isEmpty) return 'patternPrinter_knitting_noStitches'.tr;

    final List<String> rangeTexts = [];

    for (final range in ranges) {
      if (range.startNeedle == range.endNeedle) {
        // Single needle
        rangeTexts.add(_formatNeedleNumber(range.startNeedle, needleCount,
            useLRNotation: useLRNotation));
      } else {
        // Range of needles
        final startText = _formatNeedleNumber(range.startNeedle, needleCount,
            useLRNotation: useLRNotation);
        final endText = _formatNeedleNumber(range.endNeedle, needleCount,
            useLRNotation: useLRNotation);
        rangeTexts.add('$startText-$endText');
      }
    }

    return rangeTexts.join(', ');
  }

  /// Format needle number (similar to KnittingUtils.formatNeedleNumber)
  String _formatNeedleNumber(int needlePosition, int needleCount,
      {bool useLRNotation = true}) {
    if (!useLRNotation) {
      return needlePosition.toString();
    }

    final centerNeedle = needleCount ~/ 2;

    if (needlePosition < centerNeedle) {
      return 'L${centerNeedle - needlePosition}';
    } else {
      return 'R${needlePosition - centerNeedle + 1}';
    }
  }

  /// Create zone visual representation
  pw.Widget? _createZoneVisual(KnittingZone zone, NewItemModel itemData) {
    // Apply zone configurations to get the same processed instructions as shown in the UI
    final processedInstructions =
        ZoneConfigurationProcessor.applyZoneConfiguration(zone);

    if (processedInstructions.isEmpty) {
      return _createEmptyZoneVisual(zone);
    }

    // Use the new accurate zone visualization
    return _createAccurateZoneVisual(zone, processedInstructions, itemData);
  }

  /// Create an accurate zone visual using the same logic as KnittingZonePainter
  pw.Widget _createAccurateZoneVisual(KnittingZone zone,
      List<List<bool>> processedInstructions, NewItemModel itemData) {
    // Get trimmed instructions that remove padding spaces (same as KnittingZonePainter)
    final trimmedData = _getTrimmedZoneInstructions(processedInstructions);
    final trimmedInstructions = trimmedData['instructions'] as List<List<bool>>;

    if (trimmedInstructions.isEmpty) {
      return _createEmptyZoneVisual(zone);
    }

    return pw.Container(
      width: 180,
      height: 120,
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColor.fromHex('E6007E'), width: 1),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
      ),
      child: pw.Stack(
        children: [
          // Main pattern visualization
          pw.Center(
            child:
                _buildZonePatternVisualization(trimmedInstructions, itemData),
          ),

          // Zone info overlay
          pw.Positioned(
            bottom: 5,
            left: 5,
            right: 5,
            child: pw.Container(
              padding:
                  const pw.EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: pw.BoxDecoration(
                color: PdfColor.fromHex('F0F0F0'),
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(3)),
              ),
              child: pw.Center(
                child: pw.Text(
                  '${trimmedInstructions.length} rows • ${trimmedInstructions.isNotEmpty ? trimmedInstructions[0].length : 0} stitches',
                  style: pw.TextStyle(
                    fontSize: 8,
                    color: PdfColor.fromHex('E6007E'),
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build zone pattern visualization using PDF widgets (matching KnittingZonePainter logic)
  pw.Widget _buildZonePatternVisualization(
      List<List<bool>> trimmedInstructions, NewItemModel itemData) {
    if (trimmedInstructions.isEmpty) return pw.Container();

    // Get aspect ratio from item data
    final stitchesPerCm = itemData.stitchesPerCm ?? 2.0;
    final rowsPerCm = itemData.rowsPerCm ?? 2.5;
    final aspectRatio = stitchesPerCm / rowsPerCm;

    // Calculate dimensions using the same logic as KnittingZonePainter
    const double availableWidth = 160.0;
    const double availableHeight = 100.0;

    final int contentWidth = trimmedInstructions[0].length;
    final int contentHeight = trimmedInstructions.length;

    // Use same aspect ratio logic as KnittingZonePainter
    final double internalAspectRatio =
        aspectRatio != 0 ? 1.0 / aspectRatio : 1.0;

    // Calculate cell size respecting aspect ratio
    double cellWidth;
    double cellHeight;

    final widthLimited = availableWidth / contentWidth <
        (availableHeight / contentHeight) * internalAspectRatio;

    if (widthLimited) {
      cellWidth = availableWidth / contentWidth;
      cellHeight = cellWidth / internalAspectRatio;
    } else {
      cellHeight = availableHeight / contentHeight;
      cellWidth = cellHeight * internalAspectRatio;
    }

    // Create a grid of containers representing the pattern (same as KnittingZonePainter)
    final List<pw.Widget> patternRows = [];

    for (int row = 0; row < trimmedInstructions.length; row++) {
      final List<pw.Widget> rowCells = [];

      for (int col = 0; col < trimmedInstructions[row].length; col++) {
        rowCells.add(
          pw.Container(
            width: cellWidth,
            height: cellHeight,
            decoration: pw.BoxDecoration(
              color: trimmedInstructions[row][col]
                  ? PdfColors.black
                  : PdfColors.white,
              border: pw.Border.all(
                color: PdfColor.fromHex('E0E0E0'),
                width: 0.3,
              ),
            ),
          ),
        );
      }

      patternRows.add(
        pw.Row(
          mainAxisSize: pw.MainAxisSize.min,
          children: rowCells,
        ),
      );
    }

    return pw.Container(
      width: availableWidth,
      height: availableHeight,
      child: pw.Center(
        child: pw.Column(
          mainAxisSize: pw.MainAxisSize.min,
          children: patternRows,
        ),
      ),
    );
  }

  /// Create visual for empty zones
  pw.Widget _createEmptyZoneVisual(KnittingZone zone) {
    return pw.Container(
      width: 180,
      height: 120,
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColor.fromHex('E6007E'), width: 1),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
      ),
      child: pw.Stack(
        children: [
          pw.Center(
            child: pw.Column(
              mainAxisAlignment: pw.MainAxisAlignment.center,
              children: [
                pw.Text(
                  'patternPrinter_emptyZone'.tr,
                  style: pw.TextStyle(
                    fontSize: 12,
                    color: PdfColor.fromHex('E6007E'),
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                pw.SizedBox(height: 4),
                pw.Text(
                  zone.config.value.finishingMethod.value == "bind off"
                      ? 'patternPrinter_bindOff'.tr
                      : 'patternPrinter_useScrap'.tr,
                  style: pw.TextStyle(
                    fontSize: 10,
                    color: PdfColor.fromHex('666666'),
                  ),
                ),
              ],
            ),
          ),
          pw.Positioned(
            bottom: 5,
            left: 5,
            right: 5,
            child: pw.Container(
              padding:
                  const pw.EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: pw.BoxDecoration(
                color: PdfColor.fromHex('F0F0F0'),
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(3)),
              ),
              child: pw.Center(
                child: pw.Text(
                  'Rows: ${zone.startRow ?? 1}-${zone.endRow ?? 'End'}',
                  style: pw.TextStyle(
                    fontSize: 8,
                    color: PdfColor.fromHex('E6007E'),
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Helper method to get trimmed instructions (same logic as KnittingZonePainter)
  Map<String, dynamic> _getTrimmedZoneInstructions(
      List<List<bool>> instructions) {
    if (instructions.isEmpty) {
      return {
        'instructions': <List<bool>>[],
        'leftTrim': 0,
        'rightTrim': 0,
        'topTrim': 0,
        'bottomTrim': 0,
      };
    }

    int leftTrim = instructions[0].length;
    int rightTrim = 0;
    int topTrim = 0;
    int bottomTrim = 0;

    // Find the bounding box of all true values
    bool foundContent = false;
    int firstContentRow = -1;
    int lastContentRow = -1;

    for (int row = 0; row < instructions.length; row++) {
      bool rowHasContent = false;
      for (int col = 0; col < instructions[row].length; col++) {
        if (instructions[row][col]) {
          rowHasContent = true;
          foundContent = true;
          leftTrim = leftTrim < col ? leftTrim : col;
          rightTrim = rightTrim > col ? rightTrim : col;
        }
      }
      if (rowHasContent) {
        if (firstContentRow == -1) firstContentRow = row;
        lastContentRow = row;
      }
    }

    if (!foundContent) {
      return {
        'instructions': <List<bool>>[],
        'leftTrim': 0,
        'rightTrim': 0,
        'topTrim': 0,
        'bottomTrim': 0,
      };
    }

    // Calculate trims
    topTrim = firstContentRow;
    bottomTrim = instructions.length - 1 - lastContentRow;
    final rightPadding = instructions[0].length - 1 - rightTrim;

    // Create trimmed instructions
    final List<List<bool>> trimmed = [];
    for (int row = firstContentRow; row <= lastContentRow; row++) {
      final trimmedRow = instructions[row].sublist(leftTrim, rightTrim + 1);
      trimmed.add(trimmedRow);
    }

    return {
      'instructions': trimmed,
      'leftTrim': leftTrim,
      'rightTrim': rightPadding,
      'topTrim': topTrim,
      'bottomTrim': bottomTrim,
    };
  }

  /// Create a widget for drawing an edge in the zone outline
  pw.Widget _createEdgeWidget(ZoneEdge edge) {
    // For horizontal edges
    if (edge.y1 == edge.y2) {
      return pw.Positioned(
        left: math.min(edge.x1, edge.x2),
        top: edge.y1 - 0.5, // Center the line
        child: pw.SizedBox(
          width: (edge.x2 - edge.x1).abs(),
          height: 1.0,
          child: pw.Container(
            color: PdfColor.fromHex('E6007E'),
          ),
        ),
      );
    }
    // For vertical edges
    else if (edge.x1 == edge.x2) {
      return pw.Positioned(
        left: edge.x1 - 0.5, // Center the line
        top: math.min(edge.y1, edge.y2),
        child: pw.SizedBox(
          width: 1.0,
          height: (edge.y2 - edge.y1).abs(),
          child: pw.Container(
            color: PdfColor.fromHex('E6007E'),
          ),
        ),
      );
    }
    // For diagonal edges (though they shouldn't occur with this grid-based approach)
    else {
      return pw.Container(); // Empty widget for unsupported edge types
    }
  }

  /// Count active stitches in a row
  int _countActiveStitchesInRow(List<bool> row) {
    return row.where((stitch) => stitch).length;
  }

  /// Create a geometric representation of the zone shape
  pw.Widget _createZoneShapeRepresentation({
    required int firstRowWidth,
    required int middleRowWidth,
    required int lastRowWidth,
    required int totalRows,
    required int totalStitches,
  }) {
    // Calculate normalized widths (0.0 to 1.0)
    final maxWidth =
        math.max(math.max(firstRowWidth, middleRowWidth), lastRowWidth);
    if (maxWidth == 0) {
      return pw.Container(); // Empty shape
    }

    final normalizedFirst = firstRowWidth / maxWidth;
    final normalizedMiddle = middleRowWidth / maxWidth;
    final normalizedLast = lastRowWidth / maxWidth;

    // Create a trapezoid-like shape using containers
    return pw.Column(
      mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
      children: [
        // Top section
        pw.Container(
          height: 8,
          width: 80 * normalizedFirst,
          decoration: pw.BoxDecoration(
            color: PdfColor.fromHex('FFCCE6'),
            border:
                pw.Border.all(color: PdfColor.fromHex('E6007E'), width: 0.5),
          ),
        ),
        // Middle section
        pw.Container(
          height: 12,
          width: 80 * normalizedMiddle,
          decoration: pw.BoxDecoration(
            color: PdfColor.fromHex('FFE6F2'),
            border:
                pw.Border.all(color: PdfColor.fromHex('E6007E'), width: 0.5),
          ),
        ),
        // Bottom section
        pw.Container(
          height: 8,
          width: 80 * normalizedLast,
          decoration: pw.BoxDecoration(
            color: PdfColor.fromHex('FFCCE6'),
            border:
                pw.Border.all(color: PdfColor.fromHex('E6007E'), width: 0.5),
          ),
        ),
      ],
    );
  }

  /// Calculate total pages needed for zones
  int _calculateZonePages(List<KnittingZone> zones) {
    const int maxRowsPerPage = 12; // More conservative estimate
    int totalPages = 0;

    for (final zone in zones) {
      if (zone == null) continue;

      // For each zone, calculate how many instruction rows it will have
      final zoneRowCount = _estimateZoneRowCount(zone);

      // Skip zones with no content
      if (zoneRowCount <= 0) {
        debugPrint("Warning: Zone has no content, skipping page calculation");
        continue;
      }

      // More conservative calculation: first page has visual so fewer rows
      const int firstPageRows =
          5; // Conservative estimate for first page with visual
      const int subsequentPageRows =
          15; // Conservative estimate for subsequent pages

      int zonePagesNeeded = 1; // At least one page
      int remainingRows = zoneRowCount;

      if (remainingRows > firstPageRows) {
        remainingRows -= firstPageRows;
        zonePagesNeeded += (remainingRows / subsequentPageRows).ceil();
      }

      totalPages += zonePagesNeeded;
    }

    return totalPages;
  }

  /// Estimate the number of instruction rows for a zone
  int _estimateZoneRowCount(KnittingZone zone) {
    try {
      // Get actual zone instructions for more accurate calculation
      final zoneInstructions = zone.instructions;

      if (zoneInstructions.isEmpty) {
        debugPrint("Warning: Zone has empty instructions");
        return 0;
      }

      // Apply zone configuration to get processed instructions
      final processedInstructions =
          ZoneConfigurationProcessor.applyZoneConfiguration(zone);

      if (processedInstructions.isEmpty) {
        debugPrint("Warning: Zone has empty processed instructions");
        return 0;
      }

      // Process instructions to get actual row groups
      final processedKnittingInstructions =
          _processZoneInstructionsForPrint(processedInstructions, zone);

      // Convert to row groups and count
      final rowGroups = _convertKnittingInstructionsToRowGroups(
          processedKnittingInstructions, zone);

      return rowGroups.length;
    } catch (e) {
      debugPrint("Error calculating zone row count: $e");
      return 0; // Safe fallback
    }
  }

  /// Helper method to calculate instruction pages needed (updated for zone-based structure)
  int _calculateInstructionPages(List<List<bool>> instructions) {
    if (!Get.isRegistered<ShapeEditorController>()) {
      // Fallback to original calculation
      final rowGroups = _processInstructionsFromPattern(instructions, 100);
      return (rowGroups.length / 20).ceil();
    }

    final shapeController = Get.find<ShapeEditorController>();
    final zones =
        shapeController.knittingInstructionsManager.knittingZones.value;

    if (zones.isEmpty) {
      // Fallback to original calculation
      final rowGroups = _processInstructionsFromPattern(instructions, 100);
      return (rowGroups.length / 20).ceil();
    }

    return _calculateZonePages(zones);
  }

  /// Process instructions from the actual pattern to create row groups
  List<RowGroup> _processInstructionsFromPattern(
      List<List<bool>> instructions, int needleCount) {
    final List<RowGroup> rowGroups = [];

    if (instructions.isEmpty) {
      return rowGroups;
    }

    // Reverse the instructions to start from the last row
    final reversedInstructions = instructions.reversed.toList();

    // Analyze the pattern to find similar rows
    int currentRow = 0;
    while (currentRow < reversedInstructions.length) {
      final row = reversedInstructions[currentRow];

      // Find leftmost and rightmost active stitches to determine needle range
      int leftmostNeedle = -1;
      int rightmostNeedle = -1;

      for (int i = 0; i < row.length; i++) {
        if (row[i]) {
          leftmostNeedle = i;
          break;
        }
      }

      for (int i = row.length - 1; i >= 0; i--) {
        if (row[i]) {
          rightmostNeedle = i;
          break;
        }
      }

      // Skip if no active stitches found
      if (leftmostNeedle == -1 || rightmostNeedle == -1) {
        currentRow++;
        continue;
      }

      // Calculate center position of the needle bed
      final center = needleCount ~/ 2;

      // Convert to actual needle designations
      // Needles to the left of center are L needles (L1, L2, etc., increasing outward)
      // Needles to the right of center are R needles (R1, R2, etc., increasing outward)
      // Center is at position L1/R1
      String leftNeedleDesignation;
      String rightNeedleDesignation;
      int leftNeedleNumber;
      int rightNeedleNumber;

      // Determine left needle
      if (leftmostNeedle < center) {
        // Left side (L needles)
        leftNeedleDesignation = "L";
        leftNeedleNumber = center - leftmostNeedle;
      } else {
        // Right side (R needles)
        leftNeedleDesignation = "R";
        leftNeedleNumber = leftmostNeedle - center + 1;
      }

      // Determine right needle
      if (rightmostNeedle < center) {
        // Left side (L needles)
        rightNeedleDesignation = "L";
        rightNeedleNumber = center - rightmostNeedle;
      } else {
        // Right side (R needles)
        rightNeedleDesignation = "R";
        rightNeedleNumber = rightmostNeedle - center + 1;
      }

      // Find how many consecutive rows have the same needle distribution
      int consecutiveCount = 1;
      for (int i = currentRow + 1; i < reversedInstructions.length; i++) {
        final nextRow = reversedInstructions[i];

        // Check if next row has the same left and right boundaries
        int nextLeftmost = -1;
        int nextRightmost = -1;

        for (int j = 0; j < nextRow.length; j++) {
          if (nextRow[j]) {
            nextLeftmost = j;
            break;
          }
        }

        for (int j = nextRow.length - 1; j >= 0; j--) {
          if (nextRow[j]) {
            nextRightmost = j;
            break;
          }
        }

        if (nextLeftmost == leftmostNeedle &&
            nextRightmost == rightmostNeedle) {
          consecutiveCount++;
        } else {
          break;
        }
      }

      // Add the row group with new row numbering starting from 1
      rowGroups.add(
        RowGroup(
          startRow: currentRow + 1, // 1-indexed for display
          rowCount: consecutiveCount,
          leftNeedleDesignation: leftNeedleDesignation,
          leftNeedleNumber: leftNeedleNumber,
          rightNeedleDesignation: rightNeedleDesignation,
          rightNeedleNumber: rightNeedleNumber,
        ),
      );

      // Move to the next set of rows
      currentRow += consecutiveCount;
    }

    // If no row groups were found (which shouldn't happen), provide a default
    if (rowGroups.isEmpty) {
      rowGroups.addAll([
        RowGroup(
            startRow: 1,
            rowCount: 40,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 25,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 25),
        RowGroup(
            startRow: 41,
            rowCount: 6,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 32,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 32),
        RowGroup(
            startRow: 47,
            rowCount: 4,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 31,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 31),
        RowGroup(
            startRow: 51,
            rowCount: 7,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 30,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 30),
        RowGroup(
            startRow: 58,
            rowCount: 7,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 29,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 29),
        RowGroup(
            startRow: 65,
            rowCount: 10,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 28,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 28),
        RowGroup(
            startRow: 75,
            rowCount: 15,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 27,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 27),
        RowGroup(
            startRow: 90,
            rowCount: 10,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 25,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 25),
        RowGroup(
            startRow: 100,
            rowCount: 20,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 15,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 15),
        RowGroup(
            startRow: 120,
            rowCount: 10,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 25,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 25),
        RowGroup(
            startRow: 130,
            rowCount: 15,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 27,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 27),
        RowGroup(
            startRow: 145,
            rowCount: 10,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 28,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 28),
        RowGroup(
            startRow: 155,
            rowCount: 7,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 29,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 29),
        RowGroup(
            startRow: 162,
            rowCount: 7,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 30,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 30),
        RowGroup(
            startRow: 169,
            rowCount: 4,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 31,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 31),
        RowGroup(
            startRow: 173,
            rowCount: 6,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 32,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 32),
      ]);
    }

    return rowGroups;
  }

  /// Calculate total yarn requirement from statistics and item data
  String _calculateYarnRequirement(
      Map<String, dynamic> patternStatistics, NewItemModel itemData) {
    // Try to use actual data from pattern statistics if available
    if (patternStatistics.containsKey('totalStitches')) {
      final totalStitches = patternStatistics['totalStitches'] as int? ?? 0;
      final weightPer100CmSquared = itemData.weightPer100CmSquared ?? 0.0;

      if (totalStitches > 0 && weightPer100CmSquared > 0) {
        // Very rough estimation:
        // Assuming 100cm² contains stitches × rows per cm², and we know the weight
        final stitchesPerCm = itemData.stitchesPerCm ?? 2.0;
        final rowsPerCm = itemData.rowsPerCm ?? 2.5;
        final stitchesPerCm2 = stitchesPerCm * rowsPerCm;

        if (stitchesPerCm2 > 0) {
          // Calculate pattern area in square cm
          final patternArea = totalStitches / stitchesPerCm2;

          // Calculate weight based on weight per 100cm²
          final weight = (patternArea / 100) * weightPer100CmSquared;

          // Add 10% for waste
          final totalWeight = weight * 1.1;

          return totalWeight.toStringAsFixed(0);
        }
      }
    } else if (patternStatistics.containsKey('totalRows') &&
        patternStatistics.containsKey('maxWidth')) {
      // Fallback to rectangle-based calculation if totalStitches is not available
      final totalRows = patternStatistics['totalRows'] as int? ?? 0;
      final maxWidth = patternStatistics['maxWidth'] as int? ?? 0;
      final weightPer100CmSquared = itemData.weightPer100CmSquared ?? 0.0;
      final stitchesPerCm = itemData.stitchesPerCm ?? 2.0;
      final rowsPerCm = itemData.rowsPerCm ?? 2.5;

      if (totalRows > 0 &&
          maxWidth > 0 &&
          weightPer100CmSquared > 0 &&
          stitchesPerCm > 0 &&
          rowsPerCm > 0) {
        // Calculate area as a rectangle
        final widthInCm = maxWidth / stitchesPerCm;
        final heightInCm = totalRows / rowsPerCm;
        final areaInCmSquared = widthInCm * heightInCm;

        // Calculate weight with 10% waste
        final totalWeight =
            (areaInCmSquared / 100) * weightPer100CmSquared * 1.1;

        return totalWeight.toStringAsFixed(0);
      }
    }

    // If no calculation is possible, use the default
    return '370';
  }

  /// Build an info box with title and content
  pw.Widget _buildInfoBox({
    required String title,
    required List<pw.Widget> content,
    bool expandContent = false,
  }) {
    return pw.Stack(
      children: [
        pw.Container(
          margin: const pw.EdgeInsets.only(top: 10), // Make space for title
          padding: const pw.EdgeInsets.all(10),
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColor.fromHex('E6007E'), width: 1),
            borderRadius: const pw.BorderRadius.all(pw.Radius.circular(10)),
          ),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.SizedBox(height: 5), // Add space for title at the top
              expandContent
                  ? pw.Expanded(
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: content,
                      ),
                    )
                  : pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: content,
                    ),
            ],
          ),
        ),
        // Title positioned on the border
        pw.Positioned(
          left: 20,
          top: 0,
          child: pw.Container(
            padding: const pw.EdgeInsets.symmetric(horizontal: 8),
            color: PdfColors.white,
            child: pw.Text(
              title,
              style: pw.TextStyle(
                fontSize: 14,
                color: PdfColor.fromHex('E6007E'),
                fontWeight: pw.FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Build an info row with label and value
  pw.Row _buildInfoRow(String label, String value, {double fontSize = 12}) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Text(
          label,
          style: pw.TextStyle(
            fontSize: fontSize,
            color: PdfColor.fromHex('E6007E'),
          ),
        ),
        pw.Text(
          value,
          style: pw.TextStyle(
            fontSize: fontSize,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// Build instructions table
  pw.Widget _buildInstructionsTable(List<RowGroup> rowGroups) {
    // Handle empty content gracefully
    if (rowGroups.isEmpty) {
      return pw.Container(
        padding: const pw.EdgeInsets.all(15),
        decoration: pw.BoxDecoration(
          color: PdfColor.fromHex('F8F9FA'),
          border: pw.Border.all(
            color: PdfColor.fromHex('E6007E'),
            width: 1,
          ),
          borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
        ),
        child: pw.Center(
          child: pw.Text(
            'patternPrinter_noInstructionsToDisplay'.tr,
            style: pw.TextStyle(
              fontSize: 12,
              color: PdfColor.fromHex('666666'),
              fontStyle: pw.FontStyle.italic,
            ),
            textAlign: pw.TextAlign.center,
          ),
        ),
      );
    }

    // Define column widths
    final List<pw.TableColumnWidth> columnWidths = [
      const pw.FlexColumnWidth(1), // Start row
      const pw.FlexColumnWidth(2), // Rows to knit
      const pw.FlexColumnWidth(1.5), // Left needle setting
      const pw.FlexColumnWidth(1.5), // Right needle setting
    ];

    // Build table header
    final List<pw.TableRow> rows = [
      pw.TableRow(
        decoration: pw.BoxDecoration(color: PdfColor.fromHex('E6007E')),
        children: [
          pw.Padding(
            padding: const pw.EdgeInsets.all(8),
            child: pw.Text(
              'patternPrinter_startRow'.tr,
              style: pw.TextStyle(
                color: PdfColors.white,
                fontWeight: pw.FontWeight.bold,
              ),
              textAlign: pw.TextAlign.center,
            ),
          ),
          pw.Padding(
            padding: const pw.EdgeInsets.all(8),
            child: pw.Text(
              'patternPrinter_rowsToKnit'.tr,
              style: pw.TextStyle(
                color: PdfColors.white,
                fontWeight: pw.FontWeight.bold,
              ),
              textAlign: pw.TextAlign.center,
            ),
          ),
          pw.Padding(
            padding: const pw.EdgeInsets.all(8),
            child: pw.Text(
              'patternPrinter_leftNeedleSetting'.tr,
              style: pw.TextStyle(
                color: PdfColors.white,
                fontWeight: pw.FontWeight.bold,
              ),
              textAlign: pw.TextAlign.center,
            ),
          ),
          pw.Padding(
            padding: const pw.EdgeInsets.all(8),
            child: pw.Text(
              'patternPrinter_rightNeedleSetting'.tr,
              style: pw.TextStyle(
                color: PdfColors.white,
                fontWeight: pw.FontWeight.bold,
              ),
              textAlign: pw.TextAlign.center,
            ),
          ),
        ],
      ),
    ];

    // Add data rows with validation
    for (int i = 0; i < rowGroups.length; i++) {
      final group = rowGroups[i];

      // Skip invalid row groups
      if (group.rowCount <= 0) {
        debugPrint(
            "Warning: Skipping invalid row group with rowCount ${group.rowCount}");
        continue;
      }

      rows.add(
        pw.TableRow(
          decoration: i % 2 == 0
              ? null
              : const pw.BoxDecoration(color: PdfColors.grey100),
          children: [
            pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                '${group.startRow}',
                textAlign: pw.TextAlign.center,
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                'patternPrinter_knitRows'
                    .tr
                    .replaceAll('@count', '${group.rowCount}'),
                textAlign: pw.TextAlign.center,
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                '${group.leftNeedleDesignation}${group.leftNeedleNumber}',
                textAlign: pw.TextAlign.center,
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                '${group.rightNeedleDesignation}${group.rightNeedleNumber}',
                textAlign: pw.TextAlign.center,
              ),
            ),
          ],
        ),
      );
    }

    // Ensure we have at least one data row beyond the header
    if (rows.length <= 1) {
      return pw.Container(
        padding: const pw.EdgeInsets.all(15),
        decoration: pw.BoxDecoration(
          color: PdfColor.fromHex('F8F9FA'),
          border: pw.Border.all(
            color: PdfColor.fromHex('E6007E'),
            width: 1,
          ),
          borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
        ),
        child: pw.Center(
          child: pw.Text(
            'patternPrinter_noValidInstructionsToDisplay'.tr,
            style: pw.TextStyle(
              fontSize: 12,
              color: PdfColor.fromHex('666666'),
              fontStyle: pw.FontStyle.italic,
            ),
            textAlign: pw.TextAlign.center,
          ),
        ),
      );
    }

    // Build table
    return pw.Table(
      columnWidths: {
        0: columnWidths[0],
        1: columnWidths[1],
        2: columnWidths[2],
        3: columnWidths[3],
      },
      border: pw.TableBorder.all(color: PdfColors.grey300),
      children: rows,
    );
  }

  /// Build a simple measurement row for PDF output
  pw.Widget _buildSimpleMeasurementRow(String label, String value) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 4),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Row(
            children: [
              pw.Container(
                width: 12,
                height: 12,
                decoration: pw.BoxDecoration(
                  shape: pw.BoxShape.circle,
                  border: pw.Border.all(color: PdfColor.fromHex('E6007E')),
                ),
                child: pw.Center(
                    child: pw.Text('?',
                        style: pw.TextStyle(
                            fontSize: 8, color: PdfColor.fromHex('E6007E')))),
              ),
              pw.SizedBox(width: 5),
              pw.Text(label, style: pw.TextStyle(fontSize: 10)),
            ],
          ),
          pw.Container(
            padding: const pw.EdgeInsets.symmetric(horizontal: 10, vertical: 2),
            decoration: pw.BoxDecoration(
              color: PdfColor.fromHex('FFCCE6'), // Very light pink
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
            ),
            child: pw.Text(
              value,
              style: pw.TextStyle(fontSize: 10, fontWeight: pw.FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }
}

/// Helper class for row grouping in the pattern
class RowGroup {
  final int startRow;
  final int rowCount;
  final String leftNeedleDesignation;
  final int leftNeedleNumber;
  final String rightNeedleDesignation;
  final int rightNeedleNumber;

  RowGroup({
    required this.startRow,
    required this.rowCount,
    required this.leftNeedleDesignation,
    required this.leftNeedleNumber,
    required this.rightNeedleDesignation,
    required this.rightNeedleNumber,
  });
}
