import 'package:get/get.dart';

class RouteService extends GetxService {
  static RouteService get to => Get.find();

  String? _intendedRoute;
  Map<String, dynamic>? _intendedParameters;

  void saveIntendedRoute(String? route, [Map<String, dynamic>? parameters]) {
    if (route != null) {
      _intendedRoute = route;
      _intendedParameters = parameters;
    }
  }

  String? getAndClearIntendedRoute() {
    final route = _intendedRoute;
    _intendedRoute = null;
    return route;
  }

  Map<String, dynamic>? getAndClearIntendedParameters() {
    final params = _intendedParameters;
    _intendedParameters = null;
    return params;
  }
}
