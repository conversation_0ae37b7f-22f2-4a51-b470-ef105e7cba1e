// import 'package:firebase_admin/firebase_admin.dart';

// import 'package:firebase_admin/src/credential.dart';
// import 'package:get/get.dart';

// class FirebaseAdminService extends GetxService {
//   static FirebaseAdminService get to => Get.find<FirebaseAdminService>();

//   App? _app;

//   Future<FirebaseAdminService> init() async {
//     await initialize();
//     return this;
//   }

//   Future<void> initialize() async {
//     if (_app != null) return;

//     var credential = Credentials.applicationDefault();

//     credential ??= await Credentials.login();

//     _app = FirebaseAdmin.instance.initializeApp(AppOptions(
//       credential: credential,
//     ));
//   }

//   App get app {
//     if (_app == null) {
//       throw Exception('Firebase Admin not initialized');
//     }
//     return _app!;
//   }
// }
