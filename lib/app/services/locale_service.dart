import 'dart:ui';

import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:xoxknit/app/services/app_settings_service.dart';

class LocaleService extends GetxService {
  final _storage = GetStorage();
  final String _key = 'locale';

  static LocaleService get to => Get.find();

  final supportedLocales = const [
    {'code': 'it', 'country': 'IT', 'name': 'Italiano', 'flag': '🇮🇹'},
    {'code': 'en', 'country': 'US', 'name': 'English', 'flag': '🇬🇧'},
    {'code': 'fr', 'country': 'FR', 'name': 'Français', 'flag': '🇫🇷'},
    {'code': 'de', 'country': 'DE', 'name': 'Deutsch', 'flag': '🇩🇪'},
    {'code': 'es', 'country': 'ES', 'name': 'Español', 'flag': '🇪🇸'},
    {'code': 'ja', 'country': 'JP', 'name': '日本語', 'flag': '🇯🇵'},
    {'code': 'pl', 'country': 'PL', 'name': 'Polski', 'flag': '🇵🇱'},
    {'code': 'pt', 'country': 'BR', 'name': 'Português', 'flag': '🇧🇷'},
    {'code': 'uk', 'country': 'UA', 'name': 'Українська', 'flag': '🇺🇦'},
    {'code': 'zh', 'country': 'CN', 'name': '中文', 'flag': '🇨🇳'},
  ];

  final Rx<String> _currentLanguage = (Get.locale?.languageCode ?? "it").obs;

  String get currentLanguage => _currentLanguage.value;

  String? get currentLanguageName => supportedLocales
      .firstWhere((locale) => locale['code'] == _currentLanguage.value)['name'];
  String? getLanguageName(String languageCode) => supportedLocales
      .firstWhere((locale) => locale['code'] == languageCode)['name'];
  Locale? get locale {
    final savedLocale = _storage.read(_key);
    return savedLocale != null
        ? Locale(savedLocale.split('_')[0], savedLocale.split('_')[1])
        : Get.deviceLocale;
  }

  void changeLocale(String languageCode, String countryCode) {
    final locale = Locale(languageCode, countryCode);
    _storage.write(_key, '${locale.languageCode}_${locale.countryCode}');
    _currentLanguage.value = languageCode;

    Get.updateLocale(locale);
  }

  @override
  void onInit() {
    super.onInit();

    final appSettingsService = AppSettingsService.to;
    final savedLocaleString =
        _storage.read<String>(_key); // Read stored locale string

    if (appSettingsService.isFirstLogin.value) {
      // First login: Use device locale or default to 'en_US'
      final deviceLocale = Get.deviceLocale;
      final langCode = deviceLocale?.languageCode ?? 'it';
      final countryCode = deviceLocale?.countryCode ?? 'IT';
      // Call changeLocale to update storage, GetX locale, and _currentLanguage
      changeLocale(langCode, countryCode);
    } else if (savedLocaleString != null) {
      // Subsequent login: Use stored locale
      final parts = savedLocaleString.split('_');
      if (parts.length == 2) {
        final langCode = parts[0];
        final countryCode = parts[1];
        final localeToSet = Locale(langCode, countryCode);
        // Set the initial value based on stored locale BEFORE updating GetX
        _currentLanguage.value = langCode;
        // Update GetX locale immediately
        Get.updateLocale(localeToSet);
      } else {
        // Fallback if stored format is wrong
        _initializeDefaultLocale();
      }
    } else {
      // No stored locale and not first login: Use default/device
      _initializeDefaultLocale();
    }
  }

  // Helper function for default initialization
  void _initializeDefaultLocale() {
    final deviceLocale = Get.deviceLocale;
    final langCode = deviceLocale?.languageCode ?? 'en';
    final countryCode = deviceLocale?.countryCode ?? 'US';
    // Use changeLocale to ensure consistency in storage and state
    changeLocale(langCode, countryCode);
  }
}
