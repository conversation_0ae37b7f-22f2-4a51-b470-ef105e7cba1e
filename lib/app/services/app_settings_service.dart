import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:flutter/foundation.dart';

class AppSettingsService extends GetxService {
  final String _storageKey = 'app_settings';

  final _storage = GetStorage();

  final _isReady = false.obs;
  bool get isReady => _isReady.value;

  final isFirstLogin = true.obs;

  static AppSettingsService get to => Get.find<AppSettingsService>();

  // @override
  // void onInit() {
  //   super.onInit();
  // }

  Future<AppSettingsService> init() async {
    await _loadSettings();
    return this;
  }

  Future<void> _loadSettings() async {
    try {
      // Load settings from storage
      final box = GetStorage();
      final settings = box.read(_storageKey);
      if (settings != null) {
        isFirstLogin.value = settings['isFirstLogin'] ?? true;
      }
      _isReady.value = true;
    } catch (e) {
      debugPrint('Error loading app settings: $e');
    }
  }

  Future<bool> saveSettings() async {
    final data = {
      'isFirstLogin': isFirstLogin.value,
    };

    try {
      await _storage.write(_storageKey, data);
    } on Exception catch (_) {
      return false;
    }

    return true;
  }

  void updateSettings({bool? firstLogin}) {
    if (firstLogin != null) {
      isFirstLogin.value = firstLogin;
    }
    saveSettings();
  }
}
