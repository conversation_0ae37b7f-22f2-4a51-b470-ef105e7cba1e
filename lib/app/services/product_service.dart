import 'package:get/get.dart';
import 'package:shopify_flutter/shopify_flutter.dart';
import 'package:xoxknit/app/core/utils/logger.dart';

class ProductService extends GetxService {
  final ShopifyStore _shopifyStore = ShopifyStore.instance;
  final AppLogger _logger = AppLogger();

  // Cached data
  final RxList<Product> products = <Product>[].obs;
  final RxList<Collection> collections = <Collection>[].obs;
  final RxBool isLoading = false.obs;

  // Singleton instance
  static ProductService get to => Get.find();

  /// Fetch all products
  Future<List<Product>> getAllProducts() async {
    try {
      isLoading.value = true;

      final fetchedProducts = await _shopifyStore.getAllProducts();
      products.assignAll(fetchedProducts);
      return products;
    } catch (e) {
      _logger.error('Failed to fetch products: $e');
      throw Exception('Failed to load products');
    } finally {
      isLoading.value = false;
    }
  }

  /// Fetch all collections
  Future<List<Collection>> getAllCollections() async {
    try {
      isLoading.value = true;

      final fetchedCollections = await _shopifyStore.getAllCollections();
      collections.assignAll(fetchedCollections);
      return collections;
    } catch (e) {
      _logger.error('Failed to fetch collections: $e');
      throw Exception('Failed to load collections');
    } finally {
      isLoading.value = false;
    }
  }

  /// Get product by ID
  Future<Product?> getProductById(String productId) async {
    try {
      isLoading.value = true;

      final product = await _shopifyStore.getProductsByIds([productId]);

      if (product == null || product.isEmpty) {
        return null;
      }

      return product[0];
    } catch (e) {
      _logger.error('Failed to fetch product details: $e');
      throw Exception('Failed to load product details');
    } finally {
      isLoading.value = false;
    }
  }

  /// Fetch products by collection ID
  Future<List<Product>> getProductsByCollectionId(String collectionId) async {
    try {
      isLoading.value = true;

      final fetchedProducts =
          await _shopifyStore.getAllProductsFromCollectionById(collectionId);
      products.assignAll(fetchedProducts);
      return products;
    } catch (e) {
      _logger
          .error('Failed to fetch products for collection $collectionId: $e');
      throw Exception('Failed to load products for the collection');
    } finally {
      isLoading.value = false;
    }
  }

  /// Search products by query
  Future<List<Product>> searchProducts(String query) async {
    try {
      isLoading.value = true;

      final fetchedProducts = await _shopifyStore.searchProducts(query);
      if (fetchedProducts != null) {
        products.assignAll(fetchedProducts);
      } else {
        products.clear();
      }
      return products;
    } catch (e) {
      _logger.error('Failed to search products: $e');
      throw Exception('Failed to search products');
    } finally {
      isLoading.value = false;
    }
  }

  /// Check if a product is in stock
  bool isProductInStock(Product product) {
    return product.availableForSale;
  }

  /// Clear cache
  void clearCache() {
    products.clear();
    collections.clear();
  }
}
