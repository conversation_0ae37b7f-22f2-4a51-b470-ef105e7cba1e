// knitting_settings_service.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/core/utils/logger.dart';
import 'package:xoxknit/app/data/models/knitting_machine_model.dart';
import 'package:xoxknit/app/data/models/user_knitting_machine_model.dart';
import 'package:xoxknit/app/data/models/user_model.dart';
import 'package:xoxknit/app/services/auth_service.dart';
import 'firestore_service.dart';
import 'local_storage_service.dart';
import 'machine_service.dart';
import 'package:flutter/foundation.dart';

/// Service responsible for managing knitting settings and preferences.
/// Handles storage and retrieval of knitting calculations, machine preferences,
/// and synchronization between local storage and Firestore.
class KnittingSettingsService extends GetxService {
  final FirestoreService _firestoreService = FirestoreService();
  final LocalStorageService _localStorageService = LocalStorageService();
  final MachineService _machineService = MachineService();
  final AuthService _authService = AuthService.to;

  static const String _storageKey = 'calculator_last_values';
  static const String _machinesStorageKey = 'user_knitting_machines';
  static const String _lastSyncKey = 'last_machines_sync';

  final AppLogger _logger = AppLogger();

  RxBool isServiceInitialized = false.obs;

  static KnittingSettingsService get to => Get.find<KnittingSettingsService>();

  // Observable values with default nulls
  final lastSwatchWidth = RxnDouble();
  final lastSwatchLength = RxnDouble();
  final lastStitches = RxnInt();
  final lastRows = RxnInt();
  final lastPieceLength = RxnDouble();
  final lastPieceWidth = RxnDouble();
  final lastUsedMachine = Rxn<UserKnittingMachineModel>(null);
  final lastSwatchWeight = RxnDouble();

  // Remember last used units
  final RxString lastSwatchWidthUnit = RxString('cm');
  final RxString lastSwatchLengthUnit = RxString('cm');
  final RxString lastPieceWidthUnit = RxString('cm');
  final RxString lastPieceLengthUnit = RxString('cm');

  final RxList<KnittingMachineModel> knittingMachines =
      <KnittingMachineModel>[].obs;
  final RxList<UserKnittingMachineModel> userMachines =
      <UserKnittingMachineModel>[].obs;

  DateTime? lastSaveDate;

  bool get hasUserMachines => userMachines.isNotEmpty;

  final Rx<double> lastStitchesPerCm = RxDouble(0);
  final Rx<double> lastRowsPerCm = RxDouble(0);

  final _isInitialized = false.obs;
  bool get isInitialized => _isInitialized.value;

  /// Initializes the service by loading knitting machines and user preferences.
  /// Returns the initialized service instance.
  Future<KnittingSettingsService> initializeService() async {
    try {
      await _loadKnittingMachines();
      await loadUserMachinePrefs();
      isServiceInitialized.value = true;
      _isInitialized.value = true;
      return this;
    } catch (e) {
      debugPrint('Error initializing KnittingSettingsService: $e');
      rethrow;
    }
  }

  /// Loads all available knitting machine models from the database.
  Future<void> _loadKnittingMachines() async {
    knittingMachines.value = await _machineService.getAllKnittingMachines();
  }

  /// Attempts to load last used values from storage or Firestore.
  /// Returns true if values were successfully loaded, false otherwise.
  Future<bool> loadLastValues() async {
    bool loaded = _loadLastValuesFromStorage();

    if (!loaded) {
      loaded = await _loadSettingsFromFirestore();
    }

    return loaded;
  }

  /// Saves the last used values both locally and to Firestore.
  /// Parameters are optional and only provided values will be updated.
  Future<void> saveLastValues({
    UserKnittingMachineModel? machine,
    double? swatchWidth,
    double? swatchLength,
    int? stitches,
    int? rows,
    double? swatchWeight,
    double? pieceWidth,
    double? pieceLength,
    String? swatchWidthUnit,
    String? swatchLengthUnit,
    String? pieceWidthUnit,
    String? pieceLengthUnit,
    double? stitchesPerCm,
    double? rowsPerCm,
  }) async {
    if (machine != null) lastUsedMachine.value = machine;
    if (swatchWidth != null) lastSwatchWidth.value = swatchWidth;
    if (swatchLength != null) lastSwatchLength.value = swatchLength;
    if (stitches != null) lastStitches.value = stitches;
    if (rows != null) lastRows.value = rows;
    if (swatchWeight != null) lastSwatchWeight.value = swatchWeight;
    if (pieceWidth != null) lastPieceWidth.value = pieceWidth;
    if (pieceLength != null) lastPieceLength.value = pieceLength;
    if (stitchesPerCm != null) lastStitchesPerCm.value = stitchesPerCm;
    if (rowsPerCm != null) lastRowsPerCm.value = rowsPerCm;

    if (swatchWidthUnit != null) lastSwatchWidthUnit.value = swatchWidthUnit;
    if (swatchLengthUnit != null) lastSwatchLengthUnit.value = swatchLengthUnit;
    if (pieceWidthUnit != null) lastPieceWidthUnit.value = pieceWidthUnit;
    if (pieceLengthUnit != null) lastPieceLengthUnit.value = pieceLengthUnit;

    await _saveLastValuesToStorage();
    await _saveSettingsToFirestore();
  }

  /// Persists the current settings to local storage.
  Future<void> _saveLastValuesToStorage() async {
    final data = {
      'machineId': lastUsedMachine.value?.id,
      'swatchWidth': lastSwatchWidth.value,
      'swatchLength': lastSwatchLength.value,
      'stitches': lastStitches.value,
      'rows': lastRows.value,
      'swatchWeight': lastSwatchWeight.value,
      'pieceWidth': lastPieceWidth.value,
      'pieceLength': lastPieceLength.value,
      'swatchWidthUnit': lastSwatchWidthUnit.value,
      'swatchLengthUnit': lastSwatchLengthUnit.value,
      'pieceWidthUnit': lastPieceWidthUnit.value,
      'pieceLengthUnit': lastPieceLengthUnit.value,
      'lastSaveDate': DateTime.now().toIso8601String(),
      'stitchesPerCm': lastStitchesPerCm.value,
      'rowsPerCm': lastRowsPerCm.value,
    };
    await _localStorageService.saveLastValuesToStorage(
        storageKey: _storageKey, data: data);
  }

  /// Saves the current settings to Firestore for the authenticated user.
  Future<void> _saveSettingsToFirestore() async {
    final userId = _authService.currentUser.value?.shopifyIdNumber;
    if (userId == null) return;

    final data = {
      "id": userId,
      'machineId': lastUsedMachine.value?.id,
      'swatchWidth': lastSwatchWidth.value,
      'swatchLength': lastSwatchLength.value,
      'stitches': lastStitches.value,
      'rows': lastRows.value,
      'swatchWeight': lastSwatchWeight.value,
      'pieceWidth': lastPieceWidth.value,
      'pieceLength': lastPieceLength.value,
      'swatchWidthUnit': lastSwatchWidthUnit.value,
      'swatchLengthUnit': lastSwatchLengthUnit.value,
      'pieceWidthUnit': lastPieceWidthUnit.value,
      'pieceLengthUnit': lastPieceLengthUnit.value,
      'lastSaveDate':
          lastSaveDate != null ? Timestamp.fromDate(lastSaveDate!) : null,
    };

    await _firestoreService.saveSettingsToFirestore(userId: userId, data: data);
  }

  /// Loads previously saved values from local storage.
  /// Returns true if values were successfully loaded, false otherwise.
  bool _loadLastValuesFromStorage() {
    final data = _localStorageService.loadLastValuesFromStorage(_storageKey);
    if (data != null) {
      lastUsedMachine.value =
          userMachines.firstWhereOrNull((m) => m.id == data['machineId']);
      lastSwatchWidth.value = data['swatchWidth']?.toDouble();
      lastSwatchLength.value = data['swatchLength']?.toDouble();
      lastStitches.value = data['stitches']?.toInt();
      lastRows.value = data['rows']?.toInt();
      lastSwatchWeight.value = data['swatchWeight']?.toDouble();
      lastPieceWidth.value = data['pieceWidth']?.toDouble();
      lastPieceLength.value = data['pieceLength']?.toDouble();
      lastSwatchWidthUnit.value = data['swatchWidthUnit'] ?? 'cm';
      lastSwatchLengthUnit.value = data['swatchLengthUnit'] ?? 'cm';
      lastPieceWidthUnit.value = data['pieceWidthUnit'] ?? 'cm';
      lastPieceLengthUnit.value = data['pieceLengthUnit'] ?? 'cm';
      lastSaveDate = DateTime.parse(data['lastSaveDate']);
      lastStitchesPerCm.value = data['stitchesPerCm']?.toDouble() ?? 0;
      lastRowsPerCm.value = data['rowsPerCm']?.toDouble() ?? 0;
      return true;
    }

    return false;
  }

  /// Loads settings from Firestore for the authenticated user.
  /// Returns true if values were successfully loaded, false otherwise.
  Future<bool> _loadSettingsFromFirestore() async {
    final userId = _authService.currentUser.value?.shopifyIdNumber;
    if (userId == null) return false;

    final data = await _firestoreService.loadSettingsFromFirestore(userId);
    if (data != null) {
      lastUsedMachine.value =
          userMachines.firstWhereOrNull((m) => m.id == data['machineId']);
      lastSwatchWidth.value = data['swatchWidth']?.toDouble();
      lastSwatchLength.value = data['swatchLength']?.toDouble();
      lastStitches.value = data['stitches']?.toInt();
      lastRows.value = data['rows']?.toInt();
      lastSwatchWeight.value = data['swatchWeight']?.toDouble();
      lastPieceWidth.value = data['pieceWidth']?.toDouble();
      lastPieceLength.value = data['pieceLength']?.toDouble();
      lastSwatchWidthUnit.value = data['swatchWidthUnit'] ?? 'cm';
      lastSwatchLengthUnit.value = data['swatchLengthUnit'] ?? 'cm';
      lastPieceWidthUnit.value = data['pieceWidthUnit'] ?? 'cm';
      lastPieceLengthUnit.value = data['pieceLengthUnit'] ?? 'cm';
      lastSaveDate = data['lastSaveDate']?.toDate();
      return true;
    }
    return false;
  }

  /// Deletes saved settings from Firestore for the current user.
  /// Returns true if deletion was successful, false otherwise.
  Future<bool> deleteSavedFirestoreValues() async {
    final userId = _authService.currentUser.value?.shopifyIdNumber;
    if (userId == null) return false;

    return await _firestoreService.deleteSettingsFromFirestore(userId);
  }

  /// Updates the user's knitting machine preferences both locally and in Firestore.
  Future<void> updateUserMachinePrefs(
      List<UserKnittingMachineModel> machines) async {
    final userId = _authService.currentUser.value?.shopifyIdNumber;
    if (userId == null) return;

    await _localStorageService.saveUserMachinesToStorage(
      storageKey: _machinesStorageKey,
      machines: machines.map((m) => m.toJson()).toList(),
    );

    await _firestoreService.updateUserMachinesInFirestore(
        userId: userId, machines: machines);
  }

  /// Loads user's knitting machine preferences from Firestore and saves them locally.
  Future<void> loadUserMachinePrefs() async {
    final userId = _authService.currentUser.value?.shopifyIdNumber;
    if (userId == null) return;

    final machines = await _firestoreService.loadUserMachinesFromFirestore(
        userId, knittingMachines);
    userMachines.value = machines;

    await _localStorageService.saveUserMachinesToStorage(
      storageKey: _machinesStorageKey,
      machines: machines.map((m) => m.toJson()).toList(),
    );
  }

  /// Adds a new machine to the user's collection of machines
  Future<void> addUserMachine(UserKnittingMachineModel machine) async {
    userMachines.add(machine);

    // Save the updated list to storage and potentially to Firestore
    await updateUserMachinePrefs(userMachines);
  }

  /// Removes a knitting machine from user's preferences and syncs with storage.
  /// Will revert changes if sync fails.
  Future<void> removeUserKnittingMachine(String machineId) async {
    final updatedMachines =
        userMachines.where((m) => m.id != machineId).toList();
    userMachines.value = updatedMachines;

    try {
      await updateUserMachinePrefs(updatedMachines);
    } catch (e) {
      userMachines.value = [...userMachines, ...updatedMachines];
      rethrow;
    }
  }

  /// Clears all saved values and removes them from both local storage and Firestore.
  Future<void> clearLastValues() async {
    lastUsedMachine.value = null;
    lastSwatchWidth.value = null;
    lastSwatchLength.value = null;
    lastStitches.value = null;
    lastRows.value = null;
    lastSwatchWeight.value = null;
    lastPieceWidth.value = null;
    lastPieceLength.value = null;
    lastSwatchWidthUnit.value = 'cm';
    lastSwatchLengthUnit.value = 'cm';
    lastPieceWidthUnit.value = 'cm';
    lastPieceLengthUnit.value = 'cm';

    final bool isDeleted = await deleteSavedFirestoreValues();

    if (isDeleted) {
      _logger.info('Settings deleted from Firestore');
      await _localStorageService.removeLastValuesFromStorage(_storageKey);
      _logger.info('Settings deleted from storage');
    }
  }
}
