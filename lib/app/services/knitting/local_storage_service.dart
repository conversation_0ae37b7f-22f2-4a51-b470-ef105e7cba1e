import 'package:get_storage/get_storage.dart';

class LocalStorageService {
  final GetStorage _storage = GetStorage();

  Future<void> saveLastValuesToStorage({
    required String storageKey,
    required Map<String, dynamic> data,
  }) async {
    await _storage.write(storageKey, data);
  }

  Map<String, dynamic>? loadLastValuesFromStorage(String storageKey) {
    return _storage.read(storageKey);
  }

  Future<void> removeLastValuesFromStorage(String storageKey) async {
    await _storage.remove(storageKey);
  }

  Future<void> saveUserMachinesToStorage({
    required String storageKey,
    required List<Map<String, dynamic>> machines,
  }) async {
    await _storage.write(storageKey, {
      'userKnittingMachines': machines,
      'lastUpdated': DateTime.now().toIso8601String(),
    });
  }

  List<Map<String, dynamic>>? loadUserMachinesFromStorage(String storageKey) {
    final data = _storage.read(storageKey);
    if (data != null) {
      return List<Map<String, dynamic>>.from(data['userKnittingMachines']);
    }
    return null;
  }
}
