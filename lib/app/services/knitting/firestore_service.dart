// firestore_service.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:xoxknit/app/core/utils/logger.dart';
import 'package:xoxknit/app/data/models/user_knitting_machine_model.dart';

import '../../data/models/knitting_machine_model.dart';

class FirestoreService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AppLogger _logger = AppLogger();

  Future<void> saveSettingsToFirestore({
    required String userId,
    required Map<String, dynamic> data,
  }) async {
    final doc = _firestore.collection("knitting_settings").doc(userId);

    if ((await doc.get()).exists) {
      await doc.update(data);
      _logger.info('Settings updated in Firestore');
    } else {
      await doc.set(data);
      _logger.info('Settings saved to Firestore');
    }
  }

  Future<Map<String, dynamic>?> loadSettingsFromFirestore(String userId) async {
    final doc = _firestore.collection("knitting_settings").doc(userId);

    try {
      final data = (await doc.get()).data();
      if (data != null) {
        _logger.info('Settings loaded from Firestore');
        return data;
      }
      return null;
    } on FirebaseException catch (e) {
      _logger.error(e.code);
      return null;
    } catch (e) {
      _logger.error(e.toString());
      return null;
    }
  }

  Future<bool> deleteSettingsFromFirestore(String userId) async {
    final doc = _firestore.collection("knitting_settings").doc(userId);
    try {
      await doc.delete();
      return true;
    } on FirebaseException catch (e) {
      _logger.error(e.code);
      return false;
    } catch (e) {
      _logger.error(e.toString());
      return false;
    }
  }

  Future<void> updateUserMachinesInFirestore({
    required String userId,
    required List<UserKnittingMachineModel> machines,
  }) async {
    final userMachinesCollection =
        _firestore.collection("user_knitting_machines");
    final batch = _firestore.batch();

    // Delete all existing machines for this user
    final existingDocs =
        await userMachinesCollection.where('userId', isEqualTo: userId).get();

    for (final doc in existingDocs.docs) {
      batch.delete(doc.reference);
    }

    // Add all current machines
    for (final machine in machines) {
      final doc = userMachinesCollection.doc(machine.id);
      final data = machine.toJson();
      data['lastUpdated'] = FieldValue.serverTimestamp();
      batch.set(doc, data);
    }

    await batch.commit();
    _logger.info('User machines saved successfully');
  }

  Future<List<UserKnittingMachineModel>> loadUserMachinesFromFirestore(
    String userId,
    List<KnittingMachineModel> allMachines,
  ) async {
    final querySnapshot = await _firestore
        .collection("user_knitting_machines")
        .where('userId', isEqualTo: userId)
        .get();

    final userMachines = <UserKnittingMachineModel>[];
    for (final doc in querySnapshot.docs) {
      final machineData = doc.data();

      // Find the base machine model
      final baseMachine = allMachines.firstWhere(
        (m) => m.id == machineData['baseModelId'],
      );
      userMachines.add(UserKnittingMachineModel(
        id: doc.id,
        customName: machineData['customName'],
        userId: machineData['userId'],
        baseModelId: machineData['baseModelId'],
        notes: machineData['notes'] ?? "",
        machineClass: baseMachine.machineClass,
        mainBrand: baseMachine.mainBrand,
        model: baseMachine.model,
        needlePitch: baseMachine.needlePitch,
        needlesCount: baseMachine.needlesCount,
        type: baseMachine.type,
        patternControlType: baseMachine.patternControlType,
        patternRepeatLength: baseMachine.patternRepeatLength,
        altBrands: baseMachine.altBrands,
      ));
    }

    return userMachines;
  }
}
