import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:xoxknit/app/core/utils/logger.dart';
import 'package:xoxknit/app/data/models/knitting_machine_model.dart';

class MachineService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AppLogger _logger = AppLogger();

  Future<List<KnittingMachineModel>> getAllKnittingMachines() async {
    final collection = _firestore.collection('knitting_machines');
    try {
      final querySnapshot = await collection.get();
      final machines = <KnittingMachineModel>[];
      for (final doc in querySnapshot.docs) {
        machines.add(KnittingMachineModel.fromJson(doc.data()));
      }
      return machines;
    } on FirebaseException catch (e) {
      _logger.error(e.code);
      return [];
    } catch (e) {
      _logger.error(e.toString());
      return [];
    }
  }
}
