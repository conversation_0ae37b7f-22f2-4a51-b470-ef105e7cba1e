import 'dart:convert';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import 'package:xoxknit/app/core/utils/logger.dart';
import 'package:xoxknit/app/data/models/new_item_model.dart';
import 'package:xoxknit/app/data/models/user_model.dart';
import 'package:xoxknit/app/data/models/wizard_state_model.dart';
import 'package:xoxknit/app/services/auth_service.dart';

class WizardStateService extends GetxService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AuthService _authService = AuthService.to;
  final AppLogger _logger = AppLogger();
  final _uuid = const Uuid();

  // Track newly created state IDs
  final Set<String> _newlyCreatedStateIds = <String>{};

  static WizardStateService get to => Get.find<WizardStateService>();

  String? get userId => _authService.currentUser.value!.shopifyIdNumber;

  // Check if a state is newly created (for cleaning up shapes)
  bool isNewState(String stateId) {
    return _newlyCreatedStateIds.contains(stateId);
  }

  // Get the application documents directory
  Future<Directory> get _appDir async =>
      await getApplicationDocumentsDirectory();
  // Get the wizard states directory
  Future<Directory> get _wizardStatesDir async {
    final appDir = await _appDir;
    final dir = Directory('${appDir.path}/wizard_states');
    if (!await dir.exists()) {
      await dir.create(recursive: true);
    }
    return dir;
  }

  // Generate a file path for a wizard state
  Future<String> _getWizardStateFilePath(String id) async {
    final dir = await _wizardStatesDir;
    return '${dir.path}/$id.xn';
  }

  // Save wizard state to local file
  Future<void> _saveToFile(WizardStateModel state) async {
    if (kIsWeb) {
      return;
    }
    try {
      final filePath = await _getWizardStateFilePath(state.id);
      final file = File(filePath);
      final json = state.toJson();
      json['lastModified'] = state.lastModified.toIso8601String();
      await file.writeAsString(jsonEncode(json));
      _logger.info('Wizard state saved to file');
    } catch (e) {
      _logger.error('Error saving wizard state to file: $e');
      rethrow;
    }
  }

  // Load wizard state from local file
  Future<WizardStateModel?> _loadFromFile(String id) async {
    try {
      final filePath = await _getWizardStateFilePath(id);
      final file = File(filePath);

      if (!await file.exists()) {
        return null;
      }

      final jsonStr = await file.readAsString();
      _logger.info('Wizard state loaded from file');
      return WizardStateModel.fromJson(jsonDecode(jsonStr));
    } catch (e) {
      _logger.error('Error loading wizard state from file: $e');
      return null;
    }
  }

  // Save wizard state to Firestore
  Future<void> _saveToFirestore(WizardStateModel state) async {
    try {
      final doc = _firestore.collection('wizard_states').doc(state.id);
      await doc.set(state.toFirestore());
      _logger.info('Wizard state saved to Firestore');
    } catch (e) {
      _logger.error('Error saving wizard state to Firestore: $e');
      rethrow;
    }
  }

  // Load wizard state from Firestore
  Future<WizardStateModel?> _loadFromFirestore(String id) async {
    try {
      final doc = await _firestore.collection('wizard_states').doc(id).get();
      if (!doc.exists) {
        return null;
      }
      return WizardStateModel.fromFirestore(doc.data()!);
    } catch (e) {
      _logger.error('Error loading wizard state from Firestore: $e');
      return null;
    }
  }

  // Create a new wizard state
  Future<WizardStateModel> createWizardState() async {
    final currentUserId = userId;
    if (currentUserId == null) {
      throw Exception('User not authenticated');
    }

    final stateId = _uuid.v4();
    final state = WizardStateModel(
      id: stateId,
      userId: currentUserId,
      currentStep: 0,
      itemData: NewItemModel(),
      lastModified: DateTime.now(),
      createdAt: DateTime.now(),
    );

    // Track this as a newly created state
    _newlyCreatedStateIds.add(stateId);

    if (!kIsWeb) {
      await _saveToFile(state);
    }
    await _saveToFirestore(state);

    return state;
  }

  // Save wizard state (both locally and to Firestore)
  Future<void> saveWizardState(WizardStateModel state) async {
    if (!kIsWeb) {
      await _saveToFile(state);
    }
    await _saveToFirestore(state);
  }

  // Load wizard state (tries local first, then Firestore)
  Future<WizardStateModel?> loadWizardState(String id) async {
    WizardStateModel? state;
    if (!kIsWeb) {
      // Try loading from local file first
      state = await _loadFromFile(id);
    }

    // If not found locally, try Firestore
    if (state == null) {
      state = await _loadFromFirestore(id);
      // If found in Firestore, save locally for next time
      if (state != null && !kIsWeb) {
        await _saveToFile(state);
      }
    }

    return state;
  }

  // List all wizard states for the current user
  Future<List<WizardStateModel>> listWizardStates(
      {bool archived = false}) async {
    final currentUserId = userId;
    if (currentUserId == null) {
      throw Exception('User not authenticated');
    }

    final states = <WizardStateModel>[];

    // Get states from Firestore
    try {
      final querySnapshot = await _firestore
          .collection('wizard_states')
          .where('userId', isEqualTo: currentUserId)
          .where('isArchived', isEqualTo: archived)
          .get();

      for (final doc in querySnapshot.docs) {
        final state = WizardStateModel.fromFirestore(doc.data());
        states.add(state);
        // Ensure we have a local copy
        if (!kIsWeb) {
          await _saveToFile(state);
        }
      }
    } catch (e) {
      _logger.error('Error listing wizard states from Firestore: $e');
    }

    // Get states from local files
    try {
      if (!kIsWeb) {
        final dir = await _wizardStatesDir;
        await for (final file in dir.list()) {
          if (file is File && file.path.endsWith('.xn')) {
            try {
              final jsonStr = await file.readAsString();
              final state = WizardStateModel.fromJson(jsonDecode(jsonStr));
              if (state.userId == currentUserId &&
                  !state.isCompleted &&
                  state.isArchived == archived) {
                // Only add if not already in the list
                if (!states.any((s) => s.id == state.id)) {
                  states.add(state);
                }
              }
            } catch (e) {
              _logger.error('Error reading local wizard state file: $e');
            }
          }
        }
      }
    } catch (e) {
      _logger.error('Error listing local wizard states: $e');
    }

    // Sort by last modified date
    states.sort((a, b) => b.lastModified.compareTo(a.lastModified));
    return states;
  }

  // Delete a wizard state
  Future<void> deleteWizardState(String id) async {
    try {
      // Delete from Firestore
      await _firestore.collection('wizard_states').doc(id).delete();

      // Delete local file
      if (!kIsWeb) {
        final filePath = await _getWizardStateFilePath(id);
        final file = File(filePath);
        if (await file.exists()) {
          await file.delete();
        }
      }

      _logger.info('Wizard state deleted: $id');
    } catch (e) {
      _logger.error('Error deleting wizard state: $e');
      rethrow;
    }
  }

  // Mark a wizard state as completed
  Future<void> completeWizardState(String id) async {
    final state = await loadWizardState(id);
    if (state == null) {
      throw Exception('Wizard state not found');
    }

    final completedState = WizardStateModel(
        id: state.id,
        userId: state.userId,
        currentStep: state.currentStep,
        itemData: state.itemData,
        lastModified: DateTime.now(),
        isCompleted: true,
        shapeTestState: state.shapeTestState,
        createdAt: state.createdAt);

    await saveWizardState(completedState);
  }

  // Archive a wizard state
  Future<void> archiveWizardState(String id) async {
    final state = await loadWizardState(id);
    if (state == null) {
      throw Exception('Wizard state not found');
    }

    final archivedState = state.copyWith(
      isArchived: true,
      lastModified: DateTime.now(),
    );

    await saveWizardState(archivedState);
  }

  // Unarchive a wizard state
  Future<void> unarchiveWizardState(String id) async {
    final state = await loadWizardState(id);
    if (state == null) {
      throw Exception('Wizard state not found');
    }

    final unarchivedState = state.copyWith(
      isArchived: false,
      lastModified: DateTime.now(),
    );

    await saveWizardState(unarchivedState);
  }
}
