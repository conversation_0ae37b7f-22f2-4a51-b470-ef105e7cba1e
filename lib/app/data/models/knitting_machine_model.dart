class KnittingMachineModel {
  final String id;
  final String mainBrand;
  final String model;
  final double needlePitch;
  final int needlesCount;
  final String? patternControlType;
  final int? patternRepeatLength;
  final List<String> altBrands;
  final String machineClass;
  final String type;

  const KnittingMachineModel({
    required this.id,
    required this.mainBrand,
    required this.model,
    required this.needlePitch,
    required this.needlesCount,
    this.patternControlType,
    this.patternRepeatLength,
    this.altBrands = const [],
    required this.machineClass,
    required this.type,
  });

  factory KnittingMachineModel.fromJson(Map<String, dynamic> json) {
    return KnittingMachineModel(
      id: json['id'] as String,
      mainBrand: json['mainBrand'] as String,
      model: json['model'] as String,
      needlePitch: (json['needlePitch'] as num).toDouble(),
      needlesCount: json['needlesCount'] as int,
      patternControlType: json['patternControlType'] as String?,
      patternRepeatLength: json['patternRepeatLength'] as int?,
      altBrands: (json['altBrands'] as List<dynamic>?)
              ?.map((brand) => brand as String)
              .toList() ??
          [],
      machineClass: json['machineClass'] as String,
      type: json['type'] as String,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'mainBrand': mainBrand,
        'model': model,
        'needlePitch': needlePitch,
        'needlesCount': needlesCount,
        'patternControlType': patternControlType,
        'patternRepeatLength': patternRepeatLength,
        'altBrands': altBrands,
        'machineClass': machineClass,
        'type': type,
      };

  KnittingMachineModel copyWith({
    String? id,
    String? mainBrand,
    String? model,
    double? needlePitch,
    int? needlesCount,
    String? patternControlType,
    int? patternRepeatLength,
    List<String>? altBrands,
    String? machineClass,
    String? type,
  }) {
    return KnittingMachineModel(
      id: id ?? this.id,
      mainBrand: mainBrand ?? this.mainBrand,
      model: model ?? this.model,
      needlePitch: needlePitch ?? this.needlePitch,
      needlesCount: needlesCount ?? this.needlesCount,
      patternControlType: patternControlType ?? this.patternControlType,
      patternRepeatLength: patternRepeatLength ?? this.patternRepeatLength,
      altBrands: altBrands ?? this.altBrands,
      machineClass: machineClass ?? this.machineClass,
      type: type ?? this.type,
    );
  }

  String get fullName => '$mainBrand $model';

  @override
  int get hashCode {
    return id.hashCode ^
        mainBrand.hashCode ^
        model.hashCode ^
        needlePitch.hashCode ^
        needlesCount.hashCode ^
        patternControlType.hashCode ^
        patternRepeatLength.hashCode ^
        altBrands.hashCode ^
        machineClass.hashCode ^
        type.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is KnittingMachineModel &&
        other.id == id &&
        other.mainBrand == mainBrand &&
        other.model == model &&
        other.needlePitch == needlePitch &&
        other.needlesCount == needlesCount &&
        other.patternControlType == patternControlType &&
        other.patternRepeatLength == patternRepeatLength &&
        other.altBrands == altBrands &&
        other.machineClass == machineClass &&
        other.type == type;
  }

  @override
  String toString() {
    return 'KnittingMachineModel(id: $id, mainBrand: $mainBrand, model: $model, '
        'needlePitch: $needlePitch, needlesCount: $needlesCount, '
        'patternControlType: $patternControlType, patternRepeatLength: $patternRepeatLength, '
        'altBrands: $altBrands, machineClass: $machineClass, type: $type)';
  }
}
