import 'package:equatable/equatable.dart';
import 'package:xoxknit/app/data/models/knitting_machine_model.dart';

/// Represents a user in the XOXKnit application
class UserModel extends Equatable {
  /// Unique identifier for the user
  final String id;
  final String firebaseId;

  /// User's email address
  final String email;

  /// User's first name
  final String? firstName;

  /// User's last name
  final String? lastName;

  /// User's phone number
  final String? phone;

  /// Whether the user accepts marketing emails
  final bool acceptsMarketing;

  /// User's preferred language (default: English)
  final String preferredLanguage;

  /// User's preferred measurement unit (cm/inches)
  final String preferredUnit;

  /// User's preferred currency
  final String preferredCurrency;

  /// Timestamp of when the user was created
  final DateTime createdAt;

  /// Timestamp of the last update to user data
  final DateTime updatedAt;

  /// List of template IDs the user has access to
  final List<String> accessibleTemplates;

  final List<String> knittingMachinesIds;

  /// Creates a new UserModel instance
  UserModel({
    required this.id,
    required this.firebaseId,
    required this.email,
    this.firstName,
    this.lastName,
    this.phone,
    this.acceptsMarketing = false,
    this.preferredLanguage = 'en',
    this.preferredUnit = 'cm',
    this.preferredCurrency = 'EUR',
    this.knittingMachinesIds = const [],
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? accessibleTemplates,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now(),
        accessibleTemplates = accessibleTemplates ?? const [];

  /// Gets the full name of the user
  String get fullName => [firstName, lastName]
      .where((element) => element != null && element.isNotEmpty)
      .join(' ');

  /// Creates a UserModel from a Shopify customer JSON
  factory UserModel.fromShopifyJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      firebaseId: '',
      email: json['email'] as String,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      phone: json['phone'] as String?,
      acceptsMarketing: json['acceptsMarketing'] as bool? ?? false,
      knittingMachinesIds: (json['knittingMachinesIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      accessibleTemplates: (json['accessibleTemplates'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
    );
  }

  /// Creates a UserModel from a local storage JSON
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      firebaseId: json['firebaseId'] as String,
      email: json['email'] as String,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      phone: json['phone'] as String?,
      acceptsMarketing: json['acceptsMarketing'] as bool? ?? false,
      preferredLanguage: json['preferredLanguage'] as String? ?? 'en',
      preferredUnit: json['preferredUnit'] as String? ?? 'cm',
      preferredCurrency: json['preferredCurrency'] as String? ?? 'EUR',
      knittingMachinesIds: (json['knittingMachinesIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      accessibleTemplates: (json['accessibleTemplates'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
    );
  }

  /// Converts the UserModel to a JSON map for local storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'firebaseId': firebaseId,
      'email': email,
      'firstName': firstName,
      'lastName': lastName,
      'phone': phone,
      'acceptsMarketing': acceptsMarketing,
      'preferredLanguage': preferredLanguage,
      'preferredUnit': preferredUnit,
      'preferredCurrency': preferredCurrency,
      'knittingMachinesIds': knittingMachinesIds,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'accessibleTemplates': accessibleTemplates,
    };
  }

  /// Creates a copy of UserModel with specified fields updated
  UserModel copyWith({
    String? firstName,
    String? lastName,
    String? phone,
    bool? acceptsMarketing,
    String? preferredLanguage,
    String? preferredUnit,
    String? preferredCurrency,
    List<String> knittingMachinesIds = const [],
    List<String>? accessibleTemplates,
  }) {
    return UserModel(
      id: id,
      firebaseId: firebaseId,
      email: email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phone: phone ?? this.phone,
      acceptsMarketing: acceptsMarketing ?? this.acceptsMarketing,
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
      preferredUnit: preferredUnit ?? this.preferredUnit,
      preferredCurrency: preferredCurrency ?? this.preferredCurrency,
      knittingMachinesIds: knittingMachinesIds,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
      accessibleTemplates: accessibleTemplates ?? this.accessibleTemplates,
    );
  }

  /// Checks if the user has access to a specific template
  bool hasAccessToTemplate(String templateId) {
    return accessibleTemplates.contains(templateId);
  }

  /// For Equatable comparison
  @override
  List<Object?> get props => [
        id,
        email,
        firstName,
        lastName,
        phone,
        acceptsMarketing,
        preferredLanguage,
        preferredUnit,
        preferredCurrency,
        knittingMachinesIds,
        createdAt,
        updatedAt,
        accessibleTemplates,
      ];
}

/// Extension methods for UserModel
extension UserModelExtensions on UserModel {
  /// Checks if the user profile is complete
  bool get isProfileComplete {
    return firstName != null &&
        firstName!.isNotEmpty &&
        lastName != null &&
        lastName!.isNotEmpty &&
        phone != null &&
        phone!.isNotEmpty;
  }

  /// Gets the number part of the user's shopify id
  String get shopifyIdNumber {
    return id.split('/').last;
  }

  /// Gets the user's initials
  String get initials {
    final firstInitial = firstName?.isNotEmpty == true ? firstName![0] : '';
    final lastInitial = lastName?.isNotEmpty == true ? lastName![0] : '';
    return (firstInitial + lastInitial).toUpperCase();
  }

  /// Gets the display name (full name if available, otherwise email)
  String get displayName {
    if (firstName?.isNotEmpty == true || lastName?.isNotEmpty == true) {
      return fullName;
    }
    return email;
  }
}
