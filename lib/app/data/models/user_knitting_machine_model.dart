import 'package:cloud_firestore/cloud_firestore.dart';

import 'knitting_machine_model.dart';

class UserKnittingMachineModel extends KnittingMachineModel {
  final String customName;
  final String userId;
  final String baseModelId;
  final String notes;

  const UserKnittingMachineModel({
    required super.id,
    required this.customName,
    required this.userId,
    required this.baseModelId,
    this.notes = "",
    required super.machineClass,
    required super.mainBrand,
    required super.model,
    required super.needlePitch,
    required super.needlesCount,
    required super.type,
    super.patternControlType,
    super.patternRepeatLength,
    super.altBrands,
  });

  factory UserKnittingMachineModel.fromJson(Map<String, dynamic> json) {
    return UserKnittingMachineModel(
      id: json['id'] as String,
      customName: json['customName'] as String,
      userId: json['userId'] as String,
      notes: (json['notes'] ?? "") as String,
      baseModelId: json['baseModelId'] as String,
      machineClass: json['machineClass'] as String,
      mainBrand: json['mainBrand'] as String,
      model: json['model'] as String,
      needlePitch: (json['needlePitch'] as num).toDouble(),
      needlesCount: json['needlesCount'] as int,
      patternControlType: json['patternControlType'] as String?,
      patternRepeatLength: json['patternRepeatLength'] as int?,
      type: json['type'] as String,
      altBrands: (json['altBrands'] as List<dynamic>?)
              ?.map((brand) => brand as String)
              .toList() ??
          [],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'customName': customName,
      'userId': userId,
      'baseModelId': baseModelId,
      'notes': notes,
    };
  }

  Map<String, dynamic> toJsonSaveString() {
    return {
      'customName': customName,
      'userId': userId,
      'baseModelId': baseModelId,
      'notes': notes,
      'machineClass': machineClass,
      'mainBrand': mainBrand,
      'model': model,
      'needlePitch': needlePitch,
      'needlesCount': needlesCount,
      'type': type,
      'patternControlType': patternControlType,
      'patternRepeatLength': patternRepeatLength,
      'altBrands': altBrands,
      'id': id,
    };
  }

  @override
  UserKnittingMachineModel copyWith({
    String? id,
    String? customName,
    String? userId,
    String? baseModelId,
    String? notes,
    String? machineClass,
    String? mainBrand,
    String? model,
    double? needlePitch,
    int? needlesCount,
    String? type,
    String? patternControlType,
    int? patternRepeatLength,
    List<String>? altBrands,
  }) {
    return UserKnittingMachineModel(
      id: id ?? this.id,
      customName: customName ?? this.customName,
      userId: userId ?? this.userId,
      baseModelId: baseModelId ?? this.baseModelId,
      notes: notes ?? this.notes,
      machineClass: machineClass ?? this.machineClass,
      mainBrand: mainBrand ?? this.mainBrand,
      model: model ?? this.model,
      needlePitch: needlePitch ?? this.needlePitch,
      needlesCount: needlesCount ?? this.needlesCount,
      type: type ?? this.type,
      patternControlType: patternControlType ?? this.patternControlType,
      patternRepeatLength: patternRepeatLength ?? this.patternRepeatLength,
      altBrands: altBrands ?? this.altBrands,
    );
  }
}
