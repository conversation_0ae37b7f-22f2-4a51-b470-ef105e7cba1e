import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:xoxknit/app/data/models/new_item_model.dart';
import 'package:xoxknit/app/data/models/shape_test_state_model.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/knitting_zone_models.dart';

class WizardStateModel {
  final String id;
  final String userId;
  final int currentStep;
  final NewItemModel itemData;
  final DateTime createdAt;
  final DateTime lastModified;
  final bool isCompleted;
  final bool isArchived;
  final ShapeTestState? shapeTestState;
  final int? currentKnittingRowIndex;
  final int? currentKnittingZoneIndex;
  final List<bool>? completedKnittingZones;

  // Add fields for zone preservation
  final List<KnittingZone>? savedKnittingZones; // For local storage only
  final List<ZoneMetadata>?
      savedZoneMetadata; // For Firestore storage (lightweight)
  final String?
      instructionsHash; // Hash of the instruction pattern for comparison

  WizardStateModel({
    required this.id,
    required this.userId,
    required this.currentStep,
    required this.itemData,
    required this.createdAt,
    required this.lastModified,
    this.isCompleted = false,
    this.isArchived = false,
    this.shapeTestState,
    this.currentKnittingRowIndex,
    this.currentKnittingZoneIndex,
    this.completedKnittingZones,
    this.savedKnittingZones,
    this.savedZoneMetadata,
    this.instructionsHash,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'currentStep': currentStep,
      'itemData': itemData.toJson(),
      'createdAt': createdAt.toIso8601String(),
      'lastModified': lastModified.toIso8601String(),
      'isCompleted': isCompleted,
      'isArchived': isArchived,
      'shapeTestState': shapeTestState?.toJson(),
      'currentKnittingRowIndex': currentKnittingRowIndex,
      'currentKnittingZoneIndex': currentKnittingZoneIndex,
      'completedKnittingZones': completedKnittingZones,
      'savedKnittingZones':
          savedKnittingZones?.map((zone) => zone.toJson()).toList(),
      'savedZoneMetadata':
          savedZoneMetadata?.map((metadata) => metadata.toJson()).toList(),
      'instructionsHash': instructionsHash,
    };
  }

  factory WizardStateModel.fromJson(Map<String, dynamic> json) {
    List<bool>? completedZones;
    if (json['completedKnittingZones'] != null) {
      completedZones = List<bool>.from(
        (json['completedKnittingZones'] as List).map((x) => x as bool),
      );
    }

    List<KnittingZone>? zones;
    if (json['savedKnittingZones'] != null) {
      zones = (json['savedKnittingZones'] as List)
          .map((zoneJson) => KnittingZone.fromJson(zoneJson))
          .toList();
    }

    List<ZoneMetadata>? metadata;
    if (json['savedZoneMetadata'] != null) {
      metadata = (json['savedZoneMetadata'] as List)
          .map((metadataJson) => ZoneMetadata.fromJson(metadataJson))
          .toList();
    }

    return WizardStateModel(
      id: json['id'],
      userId: json['userId'],
      currentStep: json['currentStep'] ?? 0,
      itemData: NewItemModel.fromJson(json['itemData']),
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
      lastModified: json['lastModified'] != null
          ? DateTime.parse(json['lastModified'])
          : DateTime.now(),
      isCompleted: json['isCompleted'] ?? false,
      isArchived: json['isArchived'] ?? false,
      shapeTestState: json['shapeTestState'] != null
          ? ShapeTestState.fromJson(json['shapeTestState'])
          : null,
      currentKnittingRowIndex: json['currentKnittingRowIndex'],
      currentKnittingZoneIndex: json['currentKnittingZoneIndex'],
      completedKnittingZones: completedZones,
      savedKnittingZones: zones,
      savedZoneMetadata: metadata,
      instructionsHash: json['instructionsHash'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'id': id,
      'userId': userId,
      'currentStep': currentStep,
      'itemData': itemData.toJson(),
      'createdAt': Timestamp.fromDate(createdAt),
      'lastModified': Timestamp.fromDate(lastModified),
      'isCompleted': isCompleted,
      'isArchived': isArchived,
      'shapeTestState': shapeTestState?.toJson(),
      'currentKnittingRowIndex': currentKnittingRowIndex,
      'currentKnittingZoneIndex': currentKnittingZoneIndex,
      'completedKnittingZones': completedKnittingZones,
      // Only save lightweight metadata to Firestore, not full zones with large instruction arrays
      'savedZoneMetadata':
          savedZoneMetadata?.map((metadata) => metadata.toJson()).toList(),
      'instructionsHash': instructionsHash,
    };
  }

  factory WizardStateModel.fromFirestore(Map<String, dynamic> data) {
    List<bool>? completedZones;
    if (data['completedKnittingZones'] != null) {
      completedZones = List<bool>.from(
        (data['completedKnittingZones'] as List).map((x) => x as bool),
      );
    }

    List<ZoneMetadata>? metadata;
    if (data['savedZoneMetadata'] != null) {
      metadata = (data['savedZoneMetadata'] as List)
          .map((metadataJson) => ZoneMetadata.fromJson(metadataJson))
          .toList();
    }

    return WizardStateModel(
      id: data['id'],
      userId: data['userId'],
      currentStep: data['currentStep'],
      itemData: NewItemModel.fromJson(data['itemData']),
      createdAt: data['createdAt'] != null
          ? (data['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      lastModified: data['lastModified'] != null
          ? (data['lastModified'] as Timestamp).toDate()
          : DateTime.now(),
      isCompleted: data['isCompleted'] ?? false,
      isArchived: data['isArchived'] ?? false,
      shapeTestState: data['shapeTestState'] != null
          ? ShapeTestState.fromJson(data['shapeTestState'])
          : null,
      currentKnittingRowIndex: data['currentKnittingRowIndex'],
      currentKnittingZoneIndex: data['currentKnittingZoneIndex'],
      completedKnittingZones: completedZones,
      savedZoneMetadata: metadata,
      instructionsHash: data['instructionsHash'],
    );
  }

  /// Creates a copy of this model with some fields replaced.
  WizardStateModel copyWith({
    String? id,
    String? userId,
    int? currentStep,
    NewItemModel? itemData,
    DateTime? createdAt,
    DateTime? lastModified,
    bool? isCompleted,
    bool? isArchived,
    ShapeTestState? shapeTestState,
    int? currentKnittingRowIndex,
    int? currentKnittingZoneIndex,
    List<bool>? completedKnittingZones,
    List<KnittingZone>? savedKnittingZones,
    List<ZoneMetadata>? savedZoneMetadata,
    String? instructionsHash,
  }) {
    return WizardStateModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      currentStep: currentStep ?? this.currentStep,
      itemData: itemData ?? this.itemData,
      createdAt: createdAt ?? this.createdAt,
      lastModified: lastModified ?? this.lastModified,
      isCompleted: isCompleted ?? this.isCompleted,
      isArchived: isArchived ?? this.isArchived,
      shapeTestState: shapeTestState ?? this.shapeTestState,
      currentKnittingRowIndex:
          currentKnittingRowIndex ?? this.currentKnittingRowIndex,
      currentKnittingZoneIndex:
          currentKnittingZoneIndex ?? this.currentKnittingZoneIndex,
      completedKnittingZones:
          completedKnittingZones ?? this.completedKnittingZones,
      savedKnittingZones: savedKnittingZones ?? this.savedKnittingZones,
      savedZoneMetadata: savedZoneMetadata ?? this.savedZoneMetadata,
      instructionsHash: instructionsHash ?? this.instructionsHash,
    );
  }
}
