import 'package:flutter/material.dart';
import 'package:xoxknit/app/modules/shape_test/models/shape_data.dart';
import 'package:xoxknit/app/modules/shape_test/utils/shape_serialization.dart';

/// Model class to store the state of the shape test editor
class ShapeTestState {
  /// The list of shapes in the editor
  final List<ShapeData> shapes;

  /// Keys associated with each shape for reference
  final List<String>? shapeKeys;

  /// Constructor
  ShapeTestState({
    required this.shapes,
    this.shapeKeys,
  });

  /// Convert the state to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'shapes': ShapeSerializer.shapesListToJson(shapes),
      'shapeKeys': shapeKeys,
    };
  }

  /// Create a state from JSON data
  factory ShapeTestState.fromJson(Map<String, dynamic> json) {
    return ShapeTestState(
      shapes: json['shapes'] != null
          ? ShapeSerializer.shapesListFromJson(json['shapes'])
          : [],
      shapeKeys: json['shapeKeys'] != null
          ? (json['shapeKeys'] as List).cast<String>()
          : null,
    );
  }

  /// Create a copy of this state with some fields replaced
  ShapeTestState copyWith({
    List<ShapeData>? shapes,
    List<String>? shapeKeys,
  }) {
    return ShapeTestState(
      shapes: shapes ?? this.shapes,
      shapeKeys: shapeKeys ?? this.shapeKeys,
    );
  }
}
