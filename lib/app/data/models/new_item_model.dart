import 'package:xoxknit/app/data/models/user_knitting_machine_model.dart';

class NewItemModel {
  String? name;
  String? stitchType;
  String? swatchNumber;
  DateTime? startDate;
  DateTime? neededByDate;
  String? yarnSupplier;
  String? yarnTitle;
  String? yarnComposition;
  int? strands;
  String? color;
  double? weightOnHand;
  UserKnittingMachineModel? knittingMachine;
  String? tension;
  String? notes;
  double? stitchesPerCm;
  double? rowsPerCm;
  double? stitchesPer10Cm;
  double? rowsPer10Cm;
  double? weightPer100CmSquared;
  Map<String, dynamic>? swatchInfo;

  NewItemModel({
    this.name,
    this.stitchType,
    this.swatchNumber,
    this.startDate,
    this.neededByDate,
    this.yarnSupplier,
    this.yarnTitle,
    this.yarnComposition,
    this.strands,
    this.color,
    this.weightOnHand,
    this.knittingMachine,
    this.tension,
    this.notes,
    this.stitchesPerCm,
    this.rowsPerCm,
    this.stitchesPer10Cm,
    this.rowsPer10Cm,
    this.weightPer100CmSquared,
    this.swatchInfo,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'stitchType': stitchType,
      'swatchNumber': swatchNumber,
      'startDate': startDate?.toIso8601String(),
      'neededByDate': neededByDate?.toIso8601String(),
      'yarnSupplier': yarnSupplier,
      'yarnTitle': yarnTitle,
      'yarnComposition': yarnComposition,
      'strands': strands,
      'color': color,
      'weightOnHand': weightOnHand,
      'knittingMachine': knittingMachine?.toJsonSaveString(),
      'tension': tension,
      'notes': notes,
      'stitchesPerCm': stitchesPerCm,
      'rowsPerCm': rowsPerCm,
      'stitchesPer10Cm': stitchesPer10Cm,
      'rowsPer10Cm': rowsPer10Cm,
      'weightPer100CmSquared': weightPer100CmSquared,
      'swatchInfo': swatchInfo,
    };
  }

  factory NewItemModel.fromJson(Map<String, dynamic> json) {
    return NewItemModel(
      name: json['name'],
      stitchType: json['stitchType'],
      swatchNumber: json['swatchNumber'],
      startDate:
          json['startDate'] != null ? DateTime.parse(json['startDate']) : null,
      neededByDate: json['neededByDate'] != null
          ? DateTime.parse(json['neededByDate'])
          : null,
      yarnSupplier: json['yarnSupplier'],
      yarnTitle: json['yarnTitle'],
      yarnComposition: json['yarnComposition'],
      strands: json['strands'],
      color: json['color'],
      weightOnHand: json['weightOnHand'] != null
          ? (json['weightOnHand'] as num).toDouble()
          : null,
      knittingMachine: json['knittingMachine'] != null
          ? UserKnittingMachineModel.fromJson(json['knittingMachine'])
          : null,
      tension: json['tension'],
      notes: json['notes'],
      stitchesPerCm: json['stitchesPerCm'] != null
          ? (json['stitchesPerCm'] as num).toDouble()
          : null,
      rowsPerCm: json['rowsPerCm'] != null
          ? (json['rowsPerCm'] as num).toDouble()
          : null,
      stitchesPer10Cm: json['stitchesPer10Cm'] != null
          ? (json['stitchesPer10Cm'] as num).toDouble()
          : null,
      rowsPer10Cm: json['rowsPer10Cm'] != null
          ? (json['rowsPer10Cm'] as num).toDouble()
          : null,
      weightPer100CmSquared: json['weightPer100CmSquared'] != null
          ? (json['weightPer100CmSquared'] as num).toDouble()
          : null,
      swatchInfo: json['swatchInfo'],
    );
  }
}
