import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/routes/app_pages.dart';
import 'package:xoxknit/app/services/knitting/knitting_settings_service.dart';

class MachineCheckMiddleware extends GetMiddleware {
  @override
  int? get priority => 1; // Higher priority than AuthMiddleware

  @override
  RouteSettings? redirect(String? route) {
    if (!KnittingSettingsService.to.hasUserMachines) {
      Get.rootDelegate.offNamed(
        Routes.SETTINGS + Routes.USER_MACHINES,
        parameters: {
          'message': 'Please add at least one knitting machine to continue'
        },
      );

      Fluttertoast.showToast(
          msg: 'Please add at least one knitting machine to continue');

      // Return empty route to prevent further navigation
      return const RouteSettings(name: '');
    }
    return null;
  }

  @override
  GetPage? onPageCalled(GetPage? page) {
    if (!KnittingSettingsService.to.hasUserMachines) {
      return GetPage(
        name: Routes.SETTINGS + Routes.USER_MACHINES,
        page: () => page!.page(),
        arguments: {
          'message': 'Please add at least one knitting machine to continue'
        },
      );
    }
    return page;
  }
}
