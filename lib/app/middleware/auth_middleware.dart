import 'package:get/get.dart';
import 'package:xoxknit/app/routes/app_pages.dart';
import 'package:xoxknit/app/services/auth_service.dart';
import 'package:xoxknit/app/services/route_service.dart';

/// Middleware to handle authentication redirection.
///
/// This middleware checks if the user is authenticated before allowing access
/// to certain routes. If the user is not authenticated and tries to access a
/// route other than the login or signup routes, they will be redirected to the
/// login route.
class AuthMiddleware extends GetMiddleware {
  @override
  int? get priority => 1;

  @override
  Future<GetNavConfig?> redirectDelegate(GetNavConfig route) async {
    // List of routes that don't require authentication
    final publicRoutes = [
      Routes.LOGIN,
      Routes.SIGNUP,
      Routes.SPLASH_SCREEN,
      Routes.RESET_PASSWORD,
    ];

    if (route.currentPage?.name == Routes.SPLASH_SCREEN) {
      return GetNavConfig.fromRoute(Routes.SPLASH_SCREEN);
    }

    // Ensure auth service is initialized before proceeding
    if (!Get.isRegistered<AuthService>()) {
      return GetNavConfig.fromRoute(Routes.SPLASH_SCREEN);
    }

    final authService = AuthService.to;

    // Wait for auth service to be fully initialized
    if (!authService.isInitialized) {
      return GetNavConfig.fromRoute(Routes.SPLASH_SCREEN);
    }

    // prevent users from accessing the login page if they are already logged in
    if (authService.isLoggedIn &&
        publicRoutes.contains(route.currentPage?.name)) {
      return GetNavConfig.fromRoute(Routes.HOME);
    }

    if (!authService.isLoggedIn &&
        !publicRoutes.contains(route.currentPage?.name)) {
      // Save the intended route before redirecting
      RouteService.to.saveIntendedRoute(route.currentPage?.name);

      // Redirect to the login page
      return GetNavConfig.fromRoute(Routes.LOGIN);
    }

    return await super.redirectDelegate(route);
  }
}
