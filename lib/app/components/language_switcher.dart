import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/services/locale_service.dart';
import '../core/theme/app_colors.dart';

class LanguageSwitcher extends StatelessWidget {
  const LanguageSwitcher({super.key});

  @override
  Widget build(BuildContext context) {
    return GetX<LocaleService>(
      init: LocaleService(),
      builder: (controller) => PopupMenuButton(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        color: Get.theme.colorScheme.surface,
        offset: const Offset(0, 40),
        icon: _buildCurrentLanguage(controller),
        itemBuilder: (context) => controller.supportedLocales
            .map(
                (locale) => _buildLanguageMenuItem(context, locale, controller))
            .toList(),
      ),
    );
  }

  Widget _buildCurrentLanguage(LocaleService controller) {
    final currentLocale = controller.supportedLocales.firstWhere(
      (locale) => locale['code'] == controller.currentLanguage,
      orElse: () => controller.supportedLocales.first,
    );

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          currentLocale['flag']!,
          style: const TextStyle(fontSize: 24),
        ),
        const SizedBox(width: 8),
        Icon(
          Icons.arrow_drop_down,
          color: Get.isDarkMode ? AppColors.white : AppColors.dark,
        ),
      ],
    );
  }

  PopupMenuItem _buildLanguageMenuItem(
    BuildContext context,
    Map<String, String> locale,
    LocaleService controller,
  ) {
    final isSelected = controller.currentLanguage == locale['code'];

    return PopupMenuItem(
      value: locale['code'],
      onTap: () => controller.changeLocale(locale['code']!, locale['country']!),
      child: Row(
        children: [
          Text(
            locale['flag']!,
            style: const TextStyle(fontSize: 24),
          ),
          const SizedBox(width: 12),
          Text(
            locale['name']!,
            style: TextStyle(
              color: isSelected ? Get.theme.colorScheme.primary : null,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          if (isSelected) ...[
            const SizedBox(width: 8),
            Icon(
              Icons.check,
              color: Get.theme.colorScheme.primary,
              size: 20,
            ),
          ],
        ],
      ),
    );
  }
}
