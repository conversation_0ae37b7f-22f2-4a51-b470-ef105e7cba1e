import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/core/theme/app_colors.dart';
import 'package:xoxknit/app/core/theme/app_typography.dart';
import 'package:xoxknit/app/routes/app_pages.dart';
import 'package:xoxknit/app/services/auth_service.dart';

class AppDrawer extends StatelessWidget {
  const AppDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(GetPlatform.isMobile ? 32 : 0),
          bottomRight: Radius.circular(GetPlatform.isMobile ? 32 : 0),
        ),
      ),
      child: Column(
        children: [
          _buildDrawerHeader(),
          Expanded(
            child: _buildDrawerBody(context),
          ),
          _buildDrawerFooter(),
        ],
      ),
    );
  }

  Widget _buildDrawerHeader() {
    final AuthService authService = AuthService.to;
    return DrawerHeader(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Get.theme.colorScheme.primary,
            Get.theme.colorScheme.primary.withOpacity(0.8),
          ],
        ),
      ),
      child: SizedBox(
        width: double.maxFinite,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            CircleAvatar(
              radius: 32,
              backgroundColor: AppColors.white,
              child: Image.asset(
                'assets/images/logo.png',
                width: 48,
                height: 48,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              authService.currentUser.value?.fullName ?? "",
              style: AppTypography.titleLarge.copyWith(
                color: AppColors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              authService.currentUser.value?.email ?? "",
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.white.withOpacity(0.9),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawerBody(BuildContext context) {
    return ListView(
      padding: EdgeInsets.zero,
      children: [
        // _buildDrawerTile(
        //   icon: CupertinoIcons.home,
        //   title: 'home_drawer_home'.tr,
        //   onTap: () {},
        // ),
        // _buildDrawerTile(
        //   icon: Icons.calculate_outlined,
        //   title: 'home_drawer_calculators'.tr,
        //   onTap: () {},
        // ),
        // _buildExpansionTile(
        //   icon: Icons.folder_outlined,
        //   title: 'My Projects',
        //   children: [
        //     _buildSubTile(
        //       title: 'Active Projects',
        //       onTap: () {},
        //     ),
        //     _buildSubTile(
        //       title: 'Completed Projects',
        //       onTap: () {},
        //     ),
        //     _buildSubTile(
        //       title: 'Templates',
        //       onTap: () {},
        //     ),
        //   ],
        // ),
        // _buildDrawerTile(
        //   icon: Icons.shopping_bag_outlined,
        //   title: 'home_drawer_shop'.tr,
        //   onTap: () {},
        // ),
        // _buildDrawerTile(
        //   icon: Icons.school_outlined,
        //   title: 'home_drawer_courses'.tr,
        //   onTap: () {},
        // ),
        // const Divider(),
        _buildDrawerTile(
          icon: CupertinoIcons.settings,
          title: 'home_drawer_settings'.tr,
          onTap: () {
            Get.rootDelegate.toNamed(Routes.SETTINGS);
          },
        ),
        _buildDrawerTile(
          icon: CupertinoIcons.question_circle,
          title: 'home_drawer_helpSupport'.tr,
          onTap: () {},
        ),
      ],
    );
  }

  Widget _buildDrawerTile({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: Get.theme.colorScheme.primary,
      ),
      title: Text(
        title,
        style: AppTypography.bodyLarge,
      ),
      onTap: onTap,
    );
  }

  Widget _buildExpansionTile({
    required IconData icon,
    required String title,
    required List<Widget> children,
  }) {
    return ExpansionTile(
      leading: Icon(
        icon,
        color: Get.theme.colorScheme.primary,
      ),
      title: Text(
        title,
        style: AppTypography.bodyLarge,
      ),
      children: children,
    );
  }

  Widget _buildSubTile({
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      contentPadding: const EdgeInsets.only(left: 72, right: 16),
      title: Text(
        title,
        style: AppTypography.bodyMedium,
      ),
      onTap: onTap,
    );
  }

  Widget _buildDrawerFooter() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Divider(),
          _buildDrawerTile(
            icon: CupertinoIcons.square_arrow_left,
            title: 'home_drawer_logout'.tr,
            onTap: () {
              AuthService.to.signOut();
            },
          ),
        ],
      ),
    );
  }
}
