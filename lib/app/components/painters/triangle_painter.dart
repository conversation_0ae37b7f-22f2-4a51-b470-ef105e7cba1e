import 'package:flutter/material.dart';
import 'dart:math' as math;

enum TriangleType { isosceles, right }

enum IsoscelesOrientation {
  pointingUp,
  pointingDown,
  pointingLeft,
  pointingRight
}

enum RightAngleOrientation { upRight, upLeft, downRight, downLeft }

class TrianglePainter extends CustomPainter {
  final Color color;
  final double progress;
  final bool showGrid;
  final int? totalStitches;
  final int? totalRows;
  final TriangleType triangleType;
  final IsoscelesOrientation? isoscelesOrientation;
  final RightAngleOrientation? rightAngleOrientation;

  TrianglePainter({
    required this.color,
    this.progress = 1.0,
    this.showGrid = false,
    this.totalStitches,
    this.totalRows,
    required this.triangleType,
    this.isoscelesOrientation,
    this.rightAngleOrientation,
  }) : assert(
          (triangleType == TriangleType.isosceles &&
                  isoscelesOrientation != null) ||
              (triangleType == TriangleType.right &&
                  rightAngleOrientation != null),
          'Must provide appropriate orientation for triangle type',
        );

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color.withOpacity(0.2)
      ..style = PaintingStyle.fill;

    final borderPaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    final path = _getTrianglePath(size);

    // Draw filled triangle
    canvas.drawPath(path, paint);

    // Draw border
    canvas.drawPath(path, borderPaint);

    if (showGrid && totalStitches != null && totalRows != null) {
      _drawGrid(canvas, size);
    }

    _drawIncreaseArrows(canvas, size);
  }

  Path _getTrianglePath(Size size) {
    return triangleType == TriangleType.isosceles
        ? _getIsoscelesPath(size)
        : _getRightAnglePath(size);
  }

  Path _getIsoscelesPath(Size size) {
    switch (isoscelesOrientation!) {
      case IsoscelesOrientation.pointingUp:
        return Path()
          ..moveTo(size.width / 2, size.height * 0.2)
          ..lineTo(size.width * 0.2, size.height * 0.8)
          ..lineTo(size.width * 0.8, size.height * 0.8)
          ..close();
      case IsoscelesOrientation.pointingDown:
        return Path()
          ..moveTo(size.width / 2, size.height * 0.8)
          ..lineTo(size.width * 0.2, size.height * 0.2)
          ..lineTo(size.width * 0.8, size.height * 0.2)
          ..close();
      case IsoscelesOrientation.pointingLeft:
        return Path()
          ..moveTo(size.width * 0.2, size.height / 2)
          ..lineTo(size.width * 0.8, size.height * 0.2)
          ..lineTo(size.width * 0.8, size.height * 0.8)
          ..close();
      case IsoscelesOrientation.pointingRight:
        return Path()
          ..moveTo(size.width * 0.8, size.height / 2)
          ..lineTo(size.width * 0.2, size.height * 0.2)
          ..lineTo(size.width * 0.2, size.height * 0.8)
          ..close();
    }
  }

  Path _getRightAnglePath(Size size) {
    switch (rightAngleOrientation!) {
      case RightAngleOrientation.upRight:
        return Path()
          ..moveTo(size.width * 0.2, size.height * 0.8)
          ..lineTo(size.width * 0.2, size.height * 0.2)
          ..lineTo(size.width * 0.8, size.height * 0.2)
          ..close();
      case RightAngleOrientation.upLeft:
        return Path()
          ..moveTo(size.width * 0.8, size.height * 0.8)
          ..lineTo(size.width * 0.8, size.height * 0.2)
          ..lineTo(size.width * 0.2, size.height * 0.2)
          ..close();
      case RightAngleOrientation.downRight:
        return Path()
          ..moveTo(size.width * 0.2, size.height * 0.2)
          ..lineTo(size.width * 0.2, size.height * 0.8)
          ..lineTo(size.width * 0.8, size.height * 0.8)
          ..close();
      case RightAngleOrientation.downLeft:
        return Path()
          ..moveTo(size.width * 0.8, size.height * 0.2)
          ..lineTo(size.width * 0.8, size.height * 0.8)
          ..lineTo(size.width * 0.2, size.height * 0.8)
          ..close();
    }
  }

  void _drawGrid(Canvas canvas, Size size) {
    final gridPaint = Paint()
      ..color = color.withOpacity(0.1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // Draw horizontal lines (rows)
    if (totalRows != null) {
      final rowSpacing = size.height / totalRows!;
      for (int i = 1; i < totalRows!; i++) {
        final y = rowSpacing * i;
        final progress = i / totalRows!;
        final currentWidth = size.width * progress;
        final startX = (size.width - currentWidth) / 2;
        final endX = startX + currentWidth;

        canvas.drawLine(
          Offset(startX, y),
          Offset(endX, y),
          gridPaint,
        );
      }
    }
  }

  void _drawIncreaseArrows(Canvas canvas, Size size) {
    final arrowPaint = Paint()
      ..color = color.withOpacity(0.6)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    // Calculate arrow positions
    final arrowLength = size.width * 0.05;
    final arrowAngle = math.pi / 6; // 30 degrees

    // Draw left side arrows
    _drawArrow(
      canvas,
      Offset(size.width * 0.25, size.height * 0.3),
      -math.pi / 4, // 45 degrees left
      arrowLength,
      arrowAngle,
      arrowPaint,
    );

    // Draw right side arrows
    _drawArrow(
      canvas,
      Offset(size.width * 0.75, size.height * 0.3),
      math.pi / 4, // 45 degrees right
      arrowLength,
      arrowAngle,
      arrowPaint,
    );
  }

  void _drawArrow(Canvas canvas, Offset start, double angle, double length,
      double arrowAngle, Paint paint) {
    final end = Offset(
      start.dx + length * math.cos(angle),
      start.dy + length * math.sin(angle),
    );

    // Draw main line
    canvas.drawLine(start, end, paint);

    // Draw arrow head
    final arrowLength = length * 0.3;
    final arrowPoint1 = Offset(
      end.dx - arrowLength * math.cos(angle - arrowAngle),
      end.dy - arrowLength * math.sin(angle - arrowAngle),
    );
    final arrowPoint2 = Offset(
      end.dx - arrowLength * math.cos(angle + arrowAngle),
      end.dy - arrowLength * math.sin(angle + arrowAngle),
    );

    canvas.drawLine(end, arrowPoint1, paint);
    canvas.drawLine(end, arrowPoint2, paint);
  }

  @override
  bool shouldRepaint(TrianglePainter oldDelegate) {
    return oldDelegate.color != color ||
        oldDelegate.progress != progress ||
        oldDelegate.showGrid != showGrid ||
        oldDelegate.totalStitches != totalStitches ||
        oldDelegate.totalRows != totalRows ||
        oldDelegate.triangleType != triangleType ||
        oldDelegate.isoscelesOrientation != isoscelesOrientation ||
        oldDelegate.rightAngleOrientation != rightAngleOrientation;
  }
}
