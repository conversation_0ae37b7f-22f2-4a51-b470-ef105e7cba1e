import 'package:flutter/material.dart';
import '../core/theme/app_typography.dart';

class BrandText extends StatelessWidget {
  final String text;
  final bool large;
  final Color? color;

  const BrandText(
      {super.key, required this.text, this.large = false, this.color});

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: large
          ? AppTypography.brandStyleLarge.copyWith(color: color)
          : AppTypography.brandStyle.copyWith(color: color),
    );
  }
}
