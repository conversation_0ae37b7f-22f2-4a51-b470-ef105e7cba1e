import 'package:flutter/material.dart';
import 'package:xoxknit/app/components/patterns/stitch_patterns.dart';

class ZigZagStitchPattern implements StitchPattern {
  const ZigZagStitchPattern();

  @override
  void drawStitch(
    Canvas canvas,
    Offset position,
    double width,
    double height,
    Color primaryColor,
    Color shadowColor,
    Color highlightColor,
  ) {
    final x = position.dx;
    final y = position.dy;

    final path = Path();
    path.moveTo(x, y + height * 0.5);

    // Create zigzag pattern
    path.lineTo(x + width * 0.25, y);
    path.lineTo(x + width * 0.5, y + height);
    path.lineTo(x + width * 0.75, y);
    path.lineTo(x + width, y + height * 0.5);

    // Draw shadow
    canvas.drawPath(
      path,
      Paint()
        ..color = shadowColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 3.0,
    );

    // Draw main stitch
    canvas.drawPath(
      path,
      Paint()
        ..color = primaryColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0,
    );

    // Add highlight accents
    final highlightPath = Path();
    highlightPath.moveTo(x + width * 0.25, y + height * 0.3);
    highlightPath.lineTo(x + width * 0.5, y + height * 0.7);

    canvas.drawPath(
      highlightPath,
      Paint()
        ..color = highlightColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0,
    );
  }
}
