import 'package:flutter/material.dart';
import 'package:xoxknit/app/components/patterns/stitch_patterns.dart';

class VStitchPattern implements StitchPattern {
  const VStitchPattern();
  @override
  void drawStitch(
    Canvas canvas,
    Offset position,
    double width,
    double height,
    Color primaryColor,
    Color shadowColor,
    Color highlightColor,
  ) {
    final x = position.dx;
    final y = position.dy;

    final path = Path();
    path.moveTo(x, y);

    // Left leg of the V
    path.cubicTo(
      x + width * 0.2,
      y + height * 0.3,
      x + width * 0.3,
      y + height * 0.7,
      x + width * 0.5,
      y + height * 0.9,
    );

    // Right leg of the V
    path.cubicTo(
      x + width * 0.7,
      y + height * 0.7,
      x + width * 0.8,
      y + height * 0.3,
      x + width,
      y,
    );

    // Draw shadow
    canvas.drawPath(
      path,
      Paint()
        ..color = shadowColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 3.0,
    );

    // Draw main stitch
    canvas.drawPath(
      path,
      Paint()
        ..color = primaryColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0,
    );

    // Add highlight
    final highlightPath = Path();
    highlightPath.moveTo(x + width * 0.4, y + height * 0.3);
    highlightPath.quadraticBezierTo(
      x + width * 0.5,
      y + height * 0.5,
      x + width * 0.6,
      y + height * 0.3,
    );

    canvas.drawPath(
      highlightPath,
      Paint()
        ..color = highlightColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0,
    );
  }
}
