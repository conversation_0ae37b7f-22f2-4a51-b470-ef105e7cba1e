import 'package:flutter/material.dart';
import 'package:xoxknit/app/components/patterns/stitch_patterns.dart';

class WaveStitchPattern implements StitchPattern {
  const WaveStitchPattern();

  @override
  void drawStitch(
    Canvas canvas,
    Offset position,
    double width,
    double height,
    Color primaryColor,
    Color shadowColor,
    Color highlightColor,
  ) {
    final x = position.dx;
    final y = position.dy;

    final path = Path();
    path.moveTo(x, y + height * 0.5);

    // Create wave pattern using bezier curves
    path.cubicTo(
      x + width * 0.25,
      y,
      x + width * 0.5,
      y + height,
      x + width * 0.75,
      y + height * 0.5,
    );

    path.cubicTo(
      x + width * 0.875,
      y + height * 0.25,
      x + width * 0.9375,
      y + height * 0.75,
      x + width,
      y + height * 0.5,
    );

    // Draw shadow
    canvas.drawPath(
      path,
      Paint()
        ..color = shadowColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 3.0,
    );

    // Draw main stitch
    canvas.drawPath(
      path,
      Paint()
        ..color = primaryColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0,
    );

    // Add highlight accents
    final highlightPath = Path();
    highlightPath.moveTo(x + width * 0.25, y + height * 0.4);
    highlightPath.quadraticBezierTo(
      x + width * 0.5,
      y + height * 0.8,
      x + width * 0.75,
      y + height * 0.4,
    );

    canvas.drawPath(
      highlightPath,
      Paint()
        ..color = highlightColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0,
    );
  }
}
