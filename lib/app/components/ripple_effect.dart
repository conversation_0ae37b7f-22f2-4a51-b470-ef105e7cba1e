import 'package:flutter/material.dart';

class RippleEffect extends StatelessWidget {
  final Color color;
  final double size;

  const RippleEffect({
    super.key,
    required this.color,
    required this.size,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: TweenAnimationBuilder<double>(
        tween: Tween(begin: 0.0, end: 1.0),
        duration: const Duration(milliseconds: 1000),
        builder: (context, value, child) {
          return Container(
            width: size * value,
            height: size * value,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: color.withOpacity(1 - value),
            ),
          );
        },
      ),
    );
  }
}
