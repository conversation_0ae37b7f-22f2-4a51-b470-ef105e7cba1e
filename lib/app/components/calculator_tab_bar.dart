import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/core/theme/app_colors.dart';
import 'package:xoxknit/app/core/theme/app_typography.dart';

class CalculatorTab {
  final String label;
  final IconData icon;

  const CalculatorTab({
    required this.label,
    required this.icon,
  });
}

class AnimatedCalculatorTabBar extends StatelessWidget {
  final List<CalculatorTab> tabs;
  final int selectedIndex;
  final Function(int) onTabSelected;

  const AnimatedCalculatorTabBar({
    super.key,
    required this.tabs,
    required this.selectedIndex,
    required this.onTabSelected,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      physics: const BouncingScrollPhysics(),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: List.generate(
            tabs.length,
            (index) => Padding(
              padding: const EdgeInsets.only(right: 16),
              child: AnimatedTabItem(
                tab: tabs[index],
                isSelected: selectedIndex == index,
                onTap: () => onTabSelected(index),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class AnimatedTabItem extends StatefulWidget {
  final CalculatorTab tab;
  final bool isSelected;
  final VoidCallback onTap;

  const AnimatedTabItem({
    super.key,
    required this.tab,
    required this.isSelected,
    required this.onTap,
  });

  @override
  State<AnimatedTabItem> createState() => _AnimatedTabItemState();
}

class _AnimatedTabItemState extends State<AnimatedTabItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.1,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => _controller.forward(),
      onTapUp: (_) {
        _controller.reverse();
        widget.onTap();
      },
      onTapCancel: () => _controller.reverse(),
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Transform.rotate(
              angle: _rotationAnimation.value * (widget.isSelected ? 1 : 0),
              child: _buildTabContent(),
            ),
          );
        },
      ),
    );
  }

  Widget _buildTabContent() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 13, vertical: 9),
      decoration: BoxDecoration(
        color: widget.isSelected
            ? Get.theme.colorScheme.primary
            : Get.isDarkMode
                ? AppColors.darkGray.withOpacity(0.8)
                : AppColors.lightGray,
        borderRadius: BorderRadius.circular(16),
        boxShadow: widget.isSelected
            ? [
                BoxShadow(
                  color: Get.theme.colorScheme.primary.withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ]
            : null,
        border: Border.all(
          color: widget.isSelected
              ? Get.theme.colorScheme.primary.withOpacity(0.5)
              : Colors.transparent,
          width: 2,
        ),
      ),
      child: AnimatedSize(
        duration: const Duration(milliseconds: 200),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedRotation(
              duration: const Duration(milliseconds: 200),
              turns: widget.isSelected ? 1 : 0,
              child: Icon(
                widget.tab.icon,
                size: 24,
                color: _getIconColor(),
              ),
            ),
            const SizedBox(height: 6),
            AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: 200),
              style: AppTypography.labelSmall.copyWith(
                color: _getTextColor(),
                fontWeight:
                    widget.isSelected ? FontWeight.w600 : FontWeight.w400,
              ),
              child: Text(widget.tab.label),
            ),
          ],
        ),
      ),
    );
  }

  Color _getIconColor() {
    if (widget.isSelected) return AppColors.white;
    return Get.isDarkMode
        ? AppColors.white.withOpacity(0.7)
        : AppColors.dark.withOpacity(0.7);
  }

  Color _getTextColor() {
    if (widget.isSelected) return AppColors.white;
    return Get.isDarkMode
        ? AppColors.white.withOpacity(0.7)
        : AppColors.dark.withOpacity(0.7);
  }
}
