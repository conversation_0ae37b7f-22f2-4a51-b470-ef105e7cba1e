import 'package:flutter/material.dart';

class MultiSelectDropdown<T> extends StatefulWidget {
  final List<T> items;
  final List<T> selectedItems;
  final Function(List<T>) onSelectionChanged;
  final String Function(T) itemAsString;
  final Widget Function(T)? itemBuilder;
  final InputDecoration? searchDecoration;
  final BoxDecoration? dropdownDecoration;
  final String searchHintText;
  final double maxHeight;
  final Color? selectedItemColor;
  final int? maxSelectedItems;
  final String placeholder;

  const MultiSelectDropdown({
    super.key,
    required this.items,
    required this.selectedItems,
    required this.onSelectionChanged,
    required this.itemAsString,
    this.itemBuilder,
    this.searchDecoration,
    this.dropdownDecoration,
    this.maxSelectedItems,
    this.searchHintText = 'Search...',
    this.placeholder = 'Select...',
    this.maxHeight = 300,
    this.selectedItemColor,
  });

  @override
  State<MultiSelectDropdown<T>> createState() => _MultiSelectDropdownState<T>();
}

class _MultiSelectDropdownState<T> extends State<MultiSelectDropdown<T>> {
  final TextEditingController _searchController = TextEditingController();
  final LayerLink _layerLink = LayerLink();
  bool _isOpen = false;
  List<T> _filteredItems = [];
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _filteredItems = widget.items;
    _searchController.addListener(_onSearchChanged);
  }

  void _onSearchChanged() {
    setState(() {
      _filteredItems = widget.items
          .where((item) => widget
              .itemAsString(item)
              .toLowerCase()
              .contains(_searchController.text.toLowerCase()))
          .toList();
    });
  }

  void _toggleSelection(T item) {
    final newSelection = List<T>.from(widget.selectedItems);
    if (newSelection.contains(item)) {
      newSelection.remove(item);
    } else {
      newSelection.add(item);
    }
    widget.onSelectionChanged(newSelection);
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: Column(
        children: [
          GestureDetector(
            onTap: () {
              setState(() => _isOpen = !_isOpen);
              if (_isOpen) _focusNode.requestFocus();
            },
            child: Container(
              decoration: widget.dropdownDecoration ??
                  BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Row(
                children: [
                  if (widget.selectedItems.isEmpty)
                    Expanded(child: Text(widget.placeholder)),
                  if (widget.selectedItems.isNotEmpty)
                    Expanded(
                      child: Wrap(
                        spacing: 8,
                        children: widget.selectedItems.map((item) {
                          return Chip(
                            label: Text(widget.itemAsString(item)),
                            onDeleted: () => _toggleSelection(item),
                            backgroundColor: widget.selectedItemColor,
                          );
                        }).toList(),
                      ),
                    ),
                  Icon(_isOpen ? Icons.arrow_drop_up : Icons.arrow_drop_down),
                ],
              ),
            ),
          ),
          if (_isOpen)
            Material(
              elevation: 8,
              child: Container(
                constraints: BoxConstraints(maxHeight: widget.maxHeight),
                decoration: widget.dropdownDecoration,
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: TextField(
                        controller: _searchController,
                        focusNode: _focusNode,
                        decoration: widget.searchDecoration ??
                            InputDecoration(
                              hintText: widget.searchHintText,
                              prefixIcon: const Icon(Icons.search),
                              border: const OutlineInputBorder(),
                            ),
                      ),
                    ),
                    Expanded(
                      child: ListView.builder(
                        itemCount: _filteredItems.length,
                        itemBuilder: (context, index) {
                          final item = _filteredItems[index];
                          final isSelected =
                              widget.selectedItems.contains(item);
                          return ListTile(
                            selected: isSelected,
                            title: widget.itemBuilder?.call(item) ??
                                Text(widget.itemAsString(item)),
                            onTap: () => _toggleSelection(item),
                            trailing: isSelected
                                ? Icon(Icons.check,
                                    color: widget.selectedItemColor)
                                : null,
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }
}
