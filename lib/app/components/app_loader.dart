import 'package:flutter/cupertino.dart';
import 'dart:io' show Platform;
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';

class AppLoader extends StatelessWidget {
  final Color? color;
  final double? size;

  const AppLoader({
    super.key,
    this.color,
    this.size,
  });

  @override
  Widget build(BuildContext context) {
    if (kIsWeb) {
      return SpinKitFadingCircle(
        color: color ?? Get.theme.primaryColor,
        size: size ?? 20,
      );
    }

    if (Platform.isIOS) {
      return CupertinoActivityIndicator(
        color: color ?? Get.theme.primaryColor,
        radius: (size ?? 20) / 2,
      );
    }

    return SpinKitFadingCircle(
      color: color ?? Get.theme.primaryColor,
      size: size ?? 20,
    );
  }
}
