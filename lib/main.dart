import 'package:display_metrics/display_metrics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_web_plugins/url_strategy.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:shopify_flutter/shopify_flutter.dart';
import 'package:xoxknit/app/bindings/initial_bindings.dart';
import 'package:xoxknit/app/core/theme/app_theme.dart';
import 'package:xoxknit/app/services/app_settings_service.dart';
import 'package:xoxknit/app/services/auth_service.dart';
import 'package:xoxknit/app/services/knitting/knitting_settings_service.dart';
import 'package:xoxknit/app/services/route_service.dart';
import 'package:xoxknit/app/services/wizard_state_service.dart';
import 'package:xoxknit/firebase_options.dart';
import 'package:xoxknit/generated/locales.g.dart';

import 'app/routes/app_pages.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize all services including GetStorage
  await initializeServices();

  runApp(DisplayMetricsWidget(
    child: GetMaterialApp.router(
      title: "app_title".tr,
      getPages: AppPages.routes,
      initialBinding: InitialBinding(),
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      locale: Get.deviceLocale,
      fallbackLocale: const Locale('it', 'IT'),
      debugShowCheckedModeBanner: false,
      translationsKeys: AppTranslation.translations,
      defaultTransition: Transition.fade,
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      routerDelegate: GetDelegate(
        backButtonPopMode: PopMode.History,
        preventDuplicateHandlingMode:
            PreventDuplicateHandlingMode.ReorderRoutes,
      ),
    ),
  ));
}

Future<void> initializeServices() async {
  try {
    usePathUrlStrategy();
    initializeDateFormatting();

    await GetStorage.init();

    await dotenv.load();
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    ShopifyConfig.setConfig(
      storefrontAccessToken: dotenv.env['SHOPIFY_STOREFRONT_ACCESS_TOKEN']!,
      storeUrl: dotenv.env['SHOPIFY_STORE_URL']!,
      storefrontApiVersion: '2024-07',
      adminAccessToken: dotenv.env['SHOPIFY_ADMIN_ACCESS_TOKEN']!,
      cachePolicy: CachePolicy.cacheAndNetwork,
      language: 'en',
    );

    await Get.putAsync(() async => await AppSettingsService().init());
    await Get.putAsync(() async => await AuthService().init());
    Get.put(RouteService());
    await Get.putAsync(
        () async => await KnittingSettingsService().initializeService());

    Get.put(WizardStateService());
  } catch (e) {
    debugPrint('Error initializing services: $e');
  }
}
