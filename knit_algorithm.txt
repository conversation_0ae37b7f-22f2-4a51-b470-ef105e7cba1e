# THE KNITTING INSTRUCTION PARSING ALGORITHM (TO obtain sub items for complex shapes)

- Create a copy of the knitting instructions 2D array
- while array copy is not empty:
    - We start knitting from a bottom right cell (or left depending on user's choice) of the array
    - As long as we keep finding ones (stitches) continue knitting
    - Clear each knitted stitch from the array copy
    - if we encounter a false (no stitch) in row after having encountered a true (stitch) already, or we hit the end of the current row:
        - we stop knitting and go to the start of the next row and repeat 4 - 9
    - if there is no next row:
         - save knitted portion as a sequential sub item (to be displayed to users)
         - clean the array to leave only rows with stitches.
    