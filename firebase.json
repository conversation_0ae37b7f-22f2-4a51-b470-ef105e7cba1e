{"flutter": {"platforms": {"android": {"default": {"projectId": "xoxknit", "appId": "1:50152326994:android:05d1fcf7dcccc2038bdc8b", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "xoxknit", "appId": "1:50152326994:ios:1a4439f2a9c9cc218bdc8b", "uploadDebugSymbols": false, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "macos": {"default": {"projectId": "xoxknit", "appId": "1:50152326994:ios:1a4439f2a9c9cc218bdc8b", "uploadDebugSymbols": false, "fileOutput": "macos/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "xoxknit", "configurations": {"android": "1:50152326994:android:05d1fcf7dcccc2038bdc8b", "ios": "1:50152326994:ios:1a4439f2a9c9cc218bdc8b", "web": "1:50152326994:web:e6d0e0c72fd2e5818bdc8b"}}}}}}