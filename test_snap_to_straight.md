# Snap-to-Straight-Line Functionality Test Plan

## Overview
This document outlines how to test the newly implemented snap-to-straight-line functionality for the shape editor's edge editing mode.

## Implementation Summary

### Features Implemented:
1. **Snap Detection**: Detects when both cubic curve control points are within threshold distance of their straight-line positions (Offset.zero)
2. **Visual Feedback**: Highlights control points in green when snap conditions are met
3. **Zoom-Aware Thresholds**: Adjusts snap threshold based on current zoom level for consistent behavior
4. **Snap Finalization**: Removes cubic curve entirely when user releases drag in snap state
5. **Bounding Box Updates**: Automatically recalculates shape bounds when edges become straight

### Files Modified:
- `lib/app/modules/shape_test/widgets/shape_handles_widget.dart` - Added snap detection and drag end handling
- `lib/app/modules/shape_test/controllers/shape_editor_controller.dart` - Added snap state management and threshold calculation
- `lib/app/modules/shape_test/painters/shape_painter.dart` - Added visual feedback for snap state
- `lib/app/modules/shape_test/views/transformable_shape.dart` - Updated to pass controller reference to painter

## Testing Instructions

### Prerequisites:
1. Open the shape editor in edge editing mode
2. Create a shape with cubic curve controls applied to edges
3. Ensure you're in "side editing" mode (not point editing mode)

### Test Cases:

#### Test 1: Basic Snap Detection
1. **Setup**: Create a rectangle and enter side editing mode
2. **Action**: Drag a cubic curve control point close to the edge midpoint (within ~8 pixels at 1.0 zoom)
3. **Expected**: Control points should turn green and appear slightly larger with a glow effect
4. **Verify**: Both control points for the edge should highlight when either is near the straight position

#### Test 2: Zoom Level Threshold Adjustment
1. **Setup**: Create a curved edge at 1.0 zoom level
2. **Action**: Zoom in to 2.0x and try to trigger snap
3. **Expected**: Snap threshold should be halved (4 pixels instead of 8), making it easier to snap when zoomed in
4. **Verify**: Snap behavior feels consistent regardless of zoom level

#### Test 3: Snap Finalization
1. **Setup**: Get an edge into snap state (green highlighting)
2. **Action**: Release the drag (lift finger/mouse)
3. **Expected**: 
   - Edge should become perfectly straight
   - Cubic curve controls should be completely removed
   - Bounding box should update to reflect straight edge
   - Green highlighting should disappear

#### Test 4: Snap Escape
1. **Setup**: Get an edge into snap state
2. **Action**: Drag the control point further away from the straight position
3. **Expected**: Green highlighting should disappear, allowing precise curve adjustments

#### Test 5: Multiple Edges
1. **Setup**: Create a shape with multiple curved edges
2. **Action**: Snap one edge to straight while leaving others curved
3. **Expected**: Only the snapped edge should become straight, others remain curved

#### Test 6: Mode Exit Cleanup
1. **Setup**: Have some edges in snap state
2. **Action**: Exit side editing mode
3. **Expected**: All snap states should be cleared automatically

### Visual Indicators to Look For:
- **Normal State**: Purple control points and lines
- **Snap State**: Green control points and lines, slightly larger radius, subtle glow effect
- **Post-Snap**: No control points visible for that edge (straight line)

### Performance Considerations:
- Snap detection should not cause noticeable lag during dragging
- Visual feedback should update smoothly
- Bounding box recalculation should be fast

## Troubleshooting

### If snap doesn't trigger:
- Check that you're in side editing mode (not point editing)
- Verify both control points are close to Offset.zero
- Try adjusting zoom level
- Ensure the edge has cubic curves applied

### If visual feedback doesn't appear:
- Check that controller and shapeKey are properly passed to ShapePainter
- Verify the snap state is being set correctly
- Check console for debug messages

### If snap doesn't finalize:
- Ensure drag end handler is being called
- Check that cubic curves are being removed from the shape data
- Verify bounding box is being recalculated

## Expected Behavior Summary:
The snap-to-straight-line functionality should feel natural and intuitive, helping users easily revert curved edges back to straight lines when appropriate. The feature should be forgiving enough to be helpful but not so aggressive as to interfere with precise curve adjustments.
