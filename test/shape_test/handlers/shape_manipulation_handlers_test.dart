import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:xoxknit/app/modules/shape_test/handlers/shape_manipulation_handlers.dart';
import 'package:xoxknit/app/modules/shape_test/models/shape_data.dart';
import 'package:xoxknit/app/modules/shape_test/models/group_shape_data.dart';

// Helper to compare offsets with tolerance
Matcher offsetCloseTo(Offset expected, {double tolerance = 1e-6}) {
  return predicate<Offset>((offset) {
    return (offset.dx - expected.dx).abs() < tolerance &&
        (offset.dy - expected.dy).abs() < tolerance;
  }, 'is close to $expected');
}

// Helper to compare lists of offsets
Matcher offsetListCloseTo(List<Offset> expected, {double tolerance = 1e-6}) {
  return predicate<List<Offset>>((list) {
    if (list.length != expected.length) return false;
    for (int i = 0; i < list.length; i++) {
      if ((list[i].dx - expected[i].dx).abs() >= tolerance ||
          (list[i].dy - expected[i].dy).abs() >= tolerance) {
        print("Mismatch at index $i: Expected ${expected[i]}, Got ${list[i]}");
        return false;
      }
    }
    return true;
  }, 'list is close to $expected');
}

// Helper to compare doubles with tolerance
Matcher doubleCloseTo(double expected, {double tolerance = 1e-6}) {
  return predicate<double>((value) {
    return (value - expected).abs() < tolerance;
  }, 'is close to $expected');
}

void main() {
  group('ShapeManipulationHandlers', () {
    // Define test constraints (e.g., screen size)
    const testConstraints = BoxConstraints(
      minWidth: 0,
      maxWidth: 1000,
      minHeight: 0,
      maxHeight: 800,
    );

    // --- Test Data ---
    final testRect = Rect.fromLTWH(100, 100, 200, 100); // Center (200, 150)
    final initialRectData = ShapeData.rectangle(testRect).copyWith(
      curveControls: {1: Offset(0, -20)}, // Curve on right edge
    );
    final initialRectCenter = initialRectData.center;

    // Group Test Data
    final childRect =
        Rect.fromLTWH(150, 125, 100, 50); // Center (200, 150) - same as parent
    final childShapeData = ShapeData.rectangle(childRect).copyWith(
        curveControls: {0: Offset(10, 10)} // Curve on top edge of child
        );
    final initialGroupData = GroupShapeData(
      vertices: initialRectData.vertices,
      center: initialRectData.center,
      boundingRect: initialRectData.boundingRect,
      childShapes: [childShapeData],
      curveControls: {}, // Group itself has no curves initially
      rotation: 0.0,
      visualRotation: 0.0,
      originalKeys: [],
    );

    group('handleRotation', () {
      test('should rotate a simple rectangle by 90 degrees', () {
        final lastPos =
            initialRectCenter + Offset(100, 0); // Point to the right
        final newPos = initialRectCenter +
            Offset(0, 100); // Point downwards (90 deg clockwise)

        final rotatedData = ShapeManipulationHandlers.handleRotation(
          shapeData: initialRectData,
          lastPosition: lastPos,
          newPosition: newPos,
          constraints: testConstraints,
        );

        // Expected vertices after 90-degree rotation around (200, 150)
        // Original: (100,100), (300,100), (300,200), (100,200)
        // Rotated: (250, 50), (250, 250), (150, 250), (150, 50)
        final expectedVertices = [
          Offset(250, 50),
          Offset(250, 250),
          Offset(150, 250),
          Offset(150, 50)
        ];

        // Expected curve control offset (original: (0, -20)) rotated 90 deg clockwise becomes (-(-20), 0) = (20, 0)
        final expectedCurveControls = {1: Offset(20, 0)};

        expect(rotatedData.vertices, offsetListCloseTo(expectedVertices));
        // Center might shift slightly due to bounding box recalculation of rotated shape
        expect(rotatedData.center.dx, closeTo(initialRectCenter.dx, 0.001));
        expect(rotatedData.center.dy, closeTo(initialRectCenter.dy, 0.001));
        expect(rotatedData.rotation,
            doubleCloseTo(0.0)); // rotation baked into vertices
        expect(rotatedData.visualRotation,
            doubleCloseTo(90.0)); // visualRotation updated
        expect(rotatedData.curveControls.length, 1);
        expect(rotatedData.curveControls[1],
            offsetCloseTo(expectedCurveControls[1]!));
      });

      test('should rotate a group shape by 45 degrees', () {
        final angleRadians = math.pi / 4; // 45 degrees
        final cosA = math.cos(angleRadians);
        final sinA = math.sin(angleRadians);

        final lastPos =
            initialGroupData.center + Offset(50, 0); // Point to the right
        // Calculate new position for 45 degrees rotation
        final rotatedDelta = Offset(50 * cosA, 50 * sinA);
        final newPos = initialGroupData.center + rotatedDelta;

        final rotatedGroup = ShapeManipulationHandlers.handleRotation(
          shapeData: initialGroupData,
          lastPosition: lastPos,
          newPosition: newPos,
          constraints: testConstraints,
        ) as GroupShapeData;

        // 1. Check Group Properties
        expect(rotatedGroup.rotation,
            doubleCloseTo(0.0)); // rotation baked into vertices
        expect(rotatedGroup.visualRotation, doubleCloseTo(45.0));
        expect(
            rotatedGroup.center.dx, closeTo(initialGroupData.center.dx, 0.001));
        expect(
            rotatedGroup.center.dy, closeTo(initialGroupData.center.dy, 0.001));
        expect(
            rotatedGroup.curveControls, isEmpty); // Group still has no curves

        // 2. Check Child Properties
        expect(rotatedGroup.childShapes.length, 1);
        final rotatedChild = rotatedGroup.childShapes.first;

        // Expected child center (200, 150) rotated 45 deg around group center (200, 150) -> stays (200, 150)
        expect(
            rotatedChild.center.dx, closeTo(childShapeData.center.dx, 0.001));
        expect(
            rotatedChild.center.dy, closeTo(childShapeData.center.dy, 0.001));

        // Original child vertices: (150, 125), (250, 125), (250, 175), (150, 175)
        // Rotate around group center (200, 150)
        // P = (x,y), C = (cx,cy), P' = (x',y')
        // x' = cx + (x-cx)cosA - (y-cy)sinA
        // y' = cy + (x-cx)sinA + (y-cy)cosA
        // A = 45 deg, sinA = cosA = 1/sqrt(2) approx 0.7071
        // V1 (150, 125): dx=-50, dy=-25 -> x'=200+(-50*0.7071 - (-25)*0.7071)=200-17.6775=182.32, y'=150+(-50*0.7071 + (-25)*0.7071)=150-53.0325=96.97
        // V2 (250, 125): dx=50, dy=-25 -> x'=200+(50*0.7071 - (-25)*0.7071)=200+53.0325=253.03, y'=150+(50*0.7071 + (-25)*0.7071)=150+17.6775=167.68
        // V3 (250, 175): dx=50, dy=25 -> x'=200+(50*0.7071 - 25*0.7071)=200+17.6775=217.68, y'=150+(50*0.7071 + 25*0.7071)=150+53.0325=203.03
        // V4 (150, 175): dx=-50, dy=25 -> x'=200+(-50*0.7071 - 25*0.7071)=200-53.0325=146.97, y'=150+(-50*0.7071 + 25*0.7071)=150-17.6775=132.32

        final expectedChildVertices = [
          Offset(182.322, 96.967),
          Offset(253.033, 167.678),
          Offset(217.678, 203.033),
          Offset(146.967, 132.322)
        ];
        expect(rotatedChild.vertices,
            offsetListCloseTo(expectedChildVertices, tolerance: 0.01));

        // Expected child curve control offset (original: (10, 10)) rotated 45 deg
        // dx' = 10*cos(45) - 10*sin(45) = 10*0.7071 - 10*0.7071 = 0
        // dy' = 10*sin(45) + 10*cos(45) = 10*0.7071 + 10*0.7071 = 14.142
        final expectedChildCurveControls = {0: Offset(0, 14.1421)};
        expect(rotatedChild.curveControls.length, 1);
        expect(rotatedChild.curveControls[0],
            offsetCloseTo(expectedChildCurveControls[0]!, tolerance: 0.01));
      });

      test('should not rotate if delta angle is negligible', () {
        final lastPos = initialRectCenter + Offset(100, 0);
        final newPos = initialRectCenter + Offset(100.000001, 0); // Tiny change

        final notRotatedData = ShapeManipulationHandlers.handleRotation(
          shapeData: initialRectData,
          lastPosition: lastPos,
          newPosition: newPos,
          constraints: testConstraints,
        );

        // Should return the original data object instance
        expect(identical(notRotatedData, initialRectData), isTrue);
      });

      test('should constrain rotation if it goes out of bounds', () {
        // Create a shape very close to the top boundary
        final edgeRect = Rect.fromLTWH(100, 10, 200, 50); // Center (200, 35)
        final edgeShapeData = ShapeData.rectangle(edgeRect);
        final edgeCenter = edgeShapeData.center;

        // Attempt to rotate 90 degrees, which would push vertices above y=0
        final lastPos = edgeCenter + Offset(100, 0);
        final newPos = edgeCenter + Offset(0, 100);

        final constrainedRotationData =
            ShapeManipulationHandlers.handleRotation(
          shapeData: edgeShapeData,
          lastPosition: lastPos,
          newPosition: newPos,
          constraints: testConstraints,
        );

        // Expect the original data to be returned because rotation is constrained
        expect(identical(constrainedRotationData, edgeShapeData), isTrue);
      });
    });

    // TODO: Add tests for handleVertexDrag
    // TODO: Add tests for handleEdgeDrag
    // TODO: Add tests for handleShapeDrag
  });
}
