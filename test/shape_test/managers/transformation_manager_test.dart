import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/shape_manager.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/transformation_manager.dart';
import 'package:xoxknit/app/modules/shape_test/models/shape_data.dart';
import 'package:xoxknit/app/modules/shape_test/models/group_shape_data.dart';

// Helper to compare offsets with tolerance
Matcher offsetCloseTo(Offset expected, {double tolerance = 1e-6}) {
  return predicate<Offset>((offset) {
    return (offset.dx - expected.dx).abs() < tolerance &&
        (offset.dy - expected.dy).abs() < tolerance;
  }, 'is close to $expected');
}

// Helper to compare lists of offsets
Matcher offsetListCloseTo(List<Offset> expected, {double tolerance = 1e-6}) {
  return predicate<List<Offset>>((list) {
    if (list.length != expected.length) return false;
    for (int i = 0; i < list.length; i++) {
      if ((list[i].dx - expected[i].dx).abs() >= tolerance ||
          (list[i].dy - expected[i].dy).abs() >= tolerance) {
        return false;
      }
    }
    return true;
  }, 'list is close to $expected');
}

void main() {
  group('TransformationManager', () {
    late ShapeManager shapeManager;
    late TransformationManager transformationManager;

    setUp(() {
      // Using a real ShapeManager as it's simple enough for these tests
      shapeManager = ShapeManager();
      transformationManager = TransformationManager(shapeManager);
    });

    // --- Test Data ---
    final testRect = Rect.fromLTWH(100, 150, 200, 100); // Center (200, 200)
    final initialRectData = ShapeData.rectangle(testRect);
    final initialRectDataWithCurve = initialRectData.copyWith(
      curveControls: {0: Offset(10, 20)}, // Curve on top edge
    );

    // Group Test Data
    final childRect =
        Rect.fromLTWH(150, 175, 100, 50); // Center (200, 200) - same as parent
    final childShapeData = ShapeData.rectangle(childRect)
        .copyWith(curveControls: {1: Offset(5, -5)} // Curve on right edge
            );
    final initialGroupData = GroupShapeData(
      vertices: initialRectData.vertices, // Group bounds match parent rect
      center: initialRectData.center,
      boundingRect: initialRectData.boundingRect,
      childShapes: [childShapeData],
      curveControls: {}, // Group itself has no curves
      rotation: 0.0,
      visualRotation: 0.0, // Added visualRotation
      originalKeys: [], // Added dummy originalKeys
    );

    group('flipShapeHorizontally', () {
      test('should flip a simple rectangle horizontally', () {
        final flippedData =
            transformationManager.flipShapeHorizontally(initialRectData);

        final expectedVertices = [
          Offset(300, 150), // Original top-left (100, 150) -> flipped top-right
          Offset(100, 150), // Original top-right (300, 150) -> flipped top-left
          Offset(100,
              250), // Original bottom-right (300, 250) -> flipped bottom-left
          Offset(300,
              250), // Original bottom-left (100, 250) -> flipped bottom-right
        ];

        expect(flippedData.vertices, offsetListCloseTo(expectedVertices));
        expect(flippedData.center,
            offsetCloseTo(initialRectData.center)); // Center shouldn't change
        expect(flippedData.curveControls, isEmpty); // No curves initially
      });

      test('should flip a rectangle with curve controls horizontally', () {
        final flippedData = transformationManager
            .flipShapeHorizontally(initialRectDataWithCurve);

        final expectedVertices = [
          Offset(300, 150),
          Offset(100, 150),
          Offset(100, 250),
          Offset(300, 250)
        ];
        final expectedCurveControls = {
          0: Offset(-10, 20) // dx is negated
        };

        expect(flippedData.vertices, offsetListCloseTo(expectedVertices));
        expect(flippedData.center, offsetCloseTo(initialRectData.center));
        expect(flippedData.curveControls.length, 1);
        expect(flippedData.curveControls[0],
            offsetCloseTo(expectedCurveControls[0]!));
      });

      test('should flip a group shape horizontally', () {
        final flippedGroup = transformationManager
            .flipShapeHorizontally(initialGroupData) as GroupShapeData;

        // 1. Check Group's own properties (bounds, center)
        final expectedGroupVertices = [
          Offset(300, 150),
          Offset(100, 150),
          Offset(100, 250),
          Offset(300, 250)
        ];
        expect(flippedGroup.vertices, offsetListCloseTo(expectedGroupVertices));
        expect(flippedGroup.center,
            offsetCloseTo(initialGroupData.center)); // Group center stays same
        expect(flippedGroup.curveControls, isEmpty); // Group has no curves

        // 2. Check Child Shape properties
        expect(flippedGroup.childShapes.length, 1);
        final flippedChild = flippedGroup.childShapes.first;

        // Child vertices flipped relative to group center (200, 200)
        // Original child vertices: (150, 175), (250, 175), (250, 225), (150, 225)
        final expectedChildVertices = [
          Offset(250, 175), // (150, 175) -> (200 + (200-150), 175) = (250, 175)
          Offset(150, 175), // (250, 175) -> (200 - (250-200), 175) = (150, 175)
          Offset(150, 225), // (250, 225) -> (200 - (250-200), 225) = (150, 225)
          Offset(250, 225), // (150, 225) -> (200 + (200-150), 225) = (250, 225)
        ];
        expect(flippedChild.vertices, offsetListCloseTo(expectedChildVertices));

        // Child center flipped relative to group center
        // Original child center (200, 200) -> Flipped (200, 200) - stays same as it's on the flip axis
        expect(flippedChild.center, offsetCloseTo(childShapeData.center));

        // Child curve controls flipped
        final expectedChildCurveControls = {
          1: Offset(-5, -5) // dx negated
        };
        expect(flippedChild.curveControls.length, 1);
        expect(flippedChild.curveControls[1],
            offsetCloseTo(expectedChildCurveControls[1]!));
      });
    });

    group('flipShapeVertically', () {
      test('should flip a simple rectangle vertically', () {
        final flippedData =
            transformationManager.flipShapeVertically(initialRectData);

        final expectedVertices = [
          Offset(
              100, 250), // Original top-left (100, 150) -> flipped bottom-left
          Offset(300,
              250), // Original top-right (300, 150) -> flipped bottom-right
          Offset(300,
              150), // Original bottom-right (300, 250) -> flipped top-right
          Offset(
              100, 150), // Original bottom-left (100, 250) -> flipped top-left
        ];

        expect(flippedData.vertices, offsetListCloseTo(expectedVertices));
        expect(flippedData.center, offsetCloseTo(initialRectData.center));
        expect(flippedData.curveControls, isEmpty);
      });

      test('should flip a rectangle with curve controls vertically', () {
        final flippedData =
            transformationManager.flipShapeVertically(initialRectDataWithCurve);

        final expectedVertices = [
          Offset(100, 250),
          Offset(300, 250),
          Offset(300, 150),
          Offset(100, 150)
        ];
        final expectedCurveControls = {
          0: Offset(10, -20) // dy is negated
        };

        expect(flippedData.vertices, offsetListCloseTo(expectedVertices));
        expect(flippedData.center, offsetCloseTo(initialRectData.center));
        expect(flippedData.curveControls.length, 1);
        expect(flippedData.curveControls[0],
            offsetCloseTo(expectedCurveControls[0]!));
      });

      test('should flip a group shape vertically', () {
        final flippedGroup = transformationManager
            .flipShapeVertically(initialGroupData) as GroupShapeData;

        // 1. Check Group's own properties (bounds, center)
        final expectedGroupVertices = [
          Offset(100, 250),
          Offset(300, 250),
          Offset(300, 150),
          Offset(100, 150)
        ];
        expect(flippedGroup.vertices, offsetListCloseTo(expectedGroupVertices));
        expect(flippedGroup.center,
            offsetCloseTo(initialGroupData.center)); // Group center stays same
        expect(flippedGroup.curveControls, isEmpty); // Group has no curves

        // 2. Check Child Shape properties
        expect(flippedGroup.childShapes.length, 1);
        final flippedChild = flippedGroup.childShapes.first;

        // Child vertices flipped relative to group center (200, 200)
        // Original child vertices: (150, 175), (250, 175), (250, 225), (150, 225)
        final expectedChildVertices = [
          Offset(150, 225), // (150, 175) -> (150, 200 + (200-175)) = (150, 225)
          Offset(250, 225), // (250, 175) -> (250, 200 + (200-175)) = (250, 225)
          Offset(250, 175), // (250, 225) -> (250, 200 - (225-200)) = (250, 175)
          Offset(150, 175), // (150, 225) -> (150, 200 - (225-200)) = (150, 175)
        ];
        expect(flippedChild.vertices, offsetListCloseTo(expectedChildVertices));

        // Child center flipped relative to group center
        // Original child center (200, 200) -> Flipped (200, 200) - stays same as it's on the flip axis
        expect(flippedChild.center, offsetCloseTo(childShapeData.center));

        // Child curve controls flipped
        final expectedChildCurveControls = {
          1: Offset(5, 5) // dy negated
        };
        expect(flippedChild.curveControls.length, 1);
        expect(flippedChild.curveControls[1],
            offsetCloseTo(expectedChildCurveControls[1]!));
      });
    });
  });
}
