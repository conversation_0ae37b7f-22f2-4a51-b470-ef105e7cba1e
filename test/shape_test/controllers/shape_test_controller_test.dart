import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/shape_editor_controller.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/index.dart';
import 'package:xoxknit/app/modules/shape_test/models/shape_data.dart';
import 'package:xoxknit/app/modules/shape_test/painters/grid_system.dart';

// Generate mocks ONLY for the managers we need to mock interactions with
@GenerateMocks([
  UndoRedoManager,
  // No need to mock others like ShapeManager, SelectionManager, etc.
  // as we will interact with the controller using its real managers.
  // Keep GridSystem and GridCoordinateConverter here ONLY if we decide to mock them too.
  // For now, we initialize them with basic real instances.
  GridSystem,
  GridCoordinateConverter,
])
import 'shape_test_controller_test.mocks.dart'; // Generated file

// Helper to compare offsets with tolerance
Matcher offsetCloseTo(Offset expected, {double tolerance = 1e-6}) {
  return predicate<Offset>((offset) {
    return (offset.dx - expected.dx).abs() < tolerance &&
        (offset.dy - expected.dy).abs() < tolerance;
  }, 'is close to $expected');
}

// Helper to compare lists of offsets
Matcher offsetListCloseTo(List<Offset> expected, {double tolerance = 1e-6}) {
  return predicate<List<Offset>>((list) {
    if (list.length != expected.length) return false;
    for (int i = 0; i < list.length; i++) {
      if ((list[i].dx - expected[i].dx).abs() >= tolerance ||
          (list[i].dy - expected[i].dy).abs() >= tolerance) {
        return false;
      }
    }
    return true;
  }, 'list is close to $expected');
}

void main() {
  // Ensure GetX bindings are initialized for testing
  TestWidgetsFlutterBinding.ensureInitialized();

  // It might be necessary to mock NewItemWizardController if ShapeEditorController interacts with it significantly during init or duplication.
  // For now, we assume it's not strictly needed for the duplication logic itself.
  // If tests fail due to missing WizardController, add mocking for it.

  group('ShapeEditorController', () {
    late ShapeEditorController controller;
    late MockUndoRedoManager mockUndoRedoManager;
    // Optional: Mocks for GridSystem/Converter if needed, otherwise use simple instances
    // late MockGridSystem mockGridSystem;
    // late MockGridCoordinateConverter mockCoordConverter;

    // Test data
    late Key initialShapeKey1;
    late ShapeData initialShapeData1;
    late Key initialShapeKey2;
    late ShapeData initialShapeData2;

    setUp(() async {
      // --- Create and Inject Mock Dependencies ---
      mockUndoRedoManager = MockUndoRedoManager();

      // Stub necessary methods on the mock
      when(mockUndoRedoManager.addHistoryEntry(any, any, any, any, any))
          .thenReturn(null);
      when(mockUndoRedoManager.canUndo).thenReturn(false.obs); // Default stub
      when(mockUndoRedoManager.canRedo).thenReturn(false.obs); // Default stub
      when(mockUndoRedoManager.lastOperation)
          .thenReturn(Rx<String?>(null)); // Default

      // --- Inject Mock using Get.put ---
      // This MUST happen before the controller is instantiated if it uses Get.find<UndoRedoManager>()
      Get.put<UndoRedoManager>(mockUndoRedoManager);

      // --- Create Controller Instance ---
      // Controller will internally create real instances of other managers,
      // but it will find the mocked UndoRedoManager via Get.find().
      controller = ShapeEditorController();

      // Allow controller's onInit to complete
      await Future.delayed(Duration.zero);

      // --- Initialize Controller State (Grid/Converter) ---
      // We need valid GridSystem and GridCoordinateConverter instances, even if basic.
      // Using real instances might be simpler if their logic doesn't interfere.
      // If complex interactions are needed, mock these as well.
      controller.gridSystem = GridSystem(
        cellWidth: 10,
        primaryColor: Colors.blue,
        gridColor: Colors.grey,
        opacity: 1.0,
        zoomLevel: 1.0,
        panOffset: Offset.zero,
        needleCount: 100,
        snapToGrid: false,
        machinePitch: 4.5,
        aspectRatio: 1.0,
      );
      controller.gridCoordinateConverter = GridCoordinateConverter(
        screenSize: Size(800, 600),
        needleCount: 100,
        aspectRatio: 1.0,
      );

      // --- Setup Initial Shapes for Tests using Controller methods ---
      initialShapeData1 = ShapeData.rectangle(Rect.fromLTWH(100, 100, 100, 50));
      controller.addShape(initialShapeData1.type);
      initialShapeKey1 = controller.shapeKeysOrder.last;
      controller.shapeStates[initialShapeKey1] = initialShapeData1;

      initialShapeData2 = ShapeData.triangle(Rect.fromLTWH(200, 200, 60, 60));
      controller.addShape(initialShapeData2.type);
      initialShapeKey2 = controller.shapeKeysOrder.last;
      controller.shapeStates[initialShapeKey2] = initialShapeData2;

      // Allow GetX state updates
      await Future.delayed(Duration.zero);
    });

    tearDown(() {
      // Clean up GetX bindings
      Get.reset();
    });

    group('duplicateSelectedShapes', () {
      test('should duplicate a single selected shape with offset', () async {
        // Arrange: Select the first shape
        controller.handleShapeTap(initialShapeKey1);
        await Future.delayed(Duration.zero);

        // Pre-check state
        expect(controller.shapeManager.shapes.length, 2);
        final index1 =
            controller.shapeManager.shapeKeysOrder.indexOf(initialShapeKey1);
        expect(controller.selectedIndices, [index1]);

        // Act: Call the duplication method
        controller.duplicateSelectedShapes();
        await Future.delayed(Duration.zero);

        // Assert: Verify interactions with mocks
        verify(mockUndoRedoManager.addHistoryEntry(any, any, any, any, any))
            .called(1); // Verify undo state was added

        // Assert: Check the final state using controller's managers
        expect(controller.shapeManager.shapes.length,
            3); // Should have three shapes now (2 initial + 1 duplicate)

        // Find the new shape (the one whose key is not initialShapeKey1 or initialShapeKey2)
        final originalKeys = {initialShapeKey1, initialShapeKey2};
        final newShapeWidget = controller.shapeManager.shapes
            .firstWhere((shape) => !originalKeys.contains(shape.key));
        final newShapeKey = newShapeWidget.key!;
        final duplicatedShape = controller.shapeStates[newShapeKey]!;

        // Check duplicated shape data
        expect(duplicatedShape.type, initialShapeData1.type);
        expect(duplicatedShape.curveControls, initialShapeData1.curveControls);
        expect(duplicatedShape.rotation, initialShapeData1.rotation);

        // Check position (should be offset from original)
        final expectedOffset = Offset(20, 20); // Default duplication offset
        final expectedCenter = initialShapeData1.center + expectedOffset;
        expect(
            duplicatedShape.center,
            offsetCloseTo(expectedCenter,
                tolerance: 1.0)); // Allow some tolerance

        final expectedVertices =
            initialShapeData1.vertices.map((v) => v + expectedOffset).toList();
        expect(
            duplicatedShape.vertices,
            offsetListCloseTo(expectedVertices,
                tolerance: 1.0)); // Allow some tolerance

        // Check selection state (only the new shape index should be selected)
        final newIndex =
            controller.shapeManager.shapeKeysOrder.indexOf(newShapeKey);
        expect(controller.selectedIndices, [newIndex]);
      });

      test('should not duplicate if no shape is selected', () async {
        // Arrange: Deselect all shapes (controller starts with 2 shapes from setUp)
        controller.deselectAllShapes();
        await Future.delayed(Duration.zero);

        expect(controller.shapeManager.shapes.length, 2); // Still two shapes
        expect(controller.selectedIndices, isEmpty);

        // Act
        controller.duplicateSelectedShapes();
        await Future.delayed(Duration.zero);

        // Assert: No new shape added, no undo state
        expect(controller.shapeManager.shapes.length, 2);
        verifyNever(
            mockUndoRedoManager.addHistoryEntry(any, any, any, any, any));
        expect(controller.selectedIndices, isEmpty);
      });

      test('should duplicate multiple selected shapes', () async {
        // Arrange: Select both shapes added in setUp
        controller.handleShapeTap(initialShapeKey1); // Select first
        // Find index and use toggleShapeSelection to add
        final index2 =
            controller.shapeManager.shapeKeysOrder.indexOf(initialShapeKey2);
        controller.selectionManager.toggleShapeSelection(index2, true);
        await Future.delayed(Duration.zero);

        expect(controller.shapeManager.shapes.length, 2);
        final index1 =
            controller.shapeManager.shapeKeysOrder.indexOf(initialShapeKey1);
        expect(controller.selectedIndices, containsAll([index1, index2]));
        expect(controller.selectedIndices.length, 2);

        // Act
        controller.duplicateSelectedShapes();
        await Future.delayed(Duration.zero);

        // Assert Interactions
        verify(mockUndoRedoManager.addHistoryEntry(any, any, any, any, any))
            .called(1); // Still one undo state

        // Assert State
        expect(controller.shapeManager.shapes.length,
            4); // Should have 4 shapes now (2 initial + 2 duplicates)

        // Find the two new shapes
        final originalKeys = {initialShapeKey1, initialShapeKey2};
        final newShapeWidgets = controller.shapeManager.shapes
            .where((shape) => !originalKeys.contains(shape.key));
        expect(newShapeWidgets.length, 2);

        final newKeys = newShapeWidgets.map((e) => e.key!).toList();
        final duplicatedShape1 = controller.shapeStates[newKeys[0]]!;
        final duplicatedShape2 = controller.shapeStates[newKeys[1]]!;

        final expectedOffset = Offset(20, 20);

        // Check properties (loosely, just check center offset and type)
        // Find which duplicated shape corresponds to which original
        ShapeData dupForShape1, dupForShape2;
        if (duplicatedShape1.type == initialShapeData1.type) {
          dupForShape1 = duplicatedShape1;
          dupForShape2 = duplicatedShape2;
        } else {
          dupForShape1 = duplicatedShape2;
          dupForShape2 = duplicatedShape1;
        }
        expect(
            dupForShape1.center,
            offsetCloseTo(initialShapeData1.center + expectedOffset,
                tolerance: 1.0));
        expect(
            dupForShape2.center,
            offsetCloseTo(initialShapeData2.center + expectedOffset,
                tolerance: 1.0));
        expect(dupForShape1.type, initialShapeData1.type);
        expect(dupForShape2.type, initialShapeData2.type);

        // Assert Selection (only the two new shape indices selected)
        final newIndex1 =
            controller.shapeManager.shapeKeysOrder.indexOf(newKeys[0]);
        final newIndex2 =
            controller.shapeManager.shapeKeysOrder.indexOf(newKeys[1]);
        expect(controller.selectedIndices, containsAll([newIndex1, newIndex2]));
        expect(controller.selectedIndices.length, 2);
      });
    });
  });
}
