// Mocks generated by Mocki<PERSON> 5.4.4 from annotations
// in xoxknit/test/shape_test/controllers/shape_test_controller_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:ui' as _i4;

import 'package:flutter/foundation.dart' as _i7;
import 'package:flutter/material.dart' as _i5;
import 'package:get/get.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:xoxknit/app/modules/shape_test/controllers/managers/undo_redo_manager.dart'
    as _i6;
import 'package:xoxknit/app/modules/shape_test/handlers/shape_manipulation_handlers.dart'
    as _i10;
import 'package:xoxknit/app/modules/shape_test/models/shape_data.dart' as _i8;
import 'package:xoxknit/app/modules/shape_test/painters/grid_system.dart'
    as _i9;
import 'package:xoxknit/app/modules/shape_test/utils/history_manager.dart'
    as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeRxBool_0 extends _i1.SmartFake implements _i2.RxBool {
  _FakeRxBool_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeRx_1<T> extends _i1.SmartFake implements _i2.Rx<T> {
  _FakeRx_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeHistoryManager_2 extends _i1.SmartFake
    implements _i3.HistoryManager {
  _FakeHistoryManager_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeColor_3 extends _i1.SmartFake implements _i4.Color {
  _FakeColor_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeOffset_4 extends _i1.SmartFake implements _i4.Offset {
  _FakeOffset_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeCustomPainter_5 extends _i1.SmartFake implements _i5.CustomPainter {
  _FakeCustomPainter_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSize_6 extends _i1.SmartFake implements _i4.Size {
  _FakeSize_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [UndoRedoManager].
///
/// See the documentation for Mockito's code generation for more information.
class MockUndoRedoManager extends _i1.Mock implements _i6.UndoRedoManager {
  MockUndoRedoManager() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.RxBool get canUndo => (super.noSuchMethod(
        Invocation.getter(#canUndo),
        returnValue: _FakeRxBool_0(
          this,
          Invocation.getter(#canUndo),
        ),
      ) as _i2.RxBool);

  @override
  _i2.RxBool get canRedo => (super.noSuchMethod(
        Invocation.getter(#canRedo),
        returnValue: _FakeRxBool_0(
          this,
          Invocation.getter(#canRedo),
        ),
      ) as _i2.RxBool);

  @override
  _i2.Rx<String?> get lastOperation => (super.noSuchMethod(
        Invocation.getter(#lastOperation),
        returnValue: _FakeRx_1<String?>(
          this,
          Invocation.getter(#lastOperation),
        ),
      ) as _i2.Rx<String?>);

  @override
  bool get hasUndo => (super.noSuchMethod(
        Invocation.getter(#hasUndo),
        returnValue: false,
      ) as bool);

  @override
  bool get hasRedo => (super.noSuchMethod(
        Invocation.getter(#hasRedo),
        returnValue: false,
      ) as bool);

  @override
  _i3.HistoryManager get historyManager => (super.noSuchMethod(
        Invocation.getter(#historyManager),
        returnValue: _FakeHistoryManager_2(
          this,
          Invocation.getter(#historyManager),
        ),
      ) as _i3.HistoryManager);

  @override
  void startHistoryTracking(
    Map<_i7.Key, dynamic>? shapeStates,
    List<_i7.Key>? shapeKeysOrder,
    List<int>? selectedIndices,
    Map<_i7.Key, bool>? curveModeStates, [
    String? operationName,
  ]) =>
      super.noSuchMethod(
        Invocation.method(
          #startHistoryTracking,
          [
            shapeStates,
            shapeKeysOrder,
            selectedIndices,
            curveModeStates,
            operationName,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void finishHistoryTracking(
    Map<_i7.Key, _i8.ShapeData>? shapeStates,
    List<_i7.Key>? shapeKeysOrder,
    List<int>? selectedIndices,
    Map<_i7.Key, bool>? curveModeStates,
  ) =>
      super.noSuchMethod(
        Invocation.method(
          #finishHistoryTracking,
          [
            shapeStates,
            shapeKeysOrder,
            selectedIndices,
            curveModeStates,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addHistoryEntry(
    Map<_i7.Key, _i8.ShapeData>? shapeStates,
    List<_i7.Key>? shapeKeysOrder,
    List<int>? selectedIndices,
    Map<_i7.Key, bool>? curveModeStates, [
    String? operationName,
  ]) =>
      super.noSuchMethod(
        Invocation.method(
          #addHistoryEntry,
          [
            shapeStates,
            shapeKeysOrder,
            selectedIndices,
            curveModeStates,
            operationName,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i3.HistoryEntry? undo(
    Map<_i7.Key, _i8.ShapeData>? shapeStates,
    List<_i7.Key>? shapeKeysOrder,
    List<int>? selectedIndices,
    Map<_i7.Key, bool>? curveModeStates,
  ) =>
      (super.noSuchMethod(Invocation.method(
        #undo,
        [
          shapeStates,
          shapeKeysOrder,
          selectedIndices,
          curveModeStates,
        ],
      )) as _i3.HistoryEntry?);

  @override
  void cancelTransaction() => super.noSuchMethod(
        Invocation.method(
          #cancelTransaction,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void clearHistory() => super.noSuchMethod(
        Invocation.method(
          #clearHistory,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [GridSystem].
///
/// See the documentation for Mockito's code generation for more information.
class MockGridSystem extends _i1.Mock implements _i9.GridSystem {
  MockGridSystem() {
    _i1.throwOnMissingStub(this);
  }

  @override
  double get cellWidth => (super.noSuchMethod(
        Invocation.getter(#cellWidth),
        returnValue: 0.0,
      ) as double);

  @override
  set cellWidth(double? _cellWidth) => super.noSuchMethod(
        Invocation.setter(
          #cellWidth,
          _cellWidth,
        ),
        returnValueForMissingStub: null,
      );

  @override
  double get aspectRatio => (super.noSuchMethod(
        Invocation.getter(#aspectRatio),
        returnValue: 0.0,
      ) as double);

  @override
  _i4.Color get primaryColor => (super.noSuchMethod(
        Invocation.getter(#primaryColor),
        returnValue: _FakeColor_3(
          this,
          Invocation.getter(#primaryColor),
        ),
      ) as _i4.Color);

  @override
  _i4.Color get gridColor => (super.noSuchMethod(
        Invocation.getter(#gridColor),
        returnValue: _FakeColor_3(
          this,
          Invocation.getter(#gridColor),
        ),
      ) as _i4.Color);

  @override
  bool get showCenterLines => (super.noSuchMethod(
        Invocation.getter(#showCenterLines),
        returnValue: false,
      ) as bool);

  @override
  bool get snapToGrid => (super.noSuchMethod(
        Invocation.getter(#snapToGrid),
        returnValue: false,
      ) as bool);

  @override
  bool get snapToCenter => (super.noSuchMethod(
        Invocation.getter(#snapToCenter),
        returnValue: false,
      ) as bool);

  @override
  set snapToCenter(bool? _snapToCenter) => super.noSuchMethod(
        Invocation.setter(
          #snapToCenter,
          _snapToCenter,
        ),
        returnValueForMissingStub: null,
      );

  @override
  bool get showNeedleLabels => (super.noSuchMethod(
        Invocation.getter(#showNeedleLabels),
        returnValue: false,
      ) as bool);

  @override
  double get snapThreshold => (super.noSuchMethod(
        Invocation.getter(#snapThreshold),
        returnValue: 0.0,
      ) as double);

  @override
  double get centerSnapThreshold => (super.noSuchMethod(
        Invocation.getter(#centerSnapThreshold),
        returnValue: 0.0,
      ) as double);

  @override
  double get opacity => (super.noSuchMethod(
        Invocation.getter(#opacity),
        returnValue: 0.0,
      ) as double);

  @override
  int get needleCount => (super.noSuchMethod(
        Invocation.getter(#needleCount),
        returnValue: 0,
      ) as int);

  @override
  double get machinePitch => (super.noSuchMethod(
        Invocation.getter(#machinePitch),
        returnValue: 0.0,
      ) as double);

  @override
  double get zoomLevel => (super.noSuchMethod(
        Invocation.getter(#zoomLevel),
        returnValue: 0.0,
      ) as double);

  @override
  set zoomLevel(double? value) => super.noSuchMethod(
        Invocation.setter(
          #zoomLevel,
          value,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i4.Offset get panOffset => (super.noSuchMethod(
        Invocation.getter(#panOffset),
        returnValue: _FakeOffset_4(
          this,
          Invocation.getter(#panOffset),
        ),
      ) as _i4.Offset);

  @override
  set panOffset(_i4.Offset? value) => super.noSuchMethod(
        Invocation.setter(
          #panOffset,
          value,
        ),
        returnValueForMissingStub: null,
      );

  @override
  double get effectiveGridSize => (super.noSuchMethod(
        Invocation.getter(#effectiveGridSize),
        returnValue: 0.0,
      ) as double);

  @override
  _i5.CustomPainter createPainter({
    double? cellWidth,
    _i10.SnapInfo? activeSnapInfo,
    Map<_i7.Key, _i8.ShapeData>? shapeStates,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createPainter,
          [],
          {
            #cellWidth: cellWidth,
            #activeSnapInfo: activeSnapInfo,
            #shapeStates: shapeStates,
          },
        ),
        returnValue: _FakeCustomPainter_5(
          this,
          Invocation.method(
            #createPainter,
            [],
            {
              #cellWidth: cellWidth,
              #activeSnapInfo: activeSnapInfo,
              #shapeStates: shapeStates,
            },
          ),
        ),
      ) as _i5.CustomPainter);

  @override
  _i4.Offset screenToGridPoint(
    _i4.Offset? screenPoint,
    _i4.Size? canvasSize,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #screenToGridPoint,
          [
            screenPoint,
            canvasSize,
          ],
        ),
        returnValue: _FakeOffset_4(
          this,
          Invocation.method(
            #screenToGridPoint,
            [
              screenPoint,
              canvasSize,
            ],
          ),
        ),
      ) as _i4.Offset);

  @override
  _i4.Offset gridToScreenPoint(
    _i4.Offset? gridPoint,
    _i4.Size? canvasSize,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #gridToScreenPoint,
          [
            gridPoint,
            canvasSize,
          ],
        ),
        returnValue: _FakeOffset_4(
          this,
          Invocation.method(
            #gridToScreenPoint,
            [
              gridPoint,
              canvasSize,
            ],
          ),
        ),
      ) as _i4.Offset);

  @override
  void updateViewport(
    double? newZoom,
    _i4.Offset? newPanOffset, {
    bool? updateNeedleMapping = false,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #updateViewport,
          [
            newZoom,
            newPanOffset,
          ],
          {#updateNeedleMapping: updateNeedleMapping},
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [GridCoordinateConverter].
///
/// See the documentation for Mockito's code generation for more information.
class MockGridCoordinateConverter extends _i1.Mock
    implements _i8.GridCoordinateConverter {
  MockGridCoordinateConverter() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Size get screenSize => (super.noSuchMethod(
        Invocation.getter(#screenSize),
        returnValue: _FakeSize_6(
          this,
          Invocation.getter(#screenSize),
        ),
      ) as _i4.Size);

  @override
  int get needleCount => (super.noSuchMethod(
        Invocation.getter(#needleCount),
        returnValue: 0,
      ) as int);

  @override
  double get aspectRatio => (super.noSuchMethod(
        Invocation.getter(#aspectRatio),
        returnValue: 0.0,
      ) as double);

  @override
  double get gridWidth => (super.noSuchMethod(
        Invocation.getter(#gridWidth),
        returnValue: 0.0,
      ) as double);

  @override
  double get gridHeight => (super.noSuchMethod(
        Invocation.getter(#gridHeight),
        returnValue: 0.0,
      ) as double);

  @override
  double get pixelWidthPerStitch => (super.noSuchMethod(
        Invocation.getter(#pixelWidthPerStitch),
        returnValue: 0.0,
      ) as double);

  @override
  double get pixelHeightPerRow => (super.noSuchMethod(
        Invocation.getter(#pixelHeightPerRow),
        returnValue: 0.0,
      ) as double);

  @override
  _i4.Offset pixelToGrid(
    _i4.Offset? pixelPos, {
    bool? isVector = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #pixelToGrid,
          [pixelPos],
          {#isVector: isVector},
        ),
        returnValue: _FakeOffset_4(
          this,
          Invocation.method(
            #pixelToGrid,
            [pixelPos],
            {#isVector: isVector},
          ),
        ),
      ) as _i4.Offset);

  @override
  _i4.Offset gridToPixel(
    _i4.Offset? gridPos, {
    bool? isVector = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #gridToPixel,
          [gridPos],
          {#isVector: isVector},
        ),
        returnValue: _FakeOffset_4(
          this,
          Invocation.method(
            #gridToPixel,
            [gridPos],
            {#isVector: isVector},
          ),
        ),
      ) as _i4.Offset);
}
