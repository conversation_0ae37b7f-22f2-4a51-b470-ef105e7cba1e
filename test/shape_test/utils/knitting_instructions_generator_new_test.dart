import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:xoxknit/app/modules/shape_test/models/shape_data.dart';
import 'package:xoxknit/app/modules/shape_test/utils/knitting_instructions_generator_new.dart';
import 'package:xoxknit/app/modules/shape_test/models/group_shape_data.dart';
import 'dart:math' as math;
import 'dart:ui'; // Needed for ui.Path

void main() {
  group('convertShapesToStitchPattern Tests', () {
    // Helper function to create a simple rectangular ShapeData
    ShapeData createRectangleShape(
        {required Offset center,
        required double width,
        required double height,
        double rotation = 0.0,
        Map<int, Offset>? curveControls}) {
      final halfWidth = width / 2;
      final halfHeight = height / 2;
      final rect =
          Rect.fromCenter(center: center, width: width, height: height);
      final vertices = [
        rect.topLeft, // TL: 0
        rect.topRight, // TR: 1
        rect.bottomRight, // BR: 2
        rect.bottomLeft, // BL: 3
      ];
      return ShapeData(
        type: ShapeType.rectangle,
        center: center,
        vertices: vertices,
        boundingRect: rect,
        rotation: rotation,
        curveControls: curveControls ?? {},
      );
    }

    test('should return single empty row for empty shapes list', () {
      final result = convertShapesToStitchPattern(
        shapes: [],
        columns: 10,
        cellWidth: 1.0,
        aspectRatio: 1.0,
      );
      expect(result.length, 1);
      expect(result[0].length, 10);
      expect(result[0].every((cell) => !cell), isTrue);
    });

    test('should generate correct grid for a simple centered rectangle', () {
      final shape = createRectangleShape(
          center: const Offset(5.0, 5.0), width: 4.0, height: 3.0);
      final cellWidth = 1.0;
      final aspectRatio = 1.0; // Makes rowSpacing == cellWidth
      final columns = 10;

      // Expected bounds: left=3, top=3.5, right=7, bottom=6.5 (width=4, height=3)
      // Column offset = floor(3.0 / 1.0) = 3
      // Rows = ceil(3.0 / 1.0) = 3
      // Expected columns affected: 3, 4, 5, 6 (indices)
      // Expected rows affected: 0, 1, 2 (indices relative to bounding box top 3.5)

      final result = convertShapesToStitchPattern(
        shapes: [shape],
        columns: columns,
        cellWidth: cellWidth,
        aspectRatio: aspectRatio,
      );

      // Print for debugging
      print('--- Simple Rectangle Test ---');
      print('Shape Center: ${shape.center}, Width: 4.0, Height: 3.0');
      print(
          'Cell Width: $cellWidth, Aspect Ratio: $aspectRatio, Columns: $columns');
      print('Generated Grid (${result.length}x${result[0].length}):');
      result.forEach((row) {
        print(row.map((c) => c ? 'X' : '.').join());
      });

      // Verify dimensions (rows might vary slightly due to aspect ratio and bounding box)
      // expect(result.length, 3, reason: "Expected 3 rows based on height 3 and aspect ratio 1"); // Row calculation can be tricky
      expect(result.isNotEmpty, isTrue, reason: "Grid should not be empty");
      expect(result[0].length, columns, reason: "Expected $columns columns");

      // Verify pattern - more robust checks needed depending on sampling precision
      // Check a few key points (adjusting for potential small shifts due to sampling/bounding box)
      expect(result[0][3], isTrue, reason: "Row 0, Col 3 should be inside");
      expect(result[0][6], isTrue, reason: "Row 0, Col 6 should be inside");
      // Check bottom row (index may vary)
      expect(result.last[3], isTrue,
          reason: "Last Row, Col 3 should be inside");
      expect(result.last[6], isTrue,
          reason: "Last Row, Col 6 should be inside");

      // Check points outside
      expect(result[0][2], isFalse, reason: "Row 0, Col 2 should be outside");
      expect(result[0][7], isFalse, reason: "Row 0, Col 7 should be outside");

      // Rough check for the overall filled area
      int filledCount = 0;
      for (var row in result) {
        for (var cell in row) {
          if (cell) filledCount++;
        }
      }
      // Expected filled cells roughly 4 * 3 = 12
      // Allow some tolerance due to sampling
      expect(filledCount, greaterThanOrEqualTo(10),
          reason: "Expected at least 10 filled cells");
      expect(filledCount, lessThanOrEqualTo(16),
          reason: "Expected at most 16 filled cells");

      // Check that optimization didn't remove essential rows
      expect(result.any((row) => row.contains(true)), isTrue,
          reason: "Grid should contain stitches");
    });

    test('should handle rotation correctly', () {
      final shape = createRectangleShape(
          center: const Offset(5.0, 5.0),
          width: 4.0,
          height: 2.0,
          rotation: math.pi / 4); // 45 degrees
      final cellWidth = 1.0;
      final aspectRatio = 1.0;
      final columns = 12;

      final result = convertShapesToStitchPattern(
        shapes: [shape],
        columns: columns,
        cellWidth: cellWidth,
        aspectRatio: aspectRatio,
      );

      // Print for debugging
      print('--- Rotated Rectangle Test ---');
      print('Generated Grid (${result.length}x${result[0].length}):');
      result.forEach((row) {
        print(row.map((c) => c ? 'X' : '.').join());
      });

      // Verification is harder due to rotation. We expect a diamond-like shape.
      // Check that *some* cells are filled and the general area makes sense.
      expect(result.any((row) => row.contains(true)), isTrue);
      // Example: Check the approximate center - rotation shouldn't drastically change the center column index if grid is large enough
      // find center row roughly:
      int centerRow = result.length ~/ 2;
      // Bounds Calculation for Rotated Rectangle:
      // The path bounds will be different from the original rect bounds.
      // For 4x2 rotated 45deg, the max extent along axes is roughly sqrt(4^2+2^2)/2 = sqrt(20)/2 = sqrt(5) approx 2.23 from center.
      // So bounds roughly 5+/-2.23 => 2.77 to 7.23. Width approx 4.47.
      // Column offset = floor(2.77/1.0) = 2 or 3 depending on exact bounds calc.
      // Height approx 4.47. Rows = ceil(4.47/1.0) = 5.
      // Expect stitches around columns 3, 4, 5, 6, 7.
      // The center (5.0, 5.0) should fall in column 5 or 6.
      expect(result[centerRow][5] || result[centerRow][6], isTrue,
          reason: "Center row, col 5 or 6 should likely be filled");

      // Check some corners are empty
      expect(result[0][0], isFalse, reason: "Corner 0,0 should be empty");
      expect(result[0][columns - 1], isFalse,
          reason: "Corner 0,end should be empty");

      // Count filled cells - should be roughly width*height = 8, but rotation affects bounding box and sampling.
      int filledCount = 0;
      result.forEach((row) => filledCount += row.where((c) => c).length);
      print("Filled Count (Rotated): $filledCount");
      expect(filledCount, greaterThanOrEqualTo(6),
          reason: "Should have a reasonable number of filled cells");
      expect(filledCount, lessThanOrEqualTo(14),
          reason:
              "Should not exceed expected area too much (bounds are larger)"); // Increased upper tolerance
    });

// Add curves test later

    test('should handle group shapes', () {
      // Create two simple rectangles
      final shape1 = createRectangleShape(
          center: const Offset(3.0, 3.0), width: 2.0, height: 2.0);
      final shape2 = createRectangleShape(
          center: const Offset(7.0, 7.0), width: 2.0, height: 2.0);

      // Create a group from these shapes
      // Note: GroupShapeData.fromShapes requires keys, we'll use dummy keys for the test
      final groupShape = GroupShapeData.fromShapes(
          [shape1, shape2], [UniqueKey(), UniqueKey()]);

      final cellWidth = 1.0;
      final aspectRatio = 1.0;
      final columns = 10;

      // Expected bounds will encompass both shapes: approx left=2, top=2, right=8, bottom=8
      // Col offset approx 2. Rows approx 6.

      final result = convertShapesToStitchPattern(
        shapes: [groupShape], // Pass the group as a single shape
        columns: columns,
        cellWidth: cellWidth,
        aspectRatio: aspectRatio,
      );

      // Print for debugging
      print('--- Group Shape Test ---');
      print('Group contains two 2x2 rectangles at (3,3) and (7,7)');
      print(
          'Cell Width: $cellWidth, Aspect Ratio: $aspectRatio, Columns: $columns');
      print('Generated Grid (${result.length}x${result[0].length}):');
      result.forEach((row) {
        print(row.map((c) => c ? 'X' : '.').join());
      });

      // Verify stitches exist
      expect(result.any((row) => row.contains(true)), isTrue);
      expect(result[0].length, columns);

      // Rough checks for the locations of the two shapes
      // Shape 1 (center 3,3) -> affects cols 2,3; rows near top
      // Shape 2 (center 7,7) -> affects cols 6,7; rows near bottom

      // Find approximate row indices for the shapes
      int shape1RowCount = 0;
      int shape2RowCount = 0;
      int firstShape1Row = -1, lastShape1Row = -1;
      int firstShape2Row = -1, lastShape2Row = -1;

      for (int r = 0; r < result.length; ++r) {
        bool shape1Present = result[r][2] || result[r][3];
        bool shape2Present = result[r][6] || result[r][7];
        if (shape1Present) {
          shape1RowCount++;
          if (firstShape1Row == -1) firstShape1Row = r;
          lastShape1Row = r;
        }
        if (shape2Present) {
          shape2RowCount++;
          if (firstShape2Row == -1) firstShape2Row = r;
          lastShape2Row = r;
        }
      }

      print(
          'Shape 1 Rows: $firstShape1Row - $lastShape1Row ($shape1RowCount rows)');
      print(
          'Shape 2 Rows: $firstShape2Row - $lastShape2Row ($shape2RowCount rows)');

      // Check if both shapes seem to be represented (allow tolerance)
      expect(shape1RowCount, greaterThanOrEqualTo(1),
          reason: "Should find stitches for shape 1");
      expect(shape2RowCount, greaterThanOrEqualTo(1),
          reason: "Should find stitches for shape 2");
      // Check if shapes are in roughly the correct vertical areas
      expect(lastShape1Row, lessThan(firstShape2Row),
          reason: "Shape 1 should be above Shape 2");

      // Check specific points inside each shape's expected area
      // Indices need careful calculation based on bounds and offset
      // Shape 1 bounds: L=2, T=2, R=4, B=4. Col offset=2. Row offset=0. Rows=2.
      // Expected cols: 2, 3. Expected rows: 0, 1.
      expect(result[0][2], isTrue, reason: "Shape 1 TL corner area");
      expect(result[1][3], isTrue, reason: "Shape 1 BR corner area");

      // Shape 2 bounds: L=6, T=6, R=8, B=8. Col offset=2. Rows=6.
      // Expected cols: 6, 7. Expected rows: 4, 5 (relative to group bounds top=2)
      expect(result[result.length - 2][6], isTrue,
          reason: "Shape 2 TL corner area (relative)");
      expect(result.last[7], isTrue,
          reason: "Shape 2 BR corner area (relative)");
    });

    test('should apply minimum width rule', () {
      // Create a tall, thin rectangle that might generate single-stitch rows
      final shape = createRectangleShape(
          center: const Offset(5.5, 5.0),
          width: 0.8,
          height: 6.0); // Width < 1 cellWidth
      final cellWidth = 1.0;
      final aspectRatio = 1.0;
      final columns = 11;

      final result = convertShapesToStitchPattern(
        shapes: [shape],
        columns: columns,
        cellWidth: cellWidth,
        aspectRatio: aspectRatio,
      );

      // Print for debugging
      print('--- Minimum Width Test ---');
      print('Shape Center: ${shape.center}, Width: 0.8, Height: 6.0');
      print(
          'Cell Width: $cellWidth, Aspect Ratio: $aspectRatio, Columns: $columns');
      print('Generated Grid (${result.length}x${result[0].length}):');
      result.forEach((row) {
        print(row.map((c) => c ? 'X' : '.').join());
      });

      // Verify that no row containing stitches has only one stitch
      for (final row in result) {
        if (row.contains(true)) {
          final stitchCount = row.where((cell) => cell).length;
          expect(stitchCount, greaterThanOrEqualTo(2),
              reason:
                  "Row should have at least 2 stitches due to minimum width rule: ${row.map((c) => c ? 'X' : '.').join()}");
        }
      }
    });

    // Test for _fillIsolatedEmptyCells (using direct call as setup is complex)
    test('_fillIsolatedEmptyCells should fill holes', () {
      final grid = [
        [false, false, false, false, false],
        [false, true, true, true, false],
        [false, true, false, true, false], // Hole at [2][2]
        [false, true, true, true, false],
        [false, false, false, false, false],
      ];

      // Call the function directly (need access or copy it here)
      _fillIsolatedEmptyCellsHelper(grid); // Use helper copy

      print('--- Hole Filling Test (Direct Call) ---');
      print('Grid After Filling:');
      grid.forEach((row) {
        print(row.map((c) => c ? 'X' : '.').join());
      });

      expect(grid[2][2], isTrue,
          reason: "The isolated empty cell should be filled");
      expect(grid[1][1], isTrue);
      expect(grid[3][3], isTrue);
      expect(grid[0][0], isFalse);
    });

    // --- Test helper functions directly (using local copies) ---

    test('_optimizeGrid removes empty top and bottom rows', () {
      final grid = [
        List.generate(5, (_) => false), // Empty top row
        List.generate(5, (_) => false), // Empty top row
        [false, true, true, false, false], // First stitch row
        [false, false, true, false, false],
        [false, true, true, true, false], // Last stitch row
        List.generate(5, (_) => false), // Empty bottom row
      ];
      final optimized = _optimizeGridHelper(grid, 5); // Use helper copy

      print('--- Optimize Grid Test (Direct Call) ---');
      print('Original Grid:');
      grid.forEach((row) => print(row.map((c) => c ? 'X' : '.').join()));
      print('Optimized Grid (${optimized.length}x${optimized[0].length}):');
      optimized.forEach((row) => print(row.map((c) => c ? 'X' : '.').join()));

      expect(optimized.length, 3,
          reason: "Should have 3 rows after optimization");
      expect(optimized[0], equals([false, true, true, false, false]));
      expect(optimized[1], equals([false, false, true, false, false]));
      expect(optimized[2], equals([false, true, true, true, false]));
      expect(optimized[0].length, 5);
    });

    test('_optimizeGrid handles grid with no stitches', () {
      final grid = [
        List.generate(5, (_) => false),
        List.generate(5, (_) => false),
      ];
      final optimized = _optimizeGridHelper(grid, 5); // Use helper copy
      expect(optimized.length, 1, reason: "Should return one empty row");
      expect(optimized[0].length, 5);
      expect(optimized[0].every((cell) => !cell), isTrue);
    });

    test('_optimizeGrid handles grid with all stitches', () {
      final grid = [
        List.generate(5, (_) => true),
        List.generate(5, (_) => true),
      ];
      final originalLength = grid.length;
      final optimized = _optimizeGridHelper(grid, 5); // Use helper copy
      expect(optimized.length, originalLength,
          reason: "Should not remove rows if all have stitches");
      expect(optimized[0].length, 5);
      expect(optimized[0].every((cell) => cell), isTrue);
      expect(optimized[1].every((cell) => cell), isTrue);
    });

    test('_ensureMinimumStitchWidth works correctly (Direct Call)', () {
      final grid = [
        [false, false, false, false, false, false],
        [false, true, false, true, false, false], // Two single stitches
        [false, false, true, false, false, false], // One single stitch middle
        [true, false, false, false, false, false], // One single stitch start
        [false, false, false, false, false, true], // One single stitch end
        [false, true, true, false, false, false], // Already two stitches
        [false, false, false, false, false, false],
      ];

      _ensureMinimumStitchWidthHelper(grid); // Use helper copy

      print('--- Ensure Minimum Width Direct Test (Direct Call) ---');
      print('Grid After Ensuring Width:');
      grid.forEach((row) {
        print(row.map((c) => c ? 'X' : '.').join());
      });

      // Row 1: .T.T.. -> .TT.T.. or .T.TT.. (prefer right -> .TT.TT.. is expected)
      expect(grid[1], equals([false, true, true, true, true, false]),
          reason: "Row 1 single stitches expanded");
      // Row 2: ..T... -> ..TT..
      expect(grid[2], equals([false, false, true, true, false, false]),
          reason: "Row 2 single stitch expanded");
      // Row 3: T..... -> TT....
      expect(grid[3], equals([true, true, false, false, false, false]),
          reason: "Row 3 single stitch expanded");
      // Row 4: .....T -> ....TT
      expect(grid[4], equals([false, false, false, false, true, true]),
          reason: "Row 4 single stitch expanded");
      // Row 5: .TT... -> .TT... (unchanged)
      expect(grid[5], equals([false, true, true, false, false, false]),
          reason: "Row 5 already valid, unchanged");
    });
  });
}

// Mock or Helper for GroupShapeData might be needed if testing groups directly.
// For now, tests focus on single shapes and helper functions.

// --- Helper function copies for direct testing (since they are private) ---

// Helper function needed by _fillIsolatedEmptyCells test setup
void _fillIsolatedEmptyCellsHelper(List<List<bool>> grid) {
  if (grid.isEmpty || grid[0].isEmpty) return;

  final rows = grid.length;
  final cols = grid[0].length;

  // Create a copy to avoid modifying the grid while iterating over the original state
  final gridCopy =
      List.generate(rows, (r) => List.generate(cols, (c) => grid[r][c]));

  for (int row = 1; row < rows - 1; row++) {
    for (int col = 1; col < cols - 1; col++) {
      // Only process cells that were originally empty
      if (gridCopy[row][col]) continue;

      int filledNeighbors = 0;
      for (var dy = -1; dy <= 1; dy++) {
        for (var dx = -1; dx <= 1; dx++) {
          if (dx == 0 && dy == 0) continue; // Skip center cell itself
          // Check the state in the *original* grid copy
          if (gridCopy[row + dy][col + dx]) {
            filledNeighbors++;
          }
        }
      }
      // If 5 or more neighbors were filled in the original grid, fill this cell in the actual grid
      if (filledNeighbors >= 5) {
        grid[row][col] = true;
      }
    }
  }
}

// Helper function needed by _ensureMinimumStitchWidth test setup
void _ensureMinimumStitchWidthHelper(List<List<bool>> grid) {
  if (grid.isEmpty || grid[0].isEmpty) return;

  final rows = grid.length;
  final cols = grid[0].length;

  // Iterate row by row
  for (int row = 0; row < rows; row++) {
    // Create a copy of the row's state *before* modifications in this row
    // This prevents a newly added stitch from immediately triggering another check in the same pass
    final originalRow = List<bool>.from(grid[row]);

    for (int col = 0; col < cols; col++) {
      // Check the state in the original row copy
      if (originalRow[col]) {
        bool leftNeighbor = (col > 0) && originalRow[col - 1];
        bool rightNeighbor = (col < cols - 1) && originalRow[col + 1];

        // If it was an isolated stitch in the original row state
        if (!leftNeighbor && !rightNeighbor) {
          // Prefer adding a stitch to the right if possible (modify the actual grid)
          if (col < cols - 1) {
            // Check if the right neighbor isn't already being set by another isolated stitch to its left
            // This implementation might still double-fill if two isolated stitches are side-by-side originally
            // The logic focuses on ensuring *at least* two stitches.
            grid[row][col + 1] = true;
          } else if (col > 0) {
            // Otherwise, add to the left (modify the actual grid)
            grid[row][col - 1] = true;
          }
          // If it's a single column grid, we can't add a neighbor.
        }
      }
    }
  }
}

// Helper function needed by _optimizeGrid test setup
List<List<bool>> _optimizeGridHelper(List<List<bool>> grid, int columns) {
  if (grid.isEmpty) return [List.generate(columns, (_) => false)];

  int firstStitchRow = -1;
  int lastStitchRow = -1;

  // Find first and last rows with stitches
  for (int i = 0; i < grid.length; i++) {
    if (grid[i].contains(true)) {
      if (firstStitchRow == -1) firstStitchRow = i;
      lastStitchRow = i;
    }
  }

  // No stitches found, return single empty row
  if (firstStitchRow == -1) {
    return [List.generate(columns, (_) => false)];
  }

  // Trim grid to only include rows with stitches
  final optimizedGrid = grid.sublist(firstStitchRow, lastStitchRow + 1);
  return optimizedGrid;
}
