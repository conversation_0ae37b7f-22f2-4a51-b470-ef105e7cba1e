import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:xoxknit/app/modules/shape_test/models/shape_data.dart';
import 'package:xoxknit/app/modules/shape_test/utils/geometry_utils.dart';

void main() {
  group('GeometryUtils Bounding Box Tests', () {
    test('should calculate accurate bounding box for shape with cubic curves', () {
      // Create a simple rectangle
      final vertices = [
        const Offset(100, 100), // Top-left
        const Offset(200, 100), // Top-right
        const Offset(200, 200), // Bottom-right
        const Offset(100, 200), // Bottom-left
      ];

      // Add a cubic curve to the top edge (index 0) that extends outward
      final cubicCurveControls = <int, List<Offset>>{
        0: [
          const Offset(0, -50), // Control point 1: extends upward
          const Offset(0, -50), // Control point 2: extends upward
        ],
      };

      final shapeData = ShapeData(
        type: ShapeType.rectangle,
        vertices: vertices,
        boundingRect: const Rect.fromLTWH(100, 100, 100, 100), // Initial rect
        center: const Offset(150, 150),
        cubicCurveControls: cubicCurveControls,
      );

      // Calculate the accurate bounding box
      final result = GeometryUtils.calculateAccurateBoundingRect(shapeData);
      expect(result, isA<Rect>());
      
      final boundingRect = result as Rect;
      
      // The bounding box should extend beyond the original vertices
      // because the curve extends upward by 50 pixels
      expect(boundingRect.top, lessThan(100), 
          reason: 'Bounding box should extend above original vertices due to curve');
      expect(boundingRect.left, closeTo(100, 1));
      expect(boundingRect.right, closeTo(200, 1));
      expect(boundingRect.bottom, closeTo(200, 1));
      
      // The curve extends the shape upward, so the height should be greater than 100
      expect(boundingRect.height, greaterThan(100),
          reason: 'Height should be greater than original due to curve extension');
    });

    test('should calculate accurate bounding box for shape with quadratic curves', () {
      // Create a simple rectangle
      final vertices = [
        const Offset(100, 100), // Top-left
        const Offset(200, 100), // Top-right
        const Offset(200, 200), // Bottom-right
        const Offset(100, 200), // Bottom-left
      ];

      // Add a quadratic curve to the right edge (index 1) that extends outward
      final curveControls = <int, Offset>{
        1: const Offset(30, 0), // Extends to the right
      };

      final shapeData = ShapeData(
        type: ShapeType.rectangle,
        vertices: vertices,
        boundingRect: const Rect.fromLTWH(100, 100, 100, 100), // Initial rect
        center: const Offset(150, 150),
        curveControls: curveControls,
      );

      // Calculate the accurate bounding box
      final result = GeometryUtils.calculateAccurateBoundingRect(shapeData);
      expect(result, isA<Rect>());
      
      final boundingRect = result as Rect;
      
      // The bounding box should extend beyond the original vertices
      // because the curve extends to the right
      expect(boundingRect.right, greaterThan(200), 
          reason: 'Bounding box should extend beyond original vertices due to curve');
      expect(boundingRect.left, closeTo(100, 1));
      expect(boundingRect.top, closeTo(100, 1));
      expect(boundingRect.bottom, closeTo(200, 1));
      
      // The curve extends the shape to the right, so the width should be greater than 100
      expect(boundingRect.width, greaterThan(100),
          reason: 'Width should be greater than original due to curve extension');
    });

    test('should use simple bounds calculation for shapes without curves', () {
      // Create a simple rectangle without any curves
      final vertices = [
        const Offset(100, 100), // Top-left
        const Offset(200, 100), // Top-right
        const Offset(200, 200), // Bottom-right
        const Offset(100, 200), // Bottom-left
      ];

      final shapeData = ShapeData(
        type: ShapeType.rectangle,
        vertices: vertices,
        boundingRect: const Rect.fromLTWH(100, 100, 100, 100), // Initial rect
        center: const Offset(150, 150),
      );

      // Calculate the accurate bounding box
      final result = GeometryUtils.calculateAccurateBoundingRect(shapeData);
      expect(result, isA<Rect>());
      
      final boundingRect = result as Rect;
      
      // For shapes without curves, the bounding box should match the vertex bounds
      expect(boundingRect.left, closeTo(100, 0.1));
      expect(boundingRect.top, closeTo(100, 0.1));
      expect(boundingRect.right, closeTo(200, 0.1));
      expect(boundingRect.bottom, closeTo(200, 0.1));
      expect(boundingRect.width, closeTo(100, 0.1));
      expect(boundingRect.height, closeTo(100, 0.1));
    });

    test('should handle shapes with both cubic and quadratic curves', () {
      // Create a simple rectangle
      final vertices = [
        const Offset(100, 100), // Top-left
        const Offset(200, 100), // Top-right
        const Offset(200, 200), // Bottom-right
        const Offset(100, 200), // Bottom-left
      ];

      // Add both types of curves
      final curveControls = <int, Offset>{
        0: const Offset(0, -20), // Quadratic curve on top edge
      };
      
      final cubicCurveControls = <int, List<Offset>>{
        1: [
          const Offset(25, 0), // Cubic curve on right edge
          const Offset(25, 0),
        ],
      };

      final shapeData = ShapeData(
        type: ShapeType.rectangle,
        vertices: vertices,
        boundingRect: const Rect.fromLTWH(100, 100, 100, 100), // Initial rect
        center: const Offset(150, 150),
        curveControls: curveControls,
        cubicCurveControls: cubicCurveControls,
      );

      // Calculate the accurate bounding box
      final result = GeometryUtils.calculateAccurateBoundingRect(shapeData);
      expect(result, isA<Rect>());
      
      final boundingRect = result as Rect;
      
      // The bounding box should extend in both directions due to both curves
      expect(boundingRect.top, lessThan(100), 
          reason: 'Should extend upward due to quadratic curve');
      expect(boundingRect.right, greaterThan(200), 
          reason: 'Should extend rightward due to cubic curve');
      expect(boundingRect.width, greaterThan(100));
      expect(boundingRect.height, greaterThan(100));
    });
  });
}
