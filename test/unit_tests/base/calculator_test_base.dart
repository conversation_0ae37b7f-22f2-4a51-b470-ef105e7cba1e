import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/mockito.dart';
import 'package:xoxknit/app/data/models/user_knitting_machine_model.dart';
import 'package:xoxknit/app/modules/base/controllers/base_calculator_controller.dart';
import 'package:xoxknit/app/services/auth_service.dart';
import 'package:xoxknit/app/services/knitting/knitting_settings_service.dart';

class MockKnittingSettingsService extends GetxService
    with Mock
    implements KnittingSettingsService {
  @override
  RxList<UserKnittingMachineModel> userMachines =
      <UserKnittingMachineModel>[].obs;
  @override
  final Rxn<UserKnittingMachineModel> lastUsedMachine =
      Rxn<UserKnittingMachineModel>();
  @override
  final RxnInt lastStitches = RxnInt();
  @override
  final RxnInt lastRows = RxnInt();
  @override
  final RxnDouble lastPieceWidth = RxnDouble();
  @override
  final RxnDouble lastPieceLength = RxnDouble();
  @override
  final RxString lastPieceWidthUnit = 'cm'.obs;
  @override
  final RxString lastPieceLengthUnit = 'cm'.obs;

  @override
  Future<bool> loadLastValues() async => true;

  @override
  Future<void> saveLastValues(
      {UserKnittingMachineModel? machine,
      double? swatchWidth,
      double? swatchLength,
      double? swatchWeight,
      int? stitches,
      int? rows,
      double? pieceWidth,
      double? pieceLength,
      String? swatchWidthUnit,
      String? swatchLengthUnit,
      String? pieceWidthUnit,
      String? pieceLengthUnit,
      double? stitchesPerCm,
      double? rowsPerCm}) async {}
}

class MockAuthService extends GetxService with Mock implements AuthService {}

abstract class CalculatorTestBase {
  late MockKnittingSettingsService mockKnittingService;
  late MockAuthService mockAuthService;

  final testMachine = UserKnittingMachineModel(
    id: '1',
    customName: 'Test Machine',
    userId: 'test-user',
    baseModelId: 'base-1',
    machineClass: 'Standard',
    mainBrand: 'Brother',
    model: 'KH860',
    needlePitch: 4.5,
    needlesCount: 200,
    type: 'Punch Card',
    patternControlType: 'Punch Card',
    patternRepeatLength: 24,
    altBrands: ['Knitking'],
  );

  void setupServices() {
    mockKnittingService = MockKnittingSettingsService();
    mockAuthService = MockAuthService();

    Get.put<KnittingSettingsService>(mockKnittingService);
    Get.put<AuthService>(mockAuthService);
  }

  Future<void> waitForCalculation() async {
    await Future.delayed(const Duration(milliseconds: 100));
  }

  void verifyCalculationResults(BaseCalculatorController controller,
      int expectedStitches, int expectedRows) {
    expect(controller.calculatedStitches.value, expectedStitches);
    expect(controller.calculatedRows.value, expectedRows);
  }
}
