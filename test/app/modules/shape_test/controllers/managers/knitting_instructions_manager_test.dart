import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/knitting_instructions_manager.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/shape_editor_controller.dart';

// Generate mocks
@GenerateMocks([ShapeEditorController])
import 'knitting_instructions_manager_test.mocks.dart';

void main() {
  group('KnittingInstructionsManager - getKnittingZones', () {
    late KnittingInstructionsManager manager;
    late MockShapeEditorController mockController;

    setUp(() {
      mockController = MockShapeEditorController();
      manager = KnittingInstructionsManager(mockController);

      // Initialize Get for Rx variables
      Get.testMode = true;
    });

    test('returns empty list when no instructions', () {
      // Arrange
      manager.currentInstructions.value = [];

      // Act
      final zones = manager.getKnittingZones();

      // Assert
      expect(zones, isEmpty);
    });

    test('creates a single zone for simple continuous pattern', () {
      // Arrange
      manager.currentInstructions.value = [
        [true, true], // Row 0 (top)
        [true, true], // Row 1 (bottom)
      ];

      // Act
      final zones = manager.getKnittingZones();

      // Assert
      expect(zones.length, 1);
      expect(zones[0].name, 'Zone 1');
      expect(zones[0].instructions.length, 2);
      expect(zones[0].instructions[0], [true, true]);
      expect(zones[0].instructions[1], [true, true]);
    });

    test('creates multiple zones for disconnected patterns', () {
      // Arrange - create a pattern with two disconnected areas
      manager.currentInstructions.value = [
        [true, true, false, false, false], // Row 0 (top)
        [true, true, false, false, false], // Row 1
        [false, false, false, true, true], // Row 2 (bottom)
      ];

      // Act
      final zones = manager.getKnittingZones();

      // Assert
      expect(zones.length, 2);

      // Sort zones by instructions length to make testing deterministic
      zones.sort(
          (a, b) => b.instructions.length.compareTo(a.instructions.length));

      // Zone 1 should have rows 0 and 1
      expect(zones[0].instructions.length, 2);
      expect(zones[0].instructions[0], [true, true]);
      expect(zones[0].instructions[1], [true, true]);

      // Zone 2 should have only row 2
      expect(zones[1].instructions.length, 1);
      expect(zones[1].instructions[0], [true, true]);
    });

    test('respects startFromRight parameter with L-shaped pattern', () {
      // Arrange - Create an L-shaped pattern
      manager.currentInstructions.value = [
        [false, false, true], // Row 0 (top)
        [true, true, true], // Row 1 (bottom)
      ];

      // Act - Test with startFromRight = true
      final zonesFromRight = manager.getKnittingZones(startFromRight: true);

      // Act - Test with startFromRight = false
      final zonesFromLeft = manager.getKnittingZones(startFromRight: false);

      // From right should create a zone with complete L shape
      expect(zonesFromRight.length, 1);
      expect(zonesFromRight[0].instructions.length, 2);
      expect(zonesFromRight[0].instructions[0], [true]);
      expect(zonesFromRight[0].instructions[1], [true, true, true]);

      // From left might create different zone structure
      // (actual results may vary based on implementation)
      expect(zonesFromLeft.length, greaterThan(0));
    });

    test('handles complex pattern correctly', () {
      // Arrange - Complex staggered pattern
      manager.currentInstructions.value = [
        [false, true, true, false, false], // Row 0 (top)
        [true, true, false, false, false], // Row 1
        [false, false, false, true, true], // Row 2
        [false, false, true, true, false] // Row 3 (bottom)
      ];

      // Act
      final zones = manager.getKnittingZones();

      // Assert
      expect(zones.length, greaterThan(1)); // Should create at least 2 zones

      // Verify total stitches across all zones matches original pattern
      int totalStitches = zones.fold(
          0,
          (sum, zone) =>
              sum +
              zone.instructions.fold(
                  0,
                  (rowSum, row) =>
                      rowSum + row.where((stitch) => stitch).length));

      int originalStitches = manager.currentInstructions.value
          .fold(0, (sum, row) => sum + row.where((stitch) => stitch).length);

      expect(totalStitches, equals(originalStitches));
    });

    test('handles empty rows correctly', () {
      // Arrange - Pattern with empty rows
      manager.currentInstructions.value = [
        [false, false, false], // Row 0 (empty)
        [true, true, false], // Row 1
        [false, false, false], // Row 2 (empty)
        [false, true, true], // Row 3
      ];

      // Act
      final zones = manager.getKnittingZones();

      // Assert
      expect(zones.length, 2);

      // Check that each zone contains only non-empty rows
      for (final zone in zones) {
        for (final row in zone.instructions) {
          expect(row.contains(true), isTrue);
        }
      }
    });

    test('handles single stitch pattern', () {
      // Arrange - Single stitch
      manager.currentInstructions.value = [
        [false, false, false],
        [false, true, false],
        [false, false, false],
      ];

      // Act
      final zones = manager.getKnittingZones();

      // Assert
      expect(zones.length, 1);
      expect(zones[0].instructions.length, 1);
      expect(zones[0].instructions[0], [true]);
    });

    test('properly processes large pattern', () {
      // Arrange - Create larger pattern (10x10)
      final pattern = List.generate(
          10, (row) => List.generate(10, (col) => (row + col) % 3 == 0));

      manager.currentInstructions.value = pattern;

      // Act
      final zones = manager.getKnittingZones();

      // Assert
      expect(zones.isNotEmpty, isTrue);

      // Count total stitches in zones
      int totalZoneStitches = 0;
      for (final zone in zones) {
        for (final row in zone.instructions) {
          totalZoneStitches += row.where((stitch) => stitch).length;
        }
      }

      // Count stitches in original pattern
      int totalPatternStitches = 0;
      for (final row in pattern) {
        totalPatternStitches += row.where((stitch) => stitch).length;
      }

      // All stitches should be accounted for
      expect(totalZoneStitches, equals(totalPatternStitches));
    });
  });
}
