import 'package:flutter_test/flutter_test.dart';
import 'package:xoxknit/app/data/models/new_item_model.dart';

void main() {
  group('Yarn Warning Logic Tests', () {
    test('should show warning when yarn on hand is less than required', () {
      // Arrange
      final item = NewItemModel(
        weightOnHand: 100.0, // 100g available
        stitchesPerCm: 4.0,
        rowsPerCm: 5.0,
        weightPer100CmSquared: 50.0,
      );

      final stats = {
        'totalStitches': 5000, // This should require more than 100g
      };

      // Calculate yarn needed (similar to the actual implementation)
      double yarnNeeded = 0.0;
      final totalStitches = stats['totalStitches'] as int;
      final stitchesPerCm = item.stitchesPerCm!;
      final rowsPerCm = item.rowsPerCm!;
      final weightPer100CmSquared = item.weightPer100CmSquared!;

      final stitchesPerCm2 = stitchesPerCm * rowsPerCm;
      if (totalStitches > 0 && stitchesPerCm2 > 0) {
        final patternArea = totalStitches / stitchesPerCm2;
        final weight = (patternArea / 100) * weightPer100CmSquared;
        yarnNeeded = weight * 1.1; // Add 10% waste
      }

      // Check warning conditions
      final hasYarnQuantity =
          item.weightOnHand != null && item.weightOnHand! > 0;
      final showYarnWarning =
          hasYarnQuantity && yarnNeeded > 0 && item.weightOnHand! < yarnNeeded;
      final yarnShortage =
          showYarnWarning ? yarnNeeded - item.weightOnHand! : 0.0;

      // Assert
      expect(hasYarnQuantity, isTrue);
      expect(yarnNeeded, greaterThan(0));
      expect(yarnNeeded, greaterThan(item.weightOnHand!));
      expect(showYarnWarning, isTrue);
      expect(yarnShortage, greaterThan(0));
    });

    test('should not show warning when yarn on hand is sufficient', () {
      // Arrange
      final item = NewItemModel(
        weightOnHand: 500.0, // 500g available (plenty)
        stitchesPerCm: 4.0,
        rowsPerCm: 5.0,
        weightPer100CmSquared: 50.0,
      );

      final stats = {
        'totalStitches': 1000, // Small pattern
      };

      // Calculate yarn needed
      double yarnNeeded = 0.0;
      final totalStitches = stats['totalStitches'] as int;
      final stitchesPerCm = item.stitchesPerCm!;
      final rowsPerCm = item.rowsPerCm!;
      final weightPer100CmSquared = item.weightPer100CmSquared!;

      final stitchesPerCm2 = stitchesPerCm * rowsPerCm;
      if (totalStitches > 0 && stitchesPerCm2 > 0) {
        final patternArea = totalStitches / stitchesPerCm2;
        final weight = (patternArea / 100) * weightPer100CmSquared;
        yarnNeeded = weight * 1.1; // Add 10% waste
      }

      // Check warning conditions
      final hasYarnQuantity =
          item.weightOnHand != null && item.weightOnHand! > 0;
      final showYarnWarning =
          hasYarnQuantity && yarnNeeded > 0 && item.weightOnHand! < yarnNeeded;

      // Assert
      expect(hasYarnQuantity, isTrue);
      expect(yarnNeeded, greaterThan(0));
      expect(yarnNeeded, lessThan(item.weightOnHand!));
      expect(showYarnWarning, isFalse);
    });

    test('should not show warning when no yarn quantity is provided', () {
      // Arrange
      final item = NewItemModel(
        weightOnHand: null, // No yarn quantity provided
        stitchesPerCm: 4.0,
        rowsPerCm: 5.0,
        weightPer100CmSquared: 50.0,
      );

      final stats = {
        'totalStitches': 2000,
      };

      // Check warning conditions
      final hasYarnQuantity =
          item.weightOnHand != null && item.weightOnHand! > 0;
      final showYarnWarning =
          hasYarnQuantity && true; // Other conditions don't matter

      // Assert
      expect(hasYarnQuantity, isFalse);
      expect(showYarnWarning, isFalse);
    });

    test('should not show warning when yarn calculation fails', () {
      // Arrange
      final item = NewItemModel(
        weightOnHand: 100.0,
        stitchesPerCm: null, // Missing gauge info
        rowsPerCm: 5.0,
        weightPer100CmSquared: 50.0,
      );

      final stats = {
        'totalStitches': 2000,
      };

      // Calculate yarn needed (should fail)
      double yarnNeeded = 0.0;
      final hasValidGauge = item.stitchesPerCm != null &&
          item.stitchesPerCm! > 0 &&
          item.rowsPerCm != null &&
          item.rowsPerCm! > 0 &&
          item.weightPer100CmSquared != null &&
          item.weightPer100CmSquared! > 0;

      // Check warning conditions
      final hasYarnQuantity =
          item.weightOnHand != null && item.weightOnHand! > 0;
      final showYarnWarning =
          hasYarnQuantity && yarnNeeded > 0 && item.weightOnHand! < yarnNeeded;

      // Assert
      expect(hasValidGauge, isFalse);
      expect(yarnNeeded, equals(0.0));
      expect(showYarnWarning, isFalse);
    });
  });
}
