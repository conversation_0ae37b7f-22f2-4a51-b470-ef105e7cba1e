#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

echo "=== Flutter App Size Minimization Script (Android Focus) ==="

# Function to check build status and retry with fallback if needed
build_with_fallback() {
  echo "Attempting build with full optimization..."
  if flutter build apk --release --split-per-abi --obfuscate --split-debug-info=build/app/outputs/symbols --tree-shake-icons; then
    echo "✅ Build successful with full optimization!"
  else
    echo "⚠️ Full optimization build failed, trying with reduced optimization..."
    
    # First, try without R8 but keeping other optimizations
    echo "Attempting build with code shrinking disabled but obfuscation enabled..."
    sed -i '' 's/minifyEnabled true/minifyEnabled false/g' android/app/build.gradle
    sed -i '' 's/shrinkResources true/shrinkResources false/g' android/app/build.gradle
    
    if flutter build apk --release --split-per-abi --obfuscate --split-debug-info=build/app/outputs/symbols --tree-shake-icons; then
      echo "✅ Build successful with code shrinking disabled!"
    else
      # Last resort: no optimization
      echo "⚠️ Optimization issues persisting, trying with no optimizations..."
      flutter build apk --release --split-per-abi
    fi
    
    # Reset build.gradle to original state
    sed -i '' 's/minifyEnabled false/minifyEnabled true/g' android/app/build.gradle
    sed -i '' 's/shrinkResources false/shrinkResources true/g' android/app/build.gradle
  fi
}

echo "Cleaning up previous build artifacts..."
flutter clean

echo "Fetching dependencies..."
flutter pub get

echo "Optimizing image assets (converting PNGs to WebP format)..."
# Check if cwebp is installed
if command -v cwebp &> /dev/null; then
  find assets -name "*.png" -exec cwebp -q 80 {} -o {}.webp \; 2>/dev/null || echo "No PNG files found or some couldn't be converted"
  echo "✓ Images optimized"
else
  echo "⚠️ cwebp not found. Install it to optimize images (e.g., 'brew install webp' on macOS)"
fi

# Generate keystore if it doesn't exist
if [ ! -f "android/upload-keystore.jks" ]; then
  echo "Keystore doesn't exist, generating one..."
  cd android
  keytool -genkey -v -keystore upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload -storepass michra25 -keypass michra25 -dname "CN=XoxKnit, OU=XoxKnit, O=XoxKnit, L=XoxKnit, S=XoxKnit, C=US"
  cd ..
  echo "✓ Keystore generated"
fi

echo "Building minimal size APK with optimized settings..."
build_with_fallback

# echo "Creating optimized Android App Bundle (recommended for Play Store)..."
# flutter build appbundle --release --obfuscate --split-debug-info=build/app/outputs/symbols --tree-shake-icons

echo "=== Build Process Completed ==="
echo ""
echo "✅ Android APKs can be found in:"
echo "   build/app/outputs/flutter-apk/"
echo ""
echo "📊 Size analysis reports saved to:"
echo "   app_size_analysis_before.txt"
echo "   app_size_analysis_after.txt"
echo ""
echo "💡 Tips for even smaller app size:"
echo "   1. Remove unused resources and code"
echo "   2. Compress images further (consider using WebP format)"
echo "   3. Use vector graphics where possible" 
echo "   4. Remove unused locales from your app"
echo "   5. Review your pubspec.yaml and remove unnecessary dependencies"
