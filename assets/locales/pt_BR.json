{"app": {"title": "xoxknit"}, "auth": {"errors": {"customerDisabled": "Esta conta foi desativada", "emailInUse": "Já existe uma conta com este email", "invalidCredentials": "<PERSON><PERSON> ou senha in<PERSON><PERSON>", "operationNotAllowed": "Este tipo de conta está atualmente desativado. Entre em contato com o suporte.", "resetFailed": "Falha ao enviar email de redefinição de senha. Tente novamente.", "signOutFailed": "Falha ao sair de todos os serviços", "unconfirmedEmail": "Verifique seu endereço de email", "userNotFound": "Nenhuma conta encontrada com este email", "weakPassword": "A senha é muito fraca", "wrongPassword": "Senha incorreta", "noUser": "Nenhum usuário autenticado encontrado para excluir", "noAccessToken": "Nenhuma sessão Shopify válida encontrada", "firebaseDeletionFailed": "Falha ao excluir sua conta de autenticação", "accountDeletionFailed": "Falha ao excluir a conta: {message}", "unknownDeletionError": "Ocorreu um erro desconhecido", "shopifyGeneric": "[TRANSLATE] An error occurred with Shopify authentication", "firebaseGeneric": "[TRANSLATE] An error occurred with Firebase authentication"}}, "common": {"add": "<PERSON><PERSON><PERSON><PERSON>", "apply": "Aplicar", "back": "Voltar", "cancel": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "continue": "<PERSON><PERSON><PERSON><PERSON>", "daysAgo": "@days dias atrás", "delete": "Excluir", "done": "<PERSON><PERSON>", "edit": "<PERSON><PERSON>", "email": "E-mail", "error": "Erro", "finished": "Acabou!", "forgotPassword": "Esqueceu sua senha?", "help": "Ajuda e documentação", "loading": "Carregando...", "next": "Próximo", "no": "Não", "ok": "OK", "password": "<PERSON><PERSON>", "previous": "Anterior", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "remove": "Remover", "retry": "Tentar novamente", "save": "<PERSON><PERSON>", "success": "Sucesso", "today": "Hoje", "undo": "<PERSON><PERSON><PERSON>", "untitled": "<PERSON><PERSON> tí<PERSON>lo", "warning": "Aviso", "yes": "<PERSON>m", "yesterday": "Ontem", "stopKnitting": "Parar de tricotar?"}, "errors": {"dataLoadFailed": "Falha ao carregar dados", "generationFailed": "Falha ao gerar instruções de tricô", "networkError": "Erro de rede. Verifique sua conexão.", "noCurrentInstructionFound": "Nenhuma instrução atual encontrada", "noInstructionsAvailable": "Nenhuma instrução disponível", "operationFailed": "A operação falhou. Tente novamente.", "partialSignOut": "Ocorreu um erro de logout parcial", "saveFailed": "<PERSON><PERSON><PERSON> a<PERSON> sal<PERSON> as alteraç<PERSON><PERSON>", "shapeTooSmall": "A forma é muito pequena para gerar um padrão", "signOutFailed": "Falha ao sair, tente novamente", "unexpected": "Ocorreu um erro inesperado. Tente novamente.", "unknownError": "Ocorreu um erro inesperado", "shapeOutOfBounds": "A forma vai sair dos limites.", "rotationPrevented": "A rotação foi bloqueada, a forma vai sair dos limites."}, "help": {"categories": {"advancedTechniques": "Técnicas avançadas", "gettingStarted": "Começando", "gridFeatures": "Recursos de grade", "keyboardShortcuts": "Atalhos de teclado", "shapeTools": "Ferramentas de forma"}, "collapse": "Colapso", "expand": "Expandir", "search": {"clear": "<PERSON><PERSON> pes<PERSON>a", "hint": "Pesquisar tópicos de ajuda..."}, "title": "Ajuda e documentação", "topics": {"advancedTechniques": {"curveMode": {"description": "Ative o modo curva para criar formas suaves e curvas. Edite as curvas arrastando os pontos de controle.", "title": "Usando o modo de curva"}, "mirrorMode": {"description": "Alterne o modo espelho para criar automaticamente cópias espelhadas de suas formas. Ótimo para designs simétricos.", "title": "<PERSON><PERSON> es<PERSON>"}, "workingWithGroups": {"description": "Agrupe formas para manipulá-las juntas. Você também pode editar formas individuais dentro de um grupo usando o painel de propriedades do grupo.", "title": "Trabalhando com grupos"}}, "gettingStarted": {"firstShape": {"description": "Clique no botão + na barra de ferramentas e selecione uma forma. Em seguida, clique na tela para colocá-la. Você pode redimensionar e girar formas usando as alças.", "title": "Criando sua primeira forma"}, "scrollableToolbar": {"description": "A barra de ferramentas na parte superior da tela é rolável. Deslize para a esquerda ou direita para acessar mais ferramentas. Procure os indicadores de seta nas bordas.", "title": "Usando a barra de ferramentas rolável"}, "selectingShapes": {"description": "Clique em uma forma para selecioná-la, depois arraste para movê-la. Use as alças para redimensionar ou girar a forma.", "title": "Selecionando e movendo formas"}}, "gridFeatures": {"gridLabels": {"description": "Exiba ou oculte rótulos de grade usando o ícone de rótulo. Os rótulos mostram posições de agulha usando notação L/R, com L1 e R1 no centro.", "title": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>"}, "gridSnapping": {"description": "Ative/desative o grid snapping com o ícone de grade na barra de ferramentas. Quando habilitado, as formas se alinharão automaticamente à grade.", "title": "Ajuste de grade"}, "realWorldDimensions": {"description": "As dimensões da forma podem ser exibidas em centímetros com base no passo da agulha da sua máquina de tricô. Alterne entre unidades de grade e cm usando o botão de unidade no painel de propriedades da forma. No zoom máximo (500%), cada célula da grade representa uma agulha.", "title": "Dimensões e peso"}, "zoomAndPan": {"description": "Aperte para aumentar/diminuir o zoom, arraste com dois dedos para panorâmica. A grade se ajusta automaticamente com o nível de zoom.", "title": "Zoom e panorâmica"}}, "keyboardShortcuts": {"editing": {"description": "Ctrl+G: Agrupar formas selecionadas Ctrl+Shift+G: Desagrupar H: Inverter horizontalmente V: Inverter verticalmente Excluir: Remover forma selecionada", "title": "Atalhos de edição"}, "history": {"description": "Ctrl+Z: Desfazer Ctrl+Y ou Ctrl+Shift+Z: Refazer", "title": "Atalhos de história"}, "selection": {"description": "Shift+Clique: Selecionar várias formas Ctrl+A: Selecionar todas as formas Esc: <PERSON><PERSON><PERSON> todas", "title": "Atalhos de seleção"}}, "shapeTools": {"flipping": {"description": "Use os botões de inversão na barra de ferramentas ou pressione as teclas H/V para inverter as formas selecionadas horizontal ou verticalmente.", "title": "Invertendo formas"}, "grouping": {"description": "Selecione várias formas com Shift+Clique e, em seguida, pressione Ctrl+G ou use o botão de grupo para agrupá-las.", "title": "Agrupando formas"}, "properties": {"description": "Visualize e edite propriedades de forma no painel HUD. Você pode ajustar posição, tamanho e rotação precisamente.", "title": "<PERSON><PERSON><PERSON><PERSON> da forma"}}}}, "home": {"drawer": {"calculators": "Calculadoras", "courses": "Cursos", "helpSupport": "Ajuda e suporte", "home": "Home", "logout": "Logout", "settings": "Configurações", "shop": "<PERSON><PERSON><PERSON>"}, "projectsGrid": {"comingSoon": "Em breve!", "myItems": "Meus itens", "newItem": "Novo item", "xoxknitItems": "Itens xoxknit"}, "title": "<PERSON>r"}, "knittingInstructions": {"bottomWidth": "Largura inferior", "description": "Descrição", "errors": {"calculationFailed": "Falha ao calcular estatísticas de padrão", "generationFailed": "Falha ao gerar instruções de tricô"}, "generalInfo": "Informações gerais", "height": "Altura", "interactive": {"basicInstructions": "Instruções básicas:", "completeAndContinue": "Concluído e continuar para linhas @rows", "completeSequence": "Sequência completa", "completionMessage": "Parabéns! Você concluiu o tricô deste padrão.", "confirmClose": "Tem certeza de que deseja cancelar as instruções de tricô e retornar ao criador de formas?", "endingRowCounter": "Contador de linhas finais", "helpTitle": "Como usar a visualização de tricô", "instructions": {"completeButton": "• Pressione 'Concluir e continuar' ou 'Concluir sequência' após concluir a etapa atual", "followHighlighted": "• Siga as instruções de tricô destacadas para cada etapa", "glassesIcon": "• Toque no ícone de óculos na barra de aplicativos para visualizar o padrão inteiro", "groupedRows": "• Grupos de linhas idênticas são tratados como uma única etapa de instrução", "needleRanges": "• Os intervalos de agulhas são destacados em roxo para melhor visibilidade", "previousButton": "• Use o botão 'Anterior' para voltar à instrução anterior", "printIcon": "• Use o ícone de impressão para imprimir seu padrão para referência offline", "progressBar": "• A barra de progresso avança após completar uma sequência inteira, não linhas individuais", "rowCounter": "• Um contador mostra em qual linha repetida você está trabalhando no momento", "rowsXY": "• Você verá &quot;Linhas XY&quot; no título e um rótulo mostrando quantas vezes repetir", "showPatternView": "• Toque em &quot;Mostrar visualização do padrão&quot; na parte inferior para ver o padrão completo", "zoomPan": "• Na visualização do padrão, você pode aplicar zoom e panorâmica para ver detalhes"}, "knittingInstructions": "Instruções de tricô", "machineInfo": {"gauge": "@stitches pt/cm × @rows linhas/cm", "notSelected": "Não selecionado"}, "navigationTools": "Navegação e ferramentas:", "needleSettings": "Configurações da agulha", "noInstructionsAvailable": "Nenhuma instrução disponível", "patternHelp": {"additionalWarnings": "• Avisos adicionais podem aparecer para tipos de linhas específicos", "changeRows": "• Mudança: linhas com aumentos ou diminuições", "discontinuousRows": "• Descontínuo: carreiras com lacunas no padrão de pontos", "interactiveTitle": "Recursos interativos", "lightGrayCells": "• As c<PERSON><PERSON>las cinza claro representam agulhas para pular (sem ponto)", "purpleCells": "• As c<PERSON><PERSON>las roxas representam pontos a serem tricotados", "repeatRows": "• Repetição: v<PERSON><PERSON>s carreiras idênticas que podem ser tricotadas juntas", "rowTypesTitle": "Tipos de linha e indicadores", "symmetricRows": "• Simétrico: linhas com simetria de espelho no padrão", "tapRow": "• Toque em uma linha para ver instruções detalhadas", "tapStitches": "• Toque em pontos individuais para obter detalhes", "title": "Ajuda para visualização de padrões", "toggleView": "• Alternar entre a visualização em grade e a visualização em texto", "understandingTitle": "Compreendendo a visualização", "warningsTitle": "Avisos", "wideGaps": "• Grandes espaços: espaços anormalmente grandes entre os pontos", "zoomControls": "• Use os controles de zoom para redimensionar a grade"}, "patternVisualization": "Visualização de padrões:", "preview": {"blueStitches": "Pontos azu<PERSON>", "magentaStitches": "Pontos magenta", "needles": "<PERSON><PERSON><PERSON><PERSON> L@left para R@right", "noKnittingPatternAvailable": "Nenhum padrão de tricô disponível para visualização", "patternPreview": "Prévia do padrão: @name", "previewNotAvailable": "Pré-visualização não disponível", "rowsTall": "@rows linhas altas", "showGrid": "Mostrar grade", "showYarnConnections": "Mostrar conexões de fios", "stitchesWide": "@width pontos largos"}, "previousRows": "Voltar para linhas @rows", "previousStep": "Passo anterior", "progressTracker": "Rastreador de progresso", "repeatProgress": "<PERSON><PERSON> @current de @total", "repeatingRows": "<PERSON><PERSON> repetidas:", "rowInstructions": "<PERSON><PERSON> @row", "rowsInstructions": "Linhas @start-@end", "startOver": "<PERSON><PERSON><PERSON><PERSON>", "startingRowCounter": "Con<PERSON>or de linhas inicial", "stayHere": "Fique aqui", "visualizer": {"noPatternAvailable": "Nenhum padrão disponível", "noPatternCreateFirst": "Nenhum padrão disponível. Crie um padrão primeiro.", "pattern": "Padrão:", "patternView": "Visualização de padrão", "patternVisualization": "Visualização completa do padrão, de L@needles a R@needles.", "zoomPanInstructions": "Use o gesto de pinça para dar zoom e mover para ver detalhes."}}, "machineDetails": "Detal<PERSON> da máquina", "maximumNeedles": "<PERSON><PERSON><PERSON><PERSON>", "measurements": "<PERSON><PERSON><PERSON>", "needlesAvailable": "<PERSON><PERSON><PERSON><PERSON> disponí<PERSON>is", "needlesRequired": "<PERSON><PERSON><PERSON><PERSON> necess<PERSON>", "print": {"failedToPrint": "<PERSON>alha ao imprimir o padrão: @error", "noPatternAvailable": "Nenhum padrão disponível para impressão", "patternSentToPrinter": "Padrão enviado para a impressora", "printError": "Erro <PERSON>", "printSent": "Impressão enviada", "printingFutureUpdate": "A impressão de padrões será implementada em uma atualização futura", "savingFutureUpdate": "O salvamento de padrões será implementado em uma atualização futura"}, "rows": "<PERSON><PERSON>", "shapeDesign": "Design de forma", "stitches": "Pontos", "strands": "<PERSON><PERSON>", "summary": {"bottomWidth": "Largura inferior", "cm": "cm", "description": "Descrição", "generalInfo": "Informações gerais", "height": "Altura", "machine": {"needlesAvailable": "<PERSON><PERSON><PERSON><PERSON> disponí<PERSON>is", "needlesRequired": "<PERSON><PERSON><PERSON><PERSON> necess<PERSON>"}, "machineDetails": "Detal<PERSON> da máquina", "maximumNeedles": "<PERSON><PERSON><PERSON><PERSON>", "rows": "<PERSON><PERSON>", "shapeDesign": "Design de forma", "stitches": "Pontos", "title": "Resumo das instruções de tricô", "topWidth": "Largura superior", "totalRowsToKnit": "Total de carreiras para tricotar", "totalYarnRequired": "Total de fios necessários", "yarn": {"strands": "@count fios", "supplier": "Fornecedor: @supplier", "tension": "Tensão: @tension", "title": "Tí<PERSON>lo do fio: @title"}, "yarnSpecifications": "Especificações do fio"}, "topWidth": "Largura superior", "totalRowsToKnit": "Total de carreiras para tricotar", "totalYarnRequired": "Total de fios necessários", "yarnSpecifications": "Especificações do fio"}, "login": {"emailHint": "Insira seu e-mail", "loginButton": "Conecte-se", "loginFailed": "Falha no login. Tente novamente.", "noAccount": "Não tem uma conta?", "passwordHint": "Digite sua senha", "signUpLink": "Inscrever-se", "title": "xoxknit", "validation": {"emailInvalid": "Por favor, insira um e-mail válido", "emailRequired": "E-mail é obrigatório", "passwordLength": "A senha deve ter pelo menos 6 caracteres", "passwordRequired": "Senha é necessária"}, "welcomeBack": "Bem vindo de volta!"}, "myItems": {"archivedItems": "Itens arquivados", "inProgress": "Em andamento", "completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deleteConfirmation": {"message": "Tem certeza que deseja excluir este item? Esta ação não pode ser desfeita.", "title": "Excluir item"}, "emptyStateActive": {"createButton": "Criar novo item", "message": "Comece criando seu primeiro item de tricô", "title": "Nenhum item ainda!"}, "emptyStateArchived": {"message": "Itens arquivados aparecerão aqui", "title": "Nenhum item arquivado!", "viewActiveButton": "Ver itens ativos"}, "itemCard": {"archive": "<PERSON><PERSON><PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "continue": "<PERSON><PERSON><PERSON><PERSON>", "markComplete": "Marcar como concluído", "markIncomplete": "Marcar como incompleto", "knitAgain": "Tricotar novamente", "unarchive": "Desar<PERSON><PERSON>", "untitledItem": "<PERSON>em sem título"}, "refreshItems": "<PERSON><PERSON><PERSON><PERSON> itens", "showActive": "Mostrar itens ativos", "showArchived": "Mostrar itens arquivados", "snackbar": {"archiveError": "Falha ao arquivar item", "archivedMessage": "O item foi arquivado", "deleteError": "Falha ao excluir item", "itemArchived": "<PERSON><PERSON> a<PERSON>", "itemRestored": "<PERSON>em foi restaurado", "loadError": "Falha ao carregar itens", "markedCompleted": "Item marcado como concluído", "markedIncomplete": "Item marcado como incompleto", "markCompletedError": "Falha ao marcar item como concluído", "markIncompleteError": "Falha ao marcar item como incompleto", "success": "Sucesso", "successDeleted": "Item excluído com sucesso", "successUnarchived": "<PERSON><PERSON> desar<PERSON>", "unarchiveError": "Falha ao desarquivar item", "undoButton": "<PERSON><PERSON><PERSON>", "undoComplete": "Operação desfeita"}, "stepDescription": {"completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gaugeCalculator": "Calculadora de tensão", "interactiveKnitting": "Tricô interativo", "itemDetails": "Detalhes do item", "patternSummary": "Resumo do padrão", "shapeEditor": "Editor de formas", "unknownStep": "Etapa desconhecida"}, "title": "Meus itens"}, "newItemWizard": {"back": "Voltar", "cancel": "<PERSON><PERSON><PERSON>", "confirmCancel": {"continue": "Con<PERSON><PERSON>r editando", "discard": "Descar<PERSON>", "message": "Tem certeza que deseja descartar suas alterações?", "title": "Descartar alterações?"}, "continue": "<PERSON><PERSON><PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": {"addShapeRequired": "Adicione pelo menos uma forma antes de continuar", "loadFailed": "Falha ao carregar dados do item", "saveFailed": "Falha ao salvar o item", "shapeEditorLoading": "Editor de formas está carregando...", "shapeEditorNotFound": "Editor de formas não encontrado. Tente novamente.", "title": "Erro"}, "form": {"addNewMachine": "Adicionar nova máquina...", "additionalNotes": "Notas adicionais", "color": "Cor", "composition": "Composição", "description": "Descrição", "itemName": "Nome do item", "machine": "Máquina", "neededBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> por", "optionalInformation": "Informações opcionais", "requiredInformation": "Informações necessárias", "startDate": "Data de início", "strands": "<PERSON><PERSON>", "supplier": "Fornecedor", "swatchNumber": "Amostra #", "tension": "Tensão", "validation": {"number": "Por favor, insira um número válido", "required": "Por favor, insira @field", "enter": "Por favor, insira @field", "validNumber": "Por favor, insira um número válido para @field", "checkFields": "Por favor, verifique todos os campos obrigatórios"}, "yarnOnHand": "<PERSON>os disponí<PERSON> (g)", "yarnTitle": "Título do fio"}, "generatingInstructions": "Gerando instruções de tricô...", "loadingDialog": {"generating": "Gerando instruções de tricô..."}, "navigation": {"next": "Próximo", "previous": "anterior"}, "next": "Próximo", "save": "<PERSON><PERSON>", "steps": {"createNewItem": "Criar novo item", "gaugeCalculator": "Calculadora de medida", "interactiveKnitting": "Tricô interativo", "itemDetails": "Detalhes do item", "knittingZoneConfig": "Configuração de Zonas de Tricô", "patternSummary": "Resumo do padrão", "shapeEditor": "Editor de formas"}, "success": {"saved": "Item salvo com sucesso", "detailsUpdated": "Detalhes do item atualizados com sucesso", "validation_errors": "Erro de validação", "validation_checkFields": "Por favor, verifique todos os campos obrigatórios"}, "title": "Criar novo item"}, "patternPrinter": {"finished": "Finalizado!", "gaugeCalculation": "Cálculo de calibre", "generalInfo": "Informações gerais", "itemDescription": "Descrição do item", "jersey": "<PERSON><PERSON>", "knitRows": "Tricotar @count carreiras", "knitting": {"noStitches": "Nenhum ponto para tricotar"}, "knittingInstructions": "Instruções de tricô", "leftNeedleSetting": "Configuração da agulha esquerda", "machineType": "T<PERSON><PERSON> de m<PERSON>", "measurements": "<PERSON><PERSON><PERSON>", "page": "página @current de @total", "results": "Resul<PERSON><PERSON>", "rightNeedleSetting": "Configuração correta da agulha", "rowsInSwatch": "5 - <PERSON><PERSON> na amostra", "rowsPer10Cm": "<PERSON><PERSON> por 10 cm:", "rowsToKnit": "Carreiras para tricotar", "shapeDesign": "Design de forma", "startRow": "Começar a linha", "stitchType": "Tipo de ponto", "stitchesInSwatch": "4 - Pontos na amostra", "stitchesPer10Cm": "Pontos por 10cm:", "supplier": "Fornecedor", "swatchLength": "2 - Comprimento da amostra", "swatchWeight": "3 - <PERSON><PERSON>o da amostra", "swatchWidth": "1 - <PERSON><PERSON><PERSON> da amostra", "tensionSetting": "Configuração de tensão", "totalYarnRequired": "Total de fios necessários", "yarnRequirements": "Requisitos de fio", "yarnTitleAndStrands": "Título do fio + fios"}, "projectsView": {"createProject": "Criar novo projeto", "filter": "Filtrar projetos", "noProjects": "Nenhum projeto ainda", "sort": "Ordenar por", "title": "Projetos"}, "resetPassword": {"emailHint": "Insira seu endereço de e-mail", "emailLabel": "E-mail", "heading": "Esqueceu sua senha?", "instructions": "Insira seu endereço de e-mail e enviaremos instruções para redefinir sua senha.", "resetButton": "<PERSON><PERSON><PERSON><PERSON>", "successMessage": "Instruções para redefinir sua senha foram enviadas para seu e-mail.", "title": "<PERSON><PERSON><PERSON><PERSON>", "validation": {"emailInvalid": "Por favor, insira um e-mail válido", "emailRequired": "E-mail é obrigatório"}}, "settings": {"appearance": {"darkMode": {"subtitle": "Alternar tema do modo escuro", "title": "<PERSON><PERSON> es<PERSON>ro"}}, "currency": {"dialogTitle": "Selecione a moeda", "eur": "EUR (€)", "subtitle": "Selecione sua moeda preferida", "title": "<PERSON><PERSON>", "usd": "Dólar americano ($)"}, "knittingMachines": {"subtitle": "Gerencie suas máquinas de tricô", "title": "Minhas Máquinas"}, "language": {"dialogTitle": "Selecione o idioma", "subtitle": "Selecione seu idioma preferido", "title": "Idioma do aplicativo"}, "measurement": {"centimeters": "Centímetros", "dialogTitle": "Selecionar unidades", "inches": "Polegadas", "subtitle": "Escolha seu sistema de unidades preferido", "title": "Unidades"}, "sections": {"appearance": "Aparência", "currency": "<PERSON><PERSON>", "knittingMachines": "Máquinas de tricô", "language": "Idioma", "measurement": "Unidades de medida", "accountManagement": "Gerenciamento de conta"}, "accountManagement": {"deleteAccount": {"title": "Excluir conta", "subtitle": "Exclua todos os seus dados, incluindo projetos, máquinas e padrões", "confirmTitle": "Excluir conta?", "confirmMessage": "Isso excluirá permanentemente sua conta e todos os dados associados. Esta ação não pode ser desfeita.", "typeToConfirm": "Digite DELETE para confirmar", "confirm": "Excluir minha conta", "successTitle": "Conta excluída", "successMessage": "Sua conta foi excluída com sucesso", "errorMessage": "Falha ao excluir conta. Por favor, tente novamente.", "deletingMessage": "Excluindo sua conta..."}}, "title": "Configurações"}, "shapeEditor": {"confirmReset": {"cancel": "<PERSON><PERSON><PERSON>", "confirm": "Reiniciar", "message": "Redefinir a forma? <PERSON>das as alterações serão perdidas.", "title": "Redefinir forma?"}, "controls": {"generate": "<PERSON><PERSON><PERSON>", "mirror": "<PERSON><PERSON><PERSON><PERSON>", "previewPattern": "Padrão de visualização", "redo": "<PERSON><PERSON><PERSON>", "reset": "Reiniciar", "save": "<PERSON><PERSON>", "undo": "<PERSON><PERSON><PERSON>"}, "errors": {"saveFailed": "Falha ao salvar a forma", "shapeInvalid": "Configuração de forma inválida", "shapeTooSmall": "A forma é muito pequena para gerar um padrão"}, "groupInfo": {"dimensions": {"center": "Centro do grupo: (@x, @y)", "height": "Altura do grupo: @height px", "rotation": "Rotação:", "width": "Largura do grupo: @width px"}, "numberOfShapes": "Número de formas: @count", "shapeTypes": "Tipos de formas:", "title": "Informações do grupo"}, "help": {"categories": {"advancedTechniques": "Técnicas avançadas", "gettingStarted": "Começando", "gridFeatures": "Recursos de grade", "keyboardShortcuts": "Atalhos de teclado", "shapeTools": "Ferramentas de forma"}, "topics": {"creatingFirstShape": {"description": "Clique no botão + na barra de ferramentas e selecione uma forma. Em seguida, clique na tela para colocá-la. Você pode redimensionar e girar formas usando as alças.", "title": "Criando sua primeira forma"}, "editingShortcuts": {"description": "Ctrl+G: Agrupar formas selecionadas Ctrl+Shift+G: Desagrupar H: Inverter horizontalmente V: Inverter verticalmente Excluir: Remover forma selecionada", "title": "Atalhos de edição"}, "flippingShapes": {"description": "Use os botões de inversão na barra de ferramentas ou pressione as teclas H/V para inverter as formas selecionadas horizontal ou verticalmente.", "title": "Invertendo formas"}, "gridLabels": {"description": "Exiba ou oculte rótulos de grade usando o ícone de rótulo. Os rótulos mostram posições de agulha usando notação L/R, com L1 e R1 no centro.", "title": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>"}, "gridSnapping": {"description": "Ative/desative o grid snapping com o ícone de grade na barra de ferramentas. Quando habilitado, as formas se alinharão automaticamente à grade.", "title": "Encaixe de grade"}, "groupingShapes": {"description": "Selecione várias formas com Shift+Clique e, em seguida, pressione Ctrl+G ou use o botão de grupo para agrupá-las.", "title": "Agrupando formas"}, "historyShortcuts": {"description": "Ctrl+Z: Desfazer Ctrl+Y ou Ctrl+Shift+Z: Refazer", "title": "Atalhos de história"}, "mirrorMode": {"description": "Alterne o modo espelho para criar automaticamente cópias espelhadas de suas formas. Ótimo para designs simétricos.", "title": "<PERSON><PERSON> es<PERSON>"}, "realWorldDimensions": {"description": "As dimensões da forma podem ser exibidas em centímetros com base no passo da agulha da sua máquina de tricô. Alterne entre unidades de grade e cm usando o botão de unidade no painel de propriedades da forma. No zoom máximo (500%), cada célula da grade representa uma agulha.", "title": "Dimensões do item em centímetros"}, "scrollableToolbar": {"description": "A barra de ferramentas na parte superior da tela é rolável. Deslize para a esquerda ou direita para acessar mais ferramentas. Procure os indicadores de seta nas bordas.", "title": "Usando a barra de ferramentas rolável"}, "selectingMovingShapes": {"description": "Clique em uma forma para selecioná-la, depois arraste para movê-la. Use as alças para redimensionar ou girar a forma.", "title": "Selecionando e movendo formas"}, "selectionShortcuts": {"description": "Shift+Clique: Selecionar várias formas Ctrl+A: Selecionar todas as formas Esc: <PERSON><PERSON><PERSON> todas", "title": "Atalhos de seleção"}, "shapeProperties": {"description": "Visualize e edite propriedades de forma no painel HUD. Você pode ajustar posição, tamanho e rotação precisamente.", "title": "<PERSON><PERSON><PERSON><PERSON> da forma"}, "usingCurveMode": {"description": "Ative o modo curva para criar formas suaves e curvas. Edite as curvas arrastando os pontos de controle.", "title": "Usando o modo de curva"}, "workingWithGroups": {"description": "Agrupe formas para manipulá-las juntas. Você também pode editar formas individuais dentro de um grupo usando o painel de propriedades do grupo.", "title": "Trabalhando com grupos"}, "zoomPan": {"description": "Aperte para aumentar/diminuir o zoom, arraste com dois dedos para panorâmica. A grade se ajusta automaticamente com o nível de zoom.", "title": "Zoom e panorâmica"}}}, "propertyHud": {"group": "Grupo", "shape": "Forma", "size": "<PERSON><PERSON><PERSON>", "hidePanel": "O<PERSON>lta<PERSON>", "units": {"cm": "cm", "st": "p", "rw": "c", "stRw": "p, c"}, "errors": {"maxSize": "<PERSON><PERSON><PERSON> excede limites ({width}{widthUnit} x {height}{heightUnit} máx.).", "maxWidth": "<PERSON><PERSON><PERSON> excede limite ({width}{widthUnit} máx.).", "maxHeight": "Altura excede limite ({height}{heightUnit} máx.)."}, "history": {"resizeGroup": "Redimensionar grupo", "manualSizeEdit": "Edição manual de tamanho", "position": "Posição", "moveLeft": "Mover 5 Agulhas para Esquerda", "moveRight": "Mover 5 Agulhas para Direita"}, "shapes": {"group": "Grupo", "rectangle": "Re<PERSON><PERSON><PERSON><PERSON>", "rightTriangle": "Triângulo retângulo", "trapezoid": "Trapé<PERSON>", "triangle": "Triângulo"}, "dimensionIndicator_stitches": "@count pontos", "dimensionIndicator_rows": "@count carreiras"}, "shapes": {"group": "Grupo", "rectangle": "Re<PERSON><PERSON><PERSON><PERSON>", "rightTriangle": "Triângulo retângulo", "trapezoid": "Trapé<PERSON>", "triangle": "Triângulo"}, "title": "Editor de formas", "toolbar": {"actions": {"addShape": "Adicionar forma", "centerLineSnapping": {"disable": "  encaixar na linha central", "enable": "✓ encaixar na linha central"}, "deleteShape": "Excluir forma", "duplicateShape": "Forma duplicada", "flipHorizontal": "Inverter horizontalmente", "flipVertical": "Virar verticalmente", "gridSnapping": {"disable": "  Ajustar à grade", "enable": "✓ Ajustar à grade"}, "group": "Formas de grupo", "mirrorMode": {"disable": "  <PERSON><PERSON> es<PERSON>", "enable": "✓ <PERSON><PERSON> es<PERSON>ho"}, "rotate": "<PERSON><PERSON><PERSON>", "ungroup": "Desagrupar formas"}, "addShapes": "<PERSON><PERSON><PERSON><PERSON> formas", "curveMode": "Modo de curva", "deleteShape": "Excluir forma", "disableCenterLineSnapping": "  Ajustar à linha central", "disableGridSnapping": "  Ajustar à grade", "disableMirrorMode": "Desativar modo espelho", "duplicateShape": "Forma duplicada (tecla D)", "editCurves": "Editar curvas", "enableCenterLineSnapping": "✓ Ajustar à linha central", "enableGridSnapping": "✓ Ajustar à grade", "exitCurveMode": "Sair do modo de curva", "flipHorizontally": "Inverter horizontalmente (tecla H)", "flipVertically": "Inverter verticalmente (tecla V)", "group": "Grupo", "groupShapes": "Formas de grupo (Ctrl+G)", "mirrorMode": "Habilitar modo espelho", "rectangle": "Re<PERSON><PERSON><PERSON><PERSON>", "resetZoom": "Ampliar para área de trabalho", "rightTriangle": "Triângulo retângulo", "rotate45Clockwise": "Girar 45° no sentido horário", "sections": {"grouping": "Ferramentas de agrupamento", "shapes": "Ferramentas de forma", "transform": "Ferramentas de transformação", "utility": "Ferramentas utilitárias"}, "transformTools": "Ferramentas de transformação", "trapezoid": "Trapé<PERSON>", "triangle": "Triângulo", "ungroup": "Desagrupar", "ungroupShape": "Desagrupar formas (Ctrl+Shift+G)", "utilityTools": "Ferramentas utilitárias"}, "tools": {"draw": "Empate", "erase": "<PERSON><PERSON><PERSON>", "move": "Mover", "resize": "Redimensionar", "rotate": "<PERSON><PERSON><PERSON>", "select": "Selecione"}, "shapeEditor_groupInfo_title": "Informações do Grupo", "shapeEditor_groupInfo_numberOfShapes": "Número de formas: {count}"}, "signup": {"acceptTerms": "Por favor, aceite os termos e condições...", "confirmPassword": "Confirme sua senha", "confirmPasswordHint": "Confirme sua senha", "createAccountButton": "Criar uma conta", "createPassword": "<PERSON><PERSON><PERSON> uma senha", "fullName": "Nome completo", "fullNameHint": "Digite seu nome completo", "haveAccount": "Já tem uma conta?", "loginLink": "Conecte-se", "phoneNumber": "Número de telefone", "phoneNumberHint": "Insira seu número de telefone", "signupFailed": "Falha na inscrição. Tente novamente.", "subtitle": "Crie uma conta para começar", "termsAndConditions1": "Eu concordo com os ", "termsAndConditions2": "termos e condições", "title": "Criar uma conta", "validation": {"confirmPasswordRequired": "Por favor confirme sua senha", "nameLength": "O nome deve ter pelo menos 2 caracteres", "nameRequired": "Nome é obrigatório", "passwordRequirements": {"length": "A senha deve ter pelo menos 8 caracteres", "number": "A senha deve conter pelo menos um número", "uppercase": "A senha deve conter pelo menos uma letra mai<PERSON>cula"}, "passwordsMismatch": "As senhas não correspondem", "phoneNumberInvalid": "Por favor, insira um número de telefone válido", "phoneNumberRequired": "Número de telefone é obrigatório"}, "welcomeMessage": "Bem-vindo ao xoxknit!"}, "swatchCalculator": {"enterMeasurement": "Insira sua medida", "enterSwatchLength": "Insira o comprimento da amostra", "enterSwatchRows": "<PERSON><PERSON><PERSON> lin<PERSON> de amostra", "enterSwatchStitches": "Insira os pontos de amostra", "enterSwatchWeight": "Insira o peso da amostra", "enterSwatchWidth": "Insira a largura da amostra", "gotIt": "<PERSON><PERSON><PERSON>", "infoTooltip": "Mais informações...", "learnMore": "Saiba mais sobre @label", "measurements": "<PERSON><PERSON><PERSON>", "resultLabels": {"rowsPer10Cm": "Linhas por 10cm", "stitchesPer10Cm": "Pontos por 10cm", "weightPer100CmSquared": "Peso por 100cm²"}, "results": "Resul<PERSON><PERSON>", "rowsInSwatch": {"description": "Conte o número total de linhas de cima para baixo da sua amostra.", "hint": "<PERSON><PERSON>", "label": "5 - lin<PERSON> na amostra"}, "stitchesInSwatch": {"description": "Conte o número total de pontos em uma fileira da sua amostra.", "hint": "Pontos", "label": "4 - pontos na amostra"}, "swatchLength": {"description": "A medida vertical da sua amostra. <PERSON><PERSON> as linhas de cima para baixo.", "hint": "Comprimento", "label": "2 - compriment<PERSON> da amostra"}, "swatchWeight": {"description": "Peso da sua amostra em gramas. <PERSON><PERSON> ajuda a calcular os requisitos de fio. Não tem problema usar o peso total da amostra (em vez das dimensões específicas medidas) para permitir lã extra necessária.", "hint": "Peso", "label": "3 - peso da amostra"}, "swatchWidth": {"description": "A medida horizontal da sua amostra. Conte os pontos de ponta a ponta.", "hint": "<PERSON><PERSON><PERSON>", "label": "1 - la<PERSON><PERSON> da amostra"}, "title": "Calculadora de amostras"}, "userMachines": {"addMachine": "<PERSON><PERSON><PERSON><PERSON>", "addYourFirstMachine": "Adicione sua primeira máquina de tricô", "deleteConfirm": {"message": "Tem certeza de que deseja excluir esta máquina? Esta ação não pode ser desfeita.", "title": "<PERSON><PERSON><PERSON> m<PERSON>a"}, "editMachine": "<PERSON><PERSON>", "errors": {"deleteFailed": "Falha ao excluir a máquina", "loadFailed": "Falha ao carregar má<PERSON>as", "saveFailed": "<PERSON>alha ao salvar a máquina"}, "machineDetails": {"gauge": "Medidor", "gaugeHint": "Medi<PERSON>", "name": "Nome da máquina", "nameHint": "Digite um apelido para esta máquina", "needleBed": "<PERSON><PERSON> a<PERSON>ha", "needleBedHint": "Número de a<PERSON>has", "type": "T<PERSON><PERSON> de m<PERSON>", "typeHint": "Selecione o tipo de máquina"}, "machineInfo": {"needles": "@count agulhas", "patternControl": "Controle de padrões", "pitch": "@pitchmm arremesso"}, "noMachines": "<PERSON><PERSON><PERSON><PERSON> má<PERSON>a configurada", "title": "Minhas máquinas de tricô"}, "knittingProgress": {"percentComplete": "@percent% concluído", "row": "car<PERSON><PERSON> @number", "needleRangeBoth": "E@left - D@right", "needleRangeLeft": "E1 - E@number", "needleRangeSingleLeft": "E1", "needleRangeRight": "D1 - D@number", "needleRangeSingleRight": "D1", "noActiveNeedles": "<PERSON><PERSON><PERSON><PERSON> agulha ativa", "dimensionIndicator_stitches": "@count pontos", "dimensionIndicator_rows": "@count carreiras"}}